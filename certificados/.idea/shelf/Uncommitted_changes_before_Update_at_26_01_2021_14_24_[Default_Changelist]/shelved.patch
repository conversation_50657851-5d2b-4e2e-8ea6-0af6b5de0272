Index: ../Servidor/sql/create.sql
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>create table if not exists empresa (\r\n    id bigint(20) not null auto_increment,\r\n    dominio varchar(255) not null,\r\n    nome varchar(255) not null,\r\n    endereco varchar(255) not null,\r\n    ativar_indicacoes bit(1) not null default 0,\r\n    whatsapp varchar(255) not null,\r\n    descricao varchar(255) not null,\r\n    instagram varchar(255) null,\r\n    titulo_fotos varchar(50) not null,\r\n    titulo_destaques varchar(50) not null,\r\n    link_maps varchar(500) not null,\r\n    qtde_mensagens bigint(20) default 200,\r\n    meio_de_envio varchar(255) not null,\r\n    primary key (id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table campanha(\r\n  id bigint(20) not null auto_increment,\r\n  nome varchar(255) not null,\r\n  mensagem text not null,\r\n  data_criacao datetime not null,\r\n  empresa_id bigint(20) not null,\r\n  filtro_id bigint(20) not null,\r\n  qtde_enviadas bigint(20),\r\n  qtde_lidas bigint(20),\r\n  tipo_de_envio varchar(255) not null default 'Unico',\r\n  horario_envio datetime null,\r\n  status varchar(255) not null,\r\n  qtde_dias_nova_notificacao bigint(20) not null default 1,\r\n  foi_testada bit(1) null default 0,\r\n  primary key (id)\r\n);\r\n\r\ncreate table contato_empresa (\r\n  id bigint(20) not null auto_increment,\r\n  nome varchar(255) not null,\r\n  email varchar(255) not null,\r\n  empresa varchar(255) not null,\r\n  telefone varchar(255) not null,\r\n  primary key (id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\n\r\ncreate table link_encurtado (\r\n  id bigint(20) not null auto_increment,\r\n  token varchar(3) not null,\r\n  url varchar(500) not null,\r\n  visitas bigint(20) not null,\r\n  data_criacao datetime not null,\r\n  ultima_visita datetime,\r\n  primary key(id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table if not exists notificacao (\r\n    id bigint(20) not null auto_increment,\r\n    ativada bigint(1) not null,\r\n    mensagem varchar(255) not null,\r\n    empresa_id bigint(20) not null,\r\n    tipo_de_notificacao varchar(255) not null,\r\n    pode_desativar bigint(1) not null,\r\n    qtde_dias_nova_notificacao bigint(20) not null default 1,\r\n    primary key (id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table if not exists mensagem_enviada (\r\n                                   id bigint(20) not null auto_increment,\r\n                                   mensagem varchar(255) not null,\r\n                                   horario datetime not null,\r\n                                   tipo_de_notificacao varchar(255) not null,\r\n                                   status varchar(255) not null,\r\n                                   telefone varchar(255) not null,\r\n                                   contato_id bigint(20) not null,\r\n                                   empresa_id bigint(20) not null,\r\n                                   campanha_id bigint(20)   null,\r\n                                   horario_modificacao datetime not null,\r\n                                   id_sms_externo varchar(50)\r\n                                   meio_de_envio varchar(255) not null,\r\n                                   primary key (id),\r\n                                   foreign key(campanha_id) references campanha(id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table if not exists usuario (\r\n  id bigint(20) not null auto_increment,\r\n  email varchar(255) not null,\r\n  nome varchar(255) not null,\r\n  senha varchar(255) not null,\r\n  ultimo_login datetime not null,\r\n  empresa_id bigint(20) not null,\r\n  ativo bit(1) not null,\r\n  admin bit(1) not null,\r\n  primary key (id),\r\n  foreign key(empresa_id) references empresa(id),\r\n  unique(email,empresa_id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\n\r\ncreate table if not exists contato (\r\n  id bigint(20) not null auto_increment,\r\n  nome varchar(255),\r\n  telefone varchar(30),\r\n  sexo varchar(20),\r\n  data_nascimento date null,\r\n  ultima_visita datetime not null,\r\n  quem_indicou_id bigint(20) null,\r\n  empresa_id bigint(20) not null,\r\n  status int unsigned not null default  0,\r\n  token varchar(5) not null,\r\n  data_ativacao datetime  null ,\r\n  data_cadastro datetime  null,\r\n  primary key (id),\r\n  unique key(empresa_id, telefone),\r\n  foreign key(empresa_id) references empresa(id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\n\r\ncreate table if not exists tipo_de_pontuacao (\r\n    id bigint(20) not null auto_increment,\r\n    tipo varchar(255) not null,\r\n    valor_por_ponto decimal(9, 3),\r\n    selos_por_atividade bigint(10),\r\n    empresa_id bigint(20) not null, primary key(id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table if not exists plano(\r\n  id bigint(20) not null auto_increment,\r\n  nome varchar(255),\r\n  id_tipo_de_pontuacao bigint(20) not null,\r\n  empresa_id bigint(20) not null,\r\n  tipo_de_acumulo varchar(255) not null,\r\n  primary key (id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\nCreate table if not exists atividade(\r\n  id bigint(20) not null auto_increment,\r\n  nome varchar(255),\r\n  valor DECIMAL(9,2),\r\n  empresa_id bigint(20) not null,\r\n  plano_id bigint(20) not null,\r\n  primary key (id),\r\n    foreign key(empresa_id) references empresa(id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table if not exists cartao(\r\n    id bigint(20) not null auto_increment,\r\n    pontos decimal(9, 3),\r\n    empresa_id bigint(20) not null,\r\n    contato_id bigint(20) not null,\r\n    plano_id bigint(20) not null,\r\n    codigo_temp varchar(10) null,\r\n    primary key (id),\r\n    foreign key(empresa_id) references empresa(id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table if not exists brinde(\r\n    id bigint(20) not null auto_increment,\r\n    nome varchar(255),\r\n    valor_em_pontos bigint(20),\r\n    empresa_id bigint(20) not null,\r\n    link_imagem varchar(255) not null,\r\n    plano_id bigint(20),\r\n    primary key (id),\r\n      foreign key(empresa_id) references empresa(id),\r\n      foreign key(plano_id) references plano(id)\r\n\r\n\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table if not exists pontuacao_registrada(\r\n    id bigint(20) not null auto_increment,\r\n    valor decimal(9, 3) not null,\r\n    cartao_id bigint(20) not null,\r\n    atividade_id bigint(20) not null,\r\n    horario datetime not null,\r\n    pontos bigint(20) not null,\r\n    empresa_id bigint(20) not null,\r\n    primary key (id),\r\n      foreign key(empresa_id) references empresa(id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table if not exists brinde_resgatado(\r\n    id bigint(20) not null auto_increment,\r\n    cartao_id bigint(20) not null,\r\n    id_brinde bigint(20) not null,\r\n    horario datetime not null,\r\n    mensagem varchar(255) not null,\r\n    valor_em_pontos bigint(20) not null,\r\n    empresa_id bigint(20) not null,\r\n\r\n    primary key (id),\r\n    foreign key(empresa_id) references empresa(id)\r\n\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table if not exists acao_contato (\r\n    id bigint(20) not null auto_increment,\r\n    contato_id bigint(20) not null,\r\n    horario datetime not null,\r\n    mensagem varchar(255) not null,\r\n    tipo_de_acao varchar(255) not null,\r\n    empresa_id bigint(20) not null,\r\n    pontos int    null,\r\n    primary key (id),\r\n    foreign key(empresa_id) references empresa(id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table if not exists produto(\r\n    id bigint(20) not null auto_increment,\r\n    nome varchar(255) not null,\r\n    descricao varchar(255) not null,\r\n    mensagem_pedido varchar(255) not null,\r\n    preco decimal(9,2) not null,\r\n    link_imagem varchar(255) not null,\r\n    empresa_id bigint(20) not null,\r\n\r\n    primary key (id),\r\n    foreign key(empresa_id) references empresa(id)\r\n\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table if not exists foto(\r\n    id bigint(20) not null auto_increment,\r\n    descricao varchar(255) not null,\r\n    titulo varchar(255) not null,\r\n    link varchar(255) not null,\r\n    empresa_id bigint(20) not null,\r\n    primary key (id),\r\n    foreign key(empresa_id) references empresa(id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table if not exists horario_funcionamento (\r\n  id bigint(20) not null auto_increment,\r\n  dia_da_semana bigint(1) not null,\r\n  funciona bit(1) not null,\r\n  horario_abertura int(2),\r\n  horario_fechamento int(2),\r\n  empresa_id bigint(20) not null,\r\n  primary key(id),\r\n   foreign key(empresa_id) references empresa(id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table if not exists links_mensagem_enviada (\r\n    empresa_id bigint(20) not null,\r\n    mensagem_enviada_id bigint(20) not null,\r\n    link_encurtado_id varchar(255) not null,\r\n    primary key (mensagem_enviada_id, link_encurtado_id),\r\n    foreign key(empresa_id) references empresa(id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\n\r\n\r\ncreate table if not exists filtro(\r\n  id bigint(20) not null auto_increment,\r\n  nome varchar(255) not null,\r\n  usuario_id bigint(20) not null,\r\n  dados longtext not null,\r\n  excluido bit(1) default 0,\r\n   primary key(id),\r\n     foreign key(usuario_id) references usuario(id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table if not exists codigo_verificacao(\r\n  telefone varchar(255) not null,\r\n  codigo varchar(50) not null,\r\n  empresa_id bigint(20) not   null,\r\n  primary key(telefone),\r\n  foreign key(empresa_id) references empresa(id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\n\r\ncreate table if not exists pet(\r\n  id bigint(20) not null auto_increment,\r\n  nome varchar(255) not null,\r\n  tipo varchar(100)   not null,\r\n  genero varchar(50)   null,\r\n  data_nascimento date null,\r\n  empresa_id bigint(20) not   null,\r\n  contato_id bigint(20) not null     ,\r\n  primary key(id),\r\n  foreign key(contato_id) references contato(id),\r\n  foreign key(empresa_id) references empresa(id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\n\r\ncreate table if not exists pontuacao_registrada_atividade(\r\n  pontuacao_registrada_id bigint(20) not null  ,\r\n  atividade_id bigint(20) not null  ,\r\n   primary key(pontuacao_registrada_id, atividade_id ),\r\n    foreign key(pontuacao_registrada_id) references pontuacao_registrada(id),\r\n    foreign key(atividade_id) references atividade(id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table if not exists regra_extra (\r\n  id bigint(20) not null auto_increment,\r\n  plano_id bigint(20) not null ,\r\n  descricao varchar(255) not null,\r\n  primary key(id),\r\n  foreign key(plano_id) references plano(id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table if not exists view_contato (\r\n  id bigint(20) not null auto_increment,\r\n  status varchar(255), /* NOVO, VIP ... */\r\n  qtde_dias_como_cliente bigint(20),\r\n  qtde_visitas bigint(20),\r\n  qtde_dias_nao_volta bigint(20),\r\n  ticket_medio decimal(9, 3),\r\n  total_gasto decimal(9, 3),\r\n  tempo_para_retornar decimal(9, 3),\r\n  empresa_id bigint(20) not null,\r\n  contato_id bigint(20) not null,\r\n  primary key (id),\r\n  foreign key(empresa_id) references empresa(id),\r\n  foreign key(contato_id) references contato(id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\n\r\nalter table add empresa column id;\r\n\r\nalter table mensagem_enviada add column campanha_id bigint(20) null;\r\n\r\nalter table campanha add column tipo_de_envio varchar(255) not null default 'Unico';\r\nalter table campanha add column horario_envio datetime null;\r\nalter table campanha add column qtde_de_dias_nova_notificacao bigint(20) not null default -1;\r\nalter table produto modify column preco decimal(9,2) null;\r\n\r\nalter table horario_funcionamento  modify column horario_abertura time null;\r\nalter table horario_funcionamento  modify column horario_fechamento time null;\r\n\r\nalter table atividade add column    plano_id bigint(20) not null;\r\nupdate atividade , plano, tipo_de_pontuacao  set atividade.plano_id = plano.id where atividade.empresa_id = plano.empresa_id and tipo_de_pontuacao.id = id_tipo_de_pontuacao;\r\nalter table atividade add foreign key(plano_id) references plano(id);\r\nalter table usuario add column operador bit(1) default false;\r\n\r\nalter table empresa add column removida bit(1)   null;\r\nalter table empresa add column data_remocao datetime null;\r\nalter table empresa  modify column instagram varchar(255) null;\r\nalter table empresa  modify column endereco varchar(255) null;\r\n\r\nalter table tipo_de_pontuacao add column pontos_por_valor int default null;\r\n\r\nalter table campanha add column ativa bit(1) null default 1;\r\n\r\nalter table contato add column tipo varchar(100) default 'Pessoa';\r\nalter table contato add column responsavel varchar(255) null;\r\nALTER TABLE cartao ADD FOREIGN KEY (contato_id) REFERENCES contato(id);\r\n\r\nalter table contato drop column responsavel;\r\nalter table contato drop column tipo;\r\n\r\nalter table campanha add column foi_testada bit(1) null default 0;\r\n\r\n\r\nupdate tipo_de_pontuacao set tipo = 'qtde-fixa' where tipo = 'qtd-por-atividade';\r\nalter table atividade add column pontos_ganhos int default null;\r\n\r\nalter table filtro add column empresa_id bigint(20) not null;\r\nupdate filtro join usuario set filtro.empresa_id = usuario.empresa_id where filtro.usuario_id = usuario.id;\r\nALTER TABLE filtro ADD FOREIGN KEY (empresa_id) REFERENCES empresa(id);\r\n\r\nupdate plano p join tipo_de_pontuacao  tp set tp.empresa_id = p.empresa_id where tp.id = id_tipo_de_pontuacao;\r\ndelete from  tipo_de_pontuacao where not  exists (select 1 from plano where id_tipo_de_pontuacao = tipo_de_pontuacao.id);\r\n\r\ncreate index petnome on pet(nome);\r\n\r\ninsert into pontuacao_registrada_atividade(pontuacao_registrada_id, atividade_id) select  id,atividade_id from pontuacao_registrada;\r\nalter table pontuacao_registrada  modify column atividade_id bigint(20) null;\r\n\r\nalter table brinde add column artigo varchar(10) not null default 'um';\r\nalter table plano add column validade int    null ;\r\nalter table mensagem_enviada add column id_sms_externo varchar(50) null;\r\nalter table mensagem_enviada add column meio_de_envio varchar(255) null default ' SMS';\r\n\r\nupdate mensagem_enviada set status = 'ENVIADA' where status = 'ENVIANDO';\r\n\r\nalter table empresa add column qtde_mensagens bigint(20) default 200;\r\n\r\n\r\nalter table notificacao add column qtde_dias_ativa int null;\r\nupdate notificacao set qtde_dias_ativa = 30 where tipo_de_notificacao = 'Sentimos Sua Falta';\r\ncreate index contatoacao on acao_contato(contato_id);\r\nupdate contato set status = 7 where status = 0 and exists (select 1 from acao_contato where contato_id = contato.id and tipo_de_acao = 5);\r\nalter table plano add column excluido bit(1) null;\r\nupdate notificacao set qtde_dias_ativa = 45 where tipo_de_notificacao = 'Cliente Perdido';\r\nupdate notificacao set ativada = false where tipo_de_notificacao = 'Sentimos Sua Falta' or tipo_de_notificacao = 'Cliente Perdido';\r\n\r\nalter table cartao add column desativado bit(1) null;\r\n\r\n\r\n/* perdidos */\r\nupdate contato c join cartao car on(car.contato_id = c.id) set c.status = 8 where c.empresa_id = 2 and token = 'imp' and car.pontos = 1;\r\n\r\n/* em perigo */\r\nupdate contato c set c.status = 6 where c.empresa_id = 2 and c.status <> 8  and c.ultima_visita < curdate() - INTERVAL 60 day;\r\n\r\n\r\ncreate table if not exists plano_empresarial (\r\n  id bigint(20) not null auto_increment,\r\n  nome varchar(255) not null ,\r\n  valor decimal(9,2) not null,\r\n  limite_contatos int null,\r\n  ativo bit(1) default null,\r\n  limite_sms int null,\r\n  recomendado bit(1) default false,\r\n  primary key(id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table if not exists contrato (\r\n  id bigint(20) not null auto_increment,\r\n  plano_id  bigint(20) not null,\r\n  empresa_id bigint(20) not null,\r\n  dia_vencimento int not null,\r\n  data_ativacao datetime null,\r\n  dias_gratis int null,\r\n  valor_negociado  decimal(9,2) null,\r\n  encerrado bit(1) default false,\r\n  data_encerramento date null,\r\n  primary key(id),\r\n  foreign key (plano_id) references plano_empresarial(id),\r\n  foreign key (empresa_id) references empresa(id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\n\r\nCREATE TABLE cartao_credito (\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  numero varchar(255) NOT NULL,\r\n  validade int(11) DEFAULT NULL,\r\n  cpf varchar(255)   NULL,\r\n  nome varchar(255)   NULL,\r\n  token varchar(255)   NULL,\r\n  empresa_id bigint(20)   NULL,\r\n  data_nascimento varchar(255)   NULL,\r\n  telefone varchar(255)   NULL,\r\n  bandeira varchar(20) NOT NULL,\r\n  hash varchar(255)   NULL,\r\n\r\n  PRIMARY KEY (id),\r\n  CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id),\r\n  CONSTRAINT   FOREIGN KEY (contato_id) REFERENCES contato (id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\nCREATE TABLE fatura (\r\n  id bigint(20) NOT NULL auto_increment,\r\n  contrato_id bigint(20) NOT NULL,\r\n  referencia int(11) NOT NULL,\r\n  status int(11) NOT NULL,\r\n  data_vencimento date NOT NULL,\r\n  data_pagamento datetime DEFAULT NULL,\r\n  valor_pago decimal(9,2) DEFAULT NULL,\r\n  observacao longtext,\r\n  pagamento_id bigint(20) DEFAULT NULL,\r\n  acerto bit(1) DEFAULT  null,\r\n  fatura_acerto_id bigint(20) DEFAULT NULL,\r\n  PRIMARY KEY (id),\r\n  CONSTRAINT  FOREIGN KEY (contrato_id) REFERENCES contrato (id),\r\n  CONSTRAINT  FOREIGN KEY (fatura_acerto_id) REFERENCES fatura (id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\nCREATE TABLE pagamento (\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  codigo varchar(100) DEFAULT NULL,\r\n  status varchar(50) NOT NULL,\r\n  tipo varchar(50) NOT NULL,\r\n  fatura_id bigint(20) not null,\r\n  horario datetime NOT NULL,\r\n  ultima_atualizacao datetime DEFAULT NULL,\r\n  cartao_credito_id bigint(20) DEFAULT NULL,\r\n  url varchar(255) DEFAULT NULL,\r\n  data_vencimento date DEFAULT NULL,\r\n  numero_transacao varchar(50) DEFAULT NULL,\r\n  numero_parcelas int(11) DEFAULT NULL,\r\n  valor decimal(9,2) DEFAULT NULL,\r\n  data_pagamento datetime DEFAULT NULL,\r\n  valor_parcela decimal(9,2) DEFAULT NULL,\r\n  total_parcelado decimal(9,2) DEFAULT NULL,\r\n  codigo_de_barras varchar(255) DEFAULT NULL,\r\n  PRIMARY KEY (id),\r\n  UNIQUE KEY codigo (codigo),\r\n  CONSTRAINT  FOREIGN KEY (fatura_id) REFERENCES fatura(id),\r\n  CONSTRAINT  FOREIGN KEY (cartao_credito_id) REFERENCES cartao_credito (id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\n\r\nCREATE TABLE servico_cobrado (\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  nome varchar(100) NOT NULL,\r\n  valor decimal(9,2) NOT NULL,\r\n  desativado bit(1)   null,\r\n  PRIMARY KEY (id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\nCREATE TABLE lancamento (\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  descricao varchar(255) not null,\r\n  fatura_id bigint(20) NOT NULL,\r\n  servico_cobrado_id bigint(20) NOT NULL,\r\n  tipo varchar(100) NOT NULL,\r\n  qtde int(11) NOT NULL DEFAULT '1',\r\n  valor decimal(9,2) NOT NULL,\r\n  desconto decimal(9,2) NOT NULL DEFAULT '0.00',\r\n  PRIMARY KEY (id),\r\n  KEY servico_cobrado_id (servico_cobrado_id),\r\n  CONSTRAINT  FOREIGN KEY (fatura_id) REFERENCES fatura (id),\r\n  CONSTRAINT  FOREIGN KEY (servico_cobrado_id) REFERENCES servico_cobrado (id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\n\r\nCREATE TABLE notificacao_meio_pagamento (\r\n  id varchar(255) NOT NULL,\r\n  meio varchar(255) NOT NULL,\r\n  tipo varchar(255) NOT NULL,\r\n  status varchar(255) NOT NULL,\r\n  codigo varchar(255) DEFAULT NULL,\r\n  referencia bigint(20) NOT NULL,\r\n  horario datetime NOT NULL,\r\n  horario_notificado datetime NOT NULL,\r\n  dados longtext NULL,\r\n  executada bit(1) NOT NULL,\r\n  erro varchar(255) DEFAULT NULL,\r\n  ignorar bit(1) DEFAULT NULL,\r\n  PRIMARY KEY (id),\r\n  KEY referencia (referencia)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table plano_iugu (\r\n  id varchar(255) not null,\r\n  token varchar(255) not null,\r\n  identificador varchar(255) not null,\r\n  plano_empresarial_id bigint(20),\r\n  data_criacao datetime not null,\r\n  ativo bit(1) not null,\r\n  primary key (id),\r\n  CONSTRAINT   FOREIGN KEY ( plano_empresarial_id) REFERENCES plano_empresarial (id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table assinatura (\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  codigo varchar(255) not null,\r\n  identificador_plano varchar(255) not null,\r\n  forma_de_pagamento varchar(50) not  null,\r\n  data_criacao datetime not null,\r\n  data_atualizacao datetime not null,\r\n  ativo bit(1) not null,\r\n  suspensa bit(1) not null,\r\n  ativa bit(1) not null,\r\n  dados longtext null,\r\n  empresa_id bigint(20) not null  ,\r\n   primary key (id),\r\n   unique(codigo),\r\n   CONSTRAINT  FOREIGN KEY (empresa_id) REFERENCES empresa(id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\n<!-- viees -->\r\ncreate or replace view resumo_compras as\r\n  select contato_id, cartao_id, plano_id ,count(*) qtde, count(distinct DATE_FORMAT(pr.horario, \"%M %d %Y\")) qtde_visitas,\r\n   sum(pr.valor) valor , sum(pr.valor)/count(distinct DATE_FORMAT(pr.horario, \"%M %d %Y\")) media,   sum(pr.pontos) pontos_acumulados, max(horario) ultima\r\n    from pontuacao_registrada pr join cartao c on c.id = cartao_id  group by cartao_id;\r\n\r\ncreate or replace view resumo_trocas as\r\n  select c.contato_id , cartao_id,  plano_id, count(*) qtde, sum(ac.pontos) pontos_resgatados, sum(ac.pontos)/count(*) media, max(horario) ultima\r\n    from   acao_contato ac join cartao c on c.id = cartao_id where tipo_de_acao = 3 group by cartao_id;\r\n\r\n\r\ncreate or replace view resumo_contato as\r\n  select rc.*, rt.pontos_resgatados\r\n      from resumo_compras rc left join resumo_trocas  rt on rt.cartao_id = rc.cartao_id;\r\n\r\n\r\n\r\ncreate or replace view empresa_contatos_pontuados as\r\n  select empresa.id,empresa.nome,  count(distinct contato.id) qtde_pontuados,\r\n       if(limite_contatos_negociado is null,  plano.limite_contatos, limite_contatos_negociado) qtde_contratado\r\n      from empresa join contrato on empresa.id = contrato.empresa_id\r\n                   join plano_empresarial plano on plano.id = plano_id\r\n                   join contato on contato.empresa_id = empresa.id\r\n                   join cartao on cartao.contato_id = contato.id and cartao.pontuou is true\r\n                                  group by empresa.id;\r\n\r\nalter table empresa add column cnpj varchar(20) null;\r\nalter table empresa add column email varchar(100) null;\r\nalter table empresa add column responsavel_id  bigint(20) null;\r\nalter table empresa add FOREIGN KEY (responsavel_id) REFERENCES usuario(id);\r\n\r\nalter table usuario add column cpf varchar(20) null;\r\nupdate plano_empresarial set ativo = true;\r\n\r\nalter table empresa add bloqueada bit(1) default null;\r\n\r\nalter table acao_contato add column cartao_id bigint(20)   null ;\r\nalter table acao_contato add  foreign key (cartao_id) references cartao(id);\r\nupdate acao_contato , contato, cartao\r\n set acao_contato.cartao_id = cartao.id\r\n    where acao_contato.contato_id = contato.id and contato.id = cartao.contato_id ;\r\n\r\nupdate acao_contato set cartao_id = 17926  where contato_id = 18113 and pontos > 5;\r\nupdate acao_contato set cartao_id = 6700 where contato_id = 6916 and pontos > 5;\r\nupdate acao_contato set cartao_id = 20956 where contato_id = 20138 and pontos > 5;\r\n\r\nalter table plano add column vencimento datetime null;\r\n\r\n<!-- cashback -->\r\nalter table atividade add column cashback  decimal(9,2)   null;\r\nalter table pontuacao_registrada modify column pontos   decimal(9,2) not null;\r\nalter table brinde_resgatado modify column valor_em_pontos   decimal(9,2) null;\r\nalter table acao_contato modify column pontos   decimal(9,2) null;\r\nalter table brinde modify link_imagem varchar(255) null;\r\n\r\nalter table contato modify column status varchar(255) not null;\r\n\r\n<!-- comprovante troca -->\r\n\r\nalter table brinde_resgatado add column saldo decimal(9, 2) null;\r\nalter table brinde_resgatado add column operador_id bigint(20) null;\r\nalter table brinde_resgatado add FOREIGN KEY (operador_id) REFERENCES usuario(id);\r\nalter table brinde_resgatado add column codigo varchar(255) null;\r\n\r\nalter table pontuacao_registrada add column referencia_externa varchar(255) null;\r\nalter table pontuacao_registrada add column operador_id bigint(20) null;\r\nalter table pontuacao_registrada add FOREIGN KEY (operador_id) REFERENCES usuario(id);\r\nalter table pontuacao_registrada add column codigo varchar(255) null;\r\n\r\nalter table plano add column  referencia_externa bit (1) default null;\r\n\r\n\r\nalter table empresa add column qtde_visitas_recorrente int(11) not null default 2;\r\nalter table empresa add column qtde_dias_em_risco int(11) not null default 30;\r\nalter table empresa add column qtde_dias_perdido int(11) not null default 60;\r\nalter table empresa add column qtde_compras_vip int(11) not null default 3;\r\nalter table empresa add column ticket_medio_vip decimal(9, 2) not null default 30;\r\n\r\nalter table empresa add column qtde_dias_periodo int(11) not null default 60;\r\n\r\n-----------------\r\n\r\nalter table   pontuacao_registrada drop column atividade_id;\r\n\r\nalter table acao_contato add column pontuacao_registrada_id bigint(20) null;\r\n\r\ndelete from acao_contato where tipo_de_acao = 2;\r\n\r\ninsert into acao_contato(contato_id, horario, mensagem, tipo_de_acao, empresa_id, pontos, cartao_id, pontuacao_registrada_id)\r\nselect c.contato_id, pr.horario, group_concat(a.nome SEPARATOR ', ') mensagem, 2 tipo_de_acao, pr.empresa_id, pr.pontos, pr.cartao_id, pr.id from pontuacao_registrada pr\r\nleft join pontuacao_registrada_atividade pra on (pr.id = pra.pontuacao_registrada_id)\r\ninner join atividade a on(pra.atividade_id = a.id)\r\ninner join cartao c on(c.id = pr.cartao_id)\r\ngroup by pr.id;\r\n\r\nalter table acao_contato add column brinde_resgatado_id bigint(20) null;\r\n\r\nupdate acao_contato ac, brinde_resgatado br set ac.brinde_resgatado_id = br.id\r\n\twhere\r\n    timestampdiff(SECOND, ac.horario, br.horario) = 3600;\r\n\r\n------------------\r\n\r\n<!-- add column pra indicar contato/cartao ja pontuarao -->\r\nalter table cartao   add column  pontuou bit(1) default null;\r\nalter table contrato add column  limite_contatos_negociado int null;\r\nalter table empresa add column cep varchar(10) null;\r\n\r\nalter table contrato add column assinatura_id bigint(20) null;\r\nalter table contrato add FOREIGN KEY (assinatura_id) REFERENCES assinatura(id);\r\n\r\nalter table fatura modify column codigo varchar(100) null;\r\nalter table fatura add unique(codigo);\r\nalter table fatura add column data_criacao datetime   null;\r\nalter table fatura add column data_atualizacao datetime   null;\r\nalter table fatura add column dados longtext null;\r\nalter table plano_empresarial add column desconto_cartao   decimal(9,2) default 0;\r\n\r\nalter table assinatura drop column ativo;\r\n\r\n--> Alterações RAFAEL ticket #96\r\nalter table contato add column removido bit(1)   null;\r\nalter table cartao add column removido bit(1)  null;\r\nalter table brinde_resgatado add column removido bit(1)  null;\r\nalter table pontuacao_registrada add column removida bit(1)  null;\r\nalter table acao_contato add column removida bit(1)  null;\r\n\r\n\r\n--> Alterações RAFAEL ticket #128\r\nalter table atividade add column removida bit(1)  null;\r\nalter table brinde add column removido bit(1) null;\r\nalter table produto add column removido bit(1) null;\r\nalter table foto add column removida bit(1) null;\r\n\r\n\r\n-->\r\nalter table empresa add column codigo_cliente varchar (255) null;\r\nalter table notificacao_meio_pagamento modify column referencia bigint(20) null;\r\n\r\nalter table plano add column valor_minimo_pontuar  decimal(9,2)  default 0;\r\n\r\nalter table cartao_credito add column codigo varchar(255) null;\r\nalter table cartao_credito add column padrao bit(1) default null;\r\nalter table cartao_credito modify column cpf varchar(255) null;\r\nalter table cartao_credito modify column nome varchar(255) null;\r\nalter table cartao_credito modify column data_nascimento varchar(255) null;\r\nalter table cartao_credito modify column telefone varchar(255) null;\r\nalter table cartao_credito add unique(codigo);\r\n\r\nalter table assinatura add column data_vencimento  date null;\r\nalter table assinatura add column cartao_credito_id  bigint(20) null;\r\nalter table assinatura add foreign key(cartao_credito_id) references cartao_credito(id);\r\n\r\nalter table plano add column cartao_consumo bit(1) null;\r\nalter table plano add column titulo_cartao varchar(255) null;\r\n\r\nupdate plano set titulo_cartao = concat('Cartão ',nome);\r\n\r\n<!-- migração pontuação vencida -->\r\nalter table pontuacao_registrada add column data_vencimento date null;\r\nalter table pontuacao_registrada add column pontos_usados decimal(9,2) default 0;\r\nalter table pontuacao_registrada add column pontos_vencidos decimal(9,2) default 0;\r\n\r\n\r\nupdate pontuacao_registrada set pontos_usados = 0;\r\n\r\nupdate  pontuacao_registrada pr join cartao on cartao.id = cartao_id join  plano on plano_id = plano.id\r\n  set pr.data_vencimento =  if(vencimento is not null, vencimento, date_add(horario, interval validade day) )\r\n    where validade is not null or vencimento is not null;\r\n\r\n\r\nupdate pontuacao_registrada\r\n   set data_vencimento = '2020-03-24 12:00:00'\r\n    where datediff('2020-03-23 23:59:59',data_vencimento) >= 0  ;\r\n\r\n\r\ninsert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)\r\n    select  true, empresa_id, 'Pontos Expirar',  '[NomeContato], você tem [PontosExpirar] expirando em [TempoRestante]. Aproveite enquanto ainda há tempo!  [LinkCartao]', true, 7,-1\r\n        from plano where excluido is not true and vencimento is not null or validade  is not null group by empresa_id;\r\n\r\n\r\ncreate table modulo(\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  nome varchar(255) not null,\r\n  primary key(id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table empresa_modulo(\r\n  empresa_id bigint(20) not null,\r\n  modulo_id bigint(20) not null,\r\n  PRIMARY KEY (empresa_id, modulo_id),\r\n    CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id),\r\n    CONSTRAINT   FOREIGN KEY (modulo_id) REFERENCES modulo (id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\n\r\nalter table contato add column data_ultimo_pedido datetime null;\r\ninsert into modulo (nome) values ('fidelidade'), ('pedidos');\r\n\r\n<!-- delivery -->\r\n<!-- importar primeiro  sql/cidades.dump.sql  -->\r\n\r\nCREATE TABLE endereco (\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  descricao varchar(255) DEFAULT NULL,\r\n  bairro varchar(255) DEFAULT NULL,\r\n  cep varchar(255) DEFAULT NULL,\r\n  complemento varchar(255) DEFAULT NULL,\r\n  logradouro varchar(255) DEFAULT NULL,\r\n  localidade varchar(255) DEFAULT NULL,\r\n  numero varchar(255) DEFAULT NULL,\r\n  cidade_id bigint(20) DEFAULT NULL,\r\n  contato_id bigint(20) DEFAULT NULL,\r\n  PRIMARY KEY (id),\r\n  CONSTRAINT   FOREIGN KEY (contato_id) REFERENCES contato (id),\r\n  CONSTRAINT   FOREIGN KEY (cidade_id) REFERENCES cidade (id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\nCREATE TABLE pedido (\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  codigo varchar(100) not null,\r\n  status varchar(25) not null,\r\n  valor decimal(9,2) not null,\r\n  taxa_entrega decimal(9,2) not null,\r\n  desconto decimal(9,2) not null,\r\n  horario datetime not null,\r\n  horario_atualizacao datetime not null,\r\n  empresa_id  bigint(20)  not null,\r\n  contato_id  bigint(20)  not null,\r\n  endereco_id  bigint(20) not null,\r\n  operador_id bigint(20) null,\r\n  pago bit(1) default false,\r\n  observacoes varchar(500) null,\r\n  unique(empresa_id, codigo),\r\n   PRIMARY KEY (id),\r\n    CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id) ,\r\n    CONSTRAINT   FOREIGN KEY (contato_id) REFERENCES contato (id) ,\r\n    CONSTRAINT   FOREIGN KEY (endereco_id) REFERENCES endereco (id),\r\n    CONSTRAINT   FOREIGN KEY (operador_id) REFERENCES usuario (id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\n\r\nCREATE TABLE pagamento_pedido (\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  valor decimal(9,2) not null,\r\n  troco_para decimal(9,2) not null,\r\n  forma_de_pagamento varchar(25) not null,\r\n  status varchar(25) not null,\r\n  pedido_id  bigint(20) not null,\r\n   PRIMARY KEY (id),\r\n   CONSTRAINT   FOREIGN KEY (pedido_id) REFERENCES pedido (id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\nCREATE TABLE item_pedido (\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  codigo varchar(25)   null,\r\n  qtde int not null,\r\n  valor decimal(9,2) not null,\r\n  desconto decimal(9,2) not null,\r\n  pedido_id  bigint(20) NOT NULL,\r\n  produto_id  bigint(20) NOT NULL,\r\n  PRIMARY KEY (id),\r\n  CONSTRAINT   FOREIGN KEY (pedido_id) REFERENCES pedido (id),\r\n  CONSTRAINT   FOREIGN KEY (produto_id) REFERENCES produto (id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\n\r\ncreate table cardapio(\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  arquivo varchar(255) not null,\r\n  empresa_id bigint(20) not null,\r\n  primary key(id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\n\r\n\r\nalter table notificacao add unique(empresa_id, tipo_de_notificacao);\r\nalter table mensagem_enviada add column imagem varchar(255) null;\r\nalter table produto add column exibir_no_site bit(1) default true;\r\nalter table produto modify column   link_imagem  varchar(255)  null;\r\nalter table produto modify column   mensagem_pedido   varchar(255)  null;\r\n\r\ninsert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)\r\n    select  true, empresa.id, 'Cardapio',  '[NomeContato], Segue nosso cardápio!', true, -1,-1\r\n        from empresa;\r\n\r\ninsert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)\r\n    select  true, empresa.id, 'ConfirmacaoPedido',  '[ConfirmacaoPedido]', true, -1,-1\r\n        from empresa;\r\n\r\n\r\ninsert into empresa_modulo(empresa_id, modulo_id)\r\n    select id, 1 from empresa where not exists (select 1 from empresa_modulo em where em.empresa_id = empresa.id);\r\n\r\n\r\nalter table acao_contato add column pedido_id bigint(20) null;\r\nalter table acao_contato add FOREIGN KEY (pedido_id) REFERENCES pedido(id);\r\n\r\n\r\n<!-- campos extras -->\r\nalter table contato add column cpf varchar(11) null;\r\nalter table contato add column email varchar(255) null;\r\n\r\ncreate table  campo_extra(\r\n  id  bigint(20) not null AUTO_INCREMENT,\r\n  nome varchar(100) not null,\r\n  primary key(id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table empresa_campo_extra(\r\n    empresa_id bigint(20) not null ,\r\n    campo_extra_id bigint(20) not null,\r\n    primary key(empresa_id, campo_extra_id),\r\n       CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id),\r\n       CONSTRAINT   FOREIGN KEY (campo_extra_id) REFERENCES campo_extra (id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\n\r\ninsert into campo_extra(nome) values ('cpf'), ('email');\r\n\r\nalter table contato add unique(empresa_id, cpf);\r\n\r\n\r\nalter table produto add column tem_estoque bit(1) default true;\r\nalter table produto add column exibir_preco_site bit(1) default true;\r\n\r\n\r\nalter table contato_empresa add column horario timestamp  DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;\r\n\r\n-- Lista de leads - alteraçãao rafael\r\n\r\nalter table contato_empresa add column cadastrado bit(1) default false;\r\n\r\n\r\n\r\n<!-- formas de entrega-->\r\ncreate table forma_de_entrega (\r\n  id  bigint(20) not null AUTO_INCREMENT,\r\n  nome varchar(100) not null,\r\n  primary key(id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table empresa_forma_de_entrega (\r\n    empresa_id bigint(20) not null ,\r\n    forma_de_entrega_id bigint(20) not null,\r\n    frete_gratis_por_valor bit(1) null,\r\n    valor_minimo_gratis decimal(9,2) null,\r\n    primary key(empresa_id, forma_de_entrega_id),\r\n       CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id),\r\n       CONSTRAINT   FOREIGN KEY (forma_de_entrega_id) REFERENCES forma_de_entrega (id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ninsert into forma_de_entrega(nome) values ('Retirar'), ('Receber em casa');\r\n\r\nalter table pedido add column forma_de_entrega_id bigint(20) null;\r\nalter table pedido add FOREIGN KEY (forma_de_entrega_id) REFERENCES forma_de_entrega(id);\r\nalter table pedido add column origem varchar(100)  null;\r\nalter table pedido modify column endereco_id bigint(20) null;\r\n\r\nalter table item_pedido add column observacao varchar(255) null;\r\n\r\ncreate table categoria (\r\n  id  bigint(20) not null AUTO_INCREMENT,\r\n  nome varchar(100) not null,\r\n  empresa_id bigint(20) not null ,\r\n  primary key(id),\r\n    CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\nalter table produto add column categoria_id bigint(20) null;\r\nalter table produto add FOREIGN KEY (categoria_id) REFERENCES categoria(id);\r\n\r\n\r\ninsert into modulo (nome) values ('cardápio');\r\nalter table categoria add column posicao int null;\r\n\r\n\r\n  ----- Alterações novos campos landing page\r\n\r\nalter table contato_empresa add column instagram varchar(255) null;\r\nalter table contato_empresa add column qtde_pedidos varchar(255) null;\r\nalter table contato_empresa add column ticket_medio varchar(255) null;\r\n\r\nalter table empresa_forma_de_entrega add column taxa_tipo varchar(255);\r\nalter table empresa_forma_de_entrega add column taxa_valor_fixo decimal(9, 2) default 0 ;\r\nalter table empresa_forma_de_entrega add column taxa_valor_km_taxa decimal(9, 2) default 0;\r\nalter table empresa_forma_de_entrega add column taxa_valor_minimo_taxa decimal(9, 2) default 0;\r\n\r\nalter table empresa add column lat_long varchar(255) null;\r\n\r\n<!-- troca junto com pedido -->\r\nalter table brinde_resgatado modify column mensagem varchar(255) null;\r\nalter table brinde_resgatado add column pedido_id bigint(20) null;\r\nalter table brinde_resgatado add FOREIGN KEY (pedido_id) REFERENCES pedido(id);\r\n\r\n\r\ninsert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)\r\n    select  true, empresa.id, 'Link Extrato Cartao',  '[NomeContato], segue o link para conferir seus pontos: [LinkExtratoCartao]', true, -1,-1\r\n        from empresa  join empresa_modulo on empresa_modulo.empresa_id = empresa.id and modulo_id = 2;\r\n\r\n\r\nalter table empresa_forma_de_entrega add column taxa_maximo_km decimal(9, 2) default 0;\r\n\r\n<!-- ampliando planos empresas\r\n CREATE TABLE vantagem (\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  descricao varchar(255) NOT NULL,\r\n  PRIMARY KEY (id)\r\n) ENGINE=InnoDB  DEFAULT CHARSET=utf8;\r\n\r\nCREATE TABLE plano_vantagem (\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  plano_empresarial_id bigint(20) NOT NULL,\r\n  vantagem_id bigint(20) NOT NULL,\r\n  ordem int(11) NOT NULL,\r\n  PRIMARY KEY (id),\r\n  KEY vantagem_id (vantagem_id),\r\n  CONSTRAINT  FOREIGN KEY (vantagem_id) REFERENCES vantagem (id),\r\n  CONSTRAINT  FOREIGN KEY (plano_empresarial_id) REFERENCES plano_empresarial (id)\r\n) ENGINE=InnoDB DEFAULT CHARSET=utf8;\r\n\r\n\r\n CREATE TABLE prospect (\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  nome varchar(255) NOT NULL,\r\n  instagram varchar(100) NOT NULL,\r\n  email varchar(100) NOT NULL,\r\n  telefone varchar(20) NOT NULL,\r\n  passo varchar(255) not null,\r\n  empresa varchar(255)   NULL,\r\n  cnpj varchar(20)   NULL,\r\n  dados longtext null,\r\n  horario datetime not null,\r\n  atualizacao datetime not null,\r\n  codigo varchar(255) not null,\r\n  PRIMARY KEY (id)\r\n) ENGINE=InnoDB  DEFAULT CHARSET=utf8;\r\n\r\n--> distancia minima para comecar a adicionar taxa na entrega\r\n\r\nalter table empresa_forma_de_entrega add column distancia_minima_km decimal(9, 2) default 0;\r\n\r\n\r\n<!-- criar empresa do prospect ->\r\n\r\nCREATE TABLE plano_modulo (\r\n  plano_empresarial_id bigint(20) not null,\r\n  modulo_id bigint(20) not null,\r\n  primary key (plano_empresarial_id, modulo_id),\r\n  foreign key(plano_empresarial_id) references plano_empresarial(id),\r\n  foreign key(modulo_id) references modulo(id)\r\n) ENGINE=InnoDB  DEFAULT CHARSET=utf8;\r\n\r\nalter table plano_empresarial add column publico bit(1) default false;\r\nalter table plano_empresarial add column recomendado bit(1) default false;\r\nalter table plano_empresarial add column limite_pedidos int null;\r\n\r\nalter table plano_vantagem add disponivel bit(1) default true;\r\n\r\nalter table empresa modify column descricao varchar(255) null;\r\nalter table empresa modify column titulo_fotos varchar(50) null;\r\nalter table empresa modify column titulo_destaques varchar(50) null;\r\nalter table empresa modify column link_maps varchar(500) null;\r\nalter table contrato add column data_fim_trial date;\r\n\r\n\r\nalter table notificacao modify column mensagem text not null;\r\n\r\nalter table pedido add column guid varchar(255) default null;\r\nupdate pedido set guid  = uuid() where guid is null;\r\n\r\nalter table plano_empresarial add column identificador varchar(255) null;\r\nalter table plano_empresarial add column codigo varchar(255) null;\r\n\r\nalter table prospect add column empresa_id bigint(20) null;\r\nalter table prospect add FOREIGN KEY (empresa_id) REFERENCES empresa(id);\r\n\r\nalter table empresa add column sempre_receber_pedidos bit(1) null;\r\n\r\n--> Tabela de adicionais\r\nCREATE TABLE adicional_produto (\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  nome varchar(255) NOT NULL,\r\n  obrigatorio bit(1) NOT NULL default false,\r\n  qtd_minima int null,\r\n  qtd_maxima int null,\r\n  pode_repetir_item bit(1) null,\r\n  tipo varchar(255) not null,\r\n  produto_id bigint(20) not null,\r\n  excluido bigint(1) null,\r\n  primary key(id),\r\n  foreign key(produto_id) references produto(id)\r\n) ENGINE=InnoDB  DEFAULT CHARSET=utf8;\r\n\r\nCREATE TABLE opcao_adicional_produto (\r\n  id bigint(20) not null auto_increment,\r\n  nome varchar(255) not null,\r\n  valor decimal(9,2) null,\r\n  adicional_produto_id bigint(20) not null,\r\n  excluido bigint(1) null,\r\n  primary key (id),\r\n  foreign key(adicional_produto_id) references adicional_produto(id)\r\n) ENGINE=InnoDB  DEFAULT CHARSET=utf8;\r\n\r\ncreate table valor_de_adicionais_escolha_simples(\r\n  id bigint(20) not null auto_increment,\r\n\r\n  campo0_id bigint(20) null,\r\n  campo1_id bigint(20) null,\r\n  campo2_id bigint(20) null,\r\n  campo3_id bigint(20) null,\r\n  campo4_id bigint(20) null,\r\n  campo5_id bigint(20) null,\r\n  campo6_id bigint(20) null,\r\n  campo7_id bigint(20) null,\r\n  campo8_id bigint(20) null,\r\n  campo9_id bigint(20) null,\r\n\r\n  produto_id bigint(20) not null,\r\n  item_id bigint(20) not null,\r\n  primary key(id),\r\n  foreign key(produto_id) references produto(id),\r\n  foreign key(item_id) references item_pedido(id),\r\n  foreign key(campo0_id) references opcao_adicional_produto(id),\r\n  foreign key(campo1_id) references opcao_adicional_produto(id),\r\n  foreign key(campo2_id) references opcao_adicional_produto(id),\r\n  foreign key(campo3_id) references opcao_adicional_produto(id),\r\n  foreign key(campo4_id) references opcao_adicional_produto(id),\r\n  foreign key(campo5_id) references opcao_adicional_produto(id),\r\n  foreign key(campo6_id) references opcao_adicional_produto(id),\r\n  foreign key(campo7_id) references opcao_adicional_produto(id),\r\n  foreign key(campo8_id) references opcao_adicional_produto(id),\r\n  foreign key(campo9_id) references opcao_adicional_produto(id)\r\n);\r\n\r\ncreate table lista_opcoes_escolhidas(\r\n    id bigint(20) not null auto_increment,\r\n\r\n    primary key (id)\r\n);\r\n\r\n\r\ncreate table valor_de_opcao_multipla_escolha(\r\n    id bigint(20) not null auto_increment,\r\n\r\n    qtde bigint(20) not null,\r\n    opcao_id bigint(20) not null,\r\n    lista_id bigint(20) not null,\r\n\r\n    primary key(id),\r\n    foreign key(opcao_id) references opcao_adicional_produto(id),\r\n    foreign key(lista_id) references lista_opcoes_escolhidas(id)\r\n);\r\n\r\n\r\ncreate table valor_de_adicionais_multipla_escolha(\r\n    id bigint(20) not null auto_increment,\r\n\r\n    lista0_id bigint(20) null,\r\n    lista1_id bigint(20) null,\r\n    lista2_id bigint(20) null,\r\n    lista3_id bigint(20) null,\r\n    lista4_id bigint(20) null,\r\n    lista5_id bigint(20) null,\r\n    lista6_id bigint(20) null,\r\n    lista7_id bigint(20) null,\r\n    lista8_id bigint(20) null,\r\n    lista9_id bigint(20) null,\r\n\r\n    produto_id bigint(20) not null,\r\n    item_id bigint(20) not null,\r\n    primary key(id),\r\n    foreign key(produto_id) references produto(id),\r\n    foreign key(item_id) references item_pedido(id),\r\n    foreign key(lista0_id) references lista_opcoes_escolhidas(id),\r\n    foreign key(lista1_id) references lista_opcoes_escolhidas(id),\r\n    foreign key(lista2_id) references lista_opcoes_escolhidas(id),\r\n    foreign key(lista3_id) references lista_opcoes_escolhidas(id),\r\n    foreign key(lista4_id) references lista_opcoes_escolhidas(id),\r\n    foreign key(lista5_id) references lista_opcoes_escolhidas(id),\r\n    foreign key(lista6_id) references lista_opcoes_escolhidas(id),\r\n    foreign key(lista7_id) references lista_opcoes_escolhidas(id),\r\n    foreign key(lista8_id) references lista_opcoes_escolhidas(id),\r\n    foreign key(lista9_id) references lista_opcoes_escolhidas(id)\r\n);\r\n\r\nalter table prospect add column cadastrado bit(1) default false;\r\n\r\ncreate index idx_msg_enviada_empresa on mensagem_enviada(empresa_id, id);\r\n\r\ncreate index idx_produto_empresa on produto(empresa_id, id);\r\n\r\n\r\n<!-- login loja contato ->\r\nalter table contato add column senha varchar(100)  null;\r\nalter table contato add unique(empresa_id, email);\r\n\r\n\r\nalter table empresa_forma_de_entrega add column frete_gratis_por_valor bit(1) null;\r\nalter table empresa_forma_de_entrega add column valor_minimo_gratis decimal(9,2) null;\r\n\r\nalter table endereco add column localizacao varchar(255) null;\r\n\r\nalter table prospect add column telefone_valido bit(1) null;\r\n\r\nCREATE TABLE token (\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  horario datetime NOT NULL,\r\n  token varchar(255) NOT NULL,\r\n  codigo varchar(10) NOT NULL,\r\n  validade int NOT NULL,\r\n  contato_id bigint(20) NOT NULL,\r\n  PRIMARY KEY (id),\r\n  CONSTRAINT FOREIGN KEY (contato_id) REFERENCES contato (id)\r\n) ENGINE=InnoDB  DEFAULT CHARSET=utf8;\r\n\r\n\r\nCREATE TABLE envio_de_email (\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  destinatario_email varchar(255) NOT NULL,\r\n  destinatario_nome varchar(255) NOT NULL,\r\n  guid varchar(255) NOT NULL,\r\n  horario datetime NOT NULL,\r\n  horario_envio datetime   NULL,\r\n  status varchar(255) NOT NULL,\r\n  tipo varchar(255) NOT NULL,\r\n  empresa_id bigint(20) not NULL,\r\n  usuario_id bigint(20) DEFAULT NULL,\r\n  contato_id bigint(20) DEFAULT NULL,\r\n  dados longtext NOT NULL,\r\n  enviado bit(1) NOT NULL DEFAULT b'0',\r\n  id_envio varchar(255) DEFAULT NULL,\r\n  mensagem_falha varchar(255) DEFAULT NULL,\r\n  tentar_reenviar bit(1) DEFAULT NULL,\r\n  tentativas_reenvio int(11) DEFAULT NULL,\r\n  PRIMARY KEY (id),\r\n  KEY usuario_id (usuario_id),\r\n  KEY contato_id (usuario_id),\r\n  CONSTRAINT  FOREIGN KEY (empresa_id) REFERENCES empresa (id),\r\n  CONSTRAINT  FOREIGN KEY (usuario_id) REFERENCES usuario (id),\r\n  CONSTRAINT  FOREIGN KEY (contato_id) REFERENCES contato (id)\r\n) ENGINE=InnoDB  DEFAULT CHARSET=utf8;\r\n\r\nalter table produto modify column descricao longtext not null;\r\nalter table empresa_forma_de_entrega add column valor_minimo_pedido decimal(9,2)   null;\r\n\r\nalter table contrato add column taxa_adesao decimal(9,2)  null;\r\nalter table usuario modify column ultimo_login datetime null;\r\nalter table usuario modify column senha varchar(255) null;\r\n\r\nalter table fatura modify column contrato_id bigint(20) null;\r\nalter table fatura add column   empresa_id bigint(20) null;\r\nalter table fatura add foreign key(empresa_id) references empresa(id);\r\n\r\nalter table opcao_adicional_produto add column disponivel bit(1) null default true;\r\n\r\nalter table produto add column ordem int(11) null;\r\n\r\n\r\n//aceitar emoticons\r\nALTER TABLE mensagem_enviada CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;\r\n\r\n\r\nalter table cardapio add column bot_ativo bit(1) default false;\r\n\r\ninsert into modulo (nome) values ('chatbot');\r\n\r\ninsert into notificacao(ativada,mensagem,empresa_id,tipo_de_notificacao,pode_desativar,qtde_dias_nova_notificacao,qtde_dias_ativa)\r\n  select 1,'[NomeContato], segue o link para conferir seus pontos: [LinkExtratoCartao]',id, 'Link Extrato Cartao',1,-1,-1 from empresa\r\n      where removida is not true and  not exists (select 1 from notificacao where tipo_de_notificacao = 'Link Extrato Cartao' and empresa_id = empresa.id);\r\n\r\n\r\nALTER TABLE campanha CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;\r\n\r\nalter table empresa add column data_bloqueio_auto date null;\r\n\r\nupdate empresa join contrato on contrato.empresa_id = empresa.id join assinatura on assinatura.id = assinatura_id\r\n   set data_bloqueio_auto = date_add(data_vencimento, INTERVAL 7 DAY)\r\n   where data_vencimento is not null;\r\n\r\n\r\n<!-- migração forma de entrega por raio de distancia -->\r\n\r\ncreate table empresa_formas_de_entrega (\r\n    id bigint(20) not null auto_increment,\r\n    empresa_id bigint(20) not null ,\r\n    forma_de_entrega_id bigint(20) not null,\r\n    valor_minimo_frete_gratis decimal(9,2) null,\r\n    valor_minimo_pedido  decimal(9,2) DEFAULT NULL,\r\n    ativa bit(1) default true,\r\n    primary key(id), unique(empresa_id, forma_de_entrega_id),\r\n       CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id),\r\n       CONSTRAINT   FOREIGN KEY (forma_de_entrega_id) REFERENCES forma_de_entrega (id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table raio_de_cobranca(\r\n    id bigint(20) not null auto_increment,\r\n    tipo varchar(100) not null,\r\n    alcance decimal(9,2) not null,\r\n    valor_fixo  decimal(9,2)    null,\r\n    valor_km_taxa  decimal(9,2)    null,\r\n    valor_minimo_taxa decimal(9,2)    null,\r\n    empresa_forma_de_entrega_id bigint(20) not null,\r\n    primary key (id),\r\n    foreign key(empresa_forma_de_entrega_id) references empresa_formas_de_entrega(id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ninsert into empresa_formas_de_entrega(empresa_id, forma_de_entrega_id, valor_minimo_frete_gratis, valor_minimo_pedido)\r\n select empresa_id,forma_de_entrega_id,  if(frete_gratis_por_valor,valor_minimo_gratis, 0 ) , valor_minimo_pedido from empresa_forma_de_entrega;\r\n\r\ninsert into raio_de_cobranca (empresa_forma_de_entrega_id, tipo, alcance, valor_fixo, valor_km_taxa, valor_minimo_taxa)\r\n  select id,taxa_tipo, taxa_maximo_km, taxa_valor_fixo, 0, 0\r\n      from empresa_formas_de_entrega efes join empresa_forma_de_entrega efe\r\n              on  efes.empresa_id = efe.empresa_id and efes.forma_de_entrega_id = efe.forma_de_entrega_id\r\n         where taxa_tipo = 'VALOR_FIXO' union\r\n  select id,taxa_tipo, taxa_maximo_km, 0, 0, 0\r\n                from empresa_formas_de_entrega efes join empresa_forma_de_entrega efe\r\n                  on  efes.empresa_id = efe.empresa_id and efes.forma_de_entrega_id = efe.forma_de_entrega_id\r\n                   where taxa_tipo = 'FRETE_GRATIS' union\r\n\r\n  select id,'VALOR_FIXO', distancia_minima_km, taxa_valor_minimo_taxa, 0, 0\r\n        from empresa_formas_de_entrega efes join empresa_forma_de_entrega efe   on  efes.empresa_id = efe.empresa_id and efes.forma_de_entrega_id = efe.forma_de_entrega_id\r\n           where taxa_tipo = 'VALOR_POR_DISTANCIA' and distancia_minima_km > 0  union\r\n\r\n  select id,taxa_tipo, if(taxa_maximo_km > 0, taxa_maximo_km, 2500), 0, taxa_valor_km_taxa, taxa_valor_minimo_taxa\r\n                from empresa_formas_de_entrega efes join empresa_forma_de_entrega efe\r\n                      on  efes.empresa_id = efe.empresa_id and efes.forma_de_entrega_id = efe.forma_de_entrega_id\r\n                                      where taxa_tipo = 'VALOR_POR_DISTANCIA';\r\n\r\nALTER TABLE contrato ADD CONSTRAINT empresa_unica  UNIQUE (empresa_id);\r\n\r\n\r\n<!-- scripts de marketing configuráveis -->\r\nalter table empresa add column pixel_facebook varchar(255) null;\r\nalter table empresa add column analytics varchar(255) null;\r\n\r\n\r\nalter table empresa add column endereco_id bigint(20) null;\r\nalter table empresa add foreign key(endereco_id) references endereco(id);\r\n\r\n\r\n<!-- novas formas de pagamento -->\r\n\r\nCREATE TABLE forma_de_pagamento (\r\n  id bigint(20) not null auto_increment,\r\n  nome varchar(255) not null,\r\n  descricao varchar(255) not null,\r\n  empresa_id  bigint(20)  not null,\r\n  exibir_cardapio bit(1) not null default true,\r\n  foreign key(empresa_id) references empresa(id),\r\n   PRIMARY KEY (id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ninsert into forma_de_pagamento(nome, descricao, empresa_id, exibir_cardapio)\r\n  select 'dinheiro', 'Dinheiro', id, true from empresa where empresa_id = 270\r\n  union select 'cartao', 'Cartão', id, false from empresa where empresa_id = 270\r\n  union select 'cartao-credito', 'Cartão de Crédito', id, true from empresa where empresa_id = 270\r\n  union select 'cartao-debito', 'Cartão de Débito', id, true from empresa where empresa_id = 270\r\n  union select 'cashback', 'Cashback', id, false from empresa where empresa_id = 270\r\n  union select 'transferencia', 'Transferência', id, true from empresa where empresa_id = 270;\r\n\r\n\r\nalter table pagamento_pedido add column forma_de_pagamento_id bigint(20) null;\r\n\r\nupdate pagamento_pedido inner join pedido on(pedido.id = pagamento_pedido.pedido_id)\r\n inner join forma_de_pagamento on (pedido.empresa_id = forma_de_pagamento.empresa_id and pagamento_pedido.forma_de_pagamento = forma_de_pagamento.nome)\r\n set forma_de_pagamento_id = forma_de_pagamento.id;\r\n\r\nalter table pagamento_pedido add foreign key(forma_de_pagamento_id) references forma_de_pagamento(id);\r\nalter table pagamento_pedido modify column forma_de_pagamento_id bigint(20) not null;\r\n\r\nalter table pagamento_pedido modify column forma_de_pagamento varchar(25) null;\r\n\r\nalter table pedido add column  visualizado bit(1) default true;\r\n\r\n\r\nalter table empresa_formas_de_entrega add column tipo_de_cobranca varchar(50)  null;\r\nupdate empresa_formas_de_entrega set tipo_de_cobranca = 'distancia';\r\n\r\n\r\ncreate table zona_de_entrega(\r\n    id bigint(20) not null auto_increment,\r\n    nome varchar(100) not null,\r\n    valor  decimal(9,2)    not null,\r\n    empresa_forma_de_entrega_id bigint(20) not null,\r\n    primary key (id),\r\n    foreign key(empresa_forma_de_entrega_id) references empresa_formas_de_entrega(id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\n\r\nalter table endereco add column zona_de_entrega_id  bigint(20)  null;\r\nalter table endereco add foreign key(zona_de_entrega_id) references zona_de_entrega(id);\r\n\r\n\r\nalter table produto add column qtd_maxima bigint(20) null;\r\n\r\n\r\n\r\n\r\n<!-- Novas notificações -->\r\n\r\n  insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)\r\n      select  true, empresa.id, 'Pedido Saiu Para Entrega',  'Seu pedido [StatusPedido]', true, -1,-1\r\n          from empresa;\r\n\r\n  insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)\r\n      select  true, empresa.id, 'Pedido Em Preparação',  'Seu pedido [StatusPedido]', true, -1,-1\r\n          from empresa;\r\n\r\n  insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)\r\n      select  true, empresa.id, 'Pedido Pronto',  'Seu pedido [StatusPedido]', true, -1,-1\r\n          from empresa;\r\n\r\n  insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)\r\n    select  true, empresa.id, 'Pedido Entregue',  'Seu pedido [StatusPedido]', true, -1,-1\r\n        from empresa;\r\n\r\n  insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)\r\n      select  true, empresa.id, 'Pedido Cancelado',  'Seu pedido [StatusPedido]', true, -1,-1\r\n          from empresa;\r\n\r\n\r\ninsert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)\r\n    select  true, empresa.id, 'Pedido Realizado Cardapio Online',  'Acabei de fazer o pedido *[CodigoPedido]*.', false, -1,-1\r\n        from empresa where exists (select 1 from empresa_modulo where empresa_id = empresa.id and modulo_id  = 2);\r\n\r\ninsert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)\r\n    select  true, empresa.id, 'Pedido Realizado Cardapio Online',  'Eu quero pedir: \\n\\n [DadosPedido]', false, -1,-1\r\n        from empresa where exists (select 1 from empresa_modulo where empresa_id = empresa.id and modulo_id = 3)\r\n          and not exists (select 1 from empresa_modulo where empresa_id = empresa.id and modulo_id  = 2);\r\n\r\nalter table contato modify column data_nascimento datetime null;\r\n\r\n<!-- Mensagem de saudação -->\r\n\r\ninsert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)\r\n    select  true, empresa.id, 'Pedido Realizado Cardapio Online',  'Acabei de fazer o pedido *[CodigoPedido]*.', false, -1,-1\r\n        from empresa where exists (select 1 from empresa_modulo where empresa_id = empresa.id and modulo_id  = 2);\r\n\r\n\r\ninsert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)\r\n    select  false, empresa.id, 'Mensagem Saudação Whatsapp Pedido',  'Olá [NomeContato], Seja bem-vindo à [Empresa]\r\n\r\n❗ Clique no link Abaixo Para Conferir Nosso Cardápio Completo e Fazer Seu Pedido❗\r\n\r\n⬇⬇⬇⬇⬇⬇⬇⬇⬇\r\n\r\n[Link_Cardapio]\r\n\r\n⬆⬆⬆⬆⬆⬆⬆⬆⬆\r\n\r\nSiga os Passos Abaixo Para Fazer Seu Pedido\r\n\r\n1⃣ Escolha os Produtos e Quantidades Desejadas\r\n2⃣ Abra o Carrinho\r\n3⃣ Endereço e Forma de Pagamento\r\n4⃣ Confirme o Pedido\r\n5⃣ Clique em Enviar Pedido para Whatsapp [Empresa].\r\n\r\nSuper Fácil e Rápido.', true, -1,-1\r\nfrom empresa where exists (select 1 from empresa_modulo where empresa_id = empresa.id and modulo_id  = 2);\r\n\r\nalter table notificacao modify column ativada bit(1) not null default true;\r\n\r\n\r\n<!-- notificações com emoticons -->\r\nALTER TABLE notificacao CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;\r\n\r\nalter table empresa add column imprimir_txt bit(1) null;\r\n\r\n\r\n<!--Cadastro de formas de pagamento-->\r\nalter table forma_de_pagamento add constraint unique(empresa_id, descricao);\r\n\r\nalter table forma_de_pagamento add column removida bit(1) default false;\r\n\r\n\r\n<!-- multiplos whatsapps -->\r\n\r\ncreate table numero_whatsapp(\r\n    id bigint(20) not null auto_increment,\r\n    empresa_id bigint(20) not null,\r\n    whatsapp varchar(255) not null,\r\n    principal bit(1) not null default false,\r\n    removido bit(1) not null default false,\r\n    primary key (id),\r\n    foreign key(empresa_id) references empresa(id)\r\n)engine=innodb default charset=utf8mb4 COLLATE=utf8mb4_bin;\r\n\r\nalter table mensagem_enviada add column numero_whatsapp_id bigint(20) not null;\r\ninsert into numero_whatsapp(empresa_id, whatsapp) select id, whatsapp from empresa;\r\n\r\nupdate mensagem_enviada join numero_whatsapp on(mensagem_enviada.empresa_id = numero_whatsapp.empresa_id)\r\n\tset mensagem_enviada.numero_whatsapp_id = numero_whatsapp.id;\r\nalter table mensagem_enviada add foreign key(numero_whatsapp_id) references numero_whatsapp(id);\r\n\r\nalter table usuario add column numero_whatsapp_id bigint(20) null null;\r\nupdate usuario join numero_whatsapp on(usuario.empresa_id = numero_whatsapp.empresa_id)\r\n\tset usuario.numero_whatsapp_id = numero_whatsapp.id;\r\nalter table usuario add foreign key(numero_whatsapp_id) references numero_whatsapp(id);\r\nalter table empresa modify column whatsapp varchar(255) null;\r\n\r\n\r\n<!-- Config impressao -->\r\ncreate table impressora(\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  nome varchar(255) not null,\r\n  tamanho varchar(255) not null,\r\n  primary key(id)\r\n);\r\n\r\ncreate table config_impressao(\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  imprimir_txt bit(1) null,\r\n  imprimir_automatico bit(1) null,\r\n  impressora_id bigint(20) null,\r\n  primary key(id),\r\n  foreign key(impressora_id) references impressora(id)\r\n);\r\n\r\nalter table empresa add column config_impressao_id bigint(20) null;\r\nalter table empresa add foreign key(config_impressao_id ) references config_impressao(id);\r\n\r\n<!-- produtos por peso -->\r\ncreate table unidade_medida(\r\n  id bigint(20) not null auto_increment,\r\n  nome varchar(100) not null,\r\n  sigla varchar(10) not null,\r\n  valor_inicial_padrao decimal(9, 2) null,\r\n  incremento_padrao decimal(9, 2) null,\r\n  primary key(id)\r\n)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\nalter table produto add column tipo_de_venda varchar(50) null;\r\nalter table produto add column valor_inicial decimal(9, 2) null;\r\nalter table produto add column incremento decimal(9, 2) null;\r\nalter table produto add column unidade_medida_id   bigint(20) null;\r\n\r\nalter table produto add foreign key(unidade_medida_id) references unidade_medida(id);\r\n\r\ninsert into unidade_medida(nome, sigla, valor_inicial_padrao, incremento_padrao)\r\n    values ('Quilo', 'Kg', 1,1), ('Grama', 'g', 500, 100);\r\n\r\n\r\nalter table item_pedido modify column qtde  decimal(9,2) not null;\r\nalter table item_pedido add column total decimal(9,2) default null;\r\nupdate item_pedido set total = qtde * valor;\r\n\r\n\r\n<-- creates mesa -->\r\n\r\ncreate table mesa(\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  nome varchar(255) not null,\r\n  empresa_id bigint(20) not null,\r\n  removida bit(1) not null default false,\r\n  primary key (id),\r\n  foreign key(empresa_id) references empresa(id)\r\n);\r\n\r\n-- config impressao 2 --\r\n\r\nalter table config_impressao add column layout_pedido varchar(255) default 'PedidoCompleto';\r\n\r\n\r\nalter table pedido add column mesa_id     bigint(20) null;\r\nalter table pedido add foreign key(mesa_id) references mesa(id);\r\n\r\n-- multiplas impressoras --\r\nalter table config_impressao add column multiplas_impressoras bit(1) default false;\r\nalter table config_impressao add column impressora_resumido_id bigint(20) default null;\r\nalter table config_impressao add foreign key(impressora_resumido_id) references impressora(id);\r\n\r\n\r\nalter table item_pedido add column unidade_medida_id   bigint(20)  null;\r\nupdate item_pedido join produto on produto.id = produto_id set  item_pedido.unidade_medida_id = produto.unidade_medida_id;\r\n\r\n\r\nalter table forma_de_pagamento add column online bit(1) default false;\r\n\r\ncreate table config_meio_pagamento(\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  client_id varchar(255) not null,\r\n  client_secret varchar(255) not null,\r\n  primary key (id)\r\n);\r\n\r\nalter table forma_de_pagamento add column config_meio_de_pagamento_id bigint(20);\r\nalter table forma_de_pagamento add foreign key(config_meio_de_pagamento_id) references config_meio_pagamento(id);\r\n\r\n\r\nalter table pedido add column horario_entrega_agendada datetime default null;\r\nalter table empresa_formas_de_entrega add column permite_agendamento bit(1) default false;\r\n\r\nalter table config_meio_pagamento add column nome_fatura_cartao varchar(13);\r\n\r\nalter table produto add column disponivel_na_mesa bit(1) default true;\r\nalter table produto add column disponivel_para_delivery bit(1) default true;\r\n\r\nalter table pagamento_pedido add column link varchar(255) null;\r\nalter table pagamento_pedido add column codigo varchar(255) null;\r\nalter table pagamento_pedido add column id_link varchar(255) null;\r\nalter table pagamento_pedido add column tipo_de_pagamento_externo varchar(255) null;\r\n\r\n\r\nCREATE TABLE cupom (\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  nome varchar(255) DEFAULT NULL,\r\n  codigo varchar(255) NOT NULL,\r\n  valor decimal(9,2) DEFAULT NULL,\r\n  percentual decimal(9,2) DEFAULT NULL,\r\n  tipo varchar(255) NOT NULL,\r\n  ativo bit(1) NOT NULL,\r\n  validade datetime NULL,\r\n  utilizado bit(1) NOT NULL,\r\n  valor_minimo decimal(9,2) DEFAULT NULL,\r\n  qtde_maxima int(11) DEFAULT NULL,\r\n  qtde_utilizado int(11) DEFAULT NULL,\r\n  empresa_id bigint(20) not  NULL,\r\n  PRIMARY KEY (id),\r\n  CONSTRAINT  FOREIGN KEY (empresa_id) REFERENCES empresa (id)\r\n)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\n\r\n CREATE TABLE cupom_contatos(\r\n    cupom_id bigint(20) NOT NULL,\r\n    contato_id  bigint(20) NOT NULL,\r\n    CONSTRAINT   FOREIGN KEY (cupom_id) REFERENCES cupom(id),\r\n    CONSTRAINT   FOREIGN KEY (contato_id) REFERENCES contato(id)\r\n)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\nalter table pedido add column cupom_id  bigint(20) NULL;\r\nalter table pedido add foreign key(cupom_id) references cupom(id);\r\nalter table empresa add column tipo_de_loja varchar(255) null default 'CARDAPIO';\r\nalter table adicional_produto add column tipo_de_cobranca varchar(255) null default 'SOMA';\r\nalter table lista_opcoes_escolhidas add column tipo_de_cobranca varchar(255) default 'SOMA';\r\nalter table impressora add column comandos_fim_impressao varchar(255) null;\r\nalter table campanha add column link_imagem varchar(255) not null;\r\n\r\nCREATE TABLE sessao_mesa (\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  hash varchar(255) NOT NULL,\r\n  horario datetime NOT NULL,\r\n  expirada bit(1) NOT NULL default false,\r\n  mesa_id bigint(20) not null,\r\n  PRIMARY KEY(id),\r\n  constraint foreign key (mesa_id) references mesa(id)\r\n);\r\n\r\nalter table cupom add column restrito bit(1) default null;\r\nalter table empresa_formas_de_entrega add column exibir_tela_busca bit(1) default true;\r\nalter table empresa_formas_de_entrega add column tempo_minimo int null;\r\nalter table empresa_formas_de_entrega add column tempo_maximo int null;\r\n\r\nalter table opcao_adicional_produto add column descricao varchar(255) null;\r\n\r\n\r\nalter table cupom add column primeira_compra bit(1) default null;\r\n\r\nalter table campanha add column origem_contatos varchar(255) default null;\r\nalter table campanha modify column filtro_id  bigint(20) null;\r\n\r\nCREATE TABLE campanha_contatos (\r\n    campanha_id bigint(20) NOT NULL,\r\n    contato_id  bigint(20) NOT NULL,\r\n    CONSTRAINT   FOREIGN KEY (campanha_id) REFERENCES campanha(id),\r\n    CONSTRAINT   FOREIGN KEY (contato_id) REFERENCES contato(id)\r\n)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\nCREATE TABLE produto_horario (\r\n id varchar(255) not null primary key,\r\n  produto_id bigint(20) NOT NULL,\r\n  dia int not null,\r\n      CONSTRAINT   FOREIGN KEY (produto_id) REFERENCES produto(id)\r\n)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\nalter table produto add column disponibilidade int null;\r\n\r\n\r\nupdate produto set disponibilidade= 0;\r\nupdate produto set disponibilidade= 2 where tem_estoque is not true;\r\n\r\nalter table contrato add column numero_parcelas int null;\r\n\r\n\r\nalter table notificacao add column encurtar_links bit(1) default true;\r\n\r\n<!--- integraçao modulo pedido com programa de fidelidade -->\r\nCREATE TABLE integracao_pedido_fidelidade (\r\n  id bigint(20)  not null primary key,\r\n  empresa_id bigint(20) NOT NULL,\r\n  plano_id bigint(20) NOT NULL,\r\n  atividade_id bigint(20) NOT NULL,\r\n  data datetime not null ,\r\n  ativa bit(1) default true,\r\n\r\n  unique(empresa_id),\r\n      CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa(id),\r\n      CONSTRAINT   FOREIGN KEY (atividade_id) REFERENCES atividade(id),\r\n      CONSTRAINT   FOREIGN KEY (plano_id) REFERENCES plano(id)\r\n)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\nalter table atividade add column integrada bit(1) default false;\r\nalter table pedido add column pontos_ganhos decimal(9,2) null;\r\nalter table pontuacao_registrada add column pedido_id bigint(20) null;\r\nalter table pontuacao_registrada add unique(pedido_id);\r\nupdate pedido set pago = false  where status >  4 and pago is true;\r\n\r\n\r\nCREATE TABLE mensagem_bot (\r\n   id bigint(20) NOT NULL AUTO_INCREMENT,\r\n   mensagem mediumtext NOT NULL,\r\n   resposta mediumtext NOT NULL,\r\n   horario datetime NOT NULL,\r\n   telefone varchar(255) NOT NULL,\r\n   contato_id bigint(20) NULL,\r\n   empresa_id bigint(20) NOT NULL,\r\n   imagem varchar(255) DEFAULT NULL,\r\n   intent varchar(255) DEFAULT NULL,\r\n   sessao_id varchar(255) DEFAULT NULL,\r\n   contextos text DEFAULT NULL,\r\n   numero_whatsapp_id bigint(20) DEFAULT NULL,\r\n   PRIMARY KEY (id),\r\n   KEY idx_msg_bot_empresa (empresa_id, id),\r\n   KEY numero_whatsapp_id(numero_whatsapp_id),\r\n   CONSTRAINT mensagem_bot_ibfk_1 FOREIGN KEY (numero_whatsapp_id) REFERENCES numero_whatsapp(id)\r\n ) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;\r\n\r\n\r\nalter table empresa add column descricao_endereco varchar(255) COLLATE utf8mb4_unicode_ci default null;\r\n\r\nalter table empresa_formas_de_entrega add column selecionar_bairro_da_zona bit(1) default false;\r\n\r\nalter table zona_de_entrega add column permite_frete_gratis bit(1) default true;\r\n\r\nalter table contato add column desativar_msg_mkt bit(1) default false;\r\n\r\nalter table cardapio add column modo_visualizacao bit(1) default false;\r\nalter table cardapio modify column arquivo varchar(255) NULL;\r\n\r\nalter table produto add column qtd_minima int null default 1;\r\n\r\nalter table config_meio_pagamento add column meio_de_pagamento varchar(255) not null;\r\nalter table config_meio_pagamento add column token varchar(255) null;\r\nalter table config_meio_pagamento add column email varchar(255) null;\r\nupdate config_meio_pagamento set meio_de_pagamento = 'cielo';\r\nalter table config_meio_pagamento modify column client_id varchar(255) null;\r\nalter table config_meio_pagamento modify column client_secret varchar(255) null;\r\n\r\n\r\n-- Implementação da API\r\nCREATE TABLE cliente_api (\r\n  id varchar(255) NOT NULL,\r\n  acesso_direto bit(1) NOT NULL,\r\n  ativo bit(1) NOT NULL,\r\n  nome varchar(255) NOT NULL,\r\n  segredo varchar(255) NOT NULL,\r\n  tipo varchar(255) DEFAULT NULL,\r\n  data_criacao datetime DEFAULT NULL,\r\n  identificador varchar(255) DEFAULT NULL,\r\n  ip varchar(255) DEFAULT NULL,\r\n  empresa_id bigint(20) not null,\r\n  PRIMARY KEY (id),\r\n  foreign key(empresa_id) references empresa(id)\r\n);\r\n\r\n CREATE TABLE bearer_token (\r\n   id bigint(20) NOT NULL auto_increment,\r\n  token varchar(255) NOT NULL,\r\n  cliente_id varchar(255) NOT NULL,\r\n  usuario_id bigint(20) DEFAULT NULL,\r\n  empresa_id bigint(20) not null,\r\n  data_criacao datetime NOT NULL,\r\n  PRIMARY KEY (id),\r\n  KEY (token),\r\n  KEY FK7999539B841AC73C (usuario_id),\r\n  KEY FK7999539BD0F7A7BE (cliente_id),\r\n  CONSTRAINT FK7999539B841AC73C FOREIGN KEY (usuario_id) REFERENCES usuario (id),\r\n  CONSTRAINT FK7999539BD0F7A7BE FOREIGN KEY (cliente_id) REFERENCES cliente_api (id),\r\n  foreign key(empresa_id) references empresa(id)\r\n);\r\n\r\n\r\nalter table pagamento_pedido add column codigo_transacao varchar(255) null;\r\n\r\n\r\n\r\n<!--- add template pizza -->\r\ncreate table produto_template(\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  empresa_id bigint(20) NOT NULL,\r\n  tipo varchar(255) not null,\r\n  tipo_de_cobranca varchar(20) not null,\r\n  ativo bit(1) default true,\r\n  PRIMARY KEY (id),\r\n  CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa(id)\r\n)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table produto_template_tamanho(\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  descricao varchar(255) not null,\r\n  qtde_pedacos int null,\r\n  qtde_sabores int null,\r\n  produto_template_id bigint(20) NOT NULL,\r\n  disponivel bit(1) default true,\r\n  PRIMARY KEY (id),\r\n  CONSTRAINT   FOREIGN KEY (produto_template_id) REFERENCES produto_template(id)\r\n)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table produto_template_adicional(\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  descricao varchar(255) not null,\r\n  tipo varchar(255) not null,\r\n  obrigatorio bit(1) default true,\r\n  disponivel bit(1) default true,\r\n  produto_template_id bigint(20) NOT NULL,\r\n  PRIMARY KEY (id),\r\n  CONSTRAINT   FOREIGN KEY (produto_template_id) REFERENCES produto_template(id)\r\n)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table produto_template_opcao(\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  nome varchar(255) not null,\r\n  descricao varchar(255)   null,\r\n  disponivel bit(1) default true,\r\n  produto_template_adicional_id bigint(20) NOT NULL,\r\n    PRIMARY KEY (id),\r\n  CONSTRAINT   FOREIGN KEY (produto_template_adicional_id) REFERENCES produto_template_adicional(id)\r\n)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\n\r\ncreate table produto_tamanho(\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  preco decimal(9,2) DEFAULT 0,\r\n  produto_id bigint(20) NOT NULL,\r\n  produto_template_tamanho_id bigint(20) NOT NULL ,\r\n    disponivel bit(1) default true,\r\n  PRIMARY KEY (id),\r\n  CONSTRAINT   FOREIGN KEY (produto_id) REFERENCES produto(id),\r\n  CONSTRAINT   FOREIGN KEY (produto_template_tamanho_id) REFERENCES produto_template_tamanho(id)\r\n)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ncreate table segmento(\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  nome varchar(255)  not null,\r\n    PRIMARY KEY (id)\r\n)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\nalter table empresa add column segmento_id     bigint(20)  null;\r\nalter table empresa add  CONSTRAINT   FOREIGN KEY (segmento_id) REFERENCES segmento(id);\r\n\r\nalter table item_pedido add column descricao varchar(255) null;\r\nalter table item_pedido add column produto_tamanho_id    bigint(20) null;\r\nalter table item_pedido add column item_do_sabor_id     bigint(20) null;\r\n\r\nalter table item_pedido add  CONSTRAINT   FOREIGN KEY (item_do_sabor_id) REFERENCES item_pedido(id);\r\nalter table item_pedido add  CONSTRAINT   FOREIGN KEY (produto_tamanho_id) REFERENCES produto_tamanho(id);\r\n\r\n\r\nalter table produto add column tipo varchar(50) default  'normal';\r\nalter table produto add column template_id  bigint(20) null;\r\nalter table adicional_produto add column produto_template_adicional_id bigint(20) null;\r\nalter table opcao_adicional_produto add column produto_template_opcao_id bigint(20) null;\r\nalter table produto add  CONSTRAINT   FOREIGN KEY (template_id) REFERENCES produto_template(id);\r\nalter table adicional_produto add  CONSTRAINT   FOREIGN KEY (produto_template_adicional_id) REFERENCES produto_template_adicional(id);\r\nalter table opcao_adicional_produto add  CONSTRAINT   FOREIGN KEY (produto_template_opcao_id) REFERENCES produto_template_opcao(id);\r\n\r\nupdate item_pedido join produto on produto_id = produto.id\r\n  set item_pedido.descricao  = produto.nome\r\n      where item_pedido.descricao is null;\r\n\r\n\r\ninsert into segmento(nome) values ('Pizzaria'), ('Hamburgueria'), ('Restaurante');\r\n\r\n\r\n<-- valor mínimo raio de cobrança -->\r\n\r\nalter table raio_de_cobranca add column valor_minimo_pedido decimal(9,2) DEFAULT NULL;\r\nalter table produto_template_opcao add column   valor decimal(9,2) DEFAULT 0;\r\n\r\nalter table produto_template add column montar_pizza bit(1) default false;\r\nalter table produto_template add column venda_por_tamanho bit(1) default false;\r\n\r\nalter table cupom add column removido bit(1) default null;\r\n\r\n\r\nalter table cardapio add column modo_teste_bot bit(1) default false;\r\n\r\nCREATE TABLE sessao_link_saudacao (\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  hash varchar(255) NOT NULL,\r\n  horario datetime NOT NULL,\r\n  expirada bit(1) NOT NULL default false,\r\n  contato_id bigint(20) not null,\r\n  PRIMARY KEY(id),\r\n  CONSTRAINT   FOREIGN KEY (contato_id) REFERENCES contato(id)\r\n);\r\n\r\n\r\nalter table token add column usuario_id bigint(20)  NULL;\r\nalter table token add  CONSTRAINT   FOREIGN KEY (usuario_id) REFERENCES usuario(id);\r\n\r\nalter table token modify contato_id bigint(20)  NULL;\r\n\r\n<!-- migração configurar impressora por setor -->\r\nalter table impressora add column  setor  varchar(255)   null;\r\nalter table impressora add column  layout   varchar(255)   null;\r\nalter table impressora add column  config_impressao_id bigint(20)   null;\r\n\r\nupdate impressora join config_impressao on config_impressao.impressora_id = impressora.id\r\n  set setor = 'Completo', layout = 'PedidoCompleto',  config_impressao_id =  config_impressao.id\r\n      where multiplas_impressoras is true;\r\n\r\n  update impressora join config_impressao on config_impressao.impressora_resumido_id = impressora.id\r\n    set setor = 'Resumido',  layout = 'PedidoResumido', config_impressao_id =  config_impressao.id\r\n         where multiplas_impressoras is true;\r\n\r\nupdate impressora join config_impressao on config_impressao.impressora_id = impressora.id\r\n  set   config_impressao_id =  config_impressao.id\r\n      where multiplas_impressoras is not true;\r\n\r\n\r\ncreate table categoria_impressora (\r\n   categoria_id bigint(20) not null,\r\n   impressora_id bigint(20) not null,\r\n   primary key(categoria_id, impressora_id),\r\n     CONSTRAINT   FOREIGN KEY (categoria_id) REFERENCES categoria(id),\r\n     CONSTRAINT   FOREIGN KEY (impressora_id) REFERENCES impressora(id)\r\n\r\n)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\nalter table impressora add column imprimir_automatico bit(1) default false;\r\n\r\n<!-- pause de categoria -->\r\nalter table categoria add column disponivel bit(1) default true;\r\n\r\n<!-- desconto produto ->\r\nalter table produto add column novo_preco decimal(9,2) null;\r\nalter table produto add column percentual_desconto   decimal(4,2) null;\r\nalter table produto add column destaque   bit(1) default null;\r\n\r\nalter table empresa_formas_de_entrega add column agendamento_obrigatorio bit(1) default false;\r\n\r\n\r\n<!-- Mensagem de bot dinâmica ->\r\ncreate table if not exists config_mensagem_de_bot (\r\n    id bigint(20) not null auto_increment,\r\n    mensagem mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin not null,\r\n    empresa_id bigint(20) not null,\r\n    tipo_de_mensagem varchar(255) not null,\r\n    encurtar_links bit(1) default true,\r\n    primary key (id)\r\n)engine=innodb default charset=utf8mb4 COLLATE=utf8mb4_bin;\r\n\r\n<!-- desconto pizza ->\r\nalter table produto_tamanho add column novo_preco decimal(9,2) null;\r\nalter table produto_tamanho add column percentual_desconto   decimal(4,2) null;\r\nalter table produto_tamanho add column destaque   bit(1) default null;\r\n\r\nalter table sessao_link_saudacao modify column contato_id bigint(20)   NULL;\r\nalter table sessao_link_saudacao add column telefone varchar(20)  NULL;\r\n\r\n\r\n<!-- atributo adicional de pedido -->\r\n\r\nalter table adicional_produto add column classe varchar(255) not null;\r\n\r\nalter table adicional_produto add column entidade varchar(255) not null default 'produto';\r\nalter table adicional_produto add column empresa_id bigint(20) null;\r\nalter table adicional_produto add FOREIGN KEY (empresa_id) REFERENCES empresa(id);\r\nalter table adicional_produto modify column produto_id bigint(20) null;\r\n\r\nalter table valor_de_adicionais_escolha_simples add column pedido_id bigint(20) null;\r\nalter table valor_de_adicionais_escolha_simples add FOREIGN KEY (pedido_id) REFERENCES pedido(id);\r\nalter table valor_de_adicionais_escolha_simples modify column produto_id bigint(20) null;\r\nalter table valor_de_adicionais_escolha_simples modify column item_id bigint(20) null;\r\n\r\nalter table valor_de_adicionais_multipla_escolha add column pedido_id bigint(20) null;\r\nalter table valor_de_adicionais_multipla_escolha add FOREIGN KEY (pedido_id) REFERENCES pedido(id);\r\nalter table valor_de_adicionais_multipla_escolha modify column produto_id bigint(20) null;\r\nalter table valor_de_adicionais_multipla_escolha modify column item_id bigint(20) null;\r\n\r\n\r\n\r\n\r\n<!-- múltiplas imagens por produjto -->\r\ncreate table if not exists imagem_do_produto (\r\n      id bigint(20) not null auto_increment,\r\n    link_imagem varchar(255) not null,\r\n    ordem int(11) not null,\r\n    produto_id  bigint(20) NOT NULL,\r\n    primary key (id),\r\n    CONSTRAINT   FOREIGN KEY (produto_id) REFERENCES produto (id)\r\n);\r\n\r\ninsert into imagem_do_produto(produto_id, link_imagem, ordem) select id, link_imagem, 0 from produto where link_imagem is not null;\r\n\r\n\r\nalter table empresa add column pedido_mesa_nao_identificado bit(1) default false;\r\nalter table plano_empresarial add column intervalo int(11) null;\r\n\r\nupdate  plano_empresarial set intervalo = 6 where nome like '%Semestral%';\r\nupdate  plano_empresarial set intervalo = 3 where nome like '%Trimestral%';\r\nupdate  plano_empresarial set intervalo = 1 where  intervalo is null;\r\n\r\n<!-- atualizar financeiro assinaturas/fatura  ->\r\nalter table assinatura add column data_primeiro_pagamento datetime null;\r\nalter table assinatura add column data_ultimo_pagamento datetime null;\r\nalter table fatura add column primeira bit(1) null;\r\n\r\n update assinatura ,\r\n  (select assinatura.id, min(data_pagamento) data_pagamento\r\n        from  contrato join assinatura on assinatura.id = assinatura_id join  fatura on fatura.contrato_id = contrato.id\r\n            where fatura.status = 2  group by assinatura.id  )  pagamento\r\n    set assinatura.data_primeiro_pagamento = pagamento.data_pagamento  where assinatura.id = pagamento.id;\r\n\r\n\r\n update assinatura ,\r\n  (select assinatura.id, max(data_pagamento) data_pagamento\r\n        from  contrato join assinatura on assinatura.id = assinatura_id join  fatura on fatura.contrato_id = contrato.id\r\n            where fatura.status = 2  group by assinatura.id  ) pagamento\r\n    set assinatura.data_ultimo_pagamento = pagamento.data_pagamento where assinatura.id = pagamento.id;\r\n\r\n\r\n\r\n  update fatura ,\r\n        (select fatura.id, min(data_pagamento) data_pagamento\r\n              from  contrato join assinatura on assinatura.id = assinatura_id join  fatura on fatura.contrato_id = contrato.id\r\n                  where fatura.status = 2  group by assinatura.id  )  pagamento\r\n       set fatura.primeira = true  where fatura.id = pagamento.id;\r\n\r\n\r\nupdate fatura join contrato on fatura.contrato_id = contrato.id\r\n      set fatura.empresa_id = contrato.empresa_id where fatura.empresa_id is null;\r\n\r\n\r\n<!--- add integracao sistema delivery  -->\r\n\r\nalter table pedido add column referencia_externa varchar(100) null;\r\nalter table forma_de_pagamento add column referencia_externa  varchar(255)  null;\r\n\r\nCREATE TABLE  integracao_delivery  (\r\n  id bigint(20) not null auto_increment,\r\n  token varchar(255)  NOT NULL,\r\n  sistema varchar(255)  NOT NULL,\r\n  empresa_id bigint(20) NOT NULL,\r\n  data datetime not null,\r\n  ativa bit(1) DEFAULT b'1',\r\n  PRIMARY KEY (id),\r\n  CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id)\r\n) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;\r\n\r\nCREATE TABLE notificacao_pedido (\r\n  id varchar(255)  NOT NULL,\r\n  origem varchar(255)  NOT NULL,\r\n  status varchar(255)  DEFAULT NULL,\r\n  codigo varchar(255)  DEFAULT NULL,\r\n  horario datetime NOT NULL,\r\n  horario_notificado datetime NOT NULL,\r\n  dados longtext ,\r\n  executada bit(1) DEFAULT NULL,\r\n  erro varchar(255)  DEFAULT NULL,\r\n  ignorar bit(1) DEFAULT NULL,\r\n  PRIMARY KEY (id)\r\n) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;\r\n\r\n<!-- ordem no adicional do produto -->\r\nalter table adicional_produto add column ordem int(11) NULL;\r\nalter table  lista_opcoes_escolhidas add column ordem int(11) null;\r\n\r\n<!-- comandas -->\r\n\r\ncreate table if not exists comanda (\r\n    id bigint(20) not null auto_increment,\r\n    mesa_id  bigint(20) NOT NULL,\r\n    empresa_id bigint(20) NOT NULL,\r\n    horario_abertura datetime NOT NULL,\r\n    horario_fechamento datetime NULL,\r\n    horario_atualizacao datetime NOT NULL,\r\n    contato_id bigint(20) NULL,\r\n    valor decimal(9,2) NOT NULL,\r\n    status varchar(255) NOT NULL,\r\n    primary key (id),\r\n    CONSTRAINT   FOREIGN KEY (mesa_id) REFERENCES mesa (id),\r\n    CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id),\r\n    CONSTRAINT   FOREIGN KEY (contato_id) REFERENCES contato (id)\r\n);\r\n\r\nalter table pedido add column comanda_id     bigint(20) null;\r\nalter table pedido add foreign key(comanda_id) references comanda(id);\r\n\r\nCREATE TABLE pagamento_comanda (\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  valor decimal(9,2) not null,\r\n  troco_para decimal(9,2) not null,\r\n  status varchar(25) not null,\r\n  comanda_id  bigint(20) not null,\r\n   PRIMARY KEY (id),\r\n   CONSTRAINT   FOREIGN KEY (comanda_id) REFERENCES comanda (id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\nalter table pagamento_comanda add column forma_de_pagamento_id bigint(20) not null;\r\nalter table pagamento_comanda add foreign key(forma_de_pagamento_id) references forma_de_pagamento(id);\r\n\r\n\r\n<-- nome categoria destaque -->\r\n\r\nalter table empresa add column nome_categoria_destaques varchar(255) not null default 'DESTAQUES';\r\nalter table empresa add column facebook varchar(255) null;\r\n\r\n<!-- integração com ERP ecletica -->\r\nalter table integracao_delivery add column tipo varchar(50) null;\r\nalter table integracao_delivery add column rede int null;\r\nalter table integracao_delivery add column loja int null;\r\nalter table integracao_delivery add column identificacao varchar(25)  null;\r\n\r\nupdate integracao_delivery set tipo = 'delivery';\r\n\r\nalter table produto add column codigo_pdv varchar(100) null;\r\n\r\ncreate table forma_de_pagamento_integrada(\r\n  sistema varchar(50) not null,\r\n  descricao varchar(100) not null,\r\n  key (sistema)\r\n) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;\r\n\r\ninsert into forma_de_pagamento_integrada (sistema, descricao)\r\n  values ('foodydelivery','money'),('foodydelivery', 'card'),\r\n         ('foodydelivery', 'online'),('foodydelivery','on_credit');\r\n\r\ninsert into forma_de_pagamento_integrada (sistema, descricao)\r\n  values ('ecletica','dinheiro'),('ecletica', 'cartao'),('ecletica', 'ticket'), ('ecletica','cheque'), ('ecletica','outras');\r\n\r\nalter table prospect add column operador_id bigint(20) null;\r\n\r\nalter table contato add column qtde_pedidos int null default 0;\r\n\r\nupdate contato , (select count(*) total, contato_id from pedido where status <= 4 group by contato_id ) pedidos\r\n  set qtde_pedidos  = pedidos.total where contato.id = pedidos.contato_id;\r\n\r\nalter table  empresa_formas_de_entrega add column bairro_opcional bit(1) default false;\r\n\r\n<!-- peso mínimo -->\r\nalter table produto add column peso_minimo DECIMAL(9,2) null;\r\nalter table produto add column peso_maximo DECIMAL(9,2) null;\r\n\r\nalter table fatura add column tentativa_pagamento int default 0;\r\n\r\ncreate table log_fatura(\r\n  id   varchar(255) not  null,\r\n  criado_em  varchar(25) not null,\r\n  descricao   varchar(100) not null,\r\n  notas   varchar(255) not null,\r\n  fatura_id bigint(20) not null,\r\n  PRIMARY KEY (id),\r\n  CONSTRAINT   FOREIGN KEY (fatura_id) REFERENCES fatura (id)\r\n);\r\n\r\n\r\n\r\n<-- suporte a modo html na impressão nativa -->\r\n\r\n  alter table config_impressao add column modo_html bit(1) null default false;\r\n\r\n\r\n<-- imagem na opção do adicional -->\r\n\r\n  alter table opcao_adicional_produto add column link_imagem varchar(255) null;\r\n\r\n\r\ncreate table historico_pedido(\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  horario datetime not null,\r\n  descricao varchar(255) not null,\r\n  pedido_id bigint(20) not null,\r\n  operador_id  bigint(20) null,\r\n  cliente_api_id   varchar(255) null,\r\n  PRIMARY KEY (id),\r\n  CONSTRAINT   FOREIGN KEY (pedido_id) REFERENCES pedido (id),\r\n  CONSTRAINT   FOREIGN KEY (operador_id) REFERENCES usuario (id),\r\n  CONSTRAINT   FOREIGN KEY (cliente_api_id) REFERENCES cliente_api (id)\r\n);\r\n\r\n\r\n<!-- identificador de mesa -->\r\nalter table empresa add column identificador_mesa varchar(255) null default 'Mesa';\r\n\r\n<!-- criação banners -->\r\n<!-- Possíveis valores de comando:\r\nBUSCA\r\nLINK\r\nNADA\r\n-->\r\nCREATE TABLE banner (\r\n  id bigint(20) NOT NULL AUTO_INCREMENT,\r\n  nome varchar(255) not null,\r\n  comando varchar(255) not null,\r\n  extra text NULL,\r\n  disponibilidade int null,\r\n  link_imagem varchar(255) not null,\r\n  removido bit(1)  null default false,\r\n  ordem int not null,\r\n  empresa_id bigint(20) not null,\r\n  PRIMARY KEY (id),\r\n  foreign key(empresa_id) references empresa(id)\r\n);\r\n\r\nCREATE TABLE banner_horario (\r\n id bigint(20) NOT NULL AUTO_INCREMENT,banner_id bigint(20) NOT NULL,\r\n dia int not null,\r\n PRIMARY KEY (id),\r\n CONSTRAINT   FOREIGN KEY (banner_id) REFERENCES banner(id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\n\r\n<!-- add integração ecletica bandeira cartao -->\r\n\r\nalter table forma_de_pagamento_integrada add column tipo_bandeira varchar(10) null;\r\n\r\nupdate forma_de_pagamento_integrada set tipo_bandeira = 'C' where sistema = 'ecletica' and descricao = 'cartao';\r\nupdate forma_de_pagamento_integrada set tipo_bandeira = 'T' where sistema = 'ecletica' and descricao = 'ticket';\r\nupdate forma_de_pagamento_integrada set tipo_bandeira = 'O' where sistema = 'ecletica' and descricao = 'outras';\r\n\r\ncreate table bandeira_cartao_integrada(\r\n    id bigint(20) NOT NULL,\r\n    nome varchar(255) not null,\r\n    tipo varchar(10) null,\r\n      PRIMARY KEY (id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\nalter table forma_de_pagamento add column bandeira_cartao_integrada_id bigint(20) null;\r\nalter table forma_de_pagamento add FOREIGN KEY (bandeira_cartao_integrada_id) REFERENCES bandeira_cartao_integrada(id);\r\n\r\n\r\nalter table sessao_link_saudacao add column qtde_acessos int not null default 0;\r\n\r\nalter table produto_template_opcao add column tamanho_id bigint(20) null;\r\nalter table produto_template_opcao add  foreign key (tamanho_id) references produto_template_tamanho(id);\r\n\r\n\r\nalter table  produto_template add column taxa_extra DECIMAL(9,2) null;\r\n\r\n\r\nalter table opcao_adicional_produto add column codigo_pdv varchar(100) null;\r\n\r\nalter table empresa add column rede varchar(255) null;\r\nalter table adicional_produto add column campo_ordenar varchar(50) null ;\r\n\r\n\r\ncreate table pausa_programada(\r\n    id bigint(20) NOT NULL auto_increment,\r\n    descricao varchar(255) not null,\r\n    data_inicio datetime not null,\r\n    data_fim datetime not null,\r\n    empresa_id bigint(20) not null,\r\n      PRIMARY KEY (id),\r\n       CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa(id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\n\r\nalter table campanha add column qtde_mensagens bigint(20) default 0;\r\nalter table mensagem_enviada modify column contato_id bigint(20) null;\r\n\r\n\r\nalter table notificacao add column fazer_preview bit(1) default 1;\r\nalter table mensagem_enviada add column fazer_preview bit(1) default 1;\r\n\r\nalter table produto_template add column campo_ordenar varchar(50) null ;\r\n\r\nalter table empresa add column saldo_mensagens bigint(20) default 0;\r\n\r\ncreate table historico_creditos_mensagens (\r\n    id bigint(20) NOT NULL auto_increment,\r\n    data datetime not null,\r\n    referencia int(11) NOT NULL,\r\n    qtde bigint(20) not null,\r\n    empresa_id bigint(20) not null,\r\n      PRIMARY KEY (id),\r\n       CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa(id)\r\n) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\nalter table contrato add column qtde_mensagens_mes bigint(20) not null default 1000;\r\n\r\n  CREATE TABLE produto_turno (\r\n    id bigint(20) NOT NULL auto_increment,\r\n  produto_id bigint(20) NOT NULL,\r\n  hora_inicio time not null,\r\n  hora_fim time not null,\r\n        PRIMARY KEY (id),\r\n      CONSTRAINT   FOREIGN KEY (produto_id) REFERENCES produto(id)\r\n)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;\r\n\r\n\r\nalter table mesa add column nao_gerar_comanda bit(1) default 0;\r\n\r\nalter table empresa add column fuso_horario int default -3;\r\n\r\n\r\nalter table comanda add column referencia_externa varchar(100) null;\r\n\r\n\r\nalter table empresa add column cobrar_taxa_servico bit(1) null default false;\r\nalter table empresa add column valor_taxa_servico DECIMAL(4,2) null default 10;\r\n\r\nalter table comanda add column cobrar_taxa_servico bit(1) null default true;\r\nalter table comanda add column taxa_servico decimal(9,2) null;\r\nalter table comanda add column total_com_taxa decimal(9,2) null;\r\n\r\nalter table cardapio drop column modo_visualizacao_qrcode;\r\nalter table cardapio add column modo_visualizacao_qr_code bit(1) default false;\r\n\r\n\r\n\r\nalter table integracao_delivery add column ultima_sincronizacao_disponivel datetime null;\r\n\r\n\r\nalter table empresa_formas_de_entrega add column cep_obrigatorio bit(1) null default false;\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../Servidor/sql/create.sql b/../Servidor/sql/create.sql
--- a/../Servidor/sql/create.sql	(revision 8644f82ba8c625d9e0e248f880ba8d1e47368504)
+++ b/../Servidor/sql/create.sql	(date 1611503024140)
@@ -2262,8 +2262,4 @@
 alter table cardapio add column modo_visualizacao_qr_code bit(1) default false;
 
 
-
 alter table integracao_delivery add column ultima_sincronizacao_disponivel datetime null;
-
-
-alter table empresa_formas_de_entrega add column cep_obrigatorio bit(1) null default false;
Index: ../MeuCardapioScriptsApp/gulpfile.js
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>var gulp = require('gulp');\r\nconst { src, dest } = require('gulp');\r\nvar replace = require('gulp-replace');\r\nvar rename = require(\"gulp-rename\");\r\nvar clean = require('gulp-clean');\r\nconst fs = require('fs');\r\nconst { series } = require('gulp');\r\nvar download = require(\"gulp-download-stream\");\r\nconst { exec } = require('child_process');\r\n\r\n\r\nvar argv = require('yargs').argv;\r\n\r\nconst nomeEmpresa = argv.empresa;\r\nconst dirDest = 'C:\\\\Users\\\\<USER>\\\\AndroidStudioProjects\\\\' + nomeEmpresa + \"\\\\\";\r\nconst dir = 'C:\\\\Users\\\\<USER>\\\\AndroidStudioProjects\\\\meucardapio_app\\\\';\r\nconst nomeDoApp = argv.app;\r\nconst logo = argv.logo;\r\n\r\nfunction defaultTask(cb) {\r\n  // place code for your default task here\r\n  cb();\r\n}\r\n\r\nfunction limparDiretorioApp() {\r\n  return src(dirDest, {read: false, allowEmpty: true})\r\n  .pipe(clean({force: true}));\r\n}\r\n\r\nfunction build(cb) {\r\n  src([dir + '**\\\\*', dir + \"..\\\\certificados\\\\\" + nomeEmpresa + \".jks\"], {dot: true}).\r\n  pipe(replace('ai.meucardapio.fibo', 'ai.meucardapio.' + nomeEmpresa)).\r\n  pipe(replace('__NomeDoAplicativo__', nomeDoApp)).\r\n  pipe(replace(\"fibo.meucardapio.ai\", nomeEmpresa + \".meucardapio.ai\")).\r\n  pipe(replace(\"chave.jks\", nomeEmpresa + \".jks\")).\r\n  \r\n  pipe(dest(dirDest)).on(\"end\", () => {\r\n    fs.renameSync(dirDest + \"android\\\\app\\\\src\\\\main\\\\kotlin\\\\ai\\\\meucardapio\\\\fibo\",\r\n    dirDest + \"android\\\\app\\\\src\\\\main\\\\kotlin\\\\ai\\\\meucardapio\\\\\" + nomeEmpresa);  \r\n\r\n    fs.unlinkSync(dirDest + \"assets\\\\icon\\\\icon.png\");\r\n\r\n    download({\r\n      file: \"icon.png\",\r\n      url: logo\r\n    })\r\n      .pipe(dest(dirDest + \"assets\\\\icon\\\\\"));\r\n      \r\n      const ls = exec('flutter pub run flutter_launcher_icons:main', {\r\n        cwd: dirDest\r\n      }, function (error, stdout, stderr) {\r\n        if (error) {\r\n          console.log(error.stack);\r\n          console.log('Error code: '+error.code);\r\n          console.log('Signal received: '+error.signal);\r\n        }\r\n        console.log('Child Process STDOUT: '+stdout);\r\n        console.log('Child Process STDERR: '+stderr);\r\n      });\r\n  });\r\n\r\n  console.log('build');\r\n  cb();\r\n}\r\n\r\nexports.build = series(limparDiretorioApp, build);\r\nexports.default = defaultTask;\r\n\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../MeuCardapioScriptsApp/gulpfile.js b/../MeuCardapioScriptsApp/gulpfile.js
--- a/../MeuCardapioScriptsApp/gulpfile.js	(revision 8644f82ba8c625d9e0e248f880ba8d1e47368504)
+++ b/../MeuCardapioScriptsApp/gulpfile.js	(date 1611503024146)
@@ -11,9 +11,11 @@
 
 var argv = require('yargs').argv;
 
+console.log(__dirname);
+
 const nomeEmpresa = argv.empresa;
-const dirDest = 'C:\\Users\\<USER>\\AndroidStudioProjects\\' + nomeEmpresa + "\\";
-const dir = 'C:\\Users\\<USER>\\AndroidStudioProjects\\meucardapio_app\\';
+const dirDest = './' + nomeEmpresa + "\\";
+const dir = './../meucardapio_app\\';
 const nomeDoApp = argv.app;
 const logo = argv.logo;
 
Index: .idea/modules.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.idea/modules.xml b/.idea/modules.xml
new file mode 100644
--- /dev/null	(date 1611681756247)
+++ b/.idea/modules.xml	(date 1611681756247)
@@ -0,0 +1,8 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<project version="4">
+  <component name="ProjectModuleManager">
+    <modules>
+      <module fileurl="file://$PROJECT_DIR$/.idea/certificados.iml" filepath="$PROJECT_DIR$/.idea/certificados.iml" />
+    </modules>
+  </component>
+</project>
\ No newline at end of file
Index: .idea/certificados.iml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.idea/certificados.iml b/.idea/certificados.iml
new file mode 100644
--- /dev/null	(date 1611681756240)
+++ b/.idea/certificados.iml	(date 1611681756240)
@@ -0,0 +1,12 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<module type="WEB_MODULE" version="4">
+  <component name="NewModuleRootManager">
+    <content url="file://$MODULE_DIR$">
+      <excludeFolder url="file://$MODULE_DIR$/temp" />
+      <excludeFolder url="file://$MODULE_DIR$/.tmp" />
+      <excludeFolder url="file://$MODULE_DIR$/tmp" />
+    </content>
+    <orderEntry type="inheritedJdk" />
+    <orderEntry type="sourceFolder" forTests="false" />
+  </component>
+</module>
\ No newline at end of file
Index: .idea/vcs.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.idea/vcs.xml b/.idea/vcs.xml
new file mode 100644
--- /dev/null	(date 1611681781853)
+++ b/.idea/vcs.xml	(date 1611681781853)
@@ -0,0 +1,6 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<project version="4">
+  <component name="VcsDirectoryMappings">
+    <mapping directory="$PROJECT_DIR$/.." vcs="Git" />
+  </component>
+</project>
\ No newline at end of file
Index: .idea/.gitignore
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.idea/.gitignore b/.idea/.gitignore
new file mode 100644
--- /dev/null	(date 1611681761392)
+++ b/.idea/.gitignore	(date 1611681761392)
@@ -0,0 +1,5 @@
+# Default ignored files
+/shelf/
+/workspace.xml
+# Editor-based HTTP Client requests
+/httpRequests/
Index: ../MeuCardapioScriptsApp/.idea/vcs.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../MeuCardapioScriptsApp/.idea/vcs.xml b/../MeuCardapioScriptsApp/.idea/vcs.xml
new file mode 100644
--- /dev/null	(date 1611503024154)
+++ b/../MeuCardapioScriptsApp/.idea/vcs.xml	(date 1611503024154)
@@ -0,0 +1,6 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<project version="4">
+  <component name="VcsDirectoryMappings">
+    <mapping directory="$PROJECT_DIR$/.." vcs="Git" />
+  </component>
+</project>
diff --git a/../MeuCardapioScriptsApp/.idea/modules.xml b/../MeuCardapioScriptsApp/.idea/modules.xml
new file mode 100644
diff --git a/../MeuCardapioScriptsApp/.idea/.gitignore b/../MeuCardapioScriptsApp/.idea/.gitignore
new file mode 100644
