<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="543416c0-f719-43eb-9d00-e49a09823e22" name="Default Changelist" comment="">
      <change afterPath="$PROJECT_DIR$/../MeuCardapioScriptsApp/.idea/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../MeuCardapioScriptsApp/.idea/modules.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../MeuCardapioScriptsApp/.idea/vcs.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/certificados.iml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/modules.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/vcs.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../MeuCardapioScriptsApp/gulpfile.js" beforeDir="false" afterPath="$PROJECT_DIR$/../MeuCardapioScriptsApp/gulpfile.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/sql/create.sql" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/sql/create.sql" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProjectId" id="1ncG21SM3WW8idBwNbZaTalriID" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$/../MeuCardapioScriptsApp" />
    <property name="nodejs_interpreter_path.stuck_in_default_project" value="undefined stuck path" />
    <property name="nodejs_npm_path_reset_for_default_project" value="true" />
    <property name="vue.rearranger.settings.migration" value="true" />
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="543416c0-f719-43eb-9d00-e49a09823e22" name="Default Changelist" comment="" />
      <created>1611681754961</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1611681754961</updated>
      <workItem from="1611681756118" duration="811000" />
    </task>
    <task id="LOCAL-00001" summary=".">
      <created>1611681809383</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1611681809383</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="." />
    <option name="LAST_COMMIT_MESSAGE" value="." />
  </component>
</project>