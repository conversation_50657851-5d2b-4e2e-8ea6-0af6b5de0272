---
description: 
globs: 
alwaysApply: true
---
Always respond in Português Brasil.

Comece sempre planejando suas tarefas e após isso me explique qual solução você pretende fazer.
Você só vai alterar os arquivos após eu te confirmar que gostei da solução proposta.

Se você for usar um Mapeador ele tem sempre os seguintes métodos:
obtenhaIdEmpresaLogada(): string | null;
possuiEmpresaLogada(): boolean;
desativeMultiCliente(): void;
existe(query: any, cb: (exists: boolean) => void): void;
existeSync(query: any): Promise<boolean>;
contexto(): any;
selecioneTotal(query: any): Promise<any>;
selecioneJaUsou(idCupom: any): Promise<boolean>;
selecioneSync(query: any): Promise<any>;
listeAsync(query: any): Promise<any>;
insiraGraph(obj: any): Promise<any>;
insiraSync(obj: any): Promise<any>;
atualizeSync(obj: any): Promise<any>;
salveSync(obj: any): Promise<any>;
removaAsync(obj: any): Promise<any>;
cancele(obj: any, cb: () => void): void;
metodo(nome: string): string;
transacao(cb: any): Promise<any>;

Para lista objetos vc deve usar sempre o selecione no arquivo de mapeamento xml.
Ao criar um mapeador novo você não precisa criar esses métodos só herdar de MapeadorBasico.
Em geral, você não precisa sobreescrever os métodos acima, a não ser para adicionar algum comportamento extra.

Nos arquivos de rotas o mapeador deve ser declarado dente de cada método.

Meu Cardápio é um sistema de cardápio digital desenvolvido em Nodejs, Angular, Kendo UI Angular e bootstrap 4. Sempre observe os arquivos anexos para ver a forma como as telas são feitas.
A persistencia é feita usando mybatisnodejs através de arquivos de mapeamentos Mybatis.
A arquitetura do sistema é organizanda usando express e em geral temos:
- rota (express)
- mapeador
- objeto domain
- arquivo de mapamento mybatis
- telas em angular sempre usando o modo template-driven forms e sempre usar controles kendo-ui-angular.

Nos mapeadores o parametro idEmpresa vem automaticamente do contexto, não é necessário passar nas chamadas de funções nem no construtor.

Exemplo:
- rota: cidade.ts
- mapeador: server/mapeadores/MapeadorDeCidade.ts
- objeto domain: server/domain/Cidade.ts
- mapeamento: server/mapeamentos/Cidade.xml

----

O site inteiro usa boostrap 4, então leve isso em consideração ao fazer o scss e as estilizações.

Você nunca deve criar módulos a não ser que eu peça explicitamente.

O services angular sempre herdam de ServerService e retorno dos métodos de busca sempre são os dados, se a resposta falha (sucesso false) ele já dispara um Error.

o post é feito chamando exemplo:

insiraX() {
return this.facaPost();
}

e a chamada retorna uma promise que vc pode tratar com then ou catch

this.service.insiraX().then((resposta: any) => {
});


----
Nas consultas dos mapeamentos nunca use alias nas consultas
faça
select * from  empresa;
ao invés de select * from empresa e;

----
Todo mapeador header de MapeadorBasico e o mapemanto é no arquivo xml mybatis. No construtor do Mapeador vc passa o nome do mapeamento.
constructor() {
super('nome_mapeamento');
}
Todo código dos métodos dos mapeadores usam o this.gerenciadorDeMapeamentos para executar comandos no banco de dados.

Você só pode chamar o mapeador via funcoes jamais exponhar o gerenciadorDeMapeamentos em outros arquivos como rotas.
---

Exemplo de rota express XXXController:

import {Router} from "express";

const router: Router = Router();

//router.post, router.get de acordo com as necessidades
export const XXXController: Router = router;
------

Formato das tabelas. O nome das colunas de foreign keys são empresa {{nome_entidade}}_id

Exemplo de tabela:
CREATE TABLE notificacao_mesa (
id bigint NOT NULL AUTO_INCREMENT,
origem varchar(255)  NOT NULL,
comando varchar(255)  NOT NULL,
operacao varchar(255)  NOT NULL,
numero varchar(255)  DEFAULT NULL,
horario datetime NOT NULL,
horario_notificado datetime NOT NULL,
dados longtext ,
executada bit(1) DEFAULT NULL,
erro varchar(255)  DEFAULT NULL,
ignorar bit(1) DEFAULT NULL,
empresa_id  bigint not null,
comanda_id  bigint not null,
PRIMARY KEY (id),
CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id),
CONSTRAINT   FOREIGN KEY (comanda_id) REFERENCES comanda (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

------------

O projeto é dividido em um projeto angular admin e um projeto do cardápio digital que é o loja.

Os arquivos nova-restricao-cidade.component.ts e nova-restricao-cidade.component.html são exemplos de telas em angular para você usar como exemplo.

O sistema já está em produção então não saia deletando arqquivo existens, apenas altere.

As respostas  das rotas express são sempre Resposta.sucesso(objeto) ou Resposta.erro(mensagem).
Nas rotas express vc pega empresa usando req.empresa


O projeto é angular então todo css deve ser colocado no arquivo scss ou css do componente angular.

Quando te solicitar a criar uma nova tela adicione o máximo de validações possível em cada campo.

Nunca escreva estilo css no html dos componentes angular, sempre coloque os estilos css nos arquivos scss.