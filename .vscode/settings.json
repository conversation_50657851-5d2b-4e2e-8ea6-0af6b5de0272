{
    "git.ignoreLimitWarning": true,
    "sqltools.connections": [
        {
            "askForPassword": false,
            "database": "promokit",
            "driver": "MySQL",
            "mysqlOptions": {
                "authProtocol": "default"
            },
            "name": "Promokit",
            "password": "root",
            "port": 3306,
            "previewLimit": 50,
            "server": "localhost",
            "username": "root"
        }
    ],
    "search.exclude": {
        "**/dist/**": true,
        "**/distServer/**": true
    },
    "livePreview.defaultPreviewPath": "/Servidor/src/app/ia/tela-templates-prompts-db/tela-templates-prompts-db.component.html",
    "accessibility.voice.speechLanguage": "pt-BR",
    "github.copilot.chat.codeGeneration.instructions": [
        {
          "file": "code-style.md" // import instructions from file `code-style.md`
        }
    ],
}