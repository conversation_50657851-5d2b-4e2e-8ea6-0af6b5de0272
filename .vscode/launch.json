{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
    {
        "type": "node",
        "request": "launch",
        "name": "<PERSON><PERSON>",
        "runtimeExecutable": "node",
        "program": "${workspaceFolder}\\Servidor\\distServer\\bin\\promokit.js",
        "restart": true,
        "cwd": "${workspaceFolder}\\Servidor",
        "console": "integratedTerminal",
        "internalConsoleOptions": "neverOpen",
        "skipFiles": [
            "<node_internals>/**"
        ]
    },
        {
            "type": "node",
            "request": "launch",
            "name": "Launch Program",
            "skipFiles": [
                "<node_internals>/**"
            ],
            "program": "${workspaceFolder}\\Servidor\\server\\service\\ComunicadorDialogFlow.ts",
            "outFiles": [
                "${workspaceFolder}/**/*.js"
            ]
        }
    ]
}