<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="BranchesTreeState">
    <expand>
      <path>
        <item name="ROOT" type="e8cecc67:BranchNodeDescriptor" />
        <item name="LOCAL_ROOT" type="e8cecc67:BranchNodeDescriptor" />
      </path>
      <path>
        <item name="ROOT" type="e8cecc67:BranchNodeDescriptor" />
        <item name="REMOTE_ROOT" type="e8cecc67:BranchNodeDescriptor" />
      </path>
      <path>
        <item name="ROOT" type="e8cecc67:BranchNodeDescriptor" />
        <item name="REMOTE_ROOT" type="e8cecc67:BranchNodeDescriptor" />
        <item name="GROUP_NODE:origin" type="e8cecc67:BranchNodeDescriptor" />
      </path>
    </expand>
    <select />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="fd7ffd9b-28a4-4f5a-a35a-54eea3ab46e7" name="Default Changelist" comment=".">
      <change afterPath="$PROJECT_DIR$/../Servidor/src/assets/icons/icon-128x128.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../Servidor/src/assets/icons/icon-144x144.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../Servidor/src/assets/icons/icon-152x152.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../Servidor/src/assets/icons/icon-192x192.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../Servidor/src/assets/icons/icon-384x384.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../Servidor/src/assets/icons/icon-512x512.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../Servidor/src/assets/icons/icon-72x72.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../Servidor/src/assets/icons/icon-96x96.png" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/Gruntfile.js" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/Gruntfile.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/ngsw-config.json" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/ngsw-config.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/server/routes/api.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/server/routes/api.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/server/service/StatusDeMensagem.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/server/service/StatusDeMensagem.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/server/views/home.ejs" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/server/views/home.ejs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/app/adicionar-pontos/adicionar-pontos.component.scss" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/app/adicionar-pontos/adicionar-pontos.component.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/app/app.component.html" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/app/app.component.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/app/app.component.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/app/app.component.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/app/app.module.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/app/app.module.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/app/fidelidade/fidelidade.module.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/app/fidelidade/fidelidade.module.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/app/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/app/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/app/novo-cartao/novo-cartao.component.css" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/app/novo-cartao/novo-cartao.component.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/app/novo-contato/novo-contato.component.scss" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/app/novo-contato/novo-contato.component.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/app/services/cliente.service.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/app/services/cliente.service.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/app/superadmin/cad-brinde/cad-brinde.component.scss" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/app/superadmin/cad-brinde/cad-brinde.component.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/app/trocar-pontos/trocar-pontos.component.scss" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/app/trocar-pontos/trocar-pontos.component.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/assets/fidelidade/Banner 01.png" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/assets/fidelidade/Banner_01.png" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/assets/fidelidade/Banner 02.png" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/assets/fidelidade/Banner_02.png" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/assets/fidelidade/Banner 03 .png" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/assets/fidelidade/Banner_03.png" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/assets/fidelidade/Banner 04 .png" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/assets/fidelidade/Banner_04.png" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/extensao/js/WhatsappAPI.js" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/extensao/js/WhatsappAPI.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/extensao/js/wapi.js" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/extensao/js/wapi.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/favicon.ico" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/favicon.ico" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/manifest.webmanifest" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/manifest.webmanifest" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../WhatsappAPI/favicon.ico" beforeDir="false" afterPath="$PROJECT_DIR$/../WhatsappAPI/favicon.ico" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="HTML File" />
        <option value="CSS File" />
        <option value="JavaScript File" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProjectId" id="1XR0ZRV4WPi2KT1RFHdkl1hXNac" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="DefaultHtmlFileTemplate" value="HTML File" />
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="SHARE_PROJECT_CONFIGURATION_FILES" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$/src" />
    <property name="list.type.of.created.stylesheet" value="CSS" />
    <property name="node.js.detected.package.eslint" value="true" />
    <property name="node.js.detected.package.tslint" value="true" />
    <property name="node.js.path.for.package.eslint" value="project" />
    <property name="node.js.path.for.package.tslint" value="project" />
    <property name="node.js.selected.package.eslint" value="(autodetect)" />
    <property name="node.js.selected.package.tslint" value="(autodetect)" />
  </component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Trabalho\promokit\WhatsappChrome\src\images" />
    </key>
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Trabalho\promokit\WhatsappChrome\src" />
      <recent name="C:\Trabalho\promokit\WhatsappChrome\src\images" />
    </key>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="fd7ffd9b-28a4-4f5a-a35a-54eea3ab46e7" name="Default Changelist" comment="" />
      <created>1580993655058</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1580993655058</updated>
      <workItem from="1580993656298" duration="60160000" />
      <workItem from="1581334253158" duration="18630000" />
      <workItem from="1581424847671" duration="20242000" />
      <workItem from="1581506311053" duration="2000" />
      <workItem from="1581506592242" duration="41390000" />
      <workItem from="1581707739231" duration="8052000" />
      <workItem from="1581716277988" duration="667000" />
      <workItem from="1581723849805" duration="11849000" />
      <workItem from="1581774784456" duration="13850000" />
      <workItem from="1581808280470" duration="280000" />
      <workItem from="1581852813334" duration="11415000" />
      <workItem from="1581959445776" duration="38796000" />
      <workItem from="1582138636852" duration="7026000" />
      <workItem from="1582210635954" duration="75000" />
      <workItem from="1582213075916" duration="14946000" />
      <workItem from="1582291650652" duration="70000" />
      <workItem from="1582292327951" duration="1752000" />
      <workItem from="1582299897812" duration="2904000" />
      <workItem from="1582755339011" duration="13139000" />
      <workItem from="1583074770600" duration="1464000" />
    </task>
    <task id="LOCAL-00001" summary=".">
      <created>1582214110514</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1582214110515</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="2" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="BRANCHES_LOG">
          <value>
            <State />
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
    <option name="oldMeFiltersMigrated" value="true" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="." />
    <option name="LAST_COMMIT_MESSAGE" value="." />
  </component>
  <component name="WindowStateProjectService">
    <state x="100" y="100" width="1263" height="840" maximized="true" key="DiffContextDialog" timestamp="1582759246676">
      <screen x="0" y="0" width="1920" height="1040" />
    </state>
    <state x="100" y="100" width="1263" height="840" maximized="true" key="DiffContextDialog/0.0.1920.1040@0.0.1920.1040" timestamp="1582759246676" />
    <state x="312" y="253" key="Vcs.Push.Dialog.v2" timestamp="1582214121665">
      <screen x="0" y="0" width="1920" height="1040" />
    </state>
    <state x="312" y="253" key="Vcs.Push.Dialog.v2/0.0.1920.1040@0.0.1920.1040" timestamp="1582214121665" />
    <state x="100" y="100" width="1263" height="840" key="com.intellij.history.integration.ui.views.DirectoryHistoryDialog" timestamp="1582809480127">
      <screen x="0" y="0" width="1920" height="1040" />
    </state>
    <state x="83" y="83" width="1053" height="695" key="com.intellij.history.integration.ui.views.DirectoryHistoryDialog/0.0.1600.860@0.0.1600.860" timestamp="1581775265105" />
    <state x="100" y="100" width="1263" height="840" key="com.intellij.history.integration.ui.views.DirectoryHistoryDialog/0.0.1920.1040@0.0.1920.1040" timestamp="1582809480127" />
    <state x="100" y="100" width="1613" height="840" key="com.intellij.history.integration.ui.views.FileHistoryDialog" timestamp="1582807927983">
      <screen x="0" y="0" width="1920" height="1040" />
    </state>
    <state x="83" y="83" width="1062" height="695" key="com.intellij.history.integration.ui.views.FileHistoryDialog/0.0.1600.860@0.0.1600.860" timestamp="1581894836734" />
    <state x="100" y="100" width="1613" height="840" key="com.intellij.history.integration.ui.views.FileHistoryDialog/0.0.1920.1040@0.0.1920.1040" timestamp="1582807927983" />
    <state x="529" y="434" key="com.intellij.openapi.vcs.update.UpdateOrStatusOptionsDialogupdate-v2" timestamp="1582283528013">
      <screen x="0" y="0" width="1920" height="1040" />
    </state>
    <state x="529" y="434" key="com.intellij.openapi.vcs.update.UpdateOrStatusOptionsDialogupdate-v2/0.0.1920.1040@0.0.1920.1040" timestamp="1582283528013" />
    <state x="433" y="251" width="651" height="538" key="find.popup" timestamp="1582815622384">
      <screen x="0" y="0" width="1920" height="1040" />
    </state>
    <state x="361" y="208" width="651" height="538" key="find.popup/0.0.1600.860@0.0.1600.860" timestamp="1581898310410" />
    <state x="433" y="251" width="651" height="538" key="find.popup/0.0.1920.1040@0.0.1920.1040" timestamp="1582815622384" />
    <state x="321" y="190" width="672" height="561" key="search.everywhere.popup" timestamp="1583099252998">
      <screen x="0" y="0" width="1600" height="860" />
    </state>
    <state x="321" y="190" width="672" height="561" key="search.everywhere.popup/0.0.1600.860@0.0.1600.860" timestamp="1583099252998" />
    <state x="385" y="230" width="672" height="678" key="search.everywhere.popup/0.0.1920.1040@0.0.1920.1040" timestamp="1582815812865" />
  </component>
</project>