<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="5679a601-6939-4b96-bcb7-ce8a011aac71" name="Default Changelist" comment="">
      <change afterPath="$PROJECT_DIR$/../Servidor/server/domain/chatbot/TraducaoMensagemChatBot.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../Servidor/server/mapeamentos/TraducaoMensagemChatBot.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../Servidor/server/routes/botpress.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../Servidor/server/routes/tradutor-mensagens-bot.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../Servidor/src/app/chatbot/tela-editar-traduzir-mensagem-bot/tela-editar-traduzir-mensagem-bot.component.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../Servidor/src/app/chatbot/tela-editar-traduzir-mensagem-bot/tela-editar-traduzir-mensagem-bot.component.scss" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../Servidor/src/app/chatbot/tela-editar-traduzir-mensagem-bot/tela-editar-traduzir-mensagem-bot.component.spec.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../Servidor/src/app/chatbot/tela-editar-traduzir-mensagem-bot/tela-editar-traduzir-mensagem-bot.component.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../Servidor/src/app/chatbot/tela-traduzir-mensagens/tela-traduzir-mensagens.component.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../Servidor/src/app/chatbot/tela-traduzir-mensagens/tela-traduzir-mensagens.component.scss" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../Servidor/src/app/chatbot/tela-traduzir-mensagens/tela-traduzir-mensagens.component.spec.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../Servidor/src/app/chatbot/tela-traduzir-mensagens/tela-traduzir-mensagens.component.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../Servidor/src/app/services/tradutor-mensagens-bot.service.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/server/App.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/server/App.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/server/EmpresaMock.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/server/EmpresaMock.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/sql/create.sql" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/sql/create.sql" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/app/app.component.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/app/app.component.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/app/app.module.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/app/app.module.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/app/componentes/menu-fidelidade/menu-fidelidade.component.html" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/app/componentes/menu-fidelidade/menu-fidelidade.component.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/app/fidelidade/fidelidade.module.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/app/fidelidade/fidelidade.module.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/app/fidelidade/fidelidade.routing.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/app/fidelidade/fidelidade.routing.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Servidor/src/app/services/bots.service.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../Servidor/src/app/services/bots.service.ts" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="GitSEFilterConfiguration">
    <file-type-list>
      <filtered-out-file-type name="LOCAL_BRANCH" />
      <filtered-out-file-type name="REMOTE_BRANCH" />
      <filtered-out-file-type name="TAG" />
      <filtered-out-file-type name="COMMIT_BY_MESSAGE" />
    </file-type-list>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="1nIe3a3sqjco7QILQO0rVsAYzli" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Trabalho/promokitnovo&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;ts.external.directory.path&quot;: &quot;C:\\Program Files\\JetBrains\\WebStorm 2022.1\\plugins\\JavaScriptLanguage\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="5679a601-6939-4b96-bcb7-ce8a011aac71" name="Default Changelist" comment="" />
      <created>1611081834065</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1611081834065</updated>
      <workItem from="1611081835257" duration="4451000" />
      <workItem from="1611153292968" duration="1614000" />
      <workItem from="1611267008015" duration="737000" />
      <workItem from="1611502238496" duration="593000" />
      <workItem from="1611681184556" duration="565000" />
      <workItem from="1611687564018" duration="1807000" />
      <workItem from="1611692741558" duration="271000" />
      <workItem from="1611861298240" duration="470000" />
      <workItem from="1634739129993" duration="2267000" />
      <workItem from="1634816333760" duration="249000" />
      <workItem from="1663762905459" duration="1058000" />
      <workItem from="1663768754950" duration="10000" />
      <workItem from="1666178653922" duration="672000" />
      <workItem from="1672318098915" duration="639000" />
      <workItem from="1672320318061" duration="441000" />
      <workItem from="1672320776990" duration="17000" />
      <workItem from="1673381166120" duration="1192000" />
      <workItem from="1673435274769" duration="611000" />
    </task>
    <task id="LOCAL-00001" summary="Adicionando projeto ao repositório.">
      <created>1611082820390</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1611082820390</updated>
    </task>
    <task id="LOCAL-00002" summary="Correção script para funcionar em qualquer sistema operacional.">
      <created>1611690343471</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1611690343471</updated>
    </task>
    <task id="LOCAL-00003" summary="Copiar os scripts app.">
      <created>1666178713421</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1666178713421</updated>
    </task>
    <option name="localTasksCounter" value="4" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="Adicionando projeto ao repositório." />
    <MESSAGE value="Correção script para funcionar em qualquer sistema operacional." />
    <MESSAGE value="Copiar os scripts app." />
    <option name="LAST_COMMIT_MESSAGE" value="Copiar os scripts app." />
  </component>
</project>
