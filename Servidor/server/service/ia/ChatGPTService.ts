import {Configuration, OpenAIApi} from "openai";
import {Ambiente} from "../Ambiente";
import {ChamadaIa} from "../../domain/chatbot/ChamadaIa";
import {MapeadorDeChamadaIa} from "../../mapeadores/MapeadorDeChamadaIa";
const axios = require("axios");

export class ChatGPTService {
  private apiKey: string =  '********************************************************';

  private configuration = new Configuration({
    //apiKey: '***************************************************',
    apiKey: '********************************************************'
  });

  async chameChatgpt2(prompt: string) {
    return new Promise(async (resolve, reject) => {
      const data = { msg: prompt };

      const response = await axios.post('http://localhost:4000/mia/index', data);

      //onFinish(response.data);
      resolve(response.data);
    });
  }

  async chameChatgptChatAsync(prompt: string, mensagens: Array<any>, onProgress: Function, onFinish: Function) {
    const openai = new OpenAIApi(this.configuration);

    console.log(prompt);

    const listaDeMensagens = [{role: "system", content: prompt}];
    //cria uma lista que une listaDeMensagens com mensagens
    const listaCompleta: any = listaDeMensagens.concat(mensagens);

    //console.log(listaCompleta);

    const resposta: any = await openai.createChatCompletion({
      model: "gpt-3.5-turbo",
      messages: listaCompleta,
      temperature: 0.7,
      presence_penalty: 0.0,
      top_p: 1,
      max_tokens: 250,
      stream: true
    }, { responseType: 'stream' });

    console.log('[mia] ' + listaCompleta.map((conversa: any) => {
      return `--${conversa.role}: ${conversa.content}`}).join("\n"));
    const result = {
      role: 'assistant',
      text: '',
      usage: {}
    }

    resposta.data.on('data', (data: Buffer) => {
      const mensagem = '';
      const lines = data.toString().split('\n').filter( (line: string) => line.trim() !== '');
      for (const line of lines) {
        const message = line.replace(/^data: /, '');
        if (message === '[DONE]') {
          onFinish(result);
          console.log('terminou');
          return; // Stream finished
        }
        try {
          const parsed = JSON.parse(message);
          if( parsed.usage ) {
            result.usage = parsed.usage;
          }
          if( parsed.choices[0].delta.content ) {
            result.text += parsed.choices[0].delta.content;
          }
          onProgress(result);
        } catch(error) {
          console.error('Could not JSON parse stream message', message, error);
        }
      }
    });
  }

  async chameChatgpt(prompt: string, onProgress: Function, onFinish: Function) {
    const openai = new OpenAIApi(this.configuration);

    console.log(prompt);

    const resposta: any = await openai.createCompletion({
      model: "text-davinci-003",
      prompt: prompt,
      temperature: 0.7,
      presence_penalty: 0.0,
      best_of: 1,
      top_p: 1,
      max_tokens: 250,
      stream: true
    }, { responseType: 'stream' });

    console.log(prompt);
    const result = {
      role: 'assistant',
      text: '',
      usage: {}
    }

    resposta.data.on('data', (data: any) => {
      const lines = data.toString().split('\n').filter( (line: string) => line.trim() !== '');
      for (const line of lines) {
        const message = line.replace(/^data: /, '');
        console.log('mensagem: ', message);

        if (message === '[DONE]') {
          onFinish(result);
          console.log('terminou');
          return; // Stream finished
        }
        try {
          const parsed = JSON.parse(message);
          if( parsed.usage ) {
            result.usage = parsed.usage;
          }
          result.text += parsed.choices[0].text;
          onProgress(result);
        } catch(error) {
          console.error('Could not JSON parse stream message', message, error);
        }
      }
    });
  }

  async chameOpenAI(prompt: string, temperatura: number) {
    return new Promise( async (resolve, reject) => {
      const openai = new OpenAIApi(this.configuration);

      console.log(prompt);

      const resposta: any = await openai.createCompletion({
        model: "gpt-4o-mini",
        prompt: prompt,
        temperature: temperatura,
        presence_penalty: 0.0,
        best_of: 1,
        top_p: 1,
        max_tokens: 1500
      }, {
        responseType: 'stream',
      });

      const result = {
        role: 'assistant',
        text: '',
        usage: resposta.data.usage
      };
      console.log(result);
      result.text += resposta.data.choices[0].text;
      resolve(result);
      return;
    });
  }

  async chameOpenAIChat(telefone: string, intent: string, prompt: string, mensagem: string, mensagens: Array<any>, temperatura: number,
                        tagLog: string = '[chatbot]', formatoResposta: any): Promise<any> {
    return new Promise( async (resolve, reject) => {
      console.log(`[chatbot] ${telefone}` + prompt.replace(/\n/g, '|'));

      const listaDeMensagens = [{role: "system", content: prompt}];
      //cria uma lista que une listaDeMensagens com mensagens
      const listaCompleta: any = listaDeMensagens.concat(mensagens);

      //console.log(`[chatbot] ${telefone}`, JSON.stringify(listaCompleta));

      let resposta: any = null;
      let inicio = new Date();

      const chamadaIa = new ChamadaIa();

      const result = {
        role: 'assistant',
        text: '',
        sucesso: true,
        usage: { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 },
        estrutura: null as any
      };

      const modelo = 'gpt-4o-mini';

      try {
        const url = 'https://api.openai.com/v1/chat/completions';

        const requestBody: any = {
          messages: listaCompleta,
          model: modelo,
          temperature: temperatura,
          max_tokens: 1500
          , response_format: formatoResposta
        };

        resposta = await axios.post(url, requestBody, {
          timeout: 30000,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ********************************************************',
          },
        });

        console.log(resposta.data);
      } catch (error) {
        console.log(error);
        if( error.response ) {
          console.log(error.response.data.error);
        }
        console.log(error.message);
        // ... tratamento de erro existente ...
      }

      if (resposta) {
        const functionCall = resposta.data.choices[0].message.function_call;
        if (functionCall && functionCall.name === "gerar_resposta_estruturada") {
          const parsedArguments = JSON.parse(functionCall.arguments);
          result.estrutura = parsedArguments.estrutura;
          result.text = parsedArguments.texto;
        } else {
          result.text = resposta.data.choices[0].message.content;
        }

        result.usage = resposta.data.usage;

        chamadaIa.promptTokens = result.usage.prompt_tokens;
        chamadaIa.completionTokens = result.usage.completion_tokens;

        chamadaIa.totalTokens = result.usage.total_tokens;
      }

      chamadaIa.resposta = result.text;
      //console.log('resposta: ', result);

      chamadaIa.mensagem = mensagem;
      chamadaIa.prompt = listaCompleta.map((conversa: any) => {
        return `--${conversa.role}: ${conversa.content}`}).join("\n");
      chamadaIa.api = 'openai-' + modelo;
      chamadaIa.dataCriacao = new Date();
      chamadaIa.horarioCriacao = new Date();

      chamadaIa.tempoChamada = (new Date().getTime() - inicio.getTime()) / 1000;
      chamadaIa.intent = intent;

      await new MapeadorDeChamadaIa().insiraGraph(chamadaIa);

      resolve(result);
      return;
    });
  }
}
