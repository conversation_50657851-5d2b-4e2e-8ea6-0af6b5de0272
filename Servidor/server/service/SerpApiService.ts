import { Resposta } from '../utils/Resposta';
import { Ambiente } from './Ambiente';
const axios = require('axios');

export class SerpApiService {
  private apiKey: string;
  private maxResults: number;
  private enabled: boolean;
  private baseUrl = 'https://serpapi.com/search';

  constructor() {
    const config = Ambiente.Instance.config;
    const cnpjConfig = config.cnpjDiscovery || {};

    this.apiKey = '3f5332127933d1996bb35ca53513796b0c0cbda9ee51cdc8e097a85d0f63fe09';
    this.maxResults = cnpjConfig.maxResults || 15;
    this.enabled = cnpjConfig.enabled !== false;
  }

  /**
   * Busca no Google usando SerpAPI
   */
  async buscarNoGoogle(query: string, maxResults: number = 10): Promise<Resposta<any>> {
    try {
      console.log(`[SerpAPI] Buscando: "${query}"`);

      const params = {
        engine: 'google',
        q: query,
        api_key: this.apiKey,
        num: maxResults,
        hl: 'pt-br',
        gl: 'br'
      };

      const response = await axios.get(this.baseUrl, {
        params,
        timeout: 15000
      });

      if (response.status === 200) {
        const data = response.data;

        // Extrair resultados orgânicos
        const resultados = data.organic_results || [];

        console.log(`[SerpAPI] Encontrados ${resultados.length} resultados`);

        return Resposta.sucesso({
          resultados,
          total: resultados.length,
          query: query
        });
      } else {
        console.error('[SerpAPI] Erro na resposta:', response.status);
        return Resposta.erro('Erro na API de busca');
      }
    } catch (error) {
      console.error('[SerpAPI] Erro na requisição:', error.message);

      if (error.response) {
        const status = error.response.status;
        const message = error.response.data?.error || 'Erro desconhecido';

        if (status === 401) {
          return Resposta.erro('API Key inválida ou expirada');
        } else if (status === 429) {
          return Resposta.erro('Limite de requisições excedido');
        }

        return Resposta.erro(`Erro na API: ${message}`);
      }

      return Resposta.erro('Erro de conexão com a API de busca');
    }
  }

  /**
   * Busca específica para encontrar CNPJ de uma empresa
   */
  async buscarCnpjEmpresa(nomeEmpresa: string, cidade?: string): Promise<Resposta<any>> {
    // Monta query otimizada para encontrar CNPJ
    let query = `"${nomeEmpresa}" CNPJ`;

    if (cidade) {
      query += ` "${cidade}"`;
    }

    // Adiciona termos que ajudam a encontrar CNPJ
    query += ' site:cnpj.biz OR site:consultacnpj.com OR site:receitaws.com.br OR "razão social"';

    return this.buscarNoGoogle(query, 15);
  }

  /**
   * Verifica se a API está configurada corretamente
   */
  isConfigured(): boolean {
    return this.enabled && this.apiKey && this.apiKey !== 'sua_serp_api_key_aqui';
  }
}
