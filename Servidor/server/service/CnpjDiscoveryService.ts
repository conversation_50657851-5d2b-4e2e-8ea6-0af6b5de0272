import { Resposta } from '../utils/Resposta';
import { SerpApiService } from './SerpApiService';
import { ChatGPTService } from './ia/ChatGPTService';

export interface CnpjOption {
  cnpj: string;
  nomeFantasia: string | null;
  razaoSocial: string | null;
  endereco: string | null;
  situacao: string | null;
  capitalSocial: string | null;
  atividadePrincipal: string | null;
  fonte: string;
  confianca: 'alta' | 'media' | 'baixa';
  detalhes?: string;
  socios?: string[];
}

export interface DiscoveryResult {
  cnpjsEncontrados: CnpjOption[];
  termoBuscado: string;
  cidadeBuscada?: string;
  totalEncontrados: number;
}

export class CnpjDiscoveryService {
  private serpApi: SerpApiService;
  private chatGPT: ChatGPTService;

  constructor() {
    this.serpApi = new SerpApiService();
    this.chatGPT = new ChatGPTService();
  }

  /**
   * Descobre múltiplos CNPJs de uma empresa através de busca no Google + IA
   */
  async descobrirCnpj(nomeEmpresa: string, cidade?: string): Promise<Resposta<DiscoveryResult>> {
    try {
      console.log(`[CnpjDiscovery] Iniciando descoberta para: "${nomeEmpresa}"${cidade ? ` em ${cidade}` : ''}`);

      // Verificar se SerpAPI está configurada
      if (!this.serpApi.isConfigured()) {
        const result: DiscoveryResult = {
          cnpjsEncontrados: [],
          termoBuscado: nomeEmpresa,
          cidadeBuscada: cidade,
          totalEncontrados: 0
        };
        return Resposta.sucesso(result) as Resposta<DiscoveryResult>;
      }

      // 1. Tentar busca com nome original
      let resultadoFinal = await this.tentarBuscaCnpj(nomeEmpresa, cidade);
      
      // 2. Se não encontrou, gerar variações inteligentes e tentar novamente
      if (!resultadoFinal.sucesso || !resultadoFinal.data?.cnpjsEncontrados?.length) {
        console.log(`[CnpjDiscovery] Nenhum resultado para "${nomeEmpresa}", gerando variações...`);
        
        const variacoes = await this.gerarVariacoesNomeEmpresa(nomeEmpresa);
        
        for (const variacao of variacoes) {
          if (variacao !== nomeEmpresa) { // Evitar repetir busca original
            console.log(`[CnpjDiscovery] Tentando variação: "${variacao}"`);
            resultadoFinal = await this.tentarBuscaCnpj(variacao, cidade);
            
            if (resultadoFinal.sucesso && resultadoFinal.data?.cnpjsEncontrados?.length > 0) {
              console.log(`[CnpjDiscovery] Sucesso com variação: "${variacao}"`);
              // Atualizar o termo buscado para mostrar qual variação funcionou
              resultadoFinal.data.termoBuscado = `${variacao} (nome simplificado)`;
              break;
            }
          }
        }
      }

      return resultadoFinal;

    } catch (error) {
      console.error('[CnpjDiscovery] Erro na descoberta:', error);
      const result: DiscoveryResult = {
        cnpjsEncontrados: [],
        termoBuscado: nomeEmpresa,
        cidadeBuscada: cidade,
        totalEncontrados: 0
      };
      return Resposta.sucesso(result) as Resposta<DiscoveryResult>;
    }
  }

  /**
   * Tenta buscar CNPJ para um nome específico
   */
  private async tentarBuscaCnpj(nomeEmpresa: string, cidade?: string): Promise<Resposta<DiscoveryResult>> {
    try {
      // 1. Buscar no Google
      const resultadoBusca = await this.serpApi.buscarCnpjEmpresa(nomeEmpresa, cidade);
      
      if (!resultadoBusca.sucesso) {
        const result: DiscoveryResult = {
          cnpjsEncontrados: [],
          termoBuscado: nomeEmpresa,
          cidadeBuscada: cidade,
          totalEncontrados: 0
        };
        return Resposta.sucesso(result) as Resposta<DiscoveryResult>;
      }

      const resultados = resultadoBusca.data.resultados;
      
      if (!resultados || resultados.length === 0) {
        const result: DiscoveryResult = {
          cnpjsEncontrados: [],
          termoBuscado: nomeEmpresa,
          cidadeBuscada: cidade,
          totalEncontrados: 0
        };
        return Resposta.sucesso(result) as Resposta<DiscoveryResult>;
      }

      // 2. Preparar texto dos resultados para IA
      const textoResultados = this.formatarResultadosParaIA(resultados);

      // 3. Usar IA para extrair múltiplos CNPJs
      const resultadoIA = await this.extrairMultiplosCnpjsComIA(nomeEmpresa, textoResultados);

      if (!resultadoIA.sucesso) {
        const result: DiscoveryResult = {
          cnpjsEncontrados: [],
          termoBuscado: nomeEmpresa,
          cidadeBuscada: cidade,
          totalEncontrados: 0
        };
        return Resposta.sucesso(result) as Resposta<DiscoveryResult>;
      }

      const dadosExtraidos = resultadoIA.data;

      console.log(`[CnpjDiscovery] Total de CNPJs encontrados:`, dadosExtraidos.totalEncontrados);

      return Resposta.sucesso(dadosExtraidos) as Resposta<DiscoveryResult>;

    } catch (error) {
      console.error('[CnpjDiscovery] Erro na descoberta:', error);
      const result: DiscoveryResult = {
        cnpjsEncontrados: [],
        termoBuscado: nomeEmpresa,
        cidadeBuscada: cidade,
        totalEncontrados: 0
      };
      return Resposta.sucesso(result) as Resposta<DiscoveryResult>;
    }
  }

  /**
   * Formatar resultados do Google para enviar para IA
   */
  private formatarResultadosParaIA(resultados: any[]): string {
    let texto = '';
    
    resultados.forEach((resultado, index) => {
      texto += `RESULTADO ${index + 1}:\n`;
      texto += `Título: ${resultado.title || ''}\n`;
      texto += `URL: ${resultado.link || ''}\n`;
      texto += `Descrição: ${resultado.snippet || ''}\n`;
      texto += `---\n`;
    });

    return texto;
  }

  /**
   * Usar ChatGPT para extrair múltiplos CNPJs dos resultados
   */
  private async extrairMultiplosCnpjsComIA(nomeEmpresa: string, textoResultados: string): Promise<Resposta<DiscoveryResult>> {
    const prompt = `
Você é um especialista em extração de dados empresariais.
Analise os resultados de busca do Google e extraia TODOS os CNPJs relacionados à empresa "${nomeEmpresa}".

REGRAS IMPORTANTES:
1. Procure por CNPJ no formato XX.XXX.XXX/XXXX-XX ou variações
2. Retorne MÚLTIPLOS CNPJs se encontrar (máximo 5 mais relevantes)
3. Para cada CNPJ, extraia o máximo de informações possível
4. Ordene por relevância/confiança (mais confiável primeiro)
5. Valide se cada CNPJ está relacionado com a empresa buscada

Para cada CNPJ encontrado, extraia:
- CNPJ formatado
- Nome fantasia (nome comercial)
- Razão social (nome jurídico)
- Endereço completo (se disponível)
- Situação (ativa/inativa/suspensa)
- Capital social (se disponível)
- Atividade principal/CNAE (se disponível)
- Fonte onde foi encontrado
- Nível de confiança
- Sócios/proprietários (se disponível)

Retorne um JSON com a seguinte estrutura EXATA:

{
  "cnpjsEncontrados": [
    {
      "cnpj": "XX.XXX.XXX/XXXX-XX",
      "nomeFantasia": "Nome comercial da empresa",
      "razaoSocial": "RAZÃO SOCIAL LTDA",
      "endereco": "Rua ABC, 123, Bairro, Cidade - UF, CEP",
      "situacao": "ATIVA",
      "capitalSocial": "R$ 10.000,00",
      "atividadePrincipal": "Atividade econômica principal",
      "fonte": "site_onde_encontrou",
      "confianca": "alta|media|baixa",
      "detalhes": "explicacao_breve",
      "socios": ["Nome do Sócio 1", "Nome do Sócio 2"]
    }
  ],
  "termoBuscado": "${nomeEmpresa}",
  "totalEncontrados": 0
}

CRITÉRIOS DE CONFIANÇA:
- ALTA: CNPJ de site oficial (gov.br, jucerja, etc.) ou fonte muito confiável
- MÉDIA: CNPJ de sites especializados (consultacnpj, cnpjbrasil, etc.)
- BAIXA: CNPJ de fontes duvidosas ou com dados incompletos

IMPORTANTE: Se não encontrar nenhum CNPJ, retorne array vazio e totalEncontrados: 0

Resultados da busca para analisar:
${textoResultados}
`;

    try {
      const resultado = await this.chatGPT.chameOpenAIChat(
        'sistema',
        'extrair_multiplos_cnpjs',
        prompt,
        textoResultados,
        [],
        0.1, // Temperatura baixa para mais precisão
        '[cnpj-discovery]',
        { type: "json_object" }
      );

      if (!resultado || !resultado.text) {
        console.error('[CnpjDiscovery] IA não retornou resposta');
        const result: DiscoveryResult = {
          cnpjsEncontrados: [],
          termoBuscado: nomeEmpresa,
          totalEncontrados: 0
        };
        return Resposta.sucesso(result) as Resposta<DiscoveryResult>;
      }

      // Parse da resposta JSON da IA
      let dadosExtraidos: DiscoveryResult;
      try {
        dadosExtraidos = JSON.parse(resultado.text);
      } catch (parseError) {
        console.error('[CnpjDiscovery] Erro ao fazer parse da resposta da IA:', parseError);
        const result: DiscoveryResult = {
          cnpjsEncontrados: [],
          termoBuscado: nomeEmpresa,
          totalEncontrados: 0
        };
        return Resposta.sucesso(result) as Resposta<DiscoveryResult>;
      }

      // Validar estrutura da resposta
      if (typeof dadosExtraidos !== 'object' || !Array.isArray(dadosExtraidos.cnpjsEncontrados)) {
        const result: DiscoveryResult = {
          cnpjsEncontrados: [],
          termoBuscado: nomeEmpresa,
          totalEncontrados: 0
        };
        return Resposta.sucesso(result) as Resposta<DiscoveryResult>;
      }

      // Validar e limpar CNPJs encontrados
      const cnpjsValidos: CnpjOption[] = [];
      
      for (const cnpjInfo of dadosExtraidos.cnpjsEncontrados) {
        if (cnpjInfo.cnpj) {
          const cnpjLimpo = cnpjInfo.cnpj.replace(/[^\d]/g, '');
          if (cnpjLimpo.length === 14) {
            // Formatar CNPJ corretamente
            const cnpjFormatado = cnpjLimpo.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/, '$1.$2.$3/$4-$5');
            
            cnpjsValidos.push({
              cnpj: cnpjFormatado,
              nomeFantasia: cnpjInfo.nomeFantasia || null,
              razaoSocial: cnpjInfo.razaoSocial || null,
              endereco: cnpjInfo.endereco || null,
              situacao: cnpjInfo.situacao || null,
              capitalSocial: cnpjInfo.capitalSocial || null,
              atividadePrincipal: cnpjInfo.atividadePrincipal || null,
              fonte: cnpjInfo.fonte || 'google_search',
              confianca: cnpjInfo.confianca || 'baixa',
              detalhes: cnpjInfo.detalhes || 'CNPJ encontrado nos resultados da busca',
              socios: cnpjInfo.socios || []
            });
          } else {
            console.warn('[CnpjDiscovery] CNPJ com formato inválido ignorado:', cnpjInfo.cnpj);
          }
        }
      }

      // Limitar a 5 CNPJs e ordenar por confiança
      const cnpjsOrdenados = cnpjsValidos
        .sort((a, b) => {
          const ordemConfianca = { 'alta': 3, 'media': 2, 'baixa': 1 };
          return ordemConfianca[b.confianca] - ordemConfianca[a.confianca];
        })
        .slice(0, 5);

      const resultado_final: DiscoveryResult = {
        cnpjsEncontrados: cnpjsOrdenados,
        termoBuscado: nomeEmpresa,
        totalEncontrados: cnpjsOrdenados.length
      };

      return Resposta.sucesso(resultado_final) as Resposta<DiscoveryResult>;

    } catch (error) {
      console.error('[CnpjDiscovery] Erro na chamada da IA:', error);
      const result: DiscoveryResult = {
        cnpjsEncontrados: [],
        termoBuscado: nomeEmpresa,
        totalEncontrados: 0
      };
      return Resposta.sucesso(result) as Resposta<DiscoveryResult>;
    }
  }

  /**
   * Gera variações inteligentes do nome da empresa usando IA
   */
  private async gerarVariacoesNomeEmpresa(nomeEmpresa: string): Promise<string[]> {
    const prompt = `
Você é especialista em nomes empresariais brasileiros.
Dado o nome comercial "${nomeEmpresa}", gere 4-5 variações de como esta empresa pode estar registrada oficialmente.

PADRÕES COMUNS:
- Empresas removem palavras descritivas: sushi, restaurante, bar, lanchonete, pizzaria, delivery, food, grill, burger, coffee, café, steakhouse, buffet, self-service, fast, express, bistro, pub, diner, kitchen, house, açaí, sorveteria, padaria, confeitaria, doceria, etc.
- Mantêm apenas o nome/marca principal
- Podem adicionar: LTDA, ME, EIRELI, EPP
- Podem usar variações com/sem acentos
- Podem usar siglas ou abreviações

EXEMPLOS:
"Himitsu Sushi" → ["Himitsu", "Himitsu Ltda", "Himitsu ME"]
"Burger King Express" → ["Burger King", "Burger King do Brasil", "BK"]
"João's Pizzaria" → ["João", "Joao", "João Ltda", "Joao Pizzas"]
"Açaí da Praia" → ["Açai da Praia", "Acai da Praia", "Praia Açai"]

Retorne APENAS um JSON válido:
{"variacoes": ["mais_provavel", "segunda_opcao", "terceira_opcao", "quarta_opcao"]}
`;

    try {
      console.log(`[CnpjDiscovery] Gerando variações para: "${nomeEmpresa}"`);
      
      const resultado = await this.chatGPT.chameOpenAIChat(
        'sistema',
        'gerar_variacoes_nome_empresa',
        prompt,
        nomeEmpresa,
        [],
        0.3, // Temperatura baixa para mais consistência
        '[cnpj-variacoes]',
        { type: "json_object" }
      );

      if (!resultado || !resultado.text) {
        console.error('[CnpjDiscovery] IA não retornou resposta para variações');
        return [nomeEmpresa]; // Fallback para nome original
      }

      try {
        const resposta = JSON.parse(resultado.text);
        if (resposta.variacoes && Array.isArray(resposta.variacoes)) {
          console.log(`[CnpjDiscovery] Variações geradas:`, resposta.variacoes);
          return resposta.variacoes.filter(v => v && v.trim().length > 0);
        }
      } catch (parseError) {
        console.error('[CnpjDiscovery] Erro ao fazer parse das variações:', parseError);
      }

      // Fallback: retorna nome original
      return [nomeEmpresa];

    } catch (error) {
      console.error('[CnpjDiscovery] Erro ao gerar variações:', error);
      return [nomeEmpresa];
    }
  }

  /**
   * Validar formato de CNPJ
   */
  private validarCnpj(cnpj: string): boolean {
    if (!cnpj) return false;
    
    const cnpjLimpo = cnpj.replace(/[^\d]/g, '');
    return cnpjLimpo.length === 14;
  }
}