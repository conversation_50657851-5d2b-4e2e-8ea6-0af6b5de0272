import axios from 'axios';
import Lead from '../../domain/crm/Lead';
import { Resposta } from '../../utils/Resposta';

interface BitrixConfig {
  baseUrl: string;
  userId: string;
  webhook: string;
}

interface BitrixPhone {
  VALUE: string;
  VALUE_TYPE: 'WORK' | 'MOBILE' | 'HOME' | 'OTHER';
}

interface BitrixEmail {
  VALUE: string;
  VALUE_TYPE: 'WORK' | 'HOME' | 'OTHER';
}

interface BitrixWeb {
  VALUE: string;
  VALUE_TYPE: 'WORK' | 'HOME' | 'OTHER';
}

interface BitrixLeadFields {
  TITLE?: string;
  NAME?: string;
  LAST_NAME?: string;
  COMPANY_TITLE?: string;
  STATUS_ID?: string;
  SOURCE_ID?: string;
  SOURCE_DESCRIPTION?: string;
  PHONE?: BitrixPhone[];
  EMAIL?: BitrixEmail[];
  WEB?: BitrixWeb[];
  COMMENTS?: string;
  ASSIGNED_BY_ID?: number;
  // Campos customizados podem ser adicionados com prefixo UF_
  [key: string]: any;
}

interface BitrixLeadPayload {
  fields: BitrixLeadFields;
  params?: {
    REGISTER_SONET_EVENT?: 'Y' | 'N'; // Notificar responsável
  };
}

interface CustomFieldMapping {
  label: string;
  fieldName: string;
  leadProperty: string;
  transform?: (value: any) => any;
}

export class BitrixService {
  private config: BitrixConfig;
  private customFieldsCache: Map<string, any> = new Map();
  private customFieldsMappingCache: Map<string, string> = new Map();

  constructor(config: BitrixConfig) {
    this.config = config;
  }

  /**
   * Configuração de mapeamento dos custom fields baseado no JSON real do Bitrix
   */
  private readonly CUSTOM_FIELD_MAPPINGS: CustomFieldMapping[] = [
    {
      label: "Instagram",
      fieldName: "UF_CRM_1615222177542",
      leadProperty: "getLinkInstagram", // Método que busca link do Instagram
      transform: (value) => value || ''
    },
    {
      label: "Telefone de contato ",
      fieldName: "UF_CRM_1621521259444",
      leadProperty: "telefone",
      transform: (value) => String(value)
    },
    {
      label: "Site",
      fieldName: "UF_CRM_1621947262974",
      leadProperty: "getLinkSite", // Método que busca link do site
      transform: (value) => value || ''
    },
    {
      label: "CNPJ",
      fieldName: "UF_CRM_1621948447906",
      leadProperty: "crmEmpresa.cnpj"
    },
    {
      label: "Rapport",
      fieldName: "UF_CRM_1622222037",
      leadProperty: "observacoes"
    },
    {
      label: "Link Ifood",
      fieldName: "UF_CRM_1623334200",
      leadProperty: "getLinkIfood", // Método que busca link do iFood
      transform: (value) => value || ''
    },
    {
      label: "Link Uber Eats",
      fieldName: "UF_CRM_1623334228",
      leadProperty: "linkUberEats"
    },
    {
      label: "Link Rappi",
      fieldName: "UF_CRM_1623431063",
      leadProperty: "linkRappi"
    },
    {
      label: "Link 99Food",
      fieldName: "UF_CRM_1634307256707",
      leadProperty: "link99Food"
    },
    {
      label: "Link do ifood ",
      fieldName: "UF_CRM_1650463024814",
      leadProperty: "getLinkIfood", // Duplicado - usar o mesmo método
      transform: (value) => value || ''
    },
    {
      label: "Site do Concorrente",
      fieldName: "UF_CRM_1623263814",
      leadProperty: "getLinkConcorrente", // Método que busca link de concorrente
      transform: (value) => value || ''
    },
    {
      label: "Todos os Links",
      fieldName: "UF_CRM_1749901110452",
      leadProperty: "getAllLinksUrls", // Método que retorna array de URLs
      transform: (value) => Array.isArray(value) ? value : []
    }
  ];

  /**
   * Cria um lead no Bitrix24
   */
  async criarLead(lead: Lead): Promise<Resposta<number>> {
    try {
      const payload = this.converterLeadParaBitrix(lead);
      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.lead.add.json`;
      
      console.log('BITRIX: Criando lead:', JSON.stringify(payload, null, 2));
      console.log('BITRIX: URL:', url);

      const response = await axios.post(url, payload);
      
      if (response.data.error) {
        console.error('BITRIX: Erro ao criar lead:', response.data);
        return Resposta.erro(`Erro do Bitrix: ${response.data.error_description}`) as any;
      }

      const leadId = parseInt(response.data.result);
      console.log('BITRIX: Lead criado com sucesso. ID:', leadId);
      
      return Resposta.sucesso(leadId) as Resposta<number>;
    } catch (error) {
      console.error('BITRIX: Erro na requisição:', error);
      return Resposta.erro(`Erro ao conectar com Bitrix: ${error.message}`) as any;
    }
  }

  /**
   * Converte um Lead do nosso sistema para o formato do Bitrix
   */
  private converterLeadParaBitrix(lead: Lead): BitrixLeadPayload {
    // Extrair nome e sobrenome
    const nomeResponsavel = lead.nomeResponsavel || '';
    const partesNome = nomeResponsavel.trim().split(' ');
    const nome = partesNome[0] || '';
    const sobrenome = partesNome.slice(1).join(' ') || '';

    // Montar comentários com informações extras
    const comentarios = this.montarComentarios(lead);

    // Determinar fonte baseada na origem
    const fonte = this.mapearOrigem(lead.origem);

    const empresa = lead.empresa || 'Empresa sem nome';
    const titulo = `${empresa} - ${nomeResponsavel}`;
    
    const fields: BitrixLeadFields = {
      TITLE: titulo,
      NAME: nome,
      LAST_NAME: sobrenome,
      COMPANY_TITLE: empresa,
      STATUS_ID: this.mapearEtapaParaStatus(lead.etapa),
      SOURCE_ID: fonte.id,
      SOURCE_DESCRIPTION: fonte.descricao,
      COMMENTS: comentarios
    };

    // Adicionar telefone se disponível
    if (lead.telefone) {
      fields.PHONE = [{
        VALUE: this.formatarTelefone(lead.telefone),
        VALUE_TYPE: 'MOBILE'
      }];
    }

    // Adicionar email da empresa se disponível
    if (lead.crmEmpresa?.email) {
      fields.EMAIL = [{
        VALUE: lead.crmEmpresa.email,
        VALUE_TYPE: 'WORK'
      }];
    }

    // Adicionar links do Instagram e site
    const webs: BitrixWeb[] = [];
    
    if (lead.instagramHandle) {
      webs.push({
        VALUE: `https://instagram.com/${lead.instagramHandle}`,
        VALUE_TYPE: 'OTHER'
      });
    }

    if (lead.linkInsta) {
      webs.push({
        VALUE: lead.linkInsta,
        VALUE_TYPE: 'OTHER'
      });
    }

    if (lead.instagramData?.website) {
      webs.push({
        VALUE: lead.instagramData.website,
        VALUE_TYPE: 'WORK'
      });
    }

    if (webs.length > 0) {
      fields.WEB = webs;
    }

    // *** NOVO: Mapear custom fields dinamicamente ***
    console.log('BITRIX: Iniciando mapeamento dinâmico de custom fields...');
    const customFields = this.mapLeadToCustomFields(lead);
    
    // Adicionar todos os custom fields mapeados ao payload
    Object.assign(fields, customFields);

    // Manter campos legados para compatibilidade (serão sobrescritos se mapeados dinamicamente)
    if (!customFields['UF_CRM_1615222177542'] && lead.instagramHandle) {
      fields['UF_CRM_1615222177542'] = `https://instagram.com/${lead.instagramHandle}`;
    }

    if (lead.score !== undefined && lead.score !== null) {
      fields['UF_SCORE'] = lead.score.toString();
    }

    if (lead.segmento) {
      fields['UF_SEGMENTO'] = lead.segmento;
    }

    if (lead.instagramData?.followers) {
      fields['UF_SEGUIDORES'] = lead.instagramData.followers.toString();
    }

    console.log('BITRIX: Custom fields finais adicionados:', Object.keys(customFields).length, 'campos');
    console.log('BITRIX: Preview dos custom fields:', customFields);

    return {
      fields,
      params: {
        REGISTER_SONET_EVENT: 'Y' // Notificar responsável
      }
    };
  }

  /**
   * Monta comentários com informações detalhadas do lead
   */
  private montarComentarios(lead: Lead): string {
    const comentarios: string[] = [];

    // Informações básicas
    comentarios.push(`Score: ${lead.score}%`);
    comentarios.push(`Segmento: ${lead.segmento || 'Não definido'}`);
    comentarios.push(`Etapa: ${lead.etapa}`);

    // Dados do Instagram
    if (lead.instagramData) {
      comentarios.push('\\n=== DADOS INSTAGRAM ===');
      if (lead.instagramData.followers) {
        comentarios.push(`Seguidores: ${lead.instagramData.followers.toLocaleString()}`);
      }
      if (lead.instagramData.following) {
        comentarios.push(`Seguindo: ${lead.instagramData.following.toLocaleString()}`);
      }
      if (lead.instagramData.accountType) {
        comentarios.push(`Tipo de conta: ${lead.instagramData.accountType}`);
      }
      if (lead.instagramData.businessCategory) {
        comentarios.push(`Categoria: ${lead.instagramData.businessCategory}`);
      }
      if (lead.bioInsta) {
        comentarios.push(`Bio: ${lead.bioInsta}`);
      }
    }

    // Observações
    if (lead.observacoes) {
      comentarios.push('\\n=== OBSERVAÇÕES DE VENDAS ===');
      comentarios.push(lead.observacoes);
    }

    // Notas do Instagram
    if (lead.notas) {
      comentarios.push('\\n=== DADOS EXTRAÍDOS ===');
      comentarios.push(lead.notas);
    }

    return comentarios.join('\\n');
  }

  /**
   * Mapeia origem do lead para fonte do Bitrix
   */
  private mapearOrigem(origem: string): { id: string; descricao: string } {
    const mapeamento: Record<string, { id: string; descricao: string }> = {
      'Instagram': { id: 'WEB', descricao: 'Lead gerado via Instagram' },
      'Site/Landing Page': { id: 'WEB', descricao: 'Lead do site/landing page' },
      'WhatsApp Direto': { id: 'OTHER', descricao: 'Contato direto via WhatsApp' },
      'Indicação': { id: 'PARTNER', descricao: 'Lead por indicação' },
      'Evento/Feira': { id: 'TRADE_SHOW', descricao: 'Lead de evento/feira' },
      'Outros': { id: 'OTHER', descricao: 'Outras fontes' }
    };

    return mapeamento[origem] || { id: 'OTHER', descricao: origem };
  }

  /**
   * Mapeia etapa do funil para status do Bitrix
   */
  private mapearEtapaParaStatus(etapa: string): string {
    const mapeamento: Record<string, string> = {
      'Prospecção': 'NEW',
      'Qualificação': 'IN_PROCESS', 
      'Objeção': 'IN_PROCESS',
      'Fechamento': 'PROCESSED',
      'Ganho': 'CONVERTED',
      'Perdido': 'JUNK'
    };

    return mapeamento[etapa] || 'NEW';
  }

  /**
   * Formata telefone para o padrão internacional
   */
  private formatarTelefone(telefone: string): string {
    const numeroLimpo = telefone.replace(/\D/g, '');
    
    if (numeroLimpo.startsWith('55')) {
      return `+${numeroLimpo}`;
    }
    
    return `+55${numeroLimpo}`;
  }

  /**
   * Busca um lead no Bitrix pelo ID
   */
  async buscarLead(leadId: number): Promise<Resposta<any>> {
    try {
      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.lead.get.json?id=${leadId}`;
      
      const response = await axios.get(url);
      
      if (response.data.error) {
        return Resposta.erro(`Erro do Bitrix: ${response.data.error_description}`);
      }

      return Resposta.sucesso(response.data.result) as Resposta<any>;
    } catch (error) {
      return Resposta.erro(`Erro ao buscar lead: ${error.message}`);
    }
  }

  /**
   * Atualiza um lead no Bitrix
   */
  async atualizarLead(leadId: number, campos: Partial<BitrixLeadFields>): Promise<Resposta<boolean>> {
    try {
      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.lead.update.json`;
      
      const payload = {
        id: leadId,
        fields: campos
      };

      const response = await axios.post(url, payload);
      
      if (response.data.error) {
        return Resposta.erro(`Erro do Bitrix: ${response.data.error_description}`) as any;
      }

      return Resposta.sucesso(true) as Resposta<boolean>;
    } catch (error) {
      return Resposta.erro(`Erro ao atualizar lead: ${error.message}`) as any;
    }
  }

  /**
   * Lista os custom fields (campos personalizados) de leads do Bitrix
   */
  async listarCustomFields(): Promise<Resposta<any[]>> {
    try {
      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.lead.userfield.list.json`;
      
      console.log('BITRIX: Listando custom fields de leads...');
      console.log('BITRIX: URL:', url);

      const response = await axios.get(url);
      
      if (response.data.error) {
        console.error('BITRIX: Erro ao listar custom fields:', response.data);
        return Resposta.erro(`Erro do Bitrix: ${response.data.error_description}`) as any;
      }

      const customFields = response.data.result;
      console.log('BITRIX: Custom fields raw data:', JSON.stringify(customFields, null, 2));
      console.log('BITRIX: Custom fields encontrados:', customFields.length);
      
      // Processar campos para exibir informações mais úteis
      const camposProcessados = customFields.map((field: any) => ({
        id: field.ID,
        fieldName: field.FIELD_NAME,
        userTypeId: field.USER_TYPE_ID,
        listLabel: field.LIST_LABEL,
        listColumnLabel: field.LIST_COLUMN_LABEL,
        listFilterLabel: field.LIST_FILTER_LABEL,
        editFormLabel: field.EDIT_FORM_LABEL,
        settings: field.SETTINGS,
        mandatory: field.MANDATORY === 'Y',
        multiple: field.MULTIPLE === 'Y',
        sort: field.SORT,
        showInList: field.SHOW_IN_LIST === 'Y',
        editInList: field.EDIT_IN_LIST === 'Y',
        showFilter: field.SHOW_FILTER === 'Y'
      }));
      
      console.log('BITRIX: Campos processados:', JSON.stringify(camposProcessados, null, 2));
      
      return Resposta.sucesso(camposProcessados) as Resposta<any[]>;
    } catch (error) {
      console.error('BITRIX: Erro na requisição de custom fields:', error);
      return Resposta.erro(`Erro ao listar custom fields: ${error.message}`) as any;
    }
  }

  /**
   * Busca um custom field específico do Bitrix pelo ID
   */
  async buscarCustomField(fieldId: string): Promise<Resposta<any>> {
    try {
      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.lead.userfield.get.json?id=${fieldId}`;
      
      console.log('BITRIX: Buscando custom field ID:', fieldId);
      console.log('BITRIX: URL:', url);

      const response = await axios.get(url);
      
      if (response.data.error) {
        console.error('BITRIX: Erro ao buscar custom field:', response.data);
        return Resposta.erro(`Erro do Bitrix: ${response.data.error_description}`);
      }

      const customField = response.data.result;
      console.log('BITRIX: Custom field raw data:', JSON.stringify(customField, null, 2));
      
      if (!customField) {
        return Resposta.erro(`Custom field com ID ${fieldId} não encontrado`);
      }
      
      // Processar campo para exibir informações mais úteis
      const campoProcessado = {
        id: customField.ID,
        fieldName: customField.FIELD_NAME,
        userTypeId: customField.USER_TYPE_ID,
        xmlId: customField.XML_ID,
        listLabel: customField.LIST_LABEL,
        listColumnLabel: customField.LIST_COLUMN_LABEL,
        listFilterLabel: customField.LIST_FILTER_LABEL,
        editFormLabel: customField.EDIT_FORM_LABEL,
        errorMessage: customField.ERROR_MESSAGE,
        helpMessage: customField.HELP_MESSAGE,
        settings: customField.SETTINGS,
        mandatory: customField.MANDATORY === 'Y',
        multiple: customField.MULTIPLE === 'Y',
        sort: customField.SORT,
        showInList: customField.SHOW_IN_LIST === 'Y',
        editInList: customField.EDIT_IN_LIST === 'Y',
        showFilter: customField.SHOW_FILTER === 'Y',
        showAdvancedFilter: customField.SHOW_ADV_FILTER === 'Y',
        entityId: customField.ENTITY_ID,
        dateCreate: customField.DATE_CREATE,
        dateModify: customField.DATE_MODIFY,
        createdBy: customField.CREATED_BY,
        modifiedBy: customField.MODIFIED_BY
      };
      
      console.log('BITRIX: Campo processado:', JSON.stringify(campoProcessado, null, 2));
      
      return Resposta.sucesso(campoProcessado) as Resposta<any>;
    } catch (error) {
      console.error('BITRIX: Erro na requisição do custom field:', error);
      return Resposta.erro(`Erro ao buscar custom field: ${error.message}`);
    }
  }

  /**
   * Lista custom fields com detalhes completos (combina list + get para cada campo)
   */
  async listarCustomFieldsCompletos(): Promise<Resposta<any[]>> {
    try {
      console.log('BITRIX: Buscando custom fields completos...');
      
      // Primeiro, listar todos os custom fields
      const resultadoLista = await this.listarCustomFields();
      
      if (!resultadoLista.sucesso) {
        return resultadoLista;
      }
      
      const customFields = resultadoLista.data;
      console.log('BITRIX: Obtendo detalhes para', customFields.length, 'custom fields...');
      
      const camposCompletos = [];
      
      // Para cada campo, buscar os detalhes completos
      for (const campo of customFields) {
        try {
          console.log('BITRIX: Buscando detalhes do campo ID:', campo.id);
          
          const resultadoDetalhe = await this.buscarCustomField(campo.id);
          
          if (resultadoDetalhe.sucesso) {
            // Combinar dados da lista com detalhes completos
            const campoCompleto = {
              id: campo.id,
              fieldName: campo.fieldName,
              userTypeId: campo.userTypeId,
              label: this.extrairLabelPortugues(resultadoDetalhe.data.listColumnLabel) || 
                     this.extrairLabelPortugues(resultadoDetalhe.data.editFormLabel) ||
                     this.extrairLabelPortugues(resultadoDetalhe.data.listFilterLabel) ||
                     campo.fieldName,
              mandatory: resultadoDetalhe.data.mandatory,
              multiple: resultadoDetalhe.data.multiple,
              showInList: resultadoDetalhe.data.showInList,
              showFilter: resultadoDetalhe.data.showFilter,
              sort: resultadoDetalhe.data.sort,
              settings: resultadoDetalhe.data.settings
            };
            
            camposCompletos.push(campoCompleto);
          } else {
            console.warn('BITRIX: Erro ao buscar detalhes do campo', campo.id, ':', resultadoDetalhe.erro);
            // Manter o campo mesmo sem detalhes completos
            camposCompletos.push(campo);
          }
          
          // Pequena pausa para evitar rate limiting
          await new Promise(resolve => setTimeout(resolve, 100));
          
        } catch (error) {
          console.error('BITRIX: Erro ao processar campo', campo.id, ':', error);
          // Manter o campo mesmo com erro
          camposCompletos.push(campo);
        }
      }
      
      console.log('BITRIX: Custom fields completos processados:', camposCompletos.length);
      
      return Resposta.sucesso(camposCompletos) as Resposta<any[]>;
      
    } catch (error) {
      console.error('BITRIX: Erro ao buscar custom fields completos:', error);
      return Resposta.erro(`Erro ao buscar custom fields completos: ${error.message}`) as any;
    }
  }

  /**
   * Extrai label em português de um objeto de labels multilíngue
   */
  private extrairLabelPortugues(labelObj: any): string {
    if (!labelObj) return '';
    
    if (typeof labelObj === 'string') {
      return labelObj;
    }
    
    if (typeof labelObj === 'object') {
      // Tentar português brasileiro primeiro, depois português, depois qualquer chave
      return labelObj.br || labelObj.pt || labelObj.en || labelObj[Object.keys(labelObj)[0]] || '';
    }
    
    return String(labelObj);
  }

  /**
   * Retorna o mapeamento estático dos custom fields (não precisa buscar do Bitrix)
   */
  private getCustomFieldsMapping(): Map<string, string> {
    if (this.customFieldsMappingCache.size === 0) {
      // Criar cache baseado na configuração estática
      this.CUSTOM_FIELD_MAPPINGS.forEach(mapping => {
        this.customFieldsMappingCache.set(mapping.label, mapping.fieldName);
      });
      console.log('BITRIX: Cache de mapeamento criado com', this.customFieldsMappingCache.size, 'campos');
    }
    
    return this.customFieldsMappingCache;
  }

  /**
   * Obtém valor de uma propriedade aninhada do objeto Lead ou chama método
   */
  private getNestedProperty(obj: any, path: string): any {
    // Se o path é um método (não contém ponto), tenta chamar como método
    if (!path.includes('.') && typeof obj[path] === 'function') {
      try {
        return obj[path]();
      } catch (error) {
        console.warn(`BITRIX: Erro ao chamar método ${path}:`, error);
        return undefined;
      }
    }

    // Caso contrário, navega pelas propriedades aninhadas
    return path.split('.').reduce((current, prop) => {
      return current && current[prop] !== undefined ? current[prop] : undefined;
    }, obj);
  }

  /**
   * Mapeia dados do Lead para custom fields do Bitrix
   */
  private mapLeadToCustomFields(lead: Lead): Record<string, any> {
    const customFields: Record<string, any> = {};

    // Buscar mapeamento dos custom fields (agora é síncrono)
    this.getCustomFieldsMapping();

    console.log('BITRIX: Iniciando mapeamento de custom fields para lead:', lead.nomeResponsavel);
    console.log('BITRIX: Lead possui', lead.links?.length || 0, 'links associados');

    // Log detalhado dos links se existirem
    if (lead.links && lead.links.length > 0) {
      console.log('BITRIX: Links detalhados:');
      lead.links.forEach((link: any, index: number) => {
        console.log(`BITRIX: Link ${index + 1}: ${link.tipo} = ${link.url} (ativo: ${link.ativo})`);
      });
    }

    // Testar métodos do Lead
    console.log('BITRIX: Testando métodos do Lead:');
    if (typeof lead.getLinkInstagram === 'function') {
      console.log('BITRIX: getLinkInstagram():', lead.getLinkInstagram());
    } else {
      console.log('BITRIX: getLinkInstagram() não é uma função');
    }

    if (typeof lead.getLinkIfood === 'function') {
      console.log('BITRIX: getLinkIfood():', lead.getLinkIfood());
    } else {
      console.log('BITRIX: getLinkIfood() não é uma função');
    }

    if (typeof lead.getLinkConcorrente === 'function') {
      console.log('BITRIX: getLinkConcorrente():', lead.getLinkConcorrente());
    } else {
      console.log('BITRIX: getLinkConcorrente() não é uma função');
    }

    // Processar cada mapeamento configurado
    for (const mapping of this.CUSTOM_FIELD_MAPPINGS) {
      if (!mapping.fieldName) {
        console.warn(`BITRIX: FieldName não encontrado para "${mapping.label}"`);
        continue;
      }

      // Obter valor da propriedade do lead
      let value = this.getNestedProperty(lead, mapping.leadProperty);

      console.log(`BITRIX: Tentando mapear ${mapping.label} (${mapping.leadProperty}): ${value}`);

      if (value !== undefined && value !== null && value !== '') {
        // Aplicar transformação se definida
        if (mapping.transform) {
          const originalValue = value;
          value = mapping.transform(value);
          console.log(`BITRIX: Transformação aplicada para ${mapping.label}: ${originalValue} -> ${value}`);
        }

        customFields[mapping.fieldName] = value;
        console.log(`BITRIX: ✓ Custom field mapeado: ${mapping.fieldName} (${mapping.label}) = ${value}`);
      } else {
        console.log(`BITRIX: ✗ Valor vazio para ${mapping.label} (${mapping.leadProperty})`);
      }
    }

    console.log('BITRIX: Total de custom fields mapeados:', Object.keys(customFields).length);
    console.log('BITRIX: Resumo dos campos enviados:', Object.keys(customFields).map(key => {
      const mapping = this.CUSTOM_FIELD_MAPPINGS.find(m => m.fieldName === key);
      return `${key} (${mapping?.label || 'Desconhecido'}): ${customFields[key]}`;
    }));

    return customFields;
  }
}

/**
 * Factory para criar instância do BitrixService com configuração padrão
 */
export class BitrixServiceFactory {
  static criarInstancia(): BitrixService {
    const config: BitrixConfig = {
      baseUrl: 'https://b24-chlbsw.bitrix24.com.br',
      userId: '1',
      webhook: '19i08a5m1x8am1f6'
    };

    return new BitrixService(config);
  }

  static criarInstanciaPersonalizada(baseUrl: string, userId: string, webhook: string): BitrixService {
    const config: BitrixConfig = {
      baseUrl,
      userId, 
      webhook
    };

    return new BitrixService(config);
  }
}