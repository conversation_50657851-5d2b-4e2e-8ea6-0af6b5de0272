import * as passport from 'passport';
import local = require('passport-local');
import bearer = require('passport-http-bearer');
import basic = require('passport-http')
import password = require('passport-oauth2-client-password')
import bcrypt = require("bcrypt");
import {UsuarioService} from "../service/UsuarioService";
import {Usuario} from "../domain/Usuario";
import {MapeadorDeContato} from "../mapeadores/MapeadorDeContato";
import {MapeadorDeBearerToken} from "../mapeadores/MapeadorDeBearerToken";
import {MapeadorDeClienteApi} from "../mapeadores/MapeadorDeClienteApi";

export class ConfigPassport{
  public passport: any;
  constructor() {
    this.passport = passport;
  }

  findUser(email: string, cb: (error: any, user?: any) => void) {
    let usuarioService = new UsuarioService();
    if(!email || email === "") return cb(null, null);
    usuarioService.obtenhaUsuarioPeloEmail(email).then(usuario => {
      cb(null, usuario)
    })
  }

  findById(id: string , cb: any) {
    let usuarioService = new UsuarioService();

    usuarioService.obtenhaUsuarioPeloId(id).then(usuario => {
      cb(null, usuario)
    })
  }

  private obtenhaIpDaRequest(req: any){
    let cabecalho = req.headers

   return  cabecalho && cabecalho['x-forwarded-for'] && cabecalho['x-forwarded-for'].length > 0  ?
           cabecalho['x-forwarded-for'][0]  :  req.connection.remoteAddress;

  }

  configure() {
    passport.serializeUser(function(user: any, done) {
      done(null, JSON.stringify(user));
    });

    passport.deserializeUser(function(json: string, done) {
      let usuario: Usuario = new Usuario();

      Object.assign(usuario, JSON.parse(json))

      done(null, usuario)
    });

    passport.use(new local.Strategy({
      usernameField: 'user[email]',
      passwordField: 'user[password]',
      passReqToCallback: true
    },
      (req, email, senha, done) => {
        this.findUser(email, (err, user) => {
          if (err)   return done(err)
          if (!user) return done(null, false)
          if (!user.senha) return done('nenhuma senha definida ainda para esse usuário')

          bcrypt.compare(senha, user.senha, (errSenha, isValid) => {
            if (errSenha)  return done(errSenha)
            if (!isValid)  return done(null, false)
            let ip: string = this.obtenhaIpDaRequest(req);
            if(ip) user.ip = ip
            return done(null, user)
          })

        })
      }
    ))

    function autentiqueClienteDireto(clientId: any, clientSecret: any, done: any) {
      console.log('VALIDANDO DIRETO')
      new MapeadorDeClienteApi().selecioneSync(clientId).then((cliente: any) => {
        if (!cliente) {
          console.log('não tem id cliente: ' + clientId)
          return done(null, false, {mensagem: 'Identificador ou segredo do cliente inválidos'});
        }
        if (cliente.segredo !== clientSecret) {
          console.log('Identificador ou segredo do cliente inválidos')
          return done(null, false, {mensagem: 'Identificador ou segredo do cliente inválidos'});
        }
        if(!cliente.acessoDireto){
          console.log('Esse cliente não possui acesso direto ao token.')
          return done(null, false, {mensagem: 'Esse cliente não possui acesso direto ao token.'});
        }

        if(!cliente.ativo) {
          console.log('Esse cliente ainda não está ativo: ' + clientId)
          return done(null, false, {mensagem: 'Esse cliente ainda não está ativo: ' + clientId})
        }
        return done(null, cliente);
      });
    }
    /**
     * BasicStrategy & ClientPasswordStrategy
     *
     * These strategies are used to authenticate registered OAuth clients.  They are
     * employed to protect the `token` endpoint, which consumers use to obtain
     * access tokens.  The OAuth 2.0 specification suggests that clients use the
     * HTTP Basic scheme to authenticate.  Use of the client password strategy
     * allows clients to send the same credentials in the request body (as opposed
     * to the `Authorization` header).  While this approach is not recommended by
     * the specification, in practice it is quite common.
     */
    passport.use('clientBasic', new basic.BasicStrategy(
      function (clientId: any, clientSecret: any, done: any) {
        autentiqueClienteDireto(clientId, clientSecret, done);
      }
    ));
    passport.use('clientPassword', new password.Strategy(
      function (clientId: any, clientSecret: any, done: any) {
        autentiqueClienteDireto(clientId, clientSecret, done);
      }
    ));

    /**
     * BearerStrategy
     *
     * This strategy is used to authenticate either users or clients based on an access token
     * (aka a bearer token).  If a user, they must have previously authorized a client
     * application, which is issued an access token to make requests on behalf of
     * the authorizing user.
     */
    passport.use(new bearer.Strategy( {
        passReqToCallback: true
      }, (req: any, acesstoken: any, done: any) => {
        //console.log('BEARER TOKEN: ' + accessToken)
        new MapeadorDeBearerToken().selecioneSync({token: acesstoken}).then((token: any) => {
          if(!token)
            return done(null, false, {mensagem: 'Token inexistente'});
          new MapeadorDeClienteApi().selecioneSync(token.cliente.id).then((cliente: any) => {
            if (!cliente) {
              console.log('Cliente inexistente')
              return done(null, false, {mensagem: 'Cliente inexistente'});
            }
            // to keep this example simple, restricted scopes are not implemented,
            // and this is just for illustrative purposes
            let info = { scope: '*' }
            let ip: string = this.obtenhaIpDaRequest(req);
            if(ip) cliente.ip = ip
            done(null, cliente, info);
          });
        });
      }
    ))


  }

  async loginContato(dados: any, done: any){
    let contato = await new MapeadorDeContato().selecioneLogin({ login: dados.login})

    if (!contato) {
      console.log('Login nao existe: ' + dados.login)
      return done(null, "Login ou senha inválidos")
    }

    if (!contato.senha) {
      console.log('Senha nao definida: ' + dados.login)
      return done(null, "Login ou senha inválidos")
    }


    bcrypt.compare(dados.senha, contato.senha, (errSenha, isValid) => {
      if (!isValid || errSenha){
        console.log('senha invalida: ' + errSenha)
        return done(null, "Login ou senha inválidos")
      }


      return done(contato)
    })
  }
}
