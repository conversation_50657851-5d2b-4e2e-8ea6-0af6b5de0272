export class CrmEmpresa {
  id?: number;
  nome = '';
  cnpj?: string;
  telefone?: string;
  email?: string;
  endereco?: string;
  ativa = true;
  createdAt?: Date;
  updatedAt?: Date;

  constructor(nome?: string) {
    if (nome) {
      this.nome = nome.trim();
    }
  }


  // Validações
  isValid(): boolean {
    return this.nome && this.nome.trim().length > 0;
  }

  // Formatação de CNPJ
  formatarCnpj(): string {
    if (!this.cnpj) return '';
    return this.cnpj.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
  }

  // Formatação de telefone
  formatarTelefone(): string {
    if (!this.telefone) return '';
    const telefone = this.telefone.replace(/\D/g, '');
    if (telefone.length === 11) {
      return telefone.replace(/^(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
    } else if (telefone.length === 10) {
      return telefone.replace(/^(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
    }
    return this.telefone;
  }

  // Status da empresa
  getStatus(): string {
    return this.ativa ? 'Ativa' : 'Inativa';
  }

  // Resumo para exibição
  getResumo(): string {
    return `${this.nome}${this.cnpj ? ` (${this.formatarCnpj()})` : ''}`;
  }
}
