import { CrmEmpresa } from './CrmEmpresa';
import { LeadLink, TipoLinkLead } from './LeadLink';
import { CrmTelefoneLead, TipoTelefoneLead } from './CrmTelefoneLead';

export enum EtapaFunilLead {
  Prospecção = 'Prospecção',
  Qualificação = 'Qualificação',
  Objeção = 'Objeção',
  Fechamento = 'Fechamento',
  Ganho = 'Ganho',
  Perdido = 'Perdido'
}

export enum OrigemLead {
  Instagram = 'Instagram',
  SiteLandingPage = 'Site/Landing Page',
  WhatsappDireto = 'WhatsApp Direto',
  Indicacao = 'Indicação',
  EventoFeira = 'Evento/Feira',
  Outros = 'Outros'
}

export enum SegmentoLead {
  Restaurante = 'Restaurante',
  Pizzaria = 'Pizzaria',
  Lanchonete = 'Lanchonete',
  Hamburgueria = 'Hamburgueria',
  Confeitaria = 'Confeitaria/Doceria',
  Bar = 'Bar/Boteco',
  FoodTruck = 'Food Truck',
  Outros = 'Outros'
}

export interface InstagramData {
  bio?: string;
  followers?: number;
  following?: number;
  accountType?: 'Pessoal' | 'Business';
  businessCategory?: string;
  location?: string;
  website?: string;
}

export interface RapportGerado {
  tipoNegocio?: string;
  pontosDor?: string[]; // itens como "filas", "cardápio físico" etc.
  sugestoesAbordagem?: string[];
  pontosInteresse?: string[];
}

export default class Lead {
  id?: number;
  crmEmpresaId: number; // Vinculação obrigatória com CrmEmpresa
  nomeResponsavel: string;
  empresa: string;
  cidade?: string; // Cidade da empresa
  endereco?: string; // Endereço completo da empresa
  telefone: string;
  instagramHandle?: string;
  linkInsta?: string; // URL/link externo da bio do Instagram
  // Bio extraída diretamente do perfil do Instagram (campo duplicado para fácil acesso)
  bioInsta?: string;

  etapa: EtapaFunilLead = EtapaFunilLead.Prospecção;
  score: number = 0; // 0-100
  origem: OrigemLead = OrigemLead.Instagram;
  segmento?: SegmentoLead;

  // Datas
  dataCriacao: Date = new Date();
  dataUltimaInteracao?: Date;
  dataProximoFollowup?: Date;
  dataFechamento?: Date;

  // Dados de negócio
  valorPotencial?: number;
  vendedorId?: number;
  motivoPerda?: string;

  // Dados do Instagram (quando origem = Instagram)
  instagramData?: InstagramData;
  avatarUrl?: string; // URL da foto de perfil do Instagram

  // Website da empresa (compatibilidade com frontend)
  website?: string;

  // Dados gerados por IA
  rapport?: RapportGerado;
  relatorioIaJson?: string; // JSON com dados completos da análise de IA

  // Observações livres
  notas?: string;
  observacoes?: string; // Observações específicas sobre vendas/negociação

  // Links do lead
  links?: LeadLink[];

  // Telefones do lead
  telefones?: CrmTelefoneLead[];

  // Campos de auditoria
  createdAt?: Date;
  updatedAt?: Date;

  crmEmpresa?: CrmEmpresa;

  constructor(
    crmEmpresaId: number,
    nomeResponsavel: string,
    empresa: string,
    telefone: string,
    instagramHandle?: string,
    bioInsta?: string,
    origem: OrigemLead = OrigemLead.Instagram,
    endereco?: string
  ) {
    this.crmEmpresaId = crmEmpresaId;
    this.nomeResponsavel = nomeResponsavel?.trim();
    this.empresa = empresa?.trim();
    this.telefone = telefone?.replace(/\D/g, '');
    this.instagramHandle = instagramHandle?.replace('@', '').trim();
    if (bioInsta) this.bioInsta = bioInsta;
    if (endereco) this.endereco = endereco?.trim();
    this.origem = origem;
  }

  // Métodos de negócio
  avancarEtapa(): boolean {
    const ordem: EtapaFunilLead[] = [
      EtapaFunilLead.Prospecção,
      EtapaFunilLead.Qualificação,
      EtapaFunilLead.Objeção,
      EtapaFunilLead.Fechamento,
      EtapaFunilLead.Ganho
    ];
    const idx = ordem.indexOf(this.etapa);
    if (idx >= 0 && idx < ordem.length - 1) {
      this.etapa = ordem[idx + 1];
      this.dataUltimaInteracao = new Date();
      return true;
    }
    return false;
  }

  retrocederEtapa(): boolean {
    const ordem: EtapaFunilLead[] = [
      EtapaFunilLead.Prospecção,
      EtapaFunilLead.Qualificação,
      EtapaFunilLead.Objeção,
      EtapaFunilLead.Fechamento,
      EtapaFunilLead.Ganho
    ];
    const idx = ordem.indexOf(this.etapa);
    if (idx > 0) {
      this.etapa = ordem[idx - 1];
      this.dataUltimaInteracao = new Date();
      return true;
    }
    return false;
  }

  marcarComoGanho(valorFechamento?: number): void {
    this.etapa = EtapaFunilLead.Ganho;
    this.dataFechamento = new Date();
    this.dataUltimaInteracao = new Date();
    if (valorFechamento) {
      this.valorPotencial = valorFechamento;
    }
    this.score = 100;
  }

  marcarComoPerdido(motivo?: string): void {
    this.etapa = EtapaFunilLead.Perdido;
    this.dataFechamento = new Date();
    this.dataUltimaInteracao = new Date();
    this.motivoPerda = motivo;
    this.score = 0;
  }

  // Classificações
  getScoreClassificacao(): 'Alto' | 'Médio' | 'Baixo' | 'Sem' {
    if (this.score >= 80) return 'Alto';
    if (this.score >= 50) return 'Médio';
    if (this.score > 0) return 'Baixo';
    return 'Sem';
  }

  getCorScore(): string {
    if (this.score >= 80) return '#28a745'; // Verde
    if (this.score >= 50) return '#ffc107'; // Amarelo
    if (this.score > 0) return '#dc3545';   // Vermelho
    return '#6c757d'; // Cinza
  }

  getIconeEtapa(): string {
    const icones = {
      'Prospecção': 'fa-eye',
      'Qualificação': 'fa-search',
      'Objeção': 'fa-exclamation-triangle',
      'Fechamento': 'fa-handshake',
      'Ganho': 'fa-check-circle',
      'Perdido': 'fa-times-circle'
    };
    return icones[this.etapa] || 'fa-circle';
  }

  // Validações
  isValid(): boolean {
    return !!(this.crmEmpresaId &&
             this.nomeResponsavel?.trim() &&
             this.empresa?.trim() &&
             this.telefone?.trim());
  }

  isFechado(): boolean {
    return this.etapa === EtapaFunilLead.Ganho || this.etapa === EtapaFunilLead.Perdido;
  }

  isAtrasado(): boolean {
    if (!this.dataProximoFollowup) return false;
    return new Date() > this.dataProximoFollowup;
  }

  // Formatações
  formatarTelefone(): string {
    if (!this.telefone) return '';
    const telefone = this.telefone.replace(/\D/g, '');
    if (telefone.length === 11) {
      return telefone.replace(/^(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
    } else if (telefone.length === 10) {
      return telefone.replace(/^(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
    }
    return this.telefone;
  }

  formatarInstagram(): string {
    if (!this.instagramHandle) return '';
    return `@${this.instagramHandle}`;
  }

  getLinkBioInstagram(): string {
    return this.linkInsta || '';
  }

  getLinkPerfilInstagram(): string {
    if (this.instagramHandle) return `https://instagram.com/${this.instagramHandle}`;
    return '';
  }

  formatarValorPotencial(): string {
    if (!this.valorPotencial) return 'N/A';
    return this.valorPotencial.toLocaleString('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    });
  }

  // Resumo para listagens
  getResumo(): string {
    return `${this.nomeResponsavel} - ${this.empresa} (${this.etapa})`;
  }

  // Formatação de endereço
  getEnderecoFormatado(): string {
    if (!this.endereco) return '';
    return this.endereco;
  }

  // Endereço completo com cidade
  getEnderecoCompleto(): string {
    const partes = [];
    if (this.endereco) partes.push(this.endereco);
    if (this.cidade) partes.push(this.cidade);
    return partes.join(', ');
  }

  // Validação de endereço
  hasEndereco(): boolean {
    return !!(this.endereco?.trim());
  }

  // Métodos para extrair links específicos por tipo
  getLinkPorTipo(tipo: string): string | null {
    if (!this.links || this.links.length === 0) return null;
    const link = this.links.find(l => l.tipo === tipo && l.ativo);
    return link ? link.url : null;
  }

  // Obter objeto LeadLink por tipo
  getLeadLinkPorTipo(tipo: TipoLinkLead): LeadLink | undefined {
    return this.links?.find(link => link.tipo === tipo && link.ativo);
  }

  getLinkInstagram(): string | null {
    return this.getLinkPorTipo('Instagram') || this.linkInsta || this.getLinkPerfilInstagram();
  }

  getLinkSite(): string | null {
    return this.getLinkPorTipo('Site') || this.website || this.instagramData?.website;
  }

  getLinkConcorrente(): string | null {
    return this.getLinkPorTipo('Concorrente');
  }

  getLinkIfood(): string | null {
    return this.getLinkPorTipo('Ifood');
  }

  getLinkWhatsApp(): string | null {
    return this.getLinkPorTipo('WhatsApp');
  }

  getLinkLocalizacao(): string | null {
    return this.getLinkPorTipo('Localização');
  }

  // Obter todas as URLs dos links ativos para envio ao Bitrix
  getAllLinksUrls(): string[] {
    if (!this.links || this.links.length === 0) return [];
    
    return this.links
      .filter(link => link.ativo)
      .map(link => link.getUrlFormatada())
      .filter(url => url && url.trim() !== '');
  }

  // ===== MÉTODOS PARA GERENCIAR LINKS =====

  // Adicionar um novo link
  adicionarLink(tipo: TipoLinkLead, url: string, descricao?: string): LeadLink {
    if (!this.links) this.links = [];

    // Verificar se já existe um link do mesmo tipo
    const linkExistente = this.links.find(link => link.tipo === tipo);
    if (linkExistente) {
      linkExistente.url = url;
      linkExistente.descricao = descricao;
      linkExistente.ativo = true;
      return linkExistente;
    }

    const novoLink = new LeadLink(this.id || 0, tipo, url, descricao, this.links.length);
    this.links.push(novoLink);
    this.sincronizarLinkInsta();
    return novoLink;
  }

  // Remover link por tipo
  removerLink(tipo: TipoLinkLead): boolean {
    if (!this.links) return false;

    const index = this.links.findIndex(link => link.tipo === tipo);
    if (index >= 0) {
      this.links.splice(index, 1);
      this.sincronizarLinkInsta();
      return true;
    }
    return false;
  }



  // Obter todos os links ativos
  getLinksAtivos(): LeadLink[] {
    return this.links?.filter(link => link.ativo) || [];
  }

  // Obter links por categoria
  getLinksPorCategoria(): { [categoria: string]: LeadLink[] } {
    const links = this.getLinksAtivos();
    return {
      'Contato': links.filter(l => ['WhatsApp', 'Instagram'].includes(l.tipo)),
      'Negócio': links.filter(l => ['Ifood', 'Site do Cardápio', 'Reservas'].includes(l.tipo)),
      'Informações': links.filter(l => ['Site', 'Localização'].includes(l.tipo))
    };
  }

  // Sincronizar campo linkInsta com array de links (compatibilidade)
  sincronizarLinkInsta(): void {
    const linkInstagram = this.getLeadLinkPorTipo(TipoLinkLead.Instagram);
    this.linkInsta = linkInstagram?.url || '';
  }

  // Inicializar links a partir do campo linkInsta existente
  inicializarLinksDoLinkInsta(): void {
    if (this.linkInsta && !this.getLeadLinkPorTipo(TipoLinkLead.Instagram)) {
      this.adicionarLink(TipoLinkLead.Instagram, this.linkInsta, 'Link da bio do Instagram');
    }
  }

  // Validar todos os links
  validarLinks(): { validos: LeadLink[], invalidos: LeadLink[] } {
    const links = this.getLinksAtivos();
    return {
      validos: links.filter(link => link.isUrlValida()),
      invalidos: links.filter(link => !link.isUrlValida())
    };
  }

  // Obter link do WhatsApp formatado
  getWhatsAppUrl(): string {
    const linkWhatsApp = this.getLeadLinkPorTipo(TipoLinkLead.Whatsapp);
    if (linkWhatsApp) return linkWhatsApp.getUrlFormatada();

    // Fallback para o telefone
    if (this.telefone) {
      const numeroLimpo = this.telefone.replace(/\D/g, '');
      return `https://wa.me/55${numeroLimpo}`;
    }
    return '';
  }

  // Obter link de localização
  getLocalizacaoUrl(): string {
    const linkLocalizacao = this.getLeadLinkPorTipo(TipoLinkLead.Localizacao);
    return linkLocalizacao?.getUrlFormatada() || '';
  }

  // ===== MÉTODOS PARA GERENCIAR TELEFONES =====

  // Adicionar um novo telefone
  adicionarTelefone(tipo: TipoTelefoneLead, numero: string, descricao?: string): CrmTelefoneLead {
    if (!this.telefones) this.telefones = [];

    // Limpar e validar número
    const numeroLimpo = CrmTelefoneLead.limparNumero(numero);
    if (!numeroLimpo) {
      throw new Error('Número de telefone inválido');
    }

    // Verificar se já existe um telefone do mesmo tipo
    const telefoneExistente = this.telefones.find(tel => tel.tipo === tipo);
    if (telefoneExistente) {
      telefoneExistente.numero = numeroLimpo;
      telefoneExistente.descricao = descricao;
      telefoneExistente.ativo = true;
      return telefoneExistente;
    }

    const novoTelefone = new CrmTelefoneLead(this.id || 0, tipo, numeroLimpo, descricao, this.telefones.length);
    this.telefones.push(novoTelefone);
    this.sincronizarTelefonePrincipal();
    return novoTelefone;
  }

  // Remover telefone por tipo
  removerTelefone(tipo: TipoTelefoneLead): boolean {
    if (!this.telefones) return false;

    const index = this.telefones.findIndex(tel => tel.tipo === tipo);
    if (index >= 0) {
      this.telefones.splice(index, 1);
      this.sincronizarTelefonePrincipal();
      return true;
    }
    return false;
  }

  // Obter telefone por tipo
  getTelefonePorTipo(tipo: TipoTelefoneLead): CrmTelefoneLead | undefined {
    return this.telefones?.find(telefone => telefone.tipo === tipo && telefone.ativo);
  }

  // Obter todos os telefones ativos
  getTelefonesAtivos(): CrmTelefoneLead[] {
    return this.telefones?.filter(telefone => telefone.ativo) || [];
  }

  // Obter telefone principal (WhatsApp > Celular > primeiro ativo)
  getTelefonePrincipal(): CrmTelefoneLead | undefined {
    const telefones = this.getTelefonesAtivos();
    if (telefones.length === 0) return undefined;

    // Prioridade: WhatsApp > Celular > outros
    const whatsapp = telefones.find(t => t.tipo === TipoTelefoneLead.WhatsApp);
    if (whatsapp) return whatsapp;

    const celular = telefones.find(t => t.tipo === TipoTelefoneLead.Celular);
    if (celular) return celular;

    return telefones[0];
  }

  // Obter telefone do WhatsApp
  getTelefoneWhatsApp(): CrmTelefoneLead | undefined {
    return this.getTelefonePorTipo(TipoTelefoneLead.WhatsApp);
  }

  // Obter telefone fixo
  getTelefoneFixo(): CrmTelefoneLead | undefined {
    return this.getTelefonePorTipo(TipoTelefoneLead.TelefoneFixo);
  }

  // Obter telefone celular
  getTelefoneCelular(): CrmTelefoneLead | undefined {
    return this.getTelefonePorTipo(TipoTelefoneLead.Celular);
  }

  // Obter telefone comercial
  getTelefoneComercial(): CrmTelefoneLead | undefined {
    return this.getTelefonePorTipo(TipoTelefoneLead.Comercial);
  }

  // Obter URL do WhatsApp (primeiro telefone disponível)
  getWhatsAppUrlTelefone(): string {
    const whatsapp = this.getTelefoneWhatsApp();
    if (whatsapp) return whatsapp.getWhatsAppUrl();

    const celular = this.getTelefoneCelular();
    if (celular) return celular.getWhatsAppUrl();

    // Fallback para o campo telefone original
    if (this.telefone) {
      const numeroLimpo = this.telefone.replace(/\D/g, '');
      return `https://wa.me/55${numeroLimpo}`;
    }

    return '';
  }

  // Sincronizar campo telefone principal com array de telefones (compatibilidade)
  sincronizarTelefonePrincipal(): void {
    const telefonePrincipal = this.getTelefonePrincipal();
    this.telefone = telefonePrincipal?.numero || '';
  }

  // Inicializar telefones a partir do campo telefone existente
  inicializarTelefonesDoTelefone(): void {
    if (this.telefone && !this.getTelefonePrincipal()) {
      const numeroLimpo = CrmTelefoneLead.limparNumero(this.telefone);
      if (numeroLimpo) {
        const tipo = CrmTelefoneLead.detectarTipo(numeroLimpo);
        this.adicionarTelefone(tipo, numeroLimpo, 'Telefone principal');
      }
    }
  }

  // Validar todos os telefones
  validarTelefones(): { validos: CrmTelefoneLead[], invalidos: CrmTelefoneLead[] } {
    const telefones = this.getTelefonesAtivos();
    return {
      validos: telefones.filter(telefone => telefone.isValid()),
      invalidos: telefones.filter(telefone => !telefone.isValid())
    };
  }

  // Obter todos os números de telefone formatados
  getTelefonesFormatados(): string[] {
    return this.getTelefonesAtivos().map(telefone => telefone.getNumeroFormatado());
  }

  // Obter telefones por categoria
  getTelefonesPorCategoria(): { [categoria: string]: CrmTelefoneLead[] } {
    const telefones = this.getTelefonesAtivos();
    return {
      'Contato Direto': telefones.filter(t => [TipoTelefoneLead.WhatsApp, TipoTelefoneLead.Celular].includes(t.tipo)),
      'Comercial': telefones.filter(t => [TipoTelefoneLead.TelefoneFixo, TipoTelefoneLead.Comercial].includes(t.tipo)),
      'Emergência': telefones.filter(t => t.tipo === TipoTelefoneLead.Emergencia)
    };
  }

  // Contar telefones ativos
  contarTelefones(): number {
    return this.getTelefonesAtivos().length;
  }

  // Adicionar telefones a partir de array de dados
  adicionarTelefonesFromArray(telefonesData: any[]): void {
    if (!telefonesData || !Array.isArray(telefonesData)) return;

    telefonesData.forEach((telefoneData, index) => {
      const tipo = telefoneData.tipo as TipoTelefoneLead;
      const numero = telefoneData.numero;
      const descricao = telefoneData.descricao;

      if (tipo && numero) {
        this.adicionarTelefone(tipo, numero, descricao);
      }
    });
  }
}
