export enum TipoLinkLead {
  Ifood = 'Ifood',
  SiteCardapio = 'Site do Cardápio',
  Concorrente = 'Concorrente',
  Reservas = 'Reservas',
  Whatsapp = 'WhatsApp',
  Localizacao = 'Localização',
  Site = 'Site',
  Instagram = 'Instagram'
}

export class LeadLink {
  id?: number;
  crmLeadId: number;
  tipo: TipoLinkLead;
  url: string;
  descricao?: string;
  ativo: boolean = true;
  ordem: number = 0;
  createdAt?: Date;
  updatedAt?: Date;

  constructor(
    crmLeadId: number,
    tipo: TipoLinkLead,
    url: string,
    descricao?: string,
    ordem: number = 0
  ) {
    this.crmLeadId = crmLeadId;
    this.tipo = tipo;
    this.url = url;
    this.descricao = descricao;
    this.ordem = ordem;
  }

  // Validações
  isValid(): boolean {
    return !!(this.crmLeadId && this.tipo && this.url?.trim());
  }

  // Formatações específicas por tipo
  getUrlFormatada(): string {
    if (!this.url) return '';

    switch (this.tipo) {
      case TipoLinkLead.Whatsapp:
        return this.formatarWhatsApp();
      case TipoLinkLead.Localizacao:
        return this.formatarLocalizacao();
      default:
        return this.url;
    }
  }

  private formatarWhatsApp(): string {
    // Se já é um link do WhatsApp, retorna como está
    if (this.url.includes('wa.me') || this.url.includes('whatsapp.com')) {
      return this.url;
    }

    // Se é apenas um número, formata para link do WhatsApp
    const numeroLimpo = this.url.replace(/\D/g, '');
    if (numeroLimpo.length >= 10) {
      return `https://wa.me/55${numeroLimpo}`;
    }

    return this.url;
  }

  private formatarLocalizacao(): string {
    // Se já é um link do Google Maps, retorna como está
    if (this.url.includes('maps.google.com') || this.url.includes('goo.gl/maps')) {
      return this.url;
    }

    // Se parece ser coordenadas ou endereço, formata para Google Maps
    if (this.url.includes(',') || this.url.includes('rua') || this.url.includes('av')) {
      return `https://maps.google.com/maps?q=${encodeURIComponent(this.url)}`;
    }

    return this.url;
  }

  // Ícone baseado no tipo
  getIcone(): string {
    const icones = {
      'Ifood': 'fa-utensils',
      'Site do Cardápio': 'fa-list-alt',
      'Concorrente': 'fa-exclamation-triangle',
      'Reservas': 'fa-calendar-check',
      'WhatsApp': 'fa-whatsapp',
      'Localização': 'fa-map-marker-alt',
      'Site': 'fa-globe',
      'Instagram': 'fa-instagram'
    };
    return icones[this.tipo] || 'fa-link';
  }

  // Cor baseada no tipo
  getCor(): string {
    const cores = {
      'Ifood': '#ea1d2c',
      'Site do Cardápio': '#007bff',
      'Concorrente': '#ff6b35',
      'Reservas': '#28a745',
      'WhatsApp': '#25d366',
      'Localização': '#dc3545',
      'Site': '#6c757d',
      'Instagram': '#e4405f'
    };
    return cores[this.tipo] || '#6c757d';
  }

  // Texto de exibição
  getTextoExibicao(): string {
    if (this.descricao) return this.descricao;

    switch (this.tipo) {
      case TipoLinkLead.Whatsapp:
        return 'WhatsApp';
      case TipoLinkLead.Localizacao:
        return 'Ver no Mapa';
      case TipoLinkLead.Ifood:
        return 'Cardápio iFood';
      case TipoLinkLead.SiteCardapio:
        return 'Cardápio Online';
      case TipoLinkLead.Concorrente:
        return 'Sistema Concorrente';
      case TipoLinkLead.Reservas:
        return 'Fazer Reserva';
      default:
        return this.tipo;
    }
  }

  // Validação de URL
  isUrlValida(): boolean {
    try {
      new URL(this.getUrlFormatada());
      return true;
    } catch {
      return false;
    }
  }
}
