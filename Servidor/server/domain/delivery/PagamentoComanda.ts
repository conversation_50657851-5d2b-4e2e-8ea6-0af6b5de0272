import {EnumFormaPagamento} from "../../lib/emun/EnumFormaPagamento";
import {EnumStatusPagamento, StatusPagamentoDeParaPagseguro} from "../../lib/emun/EnumStatusPagamento";
import {MapeadorDePagamentoComanda} from "../../mapeadores/MapeadorDePagamentoComanda";
import {integer} from "twilio/lib/base/deserialize";
import {PagamentoGenerico} from "./PagamentoGenerico";

export class PagamentoComanda extends PagamentoGenerico {
  descricaoFormaDePagamento: string;
  comanda: any;
  idLink: string;
  link: string;
  codigoTransacao: string;

  constructor(comanda: any, valor: number, trocoPara: any,
              formaDePagamento: any, status: any = EnumStatusPagamento.Gerado, linkPagamento: string = null) {
    super()
    this.comanda = comanda
    this.valor = valor;
    this.formaDePagamento = formaDePagamento;
    this.status = status;
    this.trocoPara = trocoPara || 0;
  }

  mapeador(): any {
    return new MapeadorDePagamentoComanda();
  }

  async valide(): Promise<any> {
    return new Promise<any>( (resolve, reject) => {
      if(!this.comanda) return resolve('Comanda do pagamento não informada')
      if(!this.formaDePagamento || !this.formaDePagamento.id) return resolve('Forma de pagamento não informado')
      if( !( this.valor >= 0 )) return resolve('Valor do pagamento em ' + this.formaDePagamento.nome  + ' não informado')

     // if(this.trocoPara == null  ) return resolve('Troco   não informado')

      if( this.trocoPara  > 0 && this.formaDePagamento.nome === EnumFormaPagamento.Dinheiro ) {
        if(this.trocoPara  < this.valor)
          return resolve('Ops! valor para o troco tem que ser maior que R$' + this.valor)
      }

      resolve('');
    });
  }


  // tslint:disable-next-line:member-ordering
  static get(query: any){
    return new MapeadorDePagamentoComanda().selecioneSync( query);
  }

  async atualizeComDadosLinkPagamento(dadosLinkPagamento: any) {
    this.idLink = dadosLinkPagamento.id;
    this.link = dadosLinkPagamento.shortUrl;
    await new MapeadorDePagamentoComanda().atualizeDados(this)
  }

  async atualizeCodigoPagseguro(transacao: any) {
    this.codigoTransacao = transacao.codigo;
    this.status = StatusPagamentoDeParaPagseguro.get(integer(transacao.status));

    return await new MapeadorDePagamentoComanda().atualizeDados(this)
  }


  obtenhaMensagemStatus() {
    switch (this.status) {
      case EnumStatusPagamento.Aprovado:
        return 'Seu pagamento foi aprovado.';
      case EnumStatusPagamento.Cancelado:
        return 'Seu pagamento foi cancelado. Tente um novo pagamento.';
      case EnumStatusPagamento.EmAnalise:
        return 'Seu pagamento está em análise. Você será notificado quando ele for aprovado ou reprovado.';
      case EnumStatusPagamento.Negado:
      case EnumStatusPagamento.Cancelado:
        return 'Seu pagamento foi negado. Tente um novo pagamento.';
      case EnumStatusPagamento.Reembolsado:
        return 'Seu pagamento foi reembolsado. Tente um novo pagamento.';
      case EnumStatusPagamento.Suspenso:
        return 'Seu pagamento foi suspenso. Tente um novo pagamento.';
      default:
        return 'Seu pagamento está sendo processado. Aguarde o resultado.';
    }
  }

}
