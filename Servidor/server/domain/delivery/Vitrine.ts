import {MapeadorDeVitrine} from "../../mapeadores/MapeadorDeVitrine";
import {ObjetoPersistente} from "../ObjetoPersistente";
import {ProdutoNaVitrine} from "./ProdutoNaVitrine";


export class Vitrine extends ObjetoPersistente{
  id: number
  nome: string
  ordem: number;
  disponivel: boolean;
  public empresa: any
  public produtosNaVitrine: Array<ProdutoNaVitrine> = []
  constructor( ) { super()   }

  // tslint:disable-next-line:member-ordering
  static get(query: any = {}){
    return new MapeadorDeVitrine().selecioneSync(query)
  }

  mapeador(): any {
    return new MapeadorDeVitrine();
  }

  async ative(){
    this.disponivel = true;
    return this.mapeador().atualizeDisponivel(this)
  }

  async desative(){
    this.disponivel = false;
    return this.mapeador().atualizeDisponivel(this)
  }


}
