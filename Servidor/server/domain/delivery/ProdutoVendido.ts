
import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorDeProdutoVendido} from "../../mapeadores/MapeadorDeProdutoVendido";
import {MapeadorBasico} from "../../mapeadores/MapeadorBasico";
import {ItemPedido} from "./ItemPedido";
import {IOpcaoDoAdicional, ItemPedidoIntegradoUtils} from "../../lib/integracao/ItemPedidoIntegradoUtils";

export enum EnumTipoProdutoVendido{
   Produto = 'produto',
   OpcaoAdicional  = 'opcao'
}

export class ProdutoVendido extends ObjetoPersistente{
  produtoId: number;
  opcaoId: number;
  adicionalNome: string;
  constructor(public pedido: any, public itemPedido: any, public  nome: string,   public codigo: string,
              public valor: number, public   quantidade: number, public total: number,
              public tipo: EnumTipoProdutoVendido, public indice: number) {
    super()
  }

  static async recalculeProdutosDaVenda (pedido: any){
    await new MapeadorDeProdutoVendido().removaAsync({idPedido: pedido.id, idEmpresa: pedido.empresa.id});

    await ProdutoVendido.gereProdutosDaVenda(pedido);

  }

  static async recalculeProdutosDoItemDaVenda (novoItem: any , pedido: any, itemAntigo: any, index: number){
    await new MapeadorDeProdutoVendido().removaAsync({idItemPedido: itemAntigo.id, idPedido: pedido.id, idEmpresa: pedido.empresa.id});

    if(novoItem){
      let produtosVenda: Array<ProdutoVendido> = [];

      ProdutoVendido.gereProdutosDaVendaDoItem(pedido, novoItem, index, produtosVenda);

      for(let i = 0; i < produtosVenda.length; i++)
        await produtosVenda[i].salve(true);
    }

  }

  static gereProdutosDaVendaDoItem(pedido: any, item: any, i: number, produtosVenda: any = []){
    let dtoItemPedido: ItemPedidoIntegradoUtils = new ItemPedidoIntegradoUtils(item, false, false)

    let produto = item.produto;
    let valor  = produto.obtenhaPreco(item.produtoTamanho, null);

    if(!dtoItemPedido.temSabores){
      let produtoVendido = new ProdutoVendido(pedido, item, produto.nome, produto.codigoPdv, valor, item.qtde, item.total,
        EnumTipoProdutoVendido.Produto, i);

      produtoVendido.produtoId = produto.id;

      produtosVenda.push(produtoVendido);
    }

    let opcoes: any = dtoItemPedido.adicionais;

    if(opcoes && opcoes.length){
      for(let j = 0; j < opcoes.length; j++){
        let opcao: IOpcaoDoAdicional = opcoes[j];

        let tipo = opcao.saborPizza ? EnumTipoProdutoVendido.Produto : EnumTipoProdutoVendido.OpcaoAdicional;

        let opcaoVendida =  new ProdutoVendido(pedido, item, opcao.descricao, opcao.codigoPdv, opcao.valor,
          opcao.qtdeSomada, opcao.totalSomado, tipo, i);

        opcaoVendida.produtoId = produto.id;
        opcaoVendida.opcaoId = opcao.opcaoId;
        opcaoVendida.adicionalNome = opcao.adicionalNome;

        produtosVenda.push(opcaoVendida);
      }
    }
  }

  static async gereProdutosDaVenda(pedido: any){
    let produtosVenda: Array<ProdutoVendido> = [];

    for(let i = 0; i < pedido.itens.length; i++){
      let item: ItemPedido = pedido.itens[i];

      ProdutoVendido.gereProdutosDaVendaDoItem(pedido,  item, i, produtosVenda);
    }

    let opcoesPedido: Array<IOpcaoDoAdicional> =
      ItemPedidoIntegradoUtils.obtenhaOpcoesAdicionaisDoPedido(pedido, false);

    let indice =  pedido.itens.length;

    opcoesPedido.forEach((opcao: IOpcaoDoAdicional) => {
      let opcaoVendida =  new ProdutoVendido(pedido, null, opcao.descricao, opcao.codigoPdv, opcao.valor,
        opcao.qtdeSomada, opcao.totalSomado, EnumTipoProdutoVendido.OpcaoAdicional, indice);

      opcaoVendida.opcaoId = opcao.opcaoId;
      opcaoVendida.adicionalNome = opcao.adicionalNome

      produtosVenda.push(opcaoVendida)
    })

    for(let i = 0; i < produtosVenda.length; i++)
      await produtosVenda[i].salve(true);

  }

  mapeador(): MapeadorBasico {
    return new MapeadorDeProdutoVendido();
  }
}
