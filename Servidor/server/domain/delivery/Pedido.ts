import {EnumStatusPedido} from "../../lib/emun/EnumStatusPedido";
import {ItemPedido} from "./ItemPedido";
import {Empresa} from "../Empresa";
import {PagamentoPedido} from "./PagamentoPedido";
import {Endereco} from "./Endereco";
import {Contato} from "../Contato";
import {MapeadorDePedido} from "../../mapeadores/MapeadorDePedido";
import {Produto} from "../Produto";
import {MapeadorBasico} from "../../mapeadores/MapeadorBasico";
import {Usuario} from "../Usuario";
import {FormaDeEntrega} from "./FormaDeEntrega";
import {EnumOrigemPedido} from "../../lib/emun/EnumOrigemPedido";
import {BrindeResgatado} from "../BrindeResgatado";
import {<PERSON>rinde} from "../obj/Brinde";
import {Mesa} from "../Mesa";
import {IntegracaoPedidoFidelidade} from "../IntegracaoPedidoFidelidade";
import {PontuacaoRegistrada} from "../PontuacaoRegistrada";
import {Cartao} from "../Cartao";
import {ProdutoTamanho} from "../templates/ProdutoTamanho";
import {Comanda} from "../comandas/Comanda";
import {PedidoGenerico} from "./PedidoGenerico";
import {Resposta} from "../../utils/Resposta";
import {PromocaoAplicada} from "../PromocaoAplicada";
import {Promocao} from "../Promocao";
import {EnumTipoDeVenda} from "../../lib/emun/EnumTipoDeVenda";
import {Cupom} from "../faturamento/Cupom";
import {MapeadorDeCupom} from "../../mapeadores/MapeadorDeCupom";
import {MapeadorDePagamentoPedido} from "../../mapeadores/MapeadorDePagamentoPedido";
import {HistoricoPedido} from "./HistoricoPedido";

// @ts-ignore
import uuid = require("uuid");
// @ts-ignore
import _ = require("underscore");

export class Pedido extends  PedidoGenerico {
  itens: Array<ItemPedido> = [];
  trocas: Array<BrindeResgatado> = [];

  mesa: Mesa;
  garcom: Usuario;
  comanda: Comanda;
  multipedido: any;
  tipo: string
  tempoPreparacao: number;
  taxaEntregaCalculadaId: number;

  novaComanda: boolean;

  constructor(empresa: Empresa, public operador: Usuario,   contato: Contato, endereco: Endereco,
               formaDeEntrega: FormaDeEntrega,  taxaEntrega: number,   desconto: number,
             public pagamentos: Array<PagamentoPedido>,   observacoes: string,   origem: EnumOrigemPedido,
             public id: number = null,   codigo: string = null  ) {
  super();
  this.status = EnumStatusPedido.Novo;
  this.empresa = empresa;
  this.endereco = endereco;
  this.contato = contato;
  this.observacoes = observacoes;
  this.origem = origem;
  this.formaDeEntrega = origem !== EnumOrigemPedido.Balcao ? formaDeEntrega : null;
  this.codigo = codigo;
  this.valor = 0;
  this.taxaEntrega = taxaEntrega >= 0 ? taxaEntrega : 0;
  this.desconto = desconto || 0;
  this.horario = new Date();
  this.horarioAtualizacao = new Date();
  this.guid = uuid();
  if(this.feitoNoIfood()){
    this.aceito = empresa && empresa.aceitarPedidoAutomatico;
  } else {
    this.aceito = empresa && empresa.aceitarPedidoAutomatico ? true : this.feitoNoIframe();
  }

  this.descontoTaxaEntrega = 0;
  this.descontoFormaDePagamento = 0;
  this.impresso = false;
  this.promocoesAplicadas = []
  this.pagamentos = [];
  this.tipo = 'pedido';

  if(formaDeEntrega && (formaDeEntrega as any).comerNoLocal)
    this.comerNoLocal = true;

  }

  static async obtenhaUltimoPedido(empresa: Empresa, contato: Contato): Promise<Pedido> {
    const mapeadorDePedido = new MapeadorDePedido();

    const query = {
      contato: contato,
      inicio: 0,
      total: 1
    };

    const pedidos = await mapeadorDePedido.listeUltimos(query);

    if( pedidos.length === 0 ) {
      return null;
    }

    const pedido: Pedido = pedidos[0];
    return pedido;
  }


  obtenhaItensPedido(): Array<ItemPedido>{
    return this.itens;
  }

  mapeador(): MapeadorBasico {
    return new MapeadorDePedido();
  }


  async valide(): Promise<any> {
    return new Promise<any>( (resolve, reject) => {
      if(!this.itens.length) return resolve('Nenhum item adicionado ao pedido')
      if(this.desconto < 0) return resolve('Valor do desconto inválido')
      if(this.descontoTaxaEntrega < 0) return resolve('Valor do desconto da taxa de entrega inválido')
      if(this.valor < 0){
        let erro = 'Valor do pedido inválido'
        console.log(erro)
        console.log(this.valor);
        console.log(this.taxaEntrega);
        this.itens.forEach((item: any) => {
          console.log(item)
        })
        return resolve(erro)
      }

      resolve('');
    })
  }

  adicioneProduto(produto: Produto, qtde: number, observacao: string,
                  adicionais: any,
                  tamanho: ProdutoTamanho, sabores: Array<any>){
    produto.carreguePrecoNaEmpresa();
    let valor: any = produto.obtenhaPrecoUnidade(qtde, tamanho, sabores);
    let item = new ItemPedido(produto, valor,  qtde, observacao, 0, tamanho )

    item.setAdicionais(adicionais, sabores );
    item.calculeTotal();

    if(item.total <= 0 && !produto.brinde() && this.empresa.cardapio && !this.empresa.cardapio.exibirProdutosValorZeradoMesa)
      throw Error (String(`Produto "${item.produto.nome}" no valor de R$${item.total} não pode ser adicionado ao pedido`));

    item.descricao = item.obtenhaDescricaoProduto();

    this.itens.push(item)

    return item;

  }

  async apliqueCupom(codigo: string, validarRestricoesContato = true) {
    let cupom: Cupom;

    let cupomDaRede = this.empresa.fazParteDaRedeChinaInBox() ;

    if(cupomDaRede)
      cupom =  await Cupom.get({ codigo: codigo , redechina: true});

    if(!cupom){
      if(this.fazParteMultipedido()){
        cupom = await new MapeadorDeCupom().selecioneDaEmpresa(this.empresa.id, codigo)
      } else {
        cupom =  await Cupom.get({ codigo: codigo });
      }
    }

    if(!cupom) return Resposta.erro('Cupom não encontrado');

    let erro: string = await cupom.valideUso(this, validarRestricoesContato);

    if(erro) return Resposta.erro(erro) ;

    if(cupom.empresa.id !== this.empresa.id && !cupomDaRede)
      return Resposta.erro('Cupom inválido para empresa: ' + this.empresa.id)

    let descontoCupom = this.apliqueDescontoCupom(cupom);

    return Resposta.sucesso(descontoCupom)
  }

  adicioneBrinde(cartao: any, brinde: Brinde, qtde: number, operador: any  ) {
   for(let i = 1; i <= qtde; i++)
     this.trocas.push(new BrindeResgatado(operador, cartao, brinde, brinde.valorEmPontos, null))
  }

  adicioneBrindeFidelidade(cartao: any, produto: any, qtde: number  ) {
    for(let i = 1; i <= qtde; i++)
       this.trocas.push(new BrindeResgatado(null, cartao, null, produto.valorResgate, null, null , produto))
  }
  // tslint:disable-next-line:member-ordering


  static async get(query: any = {}){
   return new MapeadorDePedido().selecioneSync(query)
  }

  obtenhaValorDosPagmentos() {
    let valor = this.pagamentos.reduce(
      ( total: number,  pagamento: any) =>  (total + (pagamento.removido || pagamento.foiPorResgate() ? 0 : pagamento.valor)) , 0);

    return Number(valor.toFixed(2))
  }


  calculeValorSemDescontos() {
    this.valor = this.obtenhaTotalItens();

    const valorAdicionais = this.obtenhaValorAdicionais();

    this.valor += valorAdicionais;
    this.valor = Number(this.valor.toFixed(2));
  }


  calculeValor() {
    this.valor = this.obtenhaTotalItens();

    if(this.desconto > 0) //TODO verificar esse código de desconto
      this.valor -= this.desconto;

    const valorAdicionais = this.obtenhaValorAdicionais();

    this.valor += valorAdicionais;
    this.valor = Number(this.valor.toFixed(2));

    this.descontoFormaDePagamento = this.obtenhaDescontoFormaPagamento()

    if(this.descontoFormaDePagamento > 0)
      this.valor -= this.descontoFormaDePagamento;

    this.taxaFormaDePagamento = 0
    if(this.pagamentos){
      let pagamentos = this.obtenhaPagamentosComValor();
      let pagamentoUnico = pagamentos.length === 1;

      pagamentos.forEach((pagamento: any) => {
        if (pagamento.formaDePagamento.taxaCobranca) {
          if(pagamentoUnico)
            pagamento.taxa = pagamento.formaDePagamento.taxaCobranca.calcule(this.obtenhaTotalPagar());

          this.taxaFormaDePagamento += pagamento.taxa;
        }
      })
    }
  }
  public obtenhaDescontoFormaPagamento() {
    if(!this.pagamentos) return 0;

    let valorTotalSemTaxas =  this.valor;
    let valorCashback  = this.obtenhaCashback();

    if(valorCashback) valorTotalSemTaxas -= valorCashback;

    let pagamentos = this.obtenhaPagamentosComValor();
    let pagamentoUnico = pagamentos.length === 1;

    if(pagamentoUnico) {
      let pagamento = pagamentos[0]

      if(!pagamento.formaDePagamento.possuiDesconto) return 0;

      return Number(( valorTotalSemTaxas * pagamento.formaDePagamento.desconto / 100).toFixed(2))
    } else {
      let totalDescontos = 0;

      //todo: fazer desconto para multipagamentos pendente
      /*this.pagamentos.forEach((pagamento: PagamentoPedido) => {
        if(pagamento.formaDePagamento.possuiDesconto) {
          let pagamentoMaximoCalculo = Math.min(pagamento.valor , valorTotalSemTaxas);

          let desconto  = Number(( pagamentoMaximoCalculo * (pagamento.formaDePagamento.desconto / 100)).toFixed(2));

          totalDescontos += desconto;
          pagamento.valor -= desconto;
        }
      })*/

      return totalDescontos;
    }
  }

  obtenhaTotalItens(){
    let total = 0

    for(let item of this.itens)
      total += item.total;

    return total;
  }

  obtenhaQtdeItens(){
    let qtde = 0

    for(let item of this.itens)
      qtde += item.qtde;

    return qtde;
  }

  obtenhaTotalDesconto(){
    return this.desconto + this.descontoTaxaEntrega + this.descontoFormaDePagamento;
  }



  obtenhaValorProdutosAplicavelAoCupom(cupom: any, valorUnitario = false): number {
    let itensProdutoQualificaveis: Array<any> = this.itens.filter( (item: any) => !item.produto.naoAceitaCupom);

    if(cupom.produtos && cupom.produtos.length && !cupom.brindeResgate){
      itensProdutoQualificaveis = this.itens.filter( (item: ItemPedido) =>  item.temAlgumProduto(cupom.produtos));

    } else  if(cupom.produtosTemplateTamanho && cupom.produtosTemplateTamanho.length){
      itensProdutoQualificaveis = itensProdutoQualificaveis.filter( (item: ItemPedido) => {
        return item.produtoEstaNoTamanho(cupom.produtosTemplateTamanho)
      });
    } else   if(cupom.categorias && cupom.categorias.length)
      itensProdutoQualificaveis =
        itensProdutoQualificaveis.filter( (item: ItemPedido) => item.produtoEstaNaCategoria(cupom.categorias));

    itensProdutoQualificaveis = itensProdutoQualificaveis.filter((itemDoPedido: any) => {
       return  !itemDoPedido.sabores.find((item: any) => item.produto.naoAceitaCupom)
    })



    let percentualMaximo = cupom.percentualMaximoDescontoProduto

    if(percentualMaximo)
      itensProdutoQualificaveis  = itensProdutoQualificaveis.filter(
        (item: any) => !item.produto.percentualDesconto || item.produto.percentualDesconto <= percentualMaximo);

    if(this.promocoesAplicadas)
      for(let promocaoAplicada of this.promocoesAplicadas) {
        let regra: any
        for(regra of promocaoAplicada.promocao.regras)
          if(regra.produto)
            itensProdutoQualificaveis = itensProdutoQualificaveis.filter((item: any) => item.produto.id !== regra.produto.id)
      }

    if(valorUnitario){
      //valor veferente apenas 1 item do carrinho
      return itensProdutoQualificaveis.length ? itensProdutoQualificaveis[0].obtenhaValorUnitario() : 0;
    } else {
      return itensProdutoQualificaveis.reduce( (sum: number, item) => sum +  item.total, 0); //
    }
  }

  obtenhaTotalParaCalculoMinimoCupom(cupom: any): number{
    if(cupom.apenasValoresProdutosComporCarrinho())
      return this.obtenhaValorProdutosAplicavelAoCupom(cupom);

    let itensProdutoQualificaveis: Array<any> = this.itens.filter( (item: any) => !item.produto.naoAceitaCupom);

    itensProdutoQualificaveis = itensProdutoQualificaveis.filter((itemDoPedido: any) => {
      return  !itemDoPedido.sabores.find((item: any) => item.produto.naoAceitaCupom)
    })

    if(cupom.produtoNaoComporMinimo){
      if(cupom.produtos && cupom.produtos.length){
        itensProdutoQualificaveis = itensProdutoQualificaveis.filter( (item: any) => !item.temAlgumProduto(cupom.produtos));
      } else    if(cupom.produtosTemplateTamanho && cupom.produtosTemplateTamanho.length){
        itensProdutoQualificaveis = itensProdutoQualificaveis.filter( (item: any) => {
          return !item.produtoEstaNoTamanho(cupom.produtosTemplateTamanho)
        });

        if(cupom.minimoApenasMesmoTamanho){
          itensProdutoQualificaveis = itensProdutoQualificaveis.filter( (item: any) => {
            return item.produtoTamanho
          });

          let mapaTamanhos = _.groupBy(itensProdutoQualificaveis, (item: any) => item.produtoTamanho.template.id);

          let maiorValorTamanho = 0;

          Object.keys(mapaTamanhos).forEach((tamanho: any) => {
            let totalTamanho =  mapaTamanhos[tamanho].reduce( (sum: number, item) => sum + item.obtenhaValorTamanho(), 0); //

            if(totalTamanho > maiorValorTamanho) maiorValorTamanho = totalTamanho;
          })

          return maiorValorTamanho;
        }
      } else if(cupom.categorias && cupom.categorias.length){
          itensProdutoQualificaveis =
            itensProdutoQualificaveis.filter( (item: ItemPedido) => !item.produtoEstaNaCategoria(cupom.categorias));
      }
    }

    return itensProdutoQualificaveis.reduce( (sum: number, item) => sum + item.total, 0); //

  }

  otenhaProdutoNaoAceitaCupom(percentualMaximoDescontoProduto: number){
    let itemPedido: any =  this.itens.find( (item: any) => item.produto.naoAceitaCupom);

    if(itemPedido) return itemPedido.produto;

    let sabor: any;

    this.itens.forEach((itemDoPedido: any) => {
       if(!sabor)
        sabor =  itemDoPedido.sabores.find((item: any) => item.produto.naoAceitaCupom);
    })

    if(sabor) return sabor.produto;

    if(percentualMaximoDescontoProduto){
      itemPedido = this.itens.find(
        (item: any) => item.produto.percentualDesconto && item.produto.percentualDesconto > percentualMaximoDescontoProduto)

    }

    if(itemPedido) return itemPedido.produto;

    return   null;
  }

  temProduto(produto: any){
    return this.itens.find( (item: any) => item.produto.id === produto.id) != null;
  }

  temCategoria(categoria: any){
    return this.itens.find( (item: any) => item.produto.categoria && item.produto.categoria.id === categoria.id) != null;
  }


  temTamanho(tamanho: any){
    return this.itens.find( (item: any) => item.produtoTamanho && item.produtoTamanho.template.id === tamanho.id) != null;
  }

 async  calculePontuacaoFidelidade(integracaoPedidoFidelidade: IntegracaoPedidoFidelidade) {
    if(!this.contato || this.contato.ehConsumidorNaoIndentificado() || !integracaoPedidoFidelidade || this.doIfood()) return;

    if(this.cupom && this.cupom.naoPontuarFidelidade) return;

    if(!this.id && integracaoPedidoFidelidade.pontuarSoLoja && this.origem !== 'loja'){
      console.log(String(`Pontuação automatica da empresa ${this.empresa.id} so feitos em loja`))
      return;
    }

   if(!integracaoPedidoFidelidade.estaPontuando()) {
      console.log(String(`Plano da empresa ${this.empresa.id} nao está pontuando mais`))
      return;
    }


   if(!integracaoPedidoFidelidade.pontuarMesas && this.mesa && this.mesa.id){
     console.log(String(`Não pontuar pedidos/mesas da empresa ${this.empresa.id}`))
     return;
   }



   if(this.cupom && this.cupom.aplicarNaFidelidade)
     integracaoPedidoFidelidade.atividade.cashback = this.cupom.percentual / 100

    let pontuacaoRegistrada: any = new PontuacaoRegistrada();

    pontuacaoRegistrada.valor = this.obtenhaValorPagoQuePontua();
    pontuacaoRegistrada.cartao = new Cartao(null, this.contato,  integracaoPedidoFidelidade.plano, 0);
    if(integracaoPedidoFidelidade.atividade)
      pontuacaoRegistrada.atividades =  [integracaoPedidoFidelidade.atividade]

   this.pontosGanhos =
      integracaoPedidoFidelidade.plano.tipoDePontuacao.calculePontos(this.empresa, pontuacaoRegistrada, this.obtenhaVenda() );

  }

  obtenhaVenda(): any{
    return  { valor: this.obtenhaValorPagoQuePontua(), itens: this.itens, desconto: this.obtenhaTotalDescontoEmProdutos()}
  }


  obtenhaValorPagoQuePontua() {
    let valorDosProdutos = this.valor; // nao entra taxa de entrega.

    let valorCashback = this.obtenhaCashback();

    if(valorCashback)
      valorDosProdutos = valorDosProdutos - valorCashback;

    if(valorDosProdutos < 0)
      valorDosProdutos = 0

    return    Number(valorDosProdutos.toFixed(2));
  }

  obtenhaPagamentosComValor(){
    if(!this.pagamentos) return [];

    return this.pagamentos.filter((item: any) => !item.pagoPorPlanoFidelidade() && item.valor)
  }

  obtenhaFormaPagamentoExterno(){
    let paramento = this.pagamentos.find((pagamento: any) => !pagamento.pagoPorPlanoFidelidade())

    return paramento ? paramento.formaDePagamento.referenciaExterna : null
  }



  obtenhaDataVenda() {
    if(this.horarioEntregaAgendada) return this.horarioEntregaAgendada;

    return this.horario;
  }

  obtenhaResumoConteudo(){
    let  itens: any
      = this.itens.map( itemPedido => itemPedido.produto.categoria ?  itemPedido.produto.categoria.nome :  itemPedido.produto.nome);

    return itens.join(', ');

  }


  apliquePromocao(promocao: Promocao) {
    let descontoAplicado = promocao.aplique(this)

    if(!descontoAplicado || descontoAplicado.totalDesconto === 0) return

    //garantindo que cada promoção só seja aplicada uma vez
    for(let promocaoAplicada of this.promocoesAplicadas)
      if(promocaoAplicada.promocao.id === promocao.id) {
        promocaoAplicada.desconto = descontoAplicado.totalDesconto
        return
      }

    let novaPromocaoAplicada = new PromocaoAplicada(this, promocao, descontoAplicado.totalDesconto)

    this.promocoesAplicadas.push(novaPromocaoAplicada)
  }


  temTroco(){
    return this.pagamentos.find((pagamento: any) => pagamento.trocoPara > 0) != null
  }

  erroDePagamento() {
    if(!this.mesa){
      let valorPagamentos =  this.obtenhaValorDosPagmentos();
      let totalPedido = this.obtenhaTotal();
      let diferenca =  totalPedido - valorPagamentos;

      if(diferenca > 0.1   )
        return String(`Valor do pagamento  R$${valorPagamentos} menor que o total do Pedido  R$${totalPedido}`)

      if(diferenca < -0.1   )
        return String(`Valor do pagamento  R$${valorPagamentos} maior que o total do Pedido  R$${totalPedido}`)

    }

    let  erroResgate, totalResgatado = this.obtenhaTotalResgatado();

    this.pagamentos.forEach((pagamento: any) => {
      if( pagamento.foiPorCashback() && !pagamento.foiPagoFidelidadeExterna()){
        if(!pagamento.id){ // não resgatou ainda
          if(this.empresa.integracaoPedidoFidelidade){
            let cartao =  this.contato.obtenhaCartaoCashback(this.empresa.integracaoPedidoFidelidade.plano);
            if(!cartao) erroResgate = 'Não é possivel pagar com cashback, nenhum cartão encontrado.';
          } else {
            erroResgate = 'Não é possivel pagar com cashback, nenhuma integração está configurada.';
          }
        }
      }
    })

    if(totalResgatado){
       let pagamentoResgate = this.pagamentos.find((pagamento: any) => pagamento.foiPorResgate())

       if(!pagamentoResgate){
         erroResgate = `Verifique em forma de pagamento opção "usar meus pontos"`;
       } else if(pagamentoResgate.valor !== totalResgatado){
         erroResgate = `Total pontos inválido ${ pagamentoResgate.valor} / ${totalResgatado}`;
       }
    }

    return erroResgate
  }

   async valideItensAdicionados() {
    let produtosLimitados: any = {};

    for(let i = 0; i < this.itens.length; i++){
      let itemPedido: ItemPedido = this.itens[i];

      let produto: any = itemPedido.produto;

      if(produto.qtdMaxima > 0) {
        if(!(produto.id in produtosLimitados)) {
          produtosLimitados[produto.id] = 0
        }

        produtosLimitados[produto.id] += itemPedido.qtde

        if(produtosLimitados[produto.id] > produto.qtdMaxima)
          throw Error("Você não pode selecionar mais do que " + produto.qtdMaxima +
            " '" + produto.nome +  "' por pedido.")
      }

      if(produto.tipoDeVenda === EnumTipoDeVenda.Unidade && itemPedido.qtde < produto.qtdeMinima)
        throw Error("Você não pode selecionar menos do que " + produto.qtdeMinima + " '" + produto.nome +  "' por pedido.")

      if(produto.tipoDeVenda === EnumTipoDeVenda.Peso && itemPedido.qtde > produto.pesoMaximo) {
        throw Error("Você não pode selecionar mais do que " + produto.pesoMaximo + ' ' +
          produto.unidadeMedida.sigla + " de '" + produto.nome +  "' por pedido.")
      }

      if(produto.tipoDeVenda === EnumTipoDeVenda.Peso && itemPedido.qtde < produto.pesoMinimo) {
        throw Error("Você não pode selecionar menos do que " + produto.pesoMinimo + ' ' +
          produto.unidadeMedida.sigla + " de '" + produto.nome +  "' por pedido.")
      }

      let erroItem = await itemPedido.valide(this.empresa, this.empresa.catalogo);

      if (erroItem)
        throw Error(erroItem);
    }
  }

  ehPedidoRecente(): boolean {
    const agora = new Date();
    const umDiaAtras = new Date(agora.getTime() - 24 * 60 * 60 * 1000); // 24 hours ago

    const dataDoPedido = this.horarioEntregaAgendada ? this.horarioEntregaAgendada : this.horario;

    return dataDoPedido >= umDiaAtras;
  }

  deMesa(){
    return this.mesa && this.mesa.id !== null
  }

  fazParteMultipedido(){
    return this.multipedido != null
  }

  setTamanhosProduto() {

    this.itens.forEach((item: any) => {
      if(item.produtoTamanho && !item.produto.tamanhos.length)
        item.produto.tamanhos.push(item.produtoTamanho)
    })
  }

  async atualizeRetornoDeliveryIfood(order: any, idLoja: string, empresa: any) {
    let ifoodExtra: any  = {displayId:  order.displayId}

    if(order.delivery){
      this.ifoodCodigoRetirada = order.delivery.pickupCode;
      ifoodExtra.deliveredBy = order.delivery.deliveredBy;
      ifoodExtra.metodosPagamento = order.payments.methods;

      this.ifoodExtra = JSON.stringify(ifoodExtra)
      this.idLojaExterna = idLoja;
    }

    await new MapeadorDePedido().atualizeExtraIfood(this);

    if(order.delivery && order.payments.methods)
      await this.altereFormaPagamentoParaDoIfood( order.payments.methods, empresa)

  }

  async altereFormaPagamentoParaDoIfood( metodosPagamento: Array<any>, empresa: Empresa){
    let pagamentosTrocar =
      this.pagamentos.filter((pg: any) => !pg.formaDePagamento.ehDoIfood() && !pg.formaDePagamento.ehPagamentoDeResgate());

    if(pagamentosTrocar.length){
      for(let i = 0; i < pagamentosTrocar.length; i ++){
        let pagamentoTrocar: PagamentoPedido = pagamentosTrocar[i];

        let metodoPagamentoIfood =  metodosPagamento.splice(0, 1)[0];

        if(metodoPagamentoIfood){
          let formaDePagmentoNaEmpresa: any = await empresa.obtenhaOuCrieFormaDePagamentoIfood(metodoPagamentoIfood);

          if(formaDePagmentoNaEmpresa){
            pagamentoTrocar.formaDePagamento = formaDePagmentoNaEmpresa;

            await new MapeadorDePagamentoPedido().atualizeFormaPagamento(pagamentoTrocar);
            let historicoPedido = new HistoricoPedido(this, 'Forma de pagamento alterada para ifood', null, null)
            await historicoPedido.salve(true);
          }
        }
      }
    }
  }

  pagouComFidelidade() {
    return this.pagamentos.find((item: any) => item.pagoPorPlanoFidelidade()) != null
  }

  obtenhaMovimentacoesEstoque(estoqueVinculadoProduto: boolean){

    let movimentacoes: any = [];

    this.itens.forEach((item: ItemPedido) => {
      item.getMovimentacoes(estoqueVinculadoProduto, this, movimentacoes)
    })

    return movimentacoes;
  }

  temResgateBrindeCupom() {
      return this.itens.find((item: any) => item.produto.brinde && item.produto.cupom) != null
  }
}
