import {EnumTipoDeTaxaDeEntrega} from "./EnumTipoDeTaxaDeEntrega";

export class RaioDeCobranca {
  public valorMinimoPedido: number;
  permiteFreteGratis = true;
  constructor(public tipo: EnumTipoDeTaxaDeEntrega, public alcance: number,
              public valorFixo: number, public valorKmTaxa: number, public valorMinimoTaxa: number) {
  }

  calculeTaxaEntrega(distancia: any) {
    if(this.tipo === EnumTipoDeTaxaDeEntrega.VALOR_MANUAL) {
      return -1;
    }

    if(this.tipo === EnumTipoDeTaxaDeEntrega.FRETE_GRATIS)
       return 0.0;

    if(this.tipo === EnumTipoDeTaxaDeEntrega.VALOR_FIXO)
      return this.valorFixo;

    let taxaCalculada = Number(distancia) * this.valorKmTaxa;

    if(this.valorMinimoTaxa > taxaCalculada) return this.valorMinimoTaxa;

    return Math.ceil(taxaCalculada);
  }
}
