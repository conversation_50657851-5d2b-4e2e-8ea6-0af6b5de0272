import {ObjetoPersistente} from "../ObjetoPersistente";
import {OpcaoDeAdicionalDeProduto} from "./OpcaoDeAdicionalDeProduto";
import {MapeadorBasico} from "../../mapeadores/MapeadorBasico";
import {ValorDeOpcaoMultiplaEscolha} from "./ValorDeOpcaoMultiplaEscolha";
import {MapeadorDeListaOpcoesEscolhidas} from "../../mapeadores/MapeadorDeListaOpcoesEscolhidas";
import {EnumTipoDeCobrancaDeAdicional} from "../../lib/emun/EnumTipoDeCobrancaDeAdicional";


export class ListaOpcoesEscolhidas extends ObjetoPersistente {
  id: number;
  tipoDeCobranca: EnumTipoDeCobrancaDeAdicional
  ordem: number;
  opcoes: ValorDeOpcaoMultiplaEscolha[];

  mapeador(): MapeadorBasico {
    return new MapeadorDeListaOpcoesEscolhidas();
  }
}
