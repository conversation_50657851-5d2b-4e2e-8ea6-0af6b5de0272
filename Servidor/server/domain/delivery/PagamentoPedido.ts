import {EnumFormaPagamento, EnumFormaPagamentoLabel} from "../../lib/emun/EnumFormaPagamento";
import {EnumStatusPagamento, StatusPagamentoDeParaPagseguro} from "../../lib/emun/EnumStatusPagamento";
import {MapeadorDePagamentoPedido} from "../../mapeadores/MapeadorDePagamentoPedido";
import {integer} from "twilio/lib/base/deserialize";
import {PagamentoGenerico} from "./PagamentoGenerico";
import {EnumStatusPagamentoWebWookTunaPay} from "../../lib/emun/EnumStatusPagamentoTunaPay";

export class PagamentoPedido extends PagamentoGenerico{
  descricaoFormaDePagamento: string;
  parcela: number;
  pedido: any;
  idLink: string;
  link: string;
  codigoTransacao: string;
  motivoReprovacao: string;
  bandeira: string;
  metodoPagamento: string;
  codigoTipoPagamento: string;
  taxa = 0;
  valorTaxaSplit = 0;
  codigoQrCode: string;
  nsu: any;
  codigoAutorizacao: string
  codigoAdquirente: string;
  codigoAutenticacao: string;
  email: string
  cpf: string
  finalCartao: string;
  urlAutenticar: string;
  tokenizacaoId: string;
  provedorExterno: string;
  dataExpiracao: Date;
  constructor(pedido: any, valor: number, trocoPara: any,
              formaDePagamento: any, status: any = EnumStatusPagamento.Gerado, linkPagamento: string = null) {
    super()
    this.pedido = pedido
    this.valor = valor;
    this.formaDePagamento = formaDePagamento;
    this.status = status;
    this.trocoPara = trocoPara;
  }

  mapeador(): any {
    return new MapeadorDePagamentoPedido();
  }

  async valide(): Promise<any> {
    return new Promise<any>( (resolve, reject) => {
      if(!this.pedido) return resolve('Pedido do pagamento não informado')
      if(!this.formaDePagamento || !this.formaDePagamento.id) return resolve('Forma de pagamento não informado')
      if( !( this.valor >= 0 )) return resolve('Valor do pagamento em ' + this.formaDePagamento.nome  + ' não informado')

     // if(this.trocoPara == null  ) return resolve('Troco   não informado')

      if( this.trocoPara  > 0 && this.formaDePagamento.nome === EnumFormaPagamento.Dinheiro ) {
        if(this.trocoPara  < this.valor)
          return resolve('Ops! valor para o troco tem que ser maior que R$' + this.valor)
      }

      resolve('');
    });
  }

  naoGerouPix(){
    return this.foiPorPix() && !this.codigoQrCode
  }

  // tslint:disable-next-line:member-ordering
  static get(query: any){
    return new MapeadorDePagamentoPedido().selecioneSync( query);
  }
  // tslint:disable-next-line:member-ordering
  static novo(){
    return new PagamentoPedido(null, null, null, null)
  }

  async atualizeComDadosLinkPagamento(dadosLinkPagamento: any) {
    this.idLink = dadosLinkPagamento.id;
    this.link = dadosLinkPagamento.shortUrl;
    await new MapeadorDePagamentoPedido().atualizeDados(this)
  }



  async atualizeRetornoMercadoPagoPix(dadosPagamentoPix: any) {
    this.codigo = dadosPagamentoPix.codigo;
    this.codigoTransacao = this.codigo;
    this.setPix(dadosPagamentoPix.codigoQrCode)

    //if(dadosPagamentoPix.dataExpiracao)
     // this.dataExpiracao = dadosPagamentoPix.dataExpiracao

    await new MapeadorDePagamentoPedido().atualizeDados(this)
  }

  async atualizeCodigoPagseguro(transacao: any) {
    this.codigoTransacao = transacao.codigo;
    this.status = StatusPagamentoDeParaPagseguro.get(integer(transacao.status));

    return await new MapeadorDePagamentoPedido().atualizeDados(this)
  }

  async atualizeMercadoPago(payment: any ) {
    this.codigo = payment.id;
    this.codigoTransacao = payment.id;
    this.bandeira = payment.payment_method_id;
    this.metodoPagamento = payment.payment_type_id;

    if(payment.card)
      this.finalCartao = payment.card.last_four_digits;

    if(payment.payer && !this.email)
      this.email = payment.payer.email

    if(payment.motivoReprovacao)
      this.motivoReprovacao = payment.motivoReprovacao

    await new MapeadorDePagamentoPedido().atualizeDados(this);
  }

  async atualizePixPagarme(order: any){
    let charge = order.charges[0];
   // if(order.dataExpiracao)
     // this.dataExpiracao = new Date(order.dataExpiracao)

    await this.atualizeRetornoPagarme(charge);
  }

  async atualizeRetornoTunaPay(payment: any, valorTaxaSplit: number = 0, dadosCartao: any = null){
    console.log(JSON.stringify(payment));

    if(payment.paymentKey)
      this.codigo = payment.paymentKey; //operationId //partnerUniqueId

    if(dadosCartao && dadosCartao.externalProvider){
      this.provedorExterno = dadosCartao.externalProvider
      this.bandeira = dadosCartao.bandeira;
      this.finalCartao = dadosCartao.ultimosNumeros;
      this.metodoPagamento =  'credit_card'
    }

    let metodo = payment.methods.slice(-1)[0];

    let foiCartao = metodo.cardInfo || metodo.methodType === '1';

    if(metodo.pixInfo){
      if(payment.dataExpiracao)
        this.dataExpiracao = new Date(payment.dataExpiracao)

      //sandbox so vem qrContent
      this.setPix(metodo.pixInfo.qrCopyPaste || metodo.pixInfo.qrContent)
    } else if(foiCartao){
      if(metodo.cardInfo){
        this.metodoPagamento =  'credit_card'
        this.bandeira = metodo.cardInfo.brandName;
        if(metodo.cardInfo.externalProvider )
          this.provedorExterno = metodo.cardInfo.externalProvider
      }

      if(payment.status ===   EnumStatusPagamentoWebWookTunaPay.Negada){
        this.urlAutenticar = null;
        this.status = EnumStatusPagamento.Negado;
        //todo: lista status erro mais detalhes: https://dev.tuna.uy/codes/paymentMethodStatus.json
        let statusErroMetodo = metodo.status;
        if(metodo.acquirer && metodo.acquirer.length){
          this.motivoReprovacao =  `Pagamento negado: ${statusErroMetodo} - "${metodo.acquirer[0].message}"`;
        } else if(metodo.message){
          const code: string =  metodo.message.code;
          const message: string =  metodo.message.code;
          const motivo = code !== message ? `${code}: ${message}` : message;

          this.motivoReprovacao =  `Pagamento negado: "${motivo}"`;
        } else {
          this.motivoReprovacao =  `Pagamento negado`
        }
      }
    }

    if(valorTaxaSplit) this.valorTaxaSplit = valorTaxaSplit;

    await new MapeadorDePagamentoPedido().atualizeDados(this)
  }

  async atualizeRetornoRede(transaction: any){
    console.log(transaction);

    if(transaction.threeDSecure){
      if(transaction.threeDSecure.url){
        this.urlAutenticar = transaction.threeDSecure.url;
      } else {
        if(transaction.threeDSecure.cavv) //autenticou com desafio manual
          this.codigoAutenticacao = transaction.threeDSecure.cavv
        else if(transaction.threeDSecure.returnCode === '200') //autenticou sem desafio
          this.codigoAutenticacao = '200'
      }
    }

    if(transaction.erro){
      this.motivoReprovacao = transaction.erro;
      this.urlAutenticar = null;
      this.status = EnumStatusPagamento.Negado;
    }

    if( transaction.tid != null)
      this.codigoTransacao = transaction.tid;

    if(transaction.tokenizationId){
      this.tokenizacaoId = transaction.tokenizationId;
      this.status =  EnumStatusPagamento.Gerado;
    }

    if(transaction.qrCodeResponse){
      this.setPix(transaction.qrCodeResponse.qrCodeData)
      //if(transaction.dataExpiracao)
       // this.dataExpiracao = transaction.dataExpiracao
    }  else {
      if(transaction['brand.name'])
        this.bandeira = transaction['brand.name']

      if(transaction.authorizationCode)
        this.codigoAutorizacao = transaction.authorizationCode

      if(transaction.authorization){
        let authorization: any = transaction.authorization;
        if(authorization.nsu)  this.nsu = authorization.nsu
        if(authorization.authorizationCode)  this.codigoAutorizacao = authorization.authorizationCode
        if(authorization.brand)
          this.bandeira =  authorization.brand.name;

      } else {
        if(transaction.nsu)  this.nsu = transaction.nsu
        if(transaction.authorizationCode)  this.codigoAutorizacao = transaction.authorizationCode
        if(transaction.brand){
          this.bandeira =  transaction.brand.name;
          //possivel motivo reprovação
          if(transaction.brand.returnMessage)
            this.motivoReprovacao = transaction.brand.returnMessage

          if(transaction['brand.returnMessage'])
            this.motivoReprovacao = transaction['brand.returnMessage']
        }
      }
    }

    await new MapeadorDePagamentoPedido().atualizeDados(this)
  }

  jaTentouPagar(){
    return this.status === EnumStatusPagamento.Negado || this.codigoTransacao != null
      || this.urlAutenticar != null || this.motivoReprovacao != null || this.codigo != null
  }

  async atualizeRetornoPagseguro(charge: any){
    console.log(charge);
    this.codigoTransacao = charge.id;
    if(charge.payment_response){
       if(charge.payment_response.raw_data){
         this.nsu = charge.payment_response.raw_data.nsu;
         this.codigoAutorizacao = charge.payment_response.raw_data.authorization_code;
       }
    }

    if(charge.payment_method){
      this.metodoPagamento = charge.payment_method.type;
      if(charge.payment_method.card)
        this.bandeira =  charge.payment_method.card.brand;

      let teveAutenticacao = charge.payment_method.authentication_method &&
        charge.payment_method.authentication_method.status === 'AUTHENTICATED';

      if(teveAutenticacao){
        //nova tentativa pagamento executada ver: POST /cartao/novoPagamento
        if(charge.codigoAutenticacao)
          this.codigoAutenticacao = charge.codigoAutenticacao
      } else {
        this.codigoAutenticacao = null
      }
    }
    return await new MapeadorDePagamentoPedido().atualizeDados(this)
  }


  setPix(qr_code: string){
    this.metodoPagamento = 'Pix'
    this.codigoQrCode = qr_code
  }

  async atualizeRetornoPagarme(charge: any ) {
    this.codigo = charge.id;

    if( charge.last_transaction){
      console.log(charge.last_transaction)
      this.codigoTransacao = charge.last_transaction.id;
      if(charge.payment_method === 'pix'){
        this.setPix(charge.last_transaction.qr_code)

      } else {
        this.metodoPagamento = charge.payment_method;
        this.parcela = charge.last_transaction.installments;
        this.nsu = charge.last_transaction.acquirer_nsu;
        this.codigoAutorizacao = charge.last_transaction.acquirer_auth_code;
        this.codigoAdquirente = charge.last_transaction.acquirer_return_code;
        if( charge.last_transaction.card)
          this.bandeira = charge.last_transaction.card.brand;
      }
      if(charge.last_transaction.gateway_response){
        console.log(charge.last_transaction.gateway_response.errors)
        let code = charge.last_transaction.gateway_response.code;
        if( Number(code) >= 400){//400
          let error = charge.last_transaction.gateway_response.errors[0];
          this.motivoReprovacao =  error.message;
        }
      }
    }

    await new MapeadorDePagamentoPedido().atualizeDados(this);
  }

  async atualizeRetornoCielo(transacao: any ) {
    this.codigo =  transacao.PaymentId;
    this.codigoTransacao = transacao.PaymentId;
    this.metodoPagamento = transacao.Type;
    this.bandeira   = transacao.CreditCard.Brand;
    this.nsu = transacao.ProofOfSale;
    this.codigoAutorizacao = transacao.AuthorizationCode;
    this.codigoAdquirente = transacao.Tid;
    this.codigoAutenticacao =  transacao.ExternalAuthentication ? transacao.ExternalAuthentication.Cavv : '';
    await new MapeadorDePagamentoPedido().atualizeDados(this);
  }

 async atualizeRetornoOrderSuperlink(order: any){
    if(order.orderNumber === this.codigo){
      this.nsu = order.payment.nsu;
      this.codigoAutorizacao = order.payment.authorizationCode;
      this.bandeira = order.payment.brand;
      this.metodoPagamento = order.payment.type;
      this.codigoTransacao = order.payment.tid;

      await new MapeadorDePagamentoPedido().atualizeDados(this);
    }
  }

  async atualizaNovaTentativa(codigo: string){
    this.status = EnumStatusPagamento.Gerado
    this.codigoTransacao = null;
    this.urlAutenticar = null;
    this.motivoReprovacao = null;
    this.codigoAutenticacao = null;
    this.nsu = null;
    this.bandeira = null;
    this.codigo = codigo;
    await new MapeadorDePagamentoPedido().atualizeDados(this)
  }

  obtenhaDescricao(){
    if(this.foiPorResgate())
      return EnumFormaPagamentoLabel.get(this.formaDePagamento.nome)

    let descricao = this.formaDePagamento.descricao;

    let carteiraDigital = this.provedorExterno === 'GooglePay' || this.provedorExterno === 'ApplePay';

    if(carteiraDigital){
      if(this.provedorExterno === 'GooglePay') {
        descricao = 'Google Pay'
      } else if(this.provedorExterno === 'ApplePay'){
        descricao = 'Apple Pay'
      }

      if(this.bandeira){
        let finalCartao = this.finalCartao ? `****${this.finalCartao}` : ''
        descricao = String(`${descricao} (${this.getDescricaoBandeira()}${finalCartao})`)
      }
    } else {
      if(this.bandeira && this.metodoPagamento){
        descricao = String(`${this.getMetodoPagamento()} - ${this.getDescricaoBandeira()}`)
      } else if (this.metodoPagamento){
        descricao = String(`${descricao} - ${this.getMetodoPagamento()}`)
      } else if (this.bandeira){
        descricao = String(`${descricao} - ${this.getDescricaoBandeira()}`)
      }
    }

    if(this.parcela && this.parcela > 1)
        descricao = String(`${descricao} (${this.parcela}x)`)

    return descricao.trim();
  }

  getMetodoPagamento(){
    if(this.metodoPagamento === 'credit_card' || this.metodoPagamento === 'CreditCard')
      return 'Cartão de crédito'

    if(this.metodoPagamento === 'debit_card' || this.metodoPagamento === 'DebitCard')
      return 'Cartão de Débito'

    return this.metodoPagamento;
  }

  getDescricaoBandeira(){
    if(this.bandeira === 'master') return 'Mastercard'
    if(this.bandeira === 'visa') return 'Visa'
    if(this.bandeira === 'amex') return 'American Express'

    return this.bandeira;
  }

  obtenhaMensagemStatus() {
    switch (Number(this.status)) {
      case EnumStatusPagamento.Aprovado:
        return 'Seu pagamento foi aprovado.';
      case EnumStatusPagamento.Cancelado:
        return 'Seu pagamento foi cancelado. Tente um novo pagamento.';
      case EnumStatusPagamento.EmAnalise:
        return 'Seu pagamento está em análise. Você será notificado quando ele for aprovado ou reprovado.';
      case EnumStatusPagamento.Negado:
      case EnumStatusPagamento.Cancelado:
        return 'Seu pagamento foi negado. Tente um novo pagamento.';
      case EnumStatusPagamento.Reembolsado:
        return 'Seu pagamento foi reembolsado. Tente um novo pagamento.';
      case EnumStatusPagamento.Suspenso:
        return 'Seu pagamento foi suspenso. Tente um novo pagamento.';
      default:
        return 'Seu pagamento está sendo processado. Aguarde o resultado.';
    }
  }

  aguardandoAprovar(){
    return this.foiOnline() && Number(this.status) < 3
  }

  aguardandoTokenizar(){
    return this.tokenizacaoId && !this.codigoTransacao && this.foiGerado()
  }
}
