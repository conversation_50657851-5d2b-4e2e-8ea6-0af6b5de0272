import {EnumFormaPagamento} from "../../lib/emun/EnumFormaPagamento";
import {Empresa} from "../Empresa";
import {ConfigMeioDePagamento} from "./ConfigMeioDePagamento";
import {TaxaCobranca} from "./TaxaCobranca";
import {FormaDePagamentoPdv} from "../pdv/FormaDePagamentoPdv";
import {Bandeira} from "../pdv/Bandeira";
import slugify from "slugify";
import {EnumTipoPagamento} from "../../lib/integracao/pagamentos/EnumTipoPagamento";
import {EnumMetodoPagamento} from "../../lib/integracao/pagamentos/EnumMetodoPagamento";
import {MapeadorDeFormaDePagamento} from "../../mapeadores/MapeadorDeFormaDePagamento";
import {EnumMeioDePagamento} from "./EnumMeioDePagamento";
import {IFoodUtils} from "../../service/integracoes/IFoodUtils";

export class FormaDePagamento {
  static nomePagarmeHub = 'pagarme-hub';
  constructor() {
    this.notificarConfirmacaoPagamento =  true;
    this.notificarNovoPedido = true;
    this.habilitarRetirada = true;
    this.habilitarEntrega = true;
  }
  id: number;
  nome: string;
  descricao: string;
  exibirCardapio: boolean;
  removida: boolean;
  empresa: Empresa;
  online: boolean;
  configMeioDePagamento: ConfigMeioDePagamento;
  referenciaExterna: string;
  formaIntegrada: any
  taxaCobranca: TaxaCobranca;
  bandeirasCartaoIntegrada: any = [];
  pix = false;
  voucher = false;
  enviarComoDesconto = false;
  numeroParcelasFixas: number
  notificarConfirmacaoPagamento: boolean;
  notificarNovoPedido: boolean;
  possuiDesconto: boolean;
  desconto: number;
  opendeliveryMethod: string;
  opendeliveryBrand: string;
  methodInfo: string;
  habilitarRetirada: boolean;
  habilitarEntrega: boolean;
  cobrarTaxaRetorno = false;
  formaDePagamentoPdv: any;
  bandeira: any;
  chavePix: string;
  public taxaRecebimento = 0;
  public diasRecebimento = 0;
  static nova(nome: string, descricao: string, exibirCardapio: boolean, empresa: Empresa) {
    const forma = new FormaDePagamento();

    forma.nome = nome;
    forma.descricao = descricao;
    forma.exibirCardapio = exibirCardapio;
    forma.empresa = empresa;

    return forma;
  }

  static novaPdv(formaPdv: FormaDePagamentoPdv, bandeira: Bandeira,
                 empresa: any = null, exibirCardapio  = false){
    const forma = new FormaDePagamento();

    forma.exibirCardapio = exibirCardapio;
    forma.formaDePagamentoPdv = formaPdv;
    forma.bandeira = bandeira;
    forma.empresa = empresa;

    forma.setDadosPdv();

    if(empresa && empresa.integracaoDelivery)
      forma.setDadosFormaPdv(empresa.integracaoDelivery.sistema)

    if(empresa && empresa.integracaoOpenDeliveryAtiva())
      forma.setDadosOpendelivery();

    return forma;
  }

  static novaResgate(empresa: any){
    let formaPdv: any = {
        id: 18, nome:  EnumFormaPagamento.Resgate.toLowerCase(),
           descricao: EnumFormaPagamento.Resgate
    }

    return  FormaDePagamento.novaPdv(formaPdv, null, empresa, false);
  }

  static async ativeNovaForma(formaInserir: any){
    let mapeadorFormaPagamento = new MapeadorDeFormaDePagamento();

    let existenteRemovida =
      await mapeadorFormaPagamento.selecioneSync({nome: formaInserir.nome, todas: true});

    if(!existenteRemovida){
      await mapeadorFormaPagamento.insiraSync(formaInserir)
    } else {
      formaInserir.id   = existenteRemovida.id;
      formaInserir.removida  = false;
      await mapeadorFormaPagamento.atualizePdvBandeira(formaInserir)
    }
  }

  ehPagamentoEmDinheiro() {
    return this.nome === EnumFormaPagamento.Dinheiro;
  }

  ehPagamentoDeResgate() {
    return this.ehResgateFidelidade() || this.ehCashback();
  }

  ehResgateFidelidade(){
    return this.nome === EnumFormaPagamento.Resgate;
  }

  ehCashback(){
    return this.nome === EnumFormaPagamento.Cashback;
  }


  setDadosPdv(){
    this.descricao  =  this.formaDePagamentoPdv.nome.trim();

    if(this.bandeira && this.bandeira.nome.toUpperCase().trim() !== this.descricao.toUpperCase())
      this.descricao =  String(`${this.descricao} - ${this.bandeira.nome}`)
    else {
      if(this.formaDePagamentoPdv.metodo === 'PIX'){
        if(this.formaDePagamentoPdv.tipo === 'MANUAL'){
          this.descricao =   String(`${this.descricao} - Chave`)
        } else {
          this.descricao =   String(`${this.descricao} - QR Code Maquininha`)
        }
      }
    }

    this.nome =  slugify(this.descricao).toLowerCase();
  }

  apagueCredenciais() {
    if(this.configMeioDePagamento){
      delete this.configMeioDePagamento.clientSecret;
      delete this.configMeioDePagamento.clientID;
      delete this.configMeioDePagamento.token;
      delete this.configMeioDePagamento.email;
      delete this.configMeioDePagamento.merchantKey;
      delete this.configMeioDePagamento.merchantId;
    }
  }

  pixManual(){
    if(!this.formaDePagamentoPdv) return false;

    return this.formaDePagamentoPdv.tipo === EnumTipoPagamento.MANUAL && this.formaDePagamentoPdv.metodo === EnumMetodoPagamento.PIX;
  }


  setDadosFormaPdv(sistema: string){
    if(this.bandeira ) this.bandeira.setImagem();

    if(this.formaDePagamentoPdv){
      if(sistema &&    ! this.formaIntegrada){
        if(this.formaDePagamentoPdv.formasIntegradas){
          let formaIntegradaPdv: any =
            this.formaDePagamentoPdv.formasIntegradas.find( (item: any) => item.sistema ===  sistema)

          if(formaIntegradaPdv) {
            this.formaIntegrada =  formaIntegradaPdv;
            this.referenciaExterna = formaIntegradaPdv.codigo;
          }
        } else {
          console.warn('nao achou formas integradas: ' , this.formaDePagamentoPdv)
        }
      }
    }
  }

  setDadosOpendelivery(){
    if(this.formaDePagamentoPdv && ! this.opendeliveryMethod )
      this.opendeliveryMethod = this.formaDePagamentoPdv.opendeliveryMethod;

  }

  gatewayPagarme(){
    if(!this.configMeioDePagamento ) return false;

    return this. hubPagarme() ||
      this.configMeioDePagamento.meioDePagamento === EnumMeioDePagamento.Pagarme
  }

  hubPagarme() {
    if(!this.configMeioDePagamento ) return false;

    return   this.configMeioDePagamento.meioDePagamento === EnumMeioDePagamento.PagarmeHub
  }

  tunapay() {
    if(!this.configMeioDePagamento ) return false;

    return   this.configMeioDePagamento.meioDePagamento === EnumMeioDePagamento.TunaPay
  }

  ERede() {
    if(!this.configMeioDePagamento ) return false;

    return   this.configMeioDePagamento.meioDePagamento === EnumMeioDePagamento.ERede
  }

  pagseguroConnect(){
      if(!this.configMeioDePagamento ) return false;

      return   this.configMeioDePagamento.meioDePagamento === EnumMeioDePagamento.PagBankConnect
  }

  antiFraudeAtivo(){
    if(!this.configMeioDePagamento ) return false;

    return !this.configMeioDePagamento.antifraudeDesabilitado
  }

  exibirEmMesas(bandeirasMesa: Array<any>){
    if(this.nome === 'dinheiro') return true;

    let bandeira = this.bandeirasCartaoIntegrada[0];

    if(!bandeira) return false; //nao configurado

    let codigoPdv = bandeira.codigoPdv || bandeira.id;

    let temBandeira =  bandeirasMesa.find((bandeiraMesa: any) => bandeiraMesa.cod_det.toString() === codigoPdv) != null;

    return temBandeira
  }

  doIfoodLoja(metodoInfo: any){
    let formaPagamentoPdv: any  = this.formaDePagamentoPdv;

    if(!formaPagamentoPdv ||  !this.ehDoIfood())  return false;

    let { type, method, card} = metodoInfo;
    let brandeiraIfood: string =  (card ? card.brand : '');

    if(method === 'CASH')
      brandeiraIfood = EnumFormaPagamento.Dinheiro;

    let bandeira: string =  this.bandeira ? this.bandeira.nome : '';

    return type === formaPagamentoPdv.tipo && IFoodUtils.mesmoNome(brandeiraIfood, bandeira)

  }

  ehDoIfood(){
    return this.nome.startsWith('ifood-loja') || this.nome.startsWith('ifood-online')
  }

  temBandeira(){
    let formaPagamentoPdv: any  = this.formaDePagamentoPdv ? this.formaDePagamentoPdv.id : null;
    let bandeira: string =  this.bandeira ? this.bandeira.nome : null;

    return  formaPagamentoPdv != null && bandeira != null
  }

  compativelComIfood(metodoInfo: any){
    let formaPagamentoPdv: any  = this.formaDePagamentoPdv;

    if(!formaPagamentoPdv) return false;

    let { type, paymentType, method, card, brand} = metodoInfo;

    let brandeiraIfood: string = brand || (card ? card.brand : null);

    if(paymentType) type = paymentType
    let bandeira: string =  this.bandeira ? this.bandeira.nome : null;

    return type === formaPagamentoPdv.tipo &&
            method === formaPagamentoPdv.opendeliveryMethod &&
             (!brandeiraIfood || IFoodUtils.mesmoNome(brandeiraIfood, bandeira))

  }

  getDescricaoPeloMetodo(){
    let formaPagamentoPdv: any  = this.formaDePagamentoPdv;

    if(formaPagamentoPdv){
      let bandeira: string =  this.bandeira ? this.bandeira.nome : null;

      return String(`${formaPagamentoPdv.tipo} ${formaPagamentoPdv.opendeliveryMethod || ''}  ${bandeira || ''}`).trim();
    }

    return this.nome;
  }
}
