import {ObjetoPersistente} from "../ObjetoPersistente";
import {FormaDePagamentoPdv} from "../pdv/FormaDePagamentoPdv";
import {MapeadorFormaDePagamentoEmpresa} from "../../mapeadores/MapeadorFormaDePagamentoEmpresa";
import {TaxaCobranca} from "./TaxaCobranca";
import {ConfigMeioDePagamento} from "./ConfigMeioDePagamento";
import {Bandeira} from "../pdv/Bandeira";

export class FormaDePagamentoEmpresa extends ObjetoPersistente{
  public empresa: any;
  public exibirCardapio: boolean;
  public formaIntegrada: any;
  public enviarComoDesconto = false;

  public possuiDesconto: boolean;
  public desconto: number;
  public numeroParcelasFixas: number;

  public habilitarRetirada: boolean;
  public habilitarEntrega: boolean;
  public cobrarTaxaRetorno = false;

  public notificarConfirmacaoPagamento: boolean;
  public notificarNovoPedido: boolean;

  public taxaCobranca: TaxaCobranca;
  public configMeioDePagamento: ConfigMeioDePagamento;
  constructor( public formaDePagamentoPromokit: FormaDePagamentoPdv, public bandeira: Bandeira) {
    super();
  }

  mapeador(): any {
    return new MapeadorFormaDePagamentoEmpresa();
  }
}
