import {ObjetoComAdicionais} from "./ObjetoComAdicionais";
import {PagamentoGenerico} from "./PagamentoGenerico";
import {PagamentoPedido} from "./PagamentoPedido";
import {Contato} from "../Contato";
import {Endereco} from "./Endereco";
import {Empresa} from "../Empresa";
import {EnumOrigemPedido} from "../../lib/emun/EnumOrigemPedido";
import {FormaDeEntrega} from "./FormaDeEntrega";
import {EnumStatusPedido} from "../../lib/emun/EnumStatusPedido";
import {EnumStatusPagamento} from "../../lib/emun/EnumStatusPagamento";
import {PromocaoAplicada} from "../PromocaoAplicada";
import {MapeadorDePizzaTamanhoSaboresDePara} from "../../mapeadores/MapeadorDePizzaTamanhoSaboresDePara";
import {ItemPedido} from "./ItemPedido";
import {Resposta} from "../../utils/Resposta";
import {Entregador} from "./Entregador";
import {DeliveryPedido} from "../integracoes/DeliveryPedido";
import {PedidoDisputa} from "../integracoes/PedidoDisputa";
import {PedidoAlteracaoEndereco} from "../integracoes/PedidoAlteracaoEndereco";
import {NotaFiscalEletronica} from "../nfce/NotaFiscalEletronica";
import {MovimentacaoEstoqueInsumo} from "../estoque/MovimentacaoEstoqueInsumo";
import {EnumFormaPagamentoLabel} from "../../lib/emun/EnumFormaPagamento";

export abstract class PedidoGenerico extends ObjetoComAdicionais{
  codigo: string;
  guid: string;
  valor: number;
  taxaEntrega: number;
  desconto: number
  taxaFormaDePagamento: number;
  descontoTaxaEntrega: number;
  descontoFormaDePagamento: number;
  comerNoLocal = false;
  observacoes: string;
  horarioEntregaAgendada: Date;
  origem: EnumOrigemPedido;
  formaDeEntrega: FormaDeEntrega;
  endereco: Endereco;
  contato: Contato;
  empresa: Empresa;
  cupom: any;
  horario: Date;
  horarioAtualizacao: Date;
  pago: boolean;
  impresso: boolean;
  visualizado  =  false;
  pontosGanhos: number
  referenciaExterna: string;
  idLojaExterna: string;
  referenciaExternaDelivery: string;
  numCupomFiscal: string;
  erroExterno: string;
  erroExternoDelivery: string;
  entregador: Entregador
  status: EnumStatusPedido;
  removeuAdicionais: boolean;
  deliveryPedido: DeliveryPedido;
  promocoesAplicadas: PromocaoAplicada[];
  pagamentos: Array<PagamentoGenerico> = [];
  aceito: boolean;
  ifoodExtra: string;
  ifoodCodigoRetirada: string;
  ifoodTaxaServico: number;
  disputa: PedidoDisputa;
  alteracaoEndereco: PedidoAlteracaoEndereco;
  beneficios: any = [];
  nfce: NotaFiscalEletronica;
  identificacaoBalcao: string;
  protected constructor() {
    super();
    this.horario = new Date();
    this.horarioAtualizacao = new Date();
    this.pago = false;
  }

  async apliqueCupom(codigo: string, validarRestricoesContato = true): Promise<any> {
    return Resposta.erro('Pedido não elégivel a cupons');
  }

  protected apliqueDescontoCupom(cupom: any){
    this.cupom = cupom;

    let descontoCupom =  cupom.calculeDesconto(this);

    if(this.cupom.aplicarNaTaxaDeEntrega){
      this.descontoTaxaEntrega = descontoCupom;
      this.taxaEntrega -= this.descontoTaxaEntrega;
      this.taxaEntrega = Number(this.taxaEntrega.toFixed(2));
    } else {
      if(this.promocoesAplicadas.length === 0)
        this.desconto = 0

      this.desconto += descontoCupom
    }

    return descontoCupom;
  }

  obtenhaValorTroco(){
    let pagamentoComTroco: any = this.pagamentos.find(pagamento => pagamento.trocoPara > 0)

    if(!pagamentoComTroco) return 0;

    return Number((pagamentoComTroco.trocoPara - pagamentoComTroco.valor).toFixed(2))

  }

  obtenhaTotalPagoEm(formaPagamentoExterna: any, valorPedido: number = 0){
    let pagamentosDaForma =
      this.pagamentos.filter( (pagamento: any) =>
        pagamento.formaDePagamento.referenciaExterna === formaPagamentoExterna)

    return pagamentosDaForma.reduce(
      ( total: number,  pagamento: any) =>  (total + (pagamento.enviarCashbackComoDesconto(valorPedido) ? 0 : pagamento.valor)) , 0);

  }

  obtenhaTrocoPara(){
    let pagamentoComTroco: any = this.pagamentos.find(pagamento => pagamento.trocoPara > 0)

    if(!pagamentoComTroco) return 0;

    return pagamentoComTroco.trocoPara;
  }

  async carregueDeParaTamanhoSabores(){
    let itens: any = this.obtenhaItensPedido();

    for(let i = 0; i < itens.length; i++){
      let produtoTamanho = itens[i].produtoTamanho;
      if(produtoTamanho){
        produtoTamanho.template.deParaTamanhoSabores =
          await new MapeadorDePizzaTamanhoSaboresDePara().listeAsync({ idTamanhoTemplate: produtoTamanho.template.id })
      }
    }
  }

  obtenhaItensPedido(): Array<ItemPedido>{
    return []
  }

  obtenhaMeioDePagamento() {
    if(!this.pagamentos || !this.pagamentos.length) return null;

    for( let i = 0; i < this.pagamentos.length; i++ ) {
      const pagamento = this.pagamentos[i];

      if( pagamento.formaDePagamento && pagamento.formaDePagamento.configMeioDePagamento ) {
        return pagamento.formaDePagamento.configMeioDePagamento.meioDePagamento;
      }
    }

    return null;
  }

  obtenhaPagamentoPrincipal() {
    if(!this.pagamentos || !this.pagamentos.length) return null;

    for( let i = 0; i < this.pagamentos.length; i++ ) {
      const pagamento = this.pagamentos[i];

      if( pagamento.formaDePagamento && pagamento.formaDePagamento.configMeioDePagamento ) {
        return pagamento;
      }
    }

    return null;
  }

  obtenhaTotal() {
    let total = this.valor;
    let taxas  = this.obtenhaTotalTaxas();

    if( taxas > 0 )
      total += taxas

    return Number(total.toFixed(2))
  }


  obtenhaTotalTaxas(){
    let taxa = 0;

    if( this.taxaEntrega  > 0 )
      taxa += this.taxaEntrega;

    if(this.taxaFormaDePagamento)
      taxa += this.taxaFormaDePagamento;

    if(this.ifoodTaxaServico)
      taxa += this.ifoodTaxaServico;

    return  Number(taxa.toFixed(2));
  }

  obtenhaTotalResgatado(){
    return this.obtenhaItensPedido().reduce( (valor: number, item: ItemPedido) =>   valor + item.valorResgatado, 0)
  }


  temDescontosOuTaxas(){
    return this.desconto || this.descontoFormaDePagamento || this.taxaFormaDePagamento
  }


  obtenhaTotalPagar(){
    let total = this.obtenhaTotal();

    if(this.pagamentos){
      this.pagamentos.forEach( (pagamento: PagamentoPedido) => {
        if(pagamento.foiPorCashback())
          total = total - pagamento.valor;
      })
    }

    return    Number( (total).toFixed(2));
  }

  pagarOnline() {
    if(!this.pagamentos || !this.pagamentos.length) return false;

    return  this.obtenhaPagamentoOnline() != null
  }

  obtenhaPagamentoOnline() {
    if(!this.pagamentos || !this.pagamentos.length) return null;

    return this.pagamentos.find((pagamento: any) => pagamento.formaDePagamento.online);
  }

  autenticouPagamentoOnline(){
    let pagamentoOnline: any = this.obtenhaPagamentoOnline();

    return pagamentoOnline && pagamentoOnline.codigoAutenticacao != null;
  }

  obtenhaContexto(urlRaiz: string) {
    let tipoDeEntrega = this.formaDeEntrega && this.formaDeEntrega.id === FormaDeEntrega.ENTREGA ? 'ENTREGA' : 'RETIRADA'

    return {
        codigoPedido: "#" + this.codigo.toString(),
        linkPedido: urlRaiz + '/pedido/chave/obtenhaPagamentoOnline' + this.guid,
        tipoDeEntrega: tipoDeEntrega
    }
  }

  feitoNaLoja(){
    return this.origem === EnumOrigemPedido.Loja
  }


  feitoNoIframe(){
    return !this.origem
  }

  feitoNoIfood(){
    return  this.origem === EnumOrigemPedido.Ifood
  }

  pagarViaPix() {
    if(!this.pagamentos || !this.pagamentos.length) return false;

    let pagamentoOnline = this. obtenhaPagamentoOnline();

    return pagamentoOnline ? pagamentoOnline.formaDePagamento.pix : false;
  }

  pagarComVaucher(){
    let pagamento = this.obtenhaPagamentoOnline();

    return pagamento && pagamento.formaDePagamento.voucher;
  }



  informouPagamento(){
    if(!this.pagamentos || !this.pagamentos.length) return false;

    return true;
  }

  obtenhaDataVenda() {
    if(this.horarioEntregaAgendada) return this.horarioEntregaAgendada;

    return this.horario;
  }

  pagamentoOnlineAprovado(){
    let pagamento = this.obtenhaPagamentoOnline();

    return pagamento && pagamento.foiAprovado();
  }


  estaEmContestacao(){
    return  this.disputa && !this.disputa.foiFinalizada()
  }

  foiCanceladoOuDevolvido() {
    return  Number(this.status ) ===  EnumStatusPedido.Cancelado ||  Number(this.status ) === EnumStatusPedido.Devolvido
  }

  foiEntregue() {
    return  Number(this.status ) === EnumStatusPedido.Entregue
  }

  ficouPronto(){
    return   Number( this.status)  === EnumStatusPedido.Pronto;
  }

  foiFinalizado(){
    return this.pago && Number( this.status)  === EnumStatusPedido.Entregue;
  }

  entrouEmPreparacao(){
    return   Number( this.status)  === EnumStatusPedido.EmPreparacao;
  }

  saiuParaEntrega(){
    return   Number( this.status)  === EnumStatusPedido.SaiuParaEntrega;
  }

  aindaNaoEntregou(){
    return  Number( this.status) <= 3
  }

  ehNovo(){
    return   Number( this.status)  === EnumStatusPedido.Novo;
  }

  obtenhaEmailCompra(){
    if(this.contato.email) return this.contato.email;

    let pagamento: PagamentoPedido = this.pagamentos.find((item: any) => item.email != null) as PagamentoPedido;

    return pagamento ? pagamento.email : '';
  }


  ultimoPagamentoNegado() {
    if(!this.pagarOnline()) return false

    let statusUltimo = this.pagamentos[this.pagamentos.length - 1].status.toString()

    return statusUltimo === EnumStatusPagamento.Negado.toString() ||
      statusUltimo === EnumStatusPagamento.Cancelado.toString()

  }

  gatewayPagamentoOnline() {
    let pagamentoOnline = this. obtenhaPagamentoOnline();

    if(pagamentoOnline)
      return pagamentoOnline.formaDePagamento.configMeioDePagamento.meioDePagamento

    return null;
  }


  ehDelivery() {
    return this.formaDeEntrega && this.formaDeEntrega.id === 2
  }

  retirarPessoalmente() {
    return this.formaDeEntrega && this.formaDeEntrega.id === 1
  }

  comerNaLoja(){
    return this.comerNoLocal && this.retirarPessoalmente();
  }

  ehMultipedido(){
    return false;
  }

  ehComanda(){
    return false;
  }

  fazParteMultipedido(){
    return false
  }

  aindaNaoSaiuEntrega(){
    return Number(this.status) === EnumStatusPedido.Novo || Number(this.status) === EnumStatusPedido.EmPreparacao ||
      Number(this.status) === EnumStatusPedido.Pronto
  }

  doIfood(){
    return this.origem === EnumOrigemPedido.Ifood
  }

  deBalcao(){
    return this.origem === EnumOrigemPedido.Balcao
  }

  notificarIood(){
    return this.doIfood() || (this.deliveryPedido && this.deliveryPedido.entregadorDoIfood())
  }

  integrouComIfood(){
    return this.doIfood() || (this.deliveryPedido && this.deliveryPedido.entregadorDoIfood() && !this.deliveryPedido.foiCancelado())
  }

  obtenhaTotalDescontoEmProdutos(){
    return (this.desconto || 0) +  (this.descontoFormaDePagamento || 0) + this.obtenhaCashback();
  }

  obtenhaCashback() {
    if( !this.pagamentos ) {
      return 0;
    }

    let valor = this.pagamentos.filter( (pagamento: any) => pagamento.foiPorCashback()).reduce(
      ( total: number,  pagamento: any) =>  (total + pagamento.valor) , 0);

    return Number(valor.toFixed(2))

  }

  abstract obtenhaMovimentacoesEstoque(estoqueVinculadoProduto: boolean): Array<MovimentacaoEstoqueInsumo>;

  aguardandoPagamentoOnline() {
    let pagamento: any = this.obtenhaPagamentoOnline();

    return pagamento && pagamento.aguardandoAprovar()
  }
}
