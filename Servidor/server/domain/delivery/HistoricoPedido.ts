import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorBasico} from "../../mapeadores/MapeadorBasico";
import {MapeadorDeHistoricoPedido} from "../../mapeadores/MapeadorDeHistoricoPedido";

export class HistoricoPedido extends ObjetoPersistente{
  horario: Date;
  constructor(public pedido: any, public descricao: string,
              public operador: any, public clienteApi: any ) {
    super();
    this.horario = new Date();
    if(this.descricao)
      this.descricao = this.descricao.substr(0, 255);
  }

  mapeador(): MapeadorBasico {
    return new MapeadorDeHistoricoPedido();
  }
}
