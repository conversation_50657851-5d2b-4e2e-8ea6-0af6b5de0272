import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorBasico} from "../../mapeadores/MapeadorBasico";
import {Produto} from "../Produto";
import {ItemPedido} from "./ItemPedido";
import {OpcaoDeAdicionalDeProduto} from "./OpcaoDeAdicionalDeProduto";
import {MapeadorDeValorDeAdicionaisEscolhaSimples} from "../../mapeadores/MapeadorDeValorDeAdicionaisEscolhaSimples";
import {Pedido} from "./Pedido";

export class ValorDeAdicionaisEscolhaSimples extends ObjetoPersistente {
  pedido: Pedido
  produto: Produto
  item: ItemPedido

  campo0: OpcaoDeAdicionalDeProduto;
  campo1: OpcaoDeAdicionalDeProduto;
  campo2: OpcaoDeAdicionalDeProduto;
  campo3: OpcaoDeAdicionalDeProduto;
  campo4: OpcaoDeAdicionalDeProduto;
  campo5: OpcaoDeAdicionalDeProduto;
  campo6: OpcaoDeAdicionalDeProduto;
  campo7: OpcaoDeAdicionalDeProduto;
  campo8: OpcaoDeAdicionalDeProduto;
  campo9: OpcaoDeAdicionalDeProduto;

  mapeador(): MapeadorBasico {
    return new MapeadorDeValorDeAdicionaisEscolhaSimples();
  }

  public convertaParaTela(): any {
    let retorno: any = {}
    for(let campo in this)
      if(campo.startsWith('campo') && this[campo])
        retorno[campo] = this[campo]


    return retorno;
  }

}
