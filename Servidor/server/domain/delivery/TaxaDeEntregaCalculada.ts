import { ZonaDeEntrega } from './../../domain/delivery/ZonaDeEntrega';
import { Resposta } from './../../utils/Resposta';
import { FormaDeEntregaEmpresa } from './../../domain/delivery/FormaDeEntregaEmpresa';
import { Endereco } from '../../domain/delivery/Endereco';
import { EnumTipoDeCobranca } from '../../domain/delivery/EnumTipoDeCobranca';

export class TaxaDeEntregaCalculada {
  id: number;
  fazEntrega: boolean;
  erro: string;
  valor: number;
  tipoDeCobranca: EnumTipoDeCobranca;
  distancia: number;
  localizacao = '';
  endereco = '';
  sucesso: boolean;
  hash = '';
  zonaDeEntrega: ZonaDeEntrega;
  simulacaoId: any;
  horario = new Date();
  taxaRetorno = 0
  calculadoPor: string = '';



  static freteGratis(tipoDeCobranca: EnumTipoDeCobranca, endereco: Endereco) {
    const taxa = new TaxaDeEntregaCalculada();
    taxa.fazEntrega = true;
    taxa.sucesso = true;
    taxa.tipoDeCobranca = tipoDeCobranca;
    taxa.endereco = endereco.obtenhaEnderecoCompleto();
    taxa.localizacao = endereco.localizacao;
    taxa.zonaDeEntrega = endereco.zonaDeEntrega;
    taxa.valor = 0.0;
    taxa.calculadoPor = 'frete-gratis-valor';

    return taxa;
  }

  static retirada() {
    const taxa = new TaxaDeEntregaCalculada();
    taxa.sucesso = true;
    taxa.fazEntrega = true;
    taxa.valor = 0.0;
    taxa.calculadoPor = 'retirada';

    return taxa;
  }

  static erro(erro: string, tipoDeCobranca: EnumTipoDeCobranca, distancia: number, endereco: Endereco) {
    const taxa = new TaxaDeEntregaCalculada();
    taxa.sucesso = false;
    taxa.erro = erro;
    taxa.tipoDeCobranca = tipoDeCobranca;
    taxa.distancia = distancia;
    taxa.endereco = endereco.obtenhaEnderecoCompleto();
    taxa.localizacao = endereco.localizacao;
    taxa.zonaDeEntrega = endereco.zonaDeEntrega;
    taxa.erro = erro;
    taxa.calculadoPor = 'erro';

    return taxa;
  }

  static naoEntrega(tipoDeCobranca: EnumTipoDeCobranca, distancia: number, endereco: Endereco) {
    const taxa = new TaxaDeEntregaCalculada();
    taxa.sucesso = true;
    taxa.fazEntrega = false;
    taxa.tipoDeCobranca = tipoDeCobranca;
    taxa.distancia = distancia;
    taxa.endereco = endereco.obtenhaEnderecoCompleto();
    taxa.localizacao = endereco.localizacao;
    taxa.zonaDeEntrega = endereco.zonaDeEntrega;
    taxa.erro = FormaDeEntregaEmpresa.MSG_NAO_ENTREGA;
    taxa.valor = 0;
    taxa.calculadoPor = 'nao-entrega';

    return taxa;
  }

  static entrega(tipoDeCobranca: EnumTipoDeCobranca, valor: number, taxaRetorno: number, distancia: number, endereco: Endereco,
                 calculadoPor: string,
                 simulacaoId: any = null) {
    const taxa = new TaxaDeEntregaCalculada();
    taxa.fazEntrega = true;
    taxa.sucesso = true;
    taxa.tipoDeCobranca = tipoDeCobranca;
    taxa.taxaRetorno = taxaRetorno || 0;
    taxa.valor = valor;
    taxa.distancia = distancia;
    taxa.endereco = endereco.obtenhaEnderecoCompleto();
    taxa.zonaDeEntrega = endereco.zonaDeEntrega;
    taxa.localizacao = endereco.localizacao;
    taxa.simulacaoId = simulacaoId;
    taxa.calculadoPor = calculadoPor;

    return taxa;
  }

  obtenhaResposta(): Resposta<any> {
    if( this.sucesso && this.fazEntrega ) {
      return Resposta.sucesso({
        localizacao: this.localizacao,
        taxaDeEntrega: this.valor,
        taxaRetorno: this.taxaRetorno,
        taxaCalculadaId: this.id,
        hash: this.hash
      });
    }

    const respErro = Resposta.erro(this.erro);
    respErro.data = this.localizacao;

    return respErro;
  }

  obtenhaTotalComRetorno(){
    return Number((this.valor + this.taxaRetorno).toFixed(2))
  }
}
