import * as moment from "moment";
import {MapeadorDePausaProgramada} from "../../mapeadores/MapeadorDePausaProgramada";

export class PausaProgramada {
  id: number;
  empresa: any;
  operadorCancelou: any;
  dataCancelamento: Date;
  cancelada: boolean;
  constructor(public descricao: string, public dataInicio: Date,
              public dataFim: Date, public operadorCadastrou: any = null, public mensagem: string = null) {

  }

  obtenhaMensagemFechado(){
    if(this.mensagem)  return this.mensagem;

    if(!this.dataFim) return  String(`Estamos fechados, voltamos em breve.`)

    let agora = new Date()

    let diffHoras = moment(this.dataFim).diff(moment(this.dataInicio), 'h');

    if(agora.getDay() )

    if(diffHoras <= 24)
      return String(`Estamos fechados, voltaremos ${agora.getDate() === new Date(this.dataFim).getDate() ? 'hoje' : 'amanhã'} às ${moment(this.dataFim).format('HH[h]:mm')}`);

    let inicio = moment(this.dataInicio).format('DD');
    let fim = moment(this.dataFim).format('DD') + " às " +  moment(this.dataFim).format('HH[h]:mm') ;

    return  String(`Estamos fechados do dia ${inicio} até o dia ${fim}, voltamos logo.`)

  }

  async cancelePorOperador(operador: any, empresa: any){
    this.operadorCancelou = operador;
    this.cancelada = true;
    this.dataCancelamento = new Date();
    this.empresa = empresa;
    console.log(`cancelado por: ${this.operadorCancelou.id}  - ${this.operadorCancelou.nome}`)
    await new MapeadorDePausaProgramada().removaAsync(this);
  }

  estaVigente(horario: any){
    if(moment(horario).isBefore(moment(this.dataInicio))) return false;

    if(!this.dataFim) return true;

    if(moment(horario).isAfter(moment(this.dataFim))) return false;

    return true;
  }

  async existeOutraEmConflito(idExistente: any){
    let query = {dataInicio: this.dataInicio, dataFim: this.dataFim, emConflito: true, idExistente: idExistente}
    let outras =
      await new MapeadorDePausaProgramada().listeAsync(query);

    return outras.length ? outras[0] : null;
  }
}
