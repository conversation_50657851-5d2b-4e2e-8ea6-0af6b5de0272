import {EnumStatusPagamento} from "../../lib/emun/EnumStatusPagamento";
import {ObjetoPersistente} from "../ObjetoPersistente";
import {EnumFormaPagamento, EnumFormaPagamentoLabel} from "../../lib/emun/EnumFormaPagamento";
import {FormaDePagamento} from "./FormaDePagamento";
import {CashbackReserva} from "./CashbackReserva";
import {EnumMeioDePagamento} from "./EnumMeioDePagamento";
import {EnumTipoPagamento} from "../../lib/integracao/pagamentos/EnumTipoPagamento";
import {EnumMetodoPagamento} from "../../lib/integracao/pagamentos/EnumMetodoPagamento";

export abstract class PagamentoGenerico  extends ObjetoPersistente{
  status: EnumStatusPagamento;
  formaDePagamento: FormaDePagamento;
  trocoPara: number
  valor: number;
  codigo: string;
  cashbackReserva: CashbackReserva;
  estornoId: string;

  foiNegado(){
    return Number(this.status) === EnumStatusPagamento.Negado
  }

  foiCancelado(){
    return Number(this.status) === EnumStatusPagamento.Cancelado
  }

  foiAprovado(){
    return  Number(this.status) === EnumStatusPagamento.Aprovado
  }

  foiEstornado(){
    return  Number(this.status) === EnumStatusPagamento.Reembolsado
  }

  foiGerado(){
    return  Number(this.status) === EnumStatusPagamento.Gerado
  }

  tentarNovamente(){
     return this.foiNegado() || this.foiCancelado();
  }

  reembolsoSolicitado(){
    return  Number(this.status) === EnumStatusPagamento.ReembolsoSolicitado
  }

  foiPorCashback(){
    return this.formaDePagamento.nome === EnumFormaPagamento.Cashback
  }

  foiPorResgate(){
    return this.formaDePagamento.nome === EnumFormaPagamento.Resgate
  }

  pagoPorPlanoFidelidade(){
    return this.foiPorResgate() || this.foiPorCashback()
  }

  foiPorDinheiro(){
    return this.formaDePagamento.nome === EnumFormaPagamento.Dinheiro || this.formaDePagamento.nome === 'ifood-loja-dinheiro'
  }

  foiPorPix(){
    return this.formaDePagamento.pix || this.formaDePagamento.nome === EnumFormaPagamento.Pix
  }

  hubPagarme(){
    return this.formaDePagamento && this.formaDePagamento.hubPagarme()
  }

  gatewayERede(){
    return this.formaDePagamento && this.formaDePagamento.ERede()
  }

  gatewayTunaPay(){
    return this.formaDePagamento && this.formaDePagamento.tunapay()
  }

  pagseguroConnect(){
    return this.formaDePagamento && this.formaDePagamento.pagseguroConnect()
  }

  integradoComGateway(){
    return this.hubPagarme() || this.pagseguroConnect()
  }

  podeEstornar(){
    return this.ehCieloCheckout() || this.ehCheckoutMercadoPago() || this.ehCheckoutPagseguro() || this.ehCheckoutPagseguroConnect() ||
           this.gatewayPagarme() || this.gatewayERede() || this.gatewayTunaPay()
  }

  ehCieloCheckout(){
    if(! this.formaDePagamento.configMeioDePagamento) return false;

    return this.formaDePagamento.configMeioDePagamento.meioDePagamento ===  EnumMeioDePagamento.CieloCheckout
  }


  ehCheckoutMercadoPago(){
    if(! this.formaDePagamento.configMeioDePagamento) return false;

    return this.formaDePagamento.configMeioDePagamento.meioDePagamento === EnumMeioDePagamento.MercadoPago
  }

  ehCheckoutPagseguro(){
    if(! this.formaDePagamento.configMeioDePagamento) return false;

    return this.formaDePagamento.configMeioDePagamento.meioDePagamento === EnumMeioDePagamento.Pagseguro
  }

  ehCheckoutPagseguroConnect(){
    if(! this.formaDePagamento.configMeioDePagamento) return false;

    return this.formaDePagamento.configMeioDePagamento.meioDePagamento === EnumMeioDePagamento.PagBankConnect
  }

  gatewayPagarme(){
    if(! this.formaDePagamento) return false;

    return this.formaDePagamento.gatewayPagarme()
  }



  prepago(){
    return this.foiOnline() || this.foiPorCashback()
  }

  foiOnline(){
    return this.formaDePagamento.online ||
      this.formaDePagamento.nome.startsWith('ifood-online') //todo: mudar para carregar pagamentoPdv.metodo === 'ONLINE'
  }

  foiPagoFidelidadeExterna(){
    return this.cashbackReserva != null;
  }

  enviarCashbackComoDesconto(totalEmProdutos: any){
    if(this.foiPagoFidelidadeExterna())
      return this.valor < totalEmProdutos;

    return this.foiPorCashback() && this.formaDePagamento.enviarComoDesconto;
  }


  obtenhaDescricao(){
    return  this.formaDePagamento ? this.formaDePagamento.descricao : ''
  }


  foiPixManual(){
    if(!this.formaDePagamento || !this.formaDePagamento.formaDePagamentoPdv)
      return false;

    return this.formaDePagamento.formaDePagamentoPdv.tipo === EnumTipoPagamento.MANUAL &&
          this.formaDePagamento.formaDePagamentoPdv.metodo === EnumMetodoPagamento.PIX;
  }


}
