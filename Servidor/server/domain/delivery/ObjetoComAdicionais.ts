import {ValorDeAdicionaisEscolhaSimples} from "./ValorDeAdicionaisEscolhaSimples";
import {ValorDeAdicionaisMultiplaEscolha} from "./ValorDeAdicionaisMultiplaEscolha";
import {ListaOpcoesEscolhidas} from "./ListaOpcoesEscolhidas";
import {ValorDeOpcaoMultiplaEscolha} from "./ValorDeOpcaoMultiplaEscolha";
import {ObjetoPersistente} from "../ObjetoPersistente";
import {EnumTipoDeCobrancaDeAdicional} from "../../lib/emun/EnumTipoDeCobrancaDeAdicional";
import {AdicionalDeProduto} from "./AdicionalDeProduto";
import {OpcaoDeAdicionalDeProduto} from "./OpcaoDeAdicionalDeProduto";

export abstract class ObjetoComAdicionais extends ObjetoPersistente {
  public adicionaisEscolhaSimples: ValorDeAdicionaisEscolhaSimples;
  public adicionaisMultiplaEscolha: ValorDeAdicionaisMultiplaEscolha;
  definicoesDosAdicionais: AdicionalDeProduto[]
  produtoTamanho: any;

  setAdicionais(adicionais: any, sabores: Array<any> = [], definicoesDosAdicionais: AdicionalDeProduto[] = []) {
    if(adicionais && Object.keys(adicionais).length){
      let adicionaisEscolhaSimples: ValorDeAdicionaisEscolhaSimples;
      let adicionaisMultiplaEscolha: ValorDeAdicionaisMultiplaEscolha;
      let definicaoDeAdicional: AdicionalDeProduto

      let index = 0, indiceLista = 0, indiceCampo = 0;

      for(definicaoDeAdicional of definicoesDosAdicionais) {
        if(definicaoDeAdicional.tipo === "multipla-escolha") {
          let nomeCampo = 'lista' + indiceLista++

          if(!adicionaisMultiplaEscolha)
            adicionaisMultiplaEscolha = new ValorDeAdicionaisMultiplaEscolha()

          let opcoesEscolhidas = adicionais[nomeCampo]

          if(opcoesEscolhidas) {

            let listaOpcoes: ListaOpcoesEscolhidas = new ListaOpcoesEscolhidas()
            listaOpcoes.opcoes = []
            listaOpcoes.tipoDeCobranca = opcoesEscolhidas.tipoDeCobranca
            listaOpcoes.ordem = index;

            for(let nomeOpcaoSelecionada in opcoesEscolhidas ) {
              if(nomeOpcaoSelecionada.startsWith('opcao')) {

                let dadosOpcaoSelecionada = opcoesEscolhidas[nomeOpcaoSelecionada]

                if(dadosOpcaoSelecionada.selecionada) {
                  let valorOpcao = new ValorDeOpcaoMultiplaEscolha()
                  valorOpcao.qtde = dadosOpcaoSelecionada.qtde ? dadosOpcaoSelecionada.qtde : 1

                 let opcaoDisponivel =   definicaoDeAdicional.opcoesDisponiveis ?
                     definicaoDeAdicional.opcoesDisponiveis.find( opcaoD => opcaoD.id === dadosOpcaoSelecionada.opcao.id) : null ;

               //  if(!opcaoDisponivel )
                  // throw Error(String(`Adicional inválido - remova do carrinho "${definicaoDeAdicional.nome}:
                  // ${dadosOpcaoSelecionada.opcao.nome}"`))

                  if(opcaoDisponivel){
                    valorOpcao.opcao = opcaoDisponivel ; //dadosOpcaoSelecionada.opcao
                    (valorOpcao.opcao as any).adicional = {
                      id: definicaoDeAdicional.id,
                      nome: definicaoDeAdicional.nome
                    }
                  } else {
                    valorOpcao.opcao = dadosOpcaoSelecionada.opcao
                  }

                  listaOpcoes.opcoes.push(valorOpcao)
                }
              }
            }
            if(listaOpcoes.opcoes.length > 0)
              adicionaisMultiplaEscolha[nomeCampo as keyof ValorDeAdicionaisMultiplaEscolha] = listaOpcoes
          }


        } else {
          if(!adicionaisEscolhaSimples)
            adicionaisEscolhaSimples = new ValorDeAdicionaisEscolhaSimples()

          let nomeCampo = 'campo' + indiceCampo++
          let opcao = adicionais[nomeCampo]

          if(opcao) {
            let opcaoDisponivel: any =
              definicaoDeAdicional.opcoesDisponiveis.find( opcaoD => opcaoD.id === opcao.id);

          //  if(!opcaoDisponivel)
           //   throw Error(String(`Adicional inválido - remova do carrinho "${definicaoDeAdicional.nome}: ${opcao.nome}"`))

            if(opcao.template && opcao.template.tamanho){
              if(!this.produtoTamanho )
                throw Error(String(`Nenhum tamanho informado para o opção ${opcao.nome}`))

              if(this.produtoTamanho.template.id !== opcao.template.tamanho.id)
                throw Error(String(`O adicional "${opcao.nome}" não pode ser vendido no tamanho  "${this.produtoTamanho.descricao}"`))
            }

            if(opcaoDisponivel){
              opcaoDisponivel.ordem = index;
              (opcaoDisponivel as any).adicional = {
                id: definicaoDeAdicional.id,
                nome: definicaoDeAdicional.nome
              }
              adicionaisEscolhaSimples[nomeCampo as keyof ValorDeAdicionaisEscolhaSimples] = opcaoDisponivel
            } else {
              opcao.ordem = index
              adicionaisEscolhaSimples[nomeCampo as keyof ValorDeAdicionaisEscolhaSimples] = opcao

            }

          }
        }

        index++;
      }

      this.adicionaisEscolhaSimples = adicionaisEscolhaSimples
      this.adicionaisMultiplaEscolha = adicionaisMultiplaEscolha
    }

    this.definicoesDosAdicionais = definicoesDosAdicionais;
  }

  obtenhaEscolhasTodosAdicionais(): any[] {
    let todosAdicionais: any = { }

    if(this.adicionaisEscolhaSimples) {
      for(let [key, value] of Object.entries(this.adicionaisEscolhaSimples)) {
        if(key.startsWith('campo')) {
          const valorAdicional: OpcaoDeAdicionalDeProduto = (value as OpcaoDeAdicionalDeProduto);

          let adicional: AdicionalDeProduto = valorAdicional.adicional

          //todo: bug promoçoes que acha adiconal em valorAdicional, busca direto no produto do item
          if(!adicional &&  (this as any).produto){
            let  camposAdicionais = (this as any).produto.camposAdicionais || [];
            adicional =  camposAdicionais.find((item: any) => item.id === (valorAdicional as any).idAdicional)
          }

          if(!adicional)
            throw Error(`valor da Opçao Simples "${valorAdicional.nome}" sem adicional no campo ${key}`)

          todosAdicionais[adicional.id] = {
            ordem: adicional.ordem,
            adicional: adicional,
            opcoesEscolhidas: [ {
              qtde: 1,
              opcao: valorAdicional,
            }]
          }
        }
      }
    }

    if(this.adicionaisMultiplaEscolha) {
      for (let [campo, value] of Object.entries(this.adicionaisMultiplaEscolha)) {
        if (campo.startsWith('lista')) {
          let lista: ListaOpcoesEscolhidas = value as ListaOpcoesEscolhidas

          if(lista.opcoes.length === 0) continue;
          let opcao: any = lista.opcoes[0].opcao;

          let adicional = opcao.adicional

          //todo: bug promoçoes que acha adiconal em valorAdicional, busca direto no produto do item
          if(!adicional &&  (this as any).produto){
            let  camposAdicionais = (this as any).produto.camposAdicionais || [];
            adicional =  camposAdicionais.find((item: any) => item.id === (opcao as any).idAdicional)
          }

          if(!adicional)
            throw Error(`valor da Opçao Multipla escolha "${opcao.nome}" sem adicional no campo ${campo}`)

          let objetoValor: any = {
            ordem: lista.ordem,
            adicional: adicional,

          }

          let opcoesEscolhidas: any[] = []

          for (let valorOpcao of lista.opcoes) {
            opcoesEscolhidas.push({
              qtde: valorOpcao.qtde,
              opcao: valorOpcao.opcao
            })
          }

          objetoValor.opcoesEscolhidas = opcoesEscolhidas

          todosAdicionais[adicional.id] = objetoValor
        }
      }
    }

    return todosAdicionais
  }

  obtenhaAdicionaisImprimir(): any {
    let adicionaisImprirmir = [];

    let mapaDeAdicionais: any = {}

    if(this.definicoesDosAdicionais)
      for(let adicional of this.definicoesDosAdicionais)
        mapaDeAdicionais[adicional.id] = adicional

    if (this.adicionaisEscolhaSimples) {
      for (let [key, value ]  of Object.entries(this.adicionaisEscolhaSimples)) {
        if (key.startsWith('campo')) {
          const valorAdicional: any = (value as any),
            adicional = valorAdicional.adicional;

          let ordemAdicional =  null;

          if(adicional )
             ordemAdicional = adicional.adicionalNoProduto ? adicional.adicionalNoProduto.ordem : adicional.ordem;

          let descricao =  String(`1x ${valorAdicional.nome}`),
            ordem = (!valorAdicional.ordem && ordemAdicional) ? ordemAdicional : valorAdicional.ordem,
            codigoPdv = valorAdicional.codigoPdv;

          let massaOuBorda =  valorAdicional.adicional &&  ( valorAdicional.adicional.nome === 'Massa' ||
            valorAdicional.adicional.nome === 'Borda');

          if( massaOuBorda)
            descricao = String(`${valorAdicional.adicional.nome}: ${valorAdicional.nome}`)

          let valor;

          let objeto: any = this;
          let produto = objeto['produto'];
          if(produto && produto.catalogo && produto.catalogo.precoPorEmpresa)
            valor = valorAdicional.opcaoNaEmpresa && valorAdicional.opcaoNaEmpresa.valor  ? valorAdicional.opcaoNaEmpresa.valor
              : valorAdicional.valor
          else
            valor = valorAdicional.valor

          valorAdicional.valor = valor;

          adicionaisImprirmir.push({
            id: valorAdicional.id,
            ordem: ordem,
            qtde: 1,
            nome: valorAdicional.nome,
            adicional: valorAdicional.adicional ? {
              id: valorAdicional.adicional.id,
              nome: valorAdicional.adicional.nome
            } : null,
            insumo: valorAdicional.insumo,
            preco: valorAdicional.valor,
            estaDisponivelFn: (typeof value.estaDisponivel === 'function') ?  value.estaDisponivel.bind(value) : null,
            descricao: descricao,
            codigoPdv: codigoPdv,
            massaOuBorda: massaOuBorda
          })
        }
      }
    }

    if (this.adicionaisMultiplaEscolha) {
      for (let [campo, value] of Object.entries(this.adicionaisMultiplaEscolha)) {
        if (campo.startsWith('lista')) {
          let listaAdicionais = []
          for (let [key, valorOpcao] of Object.entries((value as any).opcoes)) {
            let adicionalDaOpcao = mapaDeAdicionais && mapaDeAdicionais[(valorOpcao as any).opcao.idAdicional] ? {
                id: mapaDeAdicionais[(valorOpcao as any).opcao.idAdicional].id,
                nome: mapaDeAdicionais[(valorOpcao as any).opcao.idAdicional].nome
              } : null

              let opcao: any = (valorOpcao as any).opcao;

            opcao.valor = opcao.opcaoNaEmpresa && opcao.opcaoNaEmpresa.valor  ? opcao.opcaoNaEmpresa.valor
              : opcao.valor

              let adicionalImprimir: any = {
                id:  opcao.id,
                ordem: value.ordem ? value.ordem : 0,
                qtde: (valorOpcao as any).qtde,
                insumo: opcao.insumo,
                nome: opcao.nome,
                preco: opcao.valor,
                estaDisponivelFn: (typeof opcao.estaDisponivel === 'function') ?  opcao.estaDisponivel.bind(opcao)   : null,
                adicional: adicionalDaOpcao,
                codigoPdv: opcao.codigoPdv,
                descricao: String(`${(valorOpcao as any).qtde}x ${opcao.nome}`)
              }

              listaAdicionais.push(adicionalImprimir)
          }

          if(value.tipoDeCobranca === EnumTipoDeCobrancaDeAdicional.MAIOR)
              this.setPrecosCalculadosDoMaior(listaAdicionais)
          else if (value.tipoDeCobranca === EnumTipoDeCobrancaDeAdicional.MEDIA)
            this.setPrecosCalculadosDaMedia(listaAdicionais)

          listaAdicionais.forEach((item: any) => adicionaisImprirmir.push(item))
        }
      }
    }

    adicionaisImprirmir.sort((a: any, b: any) => {
      return a.ordem - b.ordem
    })

    return adicionaisImprirmir;
  }

  obtenhaValorAdicionais(){
    let valor = 0;

    if(this.adicionaisEscolhaSimples) {
      let nomeCampo: keyof ValorDeAdicionaisEscolhaSimples
      for(nomeCampo in this.adicionaisEscolhaSimples)
        if(nomeCampo.startsWith('campo'))
          valor += this.adicionaisEscolhaSimples[nomeCampo].valor
    }

    if(this.adicionaisMultiplaEscolha) {
      let nomeLista: keyof ValorDeAdicionaisMultiplaEscolha
      for(nomeLista in this.adicionaisMultiplaEscolha)
        if(nomeLista.startsWith('lista')) {
          let valorAdicional = 0
          let lista = this.adicionaisMultiplaEscolha[nomeLista]

          let tipoDeCobranca = lista.tipoDeCobranca

          switch(tipoDeCobranca) {
            case EnumTipoDeCobrancaDeAdicional.SOMA:
              for(let valorDeOpcao of lista.opcoes)
                valorAdicional += valorDeOpcao.qtde * valorDeOpcao.opcao.valor
              break;
            case EnumTipoDeCobrancaDeAdicional.MEDIA:
              let tot = 0 // lista.opcoes.length

              for(let valorDeOpcao of lista.opcoes) {
                tot += valorDeOpcao.qtde
                valorAdicional += valorDeOpcao.qtde * valorDeOpcao.opcao.valor
              }


              if(tot > 0)
                valorAdicional = +((valorAdicional / tot).toFixed(2));
              break;
            case EnumTipoDeCobrancaDeAdicional.MAIOR:
              for(let valorDeOpcao of lista.opcoes)
                if((valorDeOpcao.opcao.valor * valorDeOpcao.qtde) > valorAdicional)
                  valorAdicional = valorDeOpcao.qtde * valorDeOpcao.opcao.valor
              break;
          }

          valor += valorAdicional
        }

    }

    return valor;
  }

  private setPrecosCalculadosDoMaior(listaAdicionais: any[]) {
    let itemMaiorPreco: any;

    listaAdicionais.forEach((item: any) => {
      if(!itemMaiorPreco || item.preco > itemMaiorPreco.preco)
        itemMaiorPreco = item
    })

    listaAdicionais.forEach((item: any) => {
      item.precoCalculado = item.id ===  itemMaiorPreco.id ? itemMaiorPreco.preco : 0;
    })
  }

  private setPrecosCalculadosDaMedia(listaAdicionais: any[]){
    let totalAdicionais =  listaAdicionais.reduce((sum: number, adicional: any) => sum + adicional.preco, 0);
    let qtdeAdicionais =  listaAdicionais.reduce((sum: number, adicional: any) => sum + adicional.qtde, 0);

    let valorMedio = Number( totalAdicionais / listaAdicionais.length).toFixed(2);

    let qtdeMedia =  1 / qtdeAdicionais;
    let qtdeDistribuida = 0;


    for(let i = 0; i < listaAdicionais.length; i++ ){
      if(i + 1 === listaAdicionais.length){ // ultimo, garantir soma seja sempre 1
        listaAdicionais[i].qtdeCalculada = Number( (1 - qtdeDistribuida).toFixed(2))
      } else {
        listaAdicionais[i].qtdeCalculada = Number(qtdeMedia.toFixed(2)) *  listaAdicionais[i].qtde;
        qtdeDistribuida +=    listaAdicionais[i].qtdeCalculada;
      }

      listaAdicionais[i].precoCalculado = valorMedio;
    }
  }
}
