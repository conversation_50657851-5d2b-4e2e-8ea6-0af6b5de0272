import {PedidoGenerico} from "./PedidoGenerico";

import {MapeadorBasico} from "../../mapeadores/MapeadorBasico";
import {Contato} from "../Contato";
import {Pedido} from "./Pedido";
import {EnumStatusPedido} from "../../lib/emun/EnumStatusPedido";
import {FormaDeEntrega} from "./FormaDeEntrega";
import {EnumOrigemPedido} from "../../lib/emun/EnumOrigemPedido";
import {Endereco} from "./Endereco";
import {Produto} from "../Produto";
import {PedidoUtis} from "../../lib/PedidoUtis";
import {MapeadorDeMultipedido} from "../../mapeadores/MapeadorDeMultiPedido";
import {MapeadorDePedido} from "../../mapeadores/MapeadorDePedido";
import {ItemPedido} from "./ItemPedido";
import {PagamentoPedido} from "./PagamentoPedido";
// @ts-ignore
import uuid = require("uuid");
import {MapeadorDeCupom} from "../../mapeadores/MapeadorDeCupom";
import {Cupom} from "../faturamento/Cupom";
import {Resposta} from "../../utils/Resposta";
import {Empresa} from "../Empresa";
import {MovimentacaoEstoqueInsumo} from "../estoque/MovimentacaoEstoqueInsumo";
import {EnumTipoMovimentacao} from "../../lib/estoque/EnumTipoMovimentacao";

export class MultiPedido extends  PedidoGenerico {
  pedidos: Array<Pedido> = [];
  tipo: string;
  public pagamentos: Array<PagamentoPedido>;
  constructor(empresa: any, contato: Contato , endereco: Endereco,
              formaDeEntrega: FormaDeEntrega,      taxaEntrega: number,  observacoes: string,   origem: EnumOrigemPedido ) {
    super()
    this.status = EnumStatusPedido.Novo;
    this.empresa = empresa;
    this.contato = contato;
    this.endereco = endereco;
    this.observacoes = observacoes
    this.formaDeEntrega = formaDeEntrega;
    this.valor = 0;
    this.taxaEntrega = taxaEntrega || 0;
    this.desconto =   0 ;
    this.descontoTaxaEntrega = 0;
    this.impresso = false;
    this.pago = false;
    this.pontosGanhos = 0;
    this.origem = origem;
    this.guid = uuid();
    this.tipo = 'multipedido';
    this.aceito  =   empresa && empresa.aceitarPedidoAutomatico;
  }

  obtenhaItensPedido(): Array<ItemPedido>{
    let itens: any = []

    this.pedidos.forEach((pedido: any) => {
      pedido.itens.forEach((item: any) => {
        itens.push(item)
      })
    })

    return itens;
  }

  async apliqueCupom(codigo: string, validarRestricoesContato = true) {
    let  totalDescontos = 0, errosValidacao = []

    for(let i = 0; i < this.pedidos.length; i++){
      let pedido: any = this.pedidos[i];

      let cupom: Cupom = await  new MapeadorDeCupom().selecioneDaEmpresa(pedido.empresa.id, codigo);

      if(cupom) {
        let erro: string = await cupom.valideUso(pedido, validarRestricoesContato);

        if(!erro) {
          let descontoCupom = pedido.apliqueDescontoCupom(cupom);

          totalDescontos += descontoCupom;

          this.cupom = Object.assign({}, cupom);
        } else {
          errosValidacao.push(erro)
        }
      }
    }


    if(errosValidacao.length)  return Resposta.erro(errosValidacao.join(', '));

    if(!this.cupom)  return Resposta.erro('Cupom não encontrado');


    return Resposta.sucesso(totalDescontos)
  }


  obtenhaPedidos(){
    let pedidos: any = [];

    for(let i = 0 ; i < this.pedidos.length; i++){
      let pedido: Pedido = this.pedidos[i];

      pedido.endereco = this.endereco;

      pedido.multipedido = {
        id: this.id,
        codigo: this.codigo,
        empresa: this.empresa,
        pedidos: this.pedidos
      }

      pedidos.push(pedido)
    }

    return pedidos;
  }

  adicioneItemAoPedido(empresa: Empresa, produto: Produto, qtde: number, observacao: string,
                       adicionais: any, produtoTamanho: any, sabores: any){

    let pedido = this.pedidos.find((_pedido: any) => _pedido.empresa.catalogo.id === empresa.catalogo.id)

    if(!pedido){
      let contato = Object.assign(Contato.novo(), this.contato);
      contato.empresa = empresa;
      let endereco ;

      delete contato.id;

      if(this.ehDelivery()   ){
        endereco =  Object.assign(Endereco.novo() , this.endereco);
        endereco.contato = contato;
        delete endereco.id;
      }

      pedido = new Pedido(empresa,  null, contato, endereco, this.formaDeEntrega,
        0, 0, [], this.observacoes, EnumOrigemPedido.Loja);

      pedido.multipedido = {};
      this.pedidos.push(pedido)
    }

    pedido.adicioneProduto(produto, qtde, observacao, adicionais, produtoTamanho, sabores)

    return pedido;
  }

  calculeValor() {
    this.pedidos.forEach((pedido: any) => {
      pedido.calculeValor();
    })

    this.valor = this.obtenhaTotalPedidos();
    this.desconto = 0;

    for(let i = 0; i < this.pedidos.length ; i++){
      this.desconto += this.pedidos[i].desconto;
      this.descontoTaxaEntrega += this.pedidos[i].descontoTaxaEntrega;
    }

    if(this.descontoTaxaEntrega){
      this.taxaEntrega -= this.descontoTaxaEntrega;
      this.taxaEntrega = Number(this.taxaEntrega.toFixed(2));
    }

    if(this.desconto > 0)
      this.desconto =  Number(this.desconto.toFixed(2));

  //  const valorAdicionais = this.obtenhaValorAdicionais();
   // this.valor += valorAdicionais;
    this.valor = Number(this.valor.toFixed(2));
    this.taxaFormaDePagamento = 0;
  }

  calculeValorSemDescontos() {
    this.pedidos.forEach((pedido: any) => {
      pedido.calculeValorSemDescontos();
    })

    this.valor = this.obtenhaTotalPedidos();

    const valorAdicionais = this.obtenhaValorAdicionais();

    this.valor += valorAdicionais;
    this.valor = Number(this.valor.toFixed(2));
  }

  obtenhaTotalPedidos(){
    let total = 0

    for(let pedido of this.pedidos){
      total += pedido.valor;
    }

    return total;
  }


  mapeador(): MapeadorBasico {
    return new MapeadorDePedido();
  }

  async setCodigos(){
    this.codigo =  await PedidoUtis.obtenhaProximoCodigo(this.empresa);

    for(let i = 0; i < this.pedidos.length; i++)
      this.pedidos[i].codigo = await PedidoUtis.obtenhaProximoCodigo( this.pedidos[i].empresa);

  }

  setHorarioEntregaAgendada(horarioEntrega: Date ) {
    this.horarioEntregaAgendada = horarioEntrega;
    this.pedidos.forEach((pedido: any) => {
      pedido.horarioEntregaAgendada =  this.horarioEntregaAgendada
    })
  }

  ehMultipedido(){
    return true;
  }

  // tslint:disable-next-line:member-ordering
  static async get(query: any = {}){
    return new MapeadorDeMultipedido().selecioneSync(query)
  }

  processeTaxasPedidos() {
    if( this.pedidos.length){
      let pedidoPrincipal = this.pedidos.find((item: any) => item.empresa.id === this.empresa.id);

      if(!pedidoPrincipal)
        pedidoPrincipal = this.pedidos[0]

      pedidoPrincipal.taxaEntrega = this.taxaEntrega;
      pedidoPrincipal.taxaFormaDePagamento = this.taxaFormaDePagamento;
    }

  }

  obtenhaMovimentacoesEstoque(estoqueVinculadoProduto: boolean){
    let movimentacoes: any = [];

    this.pedidos.forEach((pedido: any) => {
      pedido.itens.forEach((item: ItemPedido) => {
        item.getMovimentacoes(estoqueVinculadoProduto, pedido, movimentacoes)
      })
    })

    return movimentacoes;
  }
}
