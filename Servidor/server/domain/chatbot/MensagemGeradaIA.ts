import {Empresa} from "../Empresa";
import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorBasico} from "../../mapeadores/MapeadorBasico";
import {MapeadorDeMensagemGeradaIA} from "../../mapeadores/MapeadorDeMensagemGeradaIA";

export class MensagemGeradaIA extends ObjetoPersistente {
  nome: string;
  prompt: string;
  mensagem: string;
  ativo: boolean;
  dataCriacao: Date = new Date();

  mapeador(): MapeadorBasico {
    return new MapeadorDeMensagemGeradaIA();
  }
}
