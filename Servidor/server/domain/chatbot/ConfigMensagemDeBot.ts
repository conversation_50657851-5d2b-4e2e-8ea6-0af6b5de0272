import {TipoDeMensagemBotEnum} from "./TipoDeMensagemBotEnum";
import {Empresa} from "../Empresa";
import {Contato} from "../Contato";
import {RespostaEncurtarLinks} from "../../utils/RespostaEncurtarLinks";
import {LinkEncurtado} from "../LinkEncurtado";

export class ConfigMensagemDeBot {
  id: string;
  mensagem: string;
  empresa: Empresa;
  tipoDeMensagem: TipoDeMensagemBotEnum;
  encurtarLinks: boolean;

  static Nova(mensagem: string, empresa: Empresa, tipoDeMensagem: TipoDeMensagemBotEnum,
              encurtarLinks: boolean = true): ConfigMensagemDeBot {
    const mensagemDeBot = new ConfigMensagemDeBot();

    mensagemDeBot.mensagem = mensagem;
    mensagemDeBot.empresa = empresa;
    mensagemDeBot.tipoDeMensagem  = tipoDeMensagem;
    mensagemDeBot.encurtarLinks = encurtarLinks;

    return mensagemDeBot;
  }

  obtenhaMensagemProcessada(empresa: Empresa, contato: Contato, contexto: any =  {}): Promise<RespostaEncurtarLinks> {
    return this.processe(empresa, contato, contexto);
  }


  processe(empresa: Empresa, contato: Contato, contexto: any = {}): Promise<RespostaEncurtarLinks> {
    let msgFinal = this.mensagem;

    contexto.contato = contato;
    contexto.empresa = empresa;

    const reg = /\[(.*?)\]/g;
    let result;
    while ((result = reg.exec(this.mensagem)) !== null) {
      //console.log(result[0]);
      const campo = result[0];

      let fnExpressao = ValorDoCampoDeBot.get(campo);

      let valorCampo = fnExpressao ? fnExpressao(contexto) : '';

      msgFinal = msgFinal.replace(campo, valorCampo);
    }

    if(!this.encurtarLinks)
      return Promise.resolve(RespostaEncurtarLinks.naoEncurtar(msgFinal))

    return LinkEncurtado.encurteLinksDaMensagem(msgFinal);
  }

  // tslint:disable-next-line:member-ordering
  static MensagemMenuPrincipal = `Bem-vindo à [Empresa] Seu telefone é: [TelefoneContato]. Sou um robô 🤖 que está aqui para te atender.` +
    ` Você pode fazer um pedido *pelo nosso cardápio* [Link_Cardapio] ou diretamente aqui comigo.

O que você deseja (Escolha uma opção no Menu abaixo)?

*1* - Você pode fazer um pedido,
*Sair* - Falar com um atendente.

*Atenção*: Para escolher uma opção, é só mandar o número do item correspondente ou 'Sair' para falar com um atendente.
`
}

export const ValorDoCampoDeBot = new Map<string, Function>([

  ['[Empresa]',  (contexto: any) =>  contexto.empresa.nome],
  ['[TelefoneContato]', (contexto: any) => contexto.contato.telefone ] ,
  ['[Link_Cardapio]', (contexto: any) => {
    return contexto.empresa.obtenhaLinkLoja(true)
  }],
  ['[Nova_Mensagem]', (contexto: any) => {
    return '[Nova_Mensagem]'
  }],
  ['[NomeContato]', (contexto: any) => {
    if( !contexto.contato.nome ) {
      return 'Cliente';
    }

    let nome = contexto.contato.nome.trim();

    const primeiroNome = nome.replace(/ .*/, '');

    if( primeiroNome.length === 0 ) {
      return '';
    }

    return primeiroNome[0].toUpperCase() + primeiroNome.slice(1).toLowerCase();
  }],
]);
