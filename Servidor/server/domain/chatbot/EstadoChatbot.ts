import { SessaoLinkSaudacao } from "../SessaoLinkSaudacao";
import {EnumEstadoChatbot} from "./EnumEstadoChatbot";

export class EstadoChatbot {
  id: number;
  telefone: string;
  empresa: string;
  ultimaInteracao: Date;
  mensagem: string;
  intent: string;
  comando: string;
  resposta: string;
  atendente: boolean;
  historico: any = '';
  desativarParaSempre: boolean;
  estado: EnumEstadoChatbot = EnumEstadoChatbot.INICIAL;
  carrinho: any; //salvar carrinho como json

  idFluxoTypebot: string;
  idSessaoTypebot: string;
  idResultadoTypebot: string;

  endereco: string;
  sessaoLinkSaudacao: SessaoLinkSaudacao;

  /*
  INICIAL = 'INICIAL',
  FAZENDO_PEDIDO = 'FAZENDO_PEDIDO',
  ESCOLHEU_PRODUTO = 'ESCOLHEU_PRODUTO',
  CONFIRMANDO_PEDIDO = 'CONFIRMANDO_PEDIDO',
  PEDIDO_CONFIRMADO = 'PEDIDO_CONFIRMADO',
   */

  constructor() {
  }

  public deveEnviarNovaMensagemDeSaudacao(tempoMensagemSaudacao: number): boolean {
    const agora: Date = new Date();
    const ultimaInteracao: Date = this.ultimaInteracao;

    // calcula a diferença em milissegundos entre a hora atual e a última interação
    const diferencaMilissegundos: number = agora.getTime() - ultimaInteracao.getTime();

    // calcula a diferença em horas
    const diferencaHoras: number = diferencaMilissegundos / (1000 * 60 * 60.0);

    // retorna true se a diferença for maior do que 12 horas, false caso contrário
    return diferencaHoras > tempoMensagemSaudacao;
  }

  ehPerguntaChamarAtendente() {
    return this.comando === 'ATENDENTE';
  }

  obtenhaHistorico(tamanho = 1) {
    if (!this.historico || this.historico === '' || true) {
      return [];
    }

    let objHistorico = JSON.parse(this.historico);

    // Limita o tamanho do histórico, se necessário
    if (tamanho > 0 && objHistorico.length > tamanho) {
      objHistorico = objHistorico.slice(-tamanho);
    }

    console.log('\n\n\n');
    console.log('tamanho do historico: ', tamanho);
    console.log('\n\n\n');

    return objHistorico;
  }

  adicioneAoHistorico(nomeBot: string, funcao: { nome: string, parametros: string } = null, respostaFuncao: { nome: string, resposta: string} = null) {
    let objHistorico = null;

    if( !this.historico || this.historico === '') {
      objHistorico = [];
    } else {
      objHistorico = [];
    }

    if( funcao ) {
      if( this.mensagem ) {
        objHistorico.push({role: 'user', content:   this.mensagem});
      }
      objHistorico.push({"role": "assistant", "content": null, "function_call": {"name": funcao.nome, "arguments": funcao.parametros}});
      //objHistorico.push({role: "function", "name": respostaFuncao.nome, "content": respostaFuncao.resposta});
      //objHistorico.push({role: 'assistant', content: this.resposta});
    } else {
      if( this.mensagem ) {
        objHistorico.push({role: 'user', content: this.mensagem});
      }
      if( this.resposta ) {
        objHistorico.push({role: 'assistant', content: this.resposta});
      }
    }

    this.historico = JSON.stringify(objHistorico);
  }

  adicioneAoHistoricoAssistant(role: string, resposta: string) {
    let objHistorico = null;

    if( !this.historico || this.historico === '') {
      objHistorico = [];
    } else {
      objHistorico = JSON.parse(this.historico);
    }


    objHistorico.push({role: role, content: resposta});

    this.historico = JSON.stringify(objHistorico);
  }

  obtenhaCarrinho() {
    if( !this.carrinho ) {
      this.carrinho = {
        items: [],
        itemPendente: {},
        totalProdutos: 0.0,
        total: 0.0,
        taxaDeEntrega: null,
        endereco: {}
      };;
    }

    return this.carrinho;
  }
}
