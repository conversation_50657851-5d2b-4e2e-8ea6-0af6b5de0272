import {Empresa} from "../Empresa";
import {EnumStatusMia} from "./EnumStatusMia";
import {EnumComportamentoForaDoEscopo} from "./EnumComportamentoForaDoEscopo";
import {EstadoChatbot} from "./EstadoChatbot";

export class ConfiguracoesMia {
  id: number;
  empresa: Empresa
  nome: string;
  status: EnumStatusMia;
  telefonesTeste: string;
  comportamentoForaDoEscopo: EnumComportamentoForaDoEscopo;
  tempoPausaMia: number; //em segundos

  tempoRecuperarCarrinho: number; //em segundos
  recuperarCarrinho: boolean;

  responderSobreProdutos: boolean = false;

  modelo: string = 'Basico';
  dataInicioTrial: Date;
  dataFimTrial : Date;

  usarFluxoTypebot: boolean;
  idFluxoTypebotWhatsapp: string = '';
  idFluxoTypebotInstagram: string = '';
  chaveApiTypebot: string = '';
  typebotConfigurado: boolean = false;
  workspaceId: string = '';
  publicIdFluxoWhatsapp: string = '';
  publicIdFluxoInstagram: string = '';

  deveReativarMia(estadoChatbot: EstadoChatbot): boolean {
    const agora = new Date();
    const tempoPausaMiaMilissegundos = this.tempoPausaMia * 1000;
    const tempoDecorridoMilissegundos = agora.getTime() - estadoChatbot.ultimaInteracao.getTime();

    return tempoDecorridoMilissegundos >= tempoPausaMiaMilissegundos;
  }

  obtenhaTamanhoHistorio() {
    if( this.modelo === 'Basico' ) {
      return 2;
    }

    if( this.modelo === 'Intermediario' ) {
      return 6;
    }

    return 30;
  }
}
