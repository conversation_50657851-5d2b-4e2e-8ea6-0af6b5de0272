import {Empresa} from "../Empresa";
import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorBasico} from "../../mapeadores/MapeadorBasico";
import {MapeadorDeTraducaoMensagemChatBot} from "../../mapeadores/MapeadorDeTraducaoMensagemChatBot";

export class TraducaoMensagemBot extends ObjetoPersistente {
  nome: string;
  template: string;
  mensagem: string;
  empresa: Empresa;
  ativo = false;


  mapeador(): MapeadorBasico {
    return new MapeadorDeTraducaoMensagemChatBot();
  }
}
