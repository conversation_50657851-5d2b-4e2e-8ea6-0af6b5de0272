import {Empresa} from "../Empresa";
import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorBasico} from "../../mapeadores/MapeadorBasico";
import {MapeadorDeTraducaoMensagemChatBot} from "../../mapeadores/MapeadorDeTraducaoMensagemChatBot";
import {MapeadorDeMensagemPadraoBot} from "../../mapeadores/MapeadorDeMensagemPadraoBot";

export class MensagemPadraoBot extends ObjetoPersistente {
  nome: string;
  template: string;
  mensagem: string;
  ativo: boolean;

  mapeador(): MapeadorBasico {
    return new MapeadorDeMensagemPadraoBot();
  }
}
