import {Usua<PERSON>} from "../Usuario";
import {GeradorDeIdentificador} from "../../utils/GeradorDeIdentificador"

export class AccessToken {
  private usuario: Usuario;
  private clientId: string;
  private expirado: boolean;
  private dataCriacao: Date;
  private token: string;

  constructor(usuario: Usuario, clientId: string = null) {
    this.usuario = usuario
    this.clientId = clientId || ''
    this.expirado = false;
    this.dataCriacao = new Date()
    this.token = GeradorDeIdentificador.gere();
  }
}

