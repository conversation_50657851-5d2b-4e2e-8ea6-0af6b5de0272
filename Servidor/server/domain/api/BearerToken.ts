import {Usua<PERSON>} from "../Usuario";
import {GeradorDeIdentificador} from "../../utils/GeradorDeIdentificador";


export class BearerToken {
  private id: number;
  private token: string;
  private dataCriacao: Date;

  constructor(private cliente: any = null, private usuario: Usuario = null) {
    this.dataCriacao = new Date()
    this.token = GeradorDeIdentificador.gere()
  }

  public getToken() {
    return this.token
  }



}
