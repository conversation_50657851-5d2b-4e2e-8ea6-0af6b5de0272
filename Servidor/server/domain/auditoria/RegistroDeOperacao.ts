import {EnumOperacao} from "./EnumOperacao";
import {Usua<PERSON>} from "../Usuario";

export class RegistroDeOperacao {
  public id: number
  public empresa: any;
  constructor(public descricao: string, public operacao: EnumOperacao, public horario: Date,
              public usuario: Usuario, public  clienteApi: any, public ip: string,
              public idObjeto: any = null, public tipoObjeto: string = null, public valorNovo: string = null) {

  }

}
