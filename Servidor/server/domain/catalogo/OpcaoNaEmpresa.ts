import {Empresa} from "../Empresa";

export class OpcaoNaEmpresa {
  id: number
  constructor(public opcao: any, public empresa: Empresa,
              public valor: number = null, public disponivel: boolean = false)  {
    if(opcao && !valor) this.valor = opcao.valor
  }
}

/*
        tem_estoque = #{temEstoque},
        exibir_preco_site = #{exibirPrecoSite},
        exibir_preco_no_cardapio = #{exibirPrecoNoCardapio},
        exibir_no_site = #{exibirNoSite},
        disponibilidade = #{disponibilidade},
        disponivel_na_mesa = #{disponivelNaMesa},
        disponivel_para_delivery = #{disponivelParaDelivery},
        nao_sincronizar = #{naoSincronizar},
        mensagem_pedido = #{mensagemPedido}
 */
