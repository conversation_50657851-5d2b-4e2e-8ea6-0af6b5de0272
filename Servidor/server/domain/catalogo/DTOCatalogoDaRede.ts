import {CatalogoDaRede} from "./CatalogoDaRede";

export class DTOCatalogoDaRede {
  id: number;
  idCatalogo: number;
  nome: string;
  ativo: boolean;
  precoPorEmpresa: boolean;
  compartilhado: boolean
  disponibilidadePorEmpresa: boolean;
  categorias: any[];



  //Usuário que criou
  rede: string;

  constructor(catalogoDaRede: CatalogoDaRede) {
    this.id = catalogoDaRede.id
    this.rede = catalogoDaRede.rede
    this.idCatalogo = catalogoDaRede.catalogo.id
    this.nome = catalogoDaRede.catalogo.nome
    this.ativo = catalogoDaRede.catalogo.ativo
    this.categorias = catalogoDaRede.catalogo.categorias
    this.precoPorEmpresa = catalogoDaRede.catalogo.precoPorEmpresa
    this.compartilhado = catalogoDaRede.catalogo.compartilhado
    this.disponibilidadePorEmpresa = catalogoDaRede.catalogo.disponibilidadePorEmpresa
  }
}
