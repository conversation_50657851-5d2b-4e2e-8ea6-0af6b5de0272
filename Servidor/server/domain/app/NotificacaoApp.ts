import {Empresa} from "../Empresa";
import {EnumStatusCampanha} from "../EnumStatusCampanha";
import {Filtro} from "../Filtro";
import {EnumTipoDeEnvioCampanha} from "../EnumTipoDeEnvioCampanha";
import {EnumOrigemContatosCampanha} from "../EnumOrigemContatosCampanha";
import {Contato} from "../Contato";
import {EnumStatusAprovacao} from "../EnumStatusAprovacao";
import {EnumStatusNotificacaoApp} from "../EnumStatusNotificacaoApp";

export class NotificacaoApp {
  id: number;
  nome: string;
  mensagem: string;
  titulo: string;
  link: string;
  dataCriacao: Date;
  empresa: Empresa;
  qtdeEnviadas = 0;
  qtdeLidas = 0;
  qtdeMensagens = 0;
  status: EnumStatusNotificacaoApp;
  horarioEnvio: Date;
  qtdeDeDiasNovaNotificacao: number;
  urlImagem = '';
  foiTestada = false;
  statusAprovacao: EnumStatusAprovacao;

  constructor() {
    this.dataCriacao = new Date();
  }

  obtenhaUrlImagem() {
    if( this.urlImagem ) {
      return '/images/empresa/' + this.urlImagem;
    }

    return '';
  }
}
