import {Pedido} from "../delivery/Pedido";
import {Mesa} from "../Mesa";
import {Empresa} from "../Empresa";
import {Contato} from "../Contato";
import {EnumStatusComanda} from "./EnumStatusComanda";
import {PedidoGenerico} from "../delivery/PedidoGenerico";
import {MapeadorBasico} from "../../mapeadores/MapeadorBasico";

import {MapeadorDeComanda} from "../../mapeadores/MapeadorDeComanda";
import {Usuario} from "../Usuario";
import {PagamentoComanda} from "../delivery/PagamentoComanda";
import {IntegracaoPedidoFidelidade} from "../IntegracaoPedidoFidelidade";
import {PontuacaoRegistrada} from "../PontuacaoRegistrada";
import {Cartao} from "../Cartao";
import {CartaoCliente} from "../CartaoCliente";
import {ItemPedido} from "../delivery/ItemPedido";

export class Comanda extends PedidoGenerico{
  mesa: Mesa;
  pedidos: Array<Pedido> = [];
  totalComTaxa: number;
  taxaServico: number;
  cobrarTaxaServico = false;
  horarioAbertura: Date;
  horarioFechamento: Date;
  status: any
  pagamentos: Array<PagamentoComanda> = [];
  garcom: Usuario
  cartaoCliente: CartaoCliente;

  constructor(empresa: Empresa, mesa: Mesa, contato: Contato, status: EnumStatusComanda,
              garcom: Usuario = null,  public codigoPdv: string  = null, desconto: number = null, cartaoCliente: CartaoCliente = null) {
    super()
    this.empresa = empresa;
    this.mesa = mesa;
    this.contato = contato;
    this.horarioAtualizacao = new Date();
    this.horarioAbertura = new Date();
    this.status = status;
    this.garcom = garcom;
    this.desconto = desconto || 0;
    this.cartaoCliente = cartaoCliente;
  }

  adicione(pedido: Pedido) {
    let indicePedido: number;

    for(let i = 0; i < this.pedidos.length; i++)
      if(this.pedidos[i].id === pedido.id)
        indicePedido = i;

    if(indicePedido >= 0)
      this.pedidos.splice(indicePedido, 1)

    this.pedidos.push(pedido);
    this.atualizeValor();
  }

  calculeValor() {
    let total = 0.0;

    for( let pedido of this.pedidos ) {
      if( Number(pedido.status) <= 4 )
        total += pedido.obtenhaTotal();
    }

    if(this.desconto)
       total -= this.desconto;

    return Number( total.toFixed(2))
  }

  atualizeValor() {
    this.valor = this.calculeValor();

    if(this.cobrarTaxaServico) {
      this.taxaServico = this.valor * this.empresa.valorTaxaServico / 100
      this.totalComTaxa = this.valor + this.taxaServico
    } else {
      this.taxaServico = 0;
      this.totalComTaxa = this.valor;
    }

  }

  async  calculePontuacaoFidelidade(integracaoPedidoFidelidade: IntegracaoPedidoFidelidade) {
    if(!this.contato || !this.contato.id)
      this.contato =  this.pedidos.length ? this.pedidos[0].contato : null;

    if( (this.contato && this.contato.ehConsumidorNaoIndentificado()) || !integracaoPedidoFidelidade) return;

    if(!integracaoPedidoFidelidade.estaPontuando()) {
      console.log(String(`Plano da empresa ${this.empresa.id} nao está pontuando mais`))
      return;
    }

    if(!integracaoPedidoFidelidade.pontuarMesas){
      console.log(String(`Não pontuar comandas da empresa ${this.empresa.id}`))
      return;
    }

    if(this.contato && this.contato.id){
      let pontuacaoRegistrada: any = new PontuacaoRegistrada();

      pontuacaoRegistrada.valor = this.valor;
      pontuacaoRegistrada.cartao = new Cartao(null, this.contato,  integracaoPedidoFidelidade.plano, 0);
      if(integracaoPedidoFidelidade.atividade)
        pontuacaoRegistrada.atividades =  [integracaoPedidoFidelidade.atividade]

      this.pontosGanhos =
        integracaoPedidoFidelidade.plano.tipoDePontuacao.calculePontos(this.empresa, pontuacaoRegistrada, this.obtenhaVenda() );
    }

  }

  obtenhaVenda(){
    let itens: any = [];

    this.pedidos.forEach((pedido: any) => {
      if(!pedido.foiCanceladoOuDevolvido()){
        itens.push(...pedido.itens)
      }
    })

    return  { valor: this.valor, itens: itens, desconto: this.obtenhaTotalDescontoEmProdutos() }
  }


  expirou() {
    const agora = new Date();

    const tempoEmHoras = (agora.getTime() - this.horarioAtualizacao.getTime()) / (60 * 60 * 1000.0);

    return tempoEmHoras > 8;
  }

  estaFechada(){
    return this.status === EnumStatusComanda.Fechada
  }

  foiCancelada(){
    return this.status === EnumStatusComanda.Cancelada
  }


  estaAberta(){
    return this.status === EnumStatusComanda.ABERTA
  }

  emFechamento(){
    return this.status === EnumStatusComanda.EmFechamento
  }

  obtenhaTotalPagar(){ // diferentemente de pedido, comada esta salvando o valor  sem o valor do desconto
    let total = super.obtenhaTotal();

    if(this.desconto) total -= this.desconto;

    return    Number( (total).toFixed(2));
  }

  mapeador(): MapeadorBasico {
    return new MapeadorDeComanda();
  }

  ehComanda(){
    return true;
  }

  obtenhaCodigoExterno() {
    return  String(`C${this.id}`);
  }

  obtenhaMovimentacoesEstoque(estoqueVinculadoProduto: boolean){
    let movimentacoes: any = [];

    this.pedidos.forEach((pedido: any) => {
      pedido.itens.forEach((item: ItemPedido) => {
        item.getMovimentacoes(estoqueVinculadoProduto, pedido, movimentacoes)
      })
    })

    return movimentacoes;
  }

}
