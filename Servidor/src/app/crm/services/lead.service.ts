import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ServerService } from '../../services/ServerService';

@Injectable()
export class LeadService extends ServerService {
  constructor(protected http: HttpClient) {
    super(http);
  }

  private endpoint = '/crm/leads';

  liste(params: any = {}): Promise<any> {
    return this.obtenha(this.endpoint, params);
  }

  selecione(id: number): Promise<any> {
    return this.obtenha(`${this.endpoint}/${id}`, {});
  }

  salveLead(lead: any): Promise<any> {
    if (lead.id) {
      // Atualizar lead existente com PUT
      return this.facaPut(`${this.endpoint}/${lead.id}`, lead);
    } else {
      // Criar novo lead com POST
      return this.facaPost(this.endpoint, lead);
    }
  }

  removaLead(id: number): Promise<any> {
    return this.remova(`${this.endpoint}/${id}`, {});
  }

  buscarDadosInstagram(username: string): Promise<any> {
    const params: any = { username };
    return this.obtenha(`${this.endpoint}/dadosig`, params);
  }

  enviarDadosInstagram(texto: any, crmEmpresaId?: number, username?: string): Promise<any> {
    const payload = {
      texto,
      username,
      crmEmpresaId: crmEmpresaId || null
    };
    return this.facaPost(`${this.endpoint}/dadosig2`, payload);
  }

  analisarWebsite(url: string): Promise<any> {
    const params = { url };
    return this.obtenha(`${this.endpoint}/analisar-website`, params);
  }

  categorizarLinks(links: string[]): Promise<any> {
    const payload = { links };
    return this.facaPost(`${this.endpoint}/categorizar-links`, payload);
  }

  descobrirCnpj(nomeEmpresa: string, cidade?: string): Promise<any> {
    const params = { nomeEmpresa, cidade };
    return this.obtenha(`${this.endpoint}/descobrir-cnpj`, params);
  }

  // ===== MÉTODOS PARA GERENCIAR LINKS =====

  listarLinks(leadId: number): Promise<any> {
    return this.obtenha(`${this.endpoint}/${leadId}/links`, {});
  }

  adicionarLink(leadId: number, link: any): Promise<any> {
    return this.facaPost(`${this.endpoint}/${leadId}/links`, link);
  }

  atualizarLink(leadId: number, linkId: number, link: any): Promise<any> {
    return this.facaPut(`${this.endpoint}/${leadId}/links/${linkId}`, link);
  }

  removerLink(leadId: number, linkId: number): Promise<any> {
    return this.remova(`${this.endpoint}/${leadId}/links/${linkId}`, {});
  }

  removerLinkPorTipo(leadId: number, tipo: string): Promise<any> {
    return this.remova(`${this.endpoint}/${leadId}/links/tipo/${tipo}`, {});
  }
}
