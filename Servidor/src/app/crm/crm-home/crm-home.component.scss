// Variáveis e cores
$primary: #3b7ddd;
$primary-dark: #2d6ec0;
$primary-light: #e8f2ff;
$accent: #2ecc71;
$accent-light: #e8f8ed;
$neutral: #4a6583;
$neutral-light: #edf1f7;
$warning: #f39c12;
$danger: #e74c3c;
$gray-light: #f8f9fa;
$gray-lighter: #f2f3f5;
$gray-border: #e0e0e0;
$gray-dark: #495057;
$text-dark: #344767;
$text-secondary: #666;
$radius: 8px;
$transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);

.crm-home-container {
  padding: 0;
  max-width: 100%;
  margin: 0;
  font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  display: flex;
  flex-direction: column;
  background-color: $gray-light;
}

// Cabeçalho moderno e limpo
.modern-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafe 100%);
  border-bottom: 1px solid rgba(59, 125, 221, 0.1);
  padding: 20px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, $primary 0%, $accent 50%, $primary 100%);
  }
}

.header-brand {
  display: flex;
  align-items: center;
  gap: 16px;
}

.brand-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, $primary 0%, darken($primary, 10%) 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  box-shadow: 0 4px 12px rgba(59, 125, 221, 0.25);
}

.brand-text {
  .brand-title {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    color: $text-dark;
    line-height: 1.2;
  }

  .brand-subtitle {
    font-size: 14px;
    color: $text-secondary;
    font-weight: 500;
  }
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: rgba(46, 204, 113, 0.1);
  border-radius: 20px;
  border: 1px solid rgba(46, 204, 113, 0.2);

  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;

    &.active {
      background-color: $accent;
      box-shadow: 0 0 0 2px rgba(46, 204, 113, 0.3);
    }
  }

  .status-text {
    font-size: 12px;
    font-weight: 600;
    color: $accent;
  }
}

// Lead card no topo - Layout condensado
.lead-card-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafe 100%);
  padding: 16px 20px;
  border-bottom: 1px solid $gray-border;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 10;
  min-height: 80px;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, $primary 0%, $accent 50%, $primary 100%);
  }
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at top right, rgba(59, 125, 221, 0.02) 0%, transparent 50%);
  pointer-events: none;
}

// Layout condensado em linha única
.lead-condensed-layout {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  z-index: 2;
  flex-wrap: wrap;
}

// Avatar e score compactos
.lead-avatar-section {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.lead-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, $primary 0%, darken($primary, 10%) 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  box-shadow: 0 3px 8px rgba(59, 125, 221, 0.25);
  border: 2px solid white;
}

// Informações principais
.lead-main-info {
  flex: 1;
  min-width: 250px;
}

.lead-header-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 4px;
}

.lead-name {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: $text-dark;
  line-height: 1.2;
}

.lead-details-row {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.detail-item {
  font-size: 12px;
  color: $text-secondary;
  display: flex;
  align-items: center;
  gap: 4px;
  
  i {
    color: $primary;
    opacity: 0.8;
    font-size: 11px;
  }
}

// Redes sociais compactas
.social-compact {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.social-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  text-decoration: none;
  transition: $transition;
  
  &.instagram {
    background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
    color: white;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(245, 87, 108, 0.4);
    }
  }
  
  &.linkedin {
    background-color: #0077b5;
    color: white;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 119, 181, 0.4);
    }
  }
  
  &.website {
    background-color: $neutral;
    color: white;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(74, 101, 131, 0.4);
    }
  }
}

// Informações extras
.extra-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 120px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  color: $text-secondary;
  
  i {
    color: $primary;
    width: 12px;
  }
  
  span {
    font-weight: 500;
  }
}


.stage-badge {
  padding: 4px 10px;
  border-radius: 16px;
  font-size: 11px;
  font-weight: 600;
  background-color: $gray-lighter;
  white-space: nowrap;
  
  &.prospecção {
    background-color: #e3f2fd;
    color: #1976d2;
  }
  
  &.qualificação {
    background-color: #e8f5e9;
    color: #388e3c;
  }
  
  &.objeção {
    background-color: #fff8e1;
    color: #f57f17;
  }
  
  &.fechamento {
    background-color: #e8eaf6;
    color: #3949ab;
  }
}

.score-badge {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  border: 2px solid white;
}

// Ações rápidas compactas
.lead-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.action-button {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  transition: $transition;
  font-size: 16px;
  
  &.phone {
    background-color: #e8f5e9;
    color: #27ae60;
    
    &:hover {
      background-color: #27ae60;
      color: white;
      transform: translateY(-1px);
    }
  }
  
  &.email {
    background-color: #e3f2fd;
    color: #3498db;
    
    &:hover {
      background-color: #3498db;
      color: white;
      transform: translateY(-1px);
    }
  }
  
  &.whatsapp {
    background-color: #e8f5e9;
    color: #25d366;
    
    &:hover {
      background-color: #25d366;
      color: white;
      transform: translateY(-1px);
    }
  }
  
  &.calendar {
    background-color: #f3e5f5;
    color: #9b59b6;
    
    &:hover {
      background-color: #9b59b6;
      color: white;
      transform: translateY(-1px);
    }
  }
}

// Conteúdo principal
.crm-main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.sugestoes-panel {
  flex: 1;
  overflow: hidden;
}

.actions-panel {
  padding: 16px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  
  .btn-primary {
    color: white;
    background-color: #3b7ddd;
    border: none;
    padding: 10px 18px;
    font-weight: 500;
    border-radius: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    
    &:hover {
      background-color: #2d6ec0;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      transform: translateY(-1px);
    }
    
    i {
      margin-right: 6px;
    }
  }
}

// Responsividade para layout condensado
@media (max-width: 1024px) {
  .lead-condensed-layout {
    gap: 12px;
  }
  
  .extra-info {
    display: none; // Oculta informações extras em telas menores
  }
}

@media (max-width: 768px) {
  .main-header {
    padding: 10px 16px;
  }
  
  .main-title {
    font-size: 18px;
  }
  
  .subtitle {
    font-size: 12px;
  }
  
  .lead-card-header {
    padding: 12px 16px;
  }
  
  .lead-condensed-layout {
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .lead-main-info {
    min-width: 200px;
    order: 1;
  }
  
  .lead-avatar-section {
    order: 0;
  }
  
  .social-compact {
    order: 2;
  }
  
  .lead-actions {
    order: 3;
    width: 100%;
    justify-content: center;
  }
  
  .lead-details-row {
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .header-content {
    gap: 8px;
  }
  
  .header-icon {
    font-size: 20px;
  }
  
  .main-title {
    font-size: 16px;
  }
  
  .subtitle {
    display: none; // Oculta subtitle em telas muito pequenas
  }
  
  .lead-name {
    font-size: 16px;
  }
  
  .detail-item {
    font-size: 11px;
  }
  
  .social-compact {
    gap: 6px;
  }
  
  .social-icon {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }
}

// Estados de carregamento modernos
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 24px;
  margin: 24px;
}

.loading-card {
  background: white;
  border-radius: 16px;
  padding: 40px 32px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(59, 125, 221, 0.1);
  max-width: 400px;
  width: 100%;

  &.warning {
    border-color: rgba(243, 156, 18, 0.2);
    background: linear-gradient(135deg, #ffffff 0%, #fffbf0 100%);
  }
}

.loading-animation {
  margin-bottom: 24px;
}

.spinner-modern {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(59, 125, 221, 0.1);
  border-left: 4px solid $primary;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.warning-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, $warning 0%, darken($warning, 10%) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  color: white;
  font-size: 24px;
  box-shadow: 0 4px 16px rgba(243, 156, 18, 0.3);
}

.loading-title {
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 600;
  color: $text-dark;
}

.loading-text {
  margin: 0;
  color: $text-secondary;
  font-size: 15px;
  line-height: 1.5;
}

// Estilos para página de download de dados
.download-data-page {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  min-height: 50vh;
}

.download-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  padding: 32px;
  max-width: 450px;
  width: 100%;
  text-align: center;
  border: 1px solid $gray-border;
}

.download-header {
  margin-bottom: 24px;
  
  i {
    font-size: 48px;
    color: #e1306c;
    margin-bottom: 16px;
    display: block;
  }
  
  h2 {
    color: $text-dark;
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
  }
  
  p {
    color: $text-secondary;
    margin: 0;
    font-size: 16px;
    
    strong {
      color: $primary;
      font-weight: 600;
    }
  }
}

.download-content {
  p {
    color: $text-secondary;
    margin-bottom: 24px;
    font-size: 15px;
    line-height: 1.5;
  }
}

.btn-download-data {
  background: linear-gradient(135deg, #e1306c 0%, #fd5949 100%);
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: $transition;
  box-shadow: 0 4px 15px rgba(225, 48, 108, 0.3);
  margin-bottom: 16px;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(225, 48, 108, 0.4);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  i {
    margin-right: 8px;
  }
}

.download-info {
  small {
    color: $text-secondary;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    
    i {
      color: $primary;
    }
  }
}

// Responsividade para download page
@media (max-width: 768px) {
  .download-card {
    padding: 24px;
    margin: 0 16px;
  }
  
  .download-header {
    i {
      font-size: 40px;
    }
    
    h2 {
      font-size: 20px;
    }
    
    p {
      font-size: 14px;
    }
  }
  
  .btn-download-data {
    padding: 12px 24px;
    font-size: 15px;
  }
}

// Estilos para a nova estrutura detalhada do lead
.lead-detailed-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  margin: 20px;
  overflow: hidden;
  border: 1px solid $gray-border;
}

// Cabeçalho do lead moderno
.lead-header-modern {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafe 100%);
  padding: 32px;
  border-bottom: 1px solid rgba(59, 125, 221, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, $primary 0%, $accent 50%, $primary 100%);
  }
}

.lead-profile-section {
  display: flex;
  align-items: center;
  gap: 24px;
  flex: 1;
}

.profile-avatar {
  position: relative;
  flex-shrink: 0;
}

.avatar-circle {
  width: 72px;
  height: 72px;
  border-radius: 50%;
  background: linear-gradient(135deg, $primary 0%, darken($primary, 10%) 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  box-shadow: 0 6px 20px rgba(59, 125, 221, 0.3);
  border: 4px solid white;
}

.score-indicator {
  position: absolute;
  bottom: -4px;
  right: -4px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 700;
  color: white;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
  border: 3px solid white;
}

.profile-info {
  flex: 1;
  min-width: 0;
}

.profile-name-row {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.profile-name {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: $text-dark;
  line-height: 1.2;
}

.profile-badges {
  display: flex;
  gap: 8px;
}

.priority-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  background-color: $gray-lighter;
  border: 1px solid $gray-border;

  i {
    font-size: 10px;
  }

  &.prospecção {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #1976d2;
    border-color: #90caf9;
  }

  &.qualificação {
    background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
    color: #388e3c;
    border-color: #a5d6a7;
  }

  &.objeção {
    background: linear-gradient(135deg, #fff8e1 0%, #fff3c4 100%);
    color: #f57f17;
    border-color: #ffcc02;
  }

  &.fechamento {
    background: linear-gradient(135deg, #e8eaf6 0%, #c5cae9 100%);
    color: #3949ab;
    border-color: #9fa8da;
  }
}

.profile-details {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;

  span {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    color: $text-secondary;
    font-weight: 500;

    i {
      color: $primary;
      font-size: 12px;
      width: 14px;
    }
  }
}

.quick-actions-modern {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
}

.modern-action-btn {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  font-size: 18px;
  transition: $transition;
  border: 2px solid transparent;

  &.phone {
    background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
    color: #27ae60;
    border-color: rgba(39, 174, 96, 0.2);

    &:hover {
      background: linear-gradient(135deg, #27ae60 0%, darken(#27ae60, 10%) 100%);
      color: white;
      transform: translateY(-3px);
      box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
    }
  }

  &.whatsapp {
    background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
    color: #25d366;
    border-color: rgba(37, 211, 102, 0.2);

    &:hover {
      background: linear-gradient(135deg, #25d366 0%, darken(#25d366, 10%) 100%);
      color: white;
      transform: translateY(-3px);
      box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
    }
  }

  &.email {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #3498db;
    border-color: rgba(52, 152, 219, 0.2);

    &:hover {
      background: linear-gradient(135deg, #3498db 0%, darken(#3498db, 10%) 100%);
      color: white;
      transform: translateY(-3px);
      box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
    }
  }
}

.lead-avatar-section {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-shrink: 0;
}

.lead-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, $primary 0%, darken($primary, 10%) 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  box-shadow: 0 4px 12px rgba(59, 125, 221, 0.3);
  border: 3px solid white;
}

.score-badge {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 700;
  color: white;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
  border: 3px solid white;
}

.lead-title-section {
  flex: 1;
  min-width: 0;
}

.lead-name {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 700;
  color: $text-dark;
  line-height: 1.2;
}

.lead-company {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: $text-secondary;
  font-weight: 500;
}

.lead-meta {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.stage-badge {
  padding: 6px 14px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  background-color: $gray-lighter;
  border: 1px solid $gray-border;
  
  &.prospecção {
    background-color: #e3f2fd;
    color: #1976d2;
    border-color: #90caf9;
  }
  
  &.qualificação {
    background-color: #e8f5e9;
    color: #388e3c;
    border-color: #a5d6a7;
  }
  
  &.objeção {
    background-color: #fff8e1;
    color: #f57f17;
    border-color: #ffcc02;
  }
  
  &.fechamento {
    background-color: #e8eaf6;
    color: #3949ab;
    border-color: #9fa8da;
  }
}

.origin-badge, .segment-badge {
  padding: 6px 14px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  background-color: $neutral-light;
  color: $neutral;
  border: 1px solid rgba(74, 101, 131, 0.3);
}

.lead-quick-actions {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 25px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 600;
  transition: $transition;
  border: 2px solid transparent;
  
  &.phone-btn {
    background-color: #e8f5e9;
    color: #27ae60;
    border-color: rgba(39, 174, 96, 0.2);
    
    &:hover {
      background-color: #27ae60;
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
    }
  }
  
  &.whatsapp-btn {
    background-color: #e8f5e9;
    color: #25d366;
    border-color: rgba(37, 211, 102, 0.2);
    
    &:hover {
      background-color: #25d366;
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
    }
  }
  
  &.email-btn {
    background-color: #e3f2fd;
    color: #3498db;
    border-color: rgba(52, 152, 219, 0.2);
    
    &:hover {
      background-color: #3498db;
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
    }
  }
}

// Seções de informações
.lead-contact-section, .lead-business-section, .lead-notes-section, .lead-links-section {
  padding: 24px;
  border-bottom: 1px solid $gray-border;

  &:last-child {
    border-bottom: none;
  }
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: $text-dark;
  
  i {
    color: $primary;
    font-size: 20px;
  }
}

// Grid de contatos
.contact-grid, .business-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.contact-item, .business-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  
  label {
    font-size: 12px;
    font-weight: 600;
    color: $text-secondary;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.contact-value {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background-color: $gray-light;
  border-radius: 8px;
  border: 1px solid $gray-border;
  
  i {
    color: $primary;
    font-size: 16px;
    width: 20px;
    text-align: center;
  }
  
  span {
    flex: 1;
    font-size: 14px;
    color: $text-dark;
    font-weight: 500;
  }
}

.contact-action {
  color: $primary;
  text-decoration: none;
  padding: 4px;
  border-radius: 4px;
  transition: $transition;
  
  &:hover {
    background-color: $primary;
    color: white;
    transform: scale(1.1);
  }
}

.website-link, .linkedin-link {
  color: $primary;
  text-decoration: none;
  font-weight: 500;
  flex: 1;
  
  &:hover {
    text-decoration: underline;
  }
}

.business-item {
  span {
    font-size: 14px;
    color: $text-dark;
    font-weight: 500;
    padding: 12px 16px;
    background-color: $gray-light;
    border-radius: 8px;
    border: 1px solid $gray-border;
  }
  
  &.follow-up-date span {
    background-color: $accent-light;
    color: $accent;
    border-color: rgba(46, 204, 113, 0.3);
    font-weight: 600;
  }
}

.interests-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 12px 16px;
  background-color: $gray-light;
  border-radius: 8px;
  border: 1px solid $gray-border;
}

.interest-tag {
  padding: 4px 12px;
  background-color: $primary-light;
  color: $primary;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid rgba(59, 125, 221, 0.3);
}

// Seção de notas
.notes-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.note-item {
  label {
    font-size: 12px;
    font-weight: 600;
    color: $text-secondary;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
    display: block;
  }
  
  p {
    margin: 0;
    padding: 16px;
    background-color: $gray-light;
    border-radius: 8px;
    border: 1px solid $gray-border;
    color: $text-dark;
    line-height: 1.5;
    font-size: 14px;
    
    &.instagram-notes {
      background-color: #e8f2ff;
      border-color: rgba(59, 125, 221, 0.3);
      white-space: pre-wrap;
      font-family: 'Courier New', monospace;
      font-size: 13px;
    }
  }
}

// Seção de links - Layout compacto
.links-compact-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.link-compact-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background-color: $gray-light;
  border-radius: 8px;
  border: 1px solid $gray-border;
  transition: $transition;

  &:hover {
    background-color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border-color: rgba(59, 125, 221, 0.3);
  }
}

.link-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
  flex-shrink: 0;

  i {
    font-size: 16px;
  }
}

.link-info {
  flex: 1;
  min-width: 0;

  .link-type {
    font-size: 12px;
    font-weight: 600;
    color: $text-secondary;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 2px;
  }

  .link-url {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #007bff;
    text-decoration: none;
    word-break: break-all;
    line-height: 1.3;
    margin-bottom: 2px;

    &:hover {
      text-decoration: underline;
      color: #0056b3;
    }
  }

  .link-description {
    font-size: 11px;
    color: $text-secondary;
    font-style: italic;
    line-height: 1.2;
  }
}

.link-external-btn {
  width: 28px;
  height: 28px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: $primary;
  color: white;
  text-decoration: none;
  transition: $transition;
  flex-shrink: 0;

  &:hover {
    background-color: darken($primary, 10%);
    transform: scale(1.05);
    color: white;
    text-decoration: none;
  }

  i {
    font-size: 12px;
  }
}

// Responsividade para o novo design
@media (max-width: 1024px) {
  .modern-header {
    padding: 16px 20px;

    .brand-text .brand-title {
      font-size: 20px;
    }

    .status-indicator {
      display: none;
    }
  }

  .lead-header-modern {
    padding: 24px;
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .profile-name {
    font-size: 24px;
  }

  .quick-actions-modern {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .modern-header {
    padding: 12px 16px;

    .brand-icon {
      width: 40px;
      height: 40px;
      font-size: 16px;
    }

    .brand-text .brand-title {
      font-size: 18px;
    }

    .brand-text .brand-subtitle {
      font-size: 12px;
    }
  }

  .loading-container {
    padding: 40px 16px;
    margin: 16px;
  }

  .loading-card {
    padding: 32px 24px;
  }

  .lead-detailed-card {
    margin: 12px;
  }

  .lead-header-modern {
    padding: 20px;
    text-align: center;
  }

  .lead-profile-section {
    flex-direction: column;
    align-items: center;
    gap: 16px;
    width: 100%;
  }

  .profile-info {
    text-align: center;
  }

  .profile-name-row {
    justify-content: center;
    flex-direction: column;
    gap: 12px;
  }

  .profile-details {
    justify-content: center;
    gap: 16px;
  }

  .avatar-circle {
    width: 64px;
    height: 64px;
    font-size: 24px;
  }

  .profile-name {
    font-size: 22px;
  }

  .contact-grid, .business-grid {
    grid-template-columns: 1fr;
  }

  .link-compact-item {
    padding: 10px 12px;
    gap: 10px;

    .link-icon {
      width: 28px;
      height: 28px;
    }

    .link-info .link-url {
      font-size: 13px;
    }

    .link-external-btn {
      width: 24px;
      height: 24px;
    }
  }

  .lead-contact-section, .lead-business-section, .lead-notes-section, .lead-links-section {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .modern-header {
    .brand-text .brand-subtitle {
      display: none;
    }
  }

  .loading-card {
    padding: 24px 16px;
  }

  .loading-title {
    font-size: 18px;
  }

  .profile-name {
    font-size: 20px;
  }

  .profile-details {
    flex-direction: column;
    gap: 8px;
    align-items: center;
  }

  .quick-actions-modern {
    gap: 8px;
  }

  .modern-action-btn {
    width: 44px;
    height: 44px;
    font-size: 16px;
  }
}
