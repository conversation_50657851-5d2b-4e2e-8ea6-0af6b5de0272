<div class="crm-home-container">
  <!-- Cabeçalho moderno e limpo -->
  <div class="modern-header">
    <div class="header-brand">
      <div class="brand-icon">
        <i class="fa-solid fa-bullseye"></i>
      </div>
      <div class="brand-text">
        <h1 class="brand-title">Assistente de Vendas</h1>
        <span class="brand-subtitle">CardápioTech CRM</span>
      </div>
    </div>
    <div class="header-actions">
      <div class="status-indicator">
        <span class="status-dot active"></span>
        <span class="status-text">Online</span>
      </div>
    </div>
  </div>

  <!-- Estado de carregamento melhorado -->
  <div class="loading-container" *ngIf="carregandoLead">
    <div class="loading-card">
      <div class="loading-animation">
        <div class="spinner-modern"></div>
      </div>
      <h3 class="loading-title">Carregando dados do lead</h3>
      <p class="loading-text">Buscando informações para @{{ username }}...</p>
    </div>
  </div>

  <!-- Mensagem de lead não encontrado -->
  <div class="loading-container" *ngIf="!carregandoLead && !leadEncontrado && username">
    <div class="loading-card warning">
      <div class="warning-icon">
        <i class="fa-solid fa-exclamation-triangle"></i>
      </div>
      <h3 class="loading-title">Lead não encontrado</h3>
      <p class="loading-text">Não foi possível encontrar dados para @{{ username }}. Redirecionando...</p>
    </div>
  </div>

  <!-- Card detalhado do lead -->
  <div class="lead-detailed-card" *ngIf="!carregandoLead && leadEncontrado && dadosLeadAtual">

    <!-- Cabeçalho do Lead Redesignado -->
    <div class="lead-header-modern">
      <div class="lead-profile-section">
        <div class="profile-avatar">
          <div class="avatar-circle">
            <i class="fa-solid fa-user"></i>
          </div>
          <div class="score-indicator" [ngStyle]="{'background-color': getCorDoScore(dadosLeadAtual?.scoreLead)}">
            {{ formatarScore(dadosLeadAtual?.scoreLead) }}
          </div>
        </div>

        <div class="profile-info">
          <div class="profile-name-row">
            <h1 class="profile-name">{{ dadosLeadAtual?.nome || 'Lead sem nome' }}</h1>
            <div class="profile-badges">
              <span class="priority-badge" [ngClass]="dadosLeadAtual?.etapaFunil?.toLowerCase() || ''">
                <i class="fa-solid fa-flag"></i>
                {{ dadosLeadAtual?.etapaFunil || 'Indefinida' }}
              </span>
            </div>
          </div>

          <div class="profile-details">
            <span class="company-name" *ngIf="dadosLeadAtual?.empresa">
              <i class="fa-solid fa-building"></i>
              {{ dadosLeadAtual?.empresa }}
            </span>
            <span class="lead-origin" *ngIf="dadosLeadAtual?.origemLead">
              <i class="fa-solid fa-source"></i>
              {{ dadosLeadAtual?.origemLead }}
            </span>
            <span class="lead-segment" *ngIf="dadosLeadAtual?.segmento">
              <i class="fa-solid fa-tag"></i>
              {{ dadosLeadAtual?.segmento }}
            </span>
          </div>
        </div>
      </div>

      <!-- Ações rápidas modernizadas -->
      <div class="quick-actions-modern">
        <a class="modern-action-btn phone"
           *ngIf="dadosLeadAtual?.telefone"
           [href]="'tel:' + dadosLeadAtual?.telefone"
           title="Ligar para {{ dadosLeadAtual?.telefone }}">
          <i class="fa-solid fa-phone"></i>
        </a>
        <a class="modern-action-btn whatsapp"
           *ngIf="dadosLeadAtual?.telefone"
           [href]="getWhatsAppUrl(dadosLeadAtual?.telefone)"
           target="_blank"
           title="WhatsApp {{ dadosLeadAtual?.telefone }}">
          <i class="fa-brands fa-whatsapp"></i>
        </a>
        <a class="modern-action-btn email"
           *ngIf="dadosLeadAtual?.email"
           [href]="'mailto:' + dadosLeadAtual?.email"
           title="Enviar email para {{ dadosLeadAtual?.email }}">
          <i class="fa-solid fa-envelope"></i>
        </a>
      </div>
    </div>

    <!-- Informações de Contato -->
    <div class="lead-contact-section">
      <h3 class="section-title">
        <i class="fa fa-address-card"></i>
        Informações de Contato
      </h3>
      
      <div class="contact-grid">
        <div class="contact-item" *ngIf="dadosLeadAtual?.telefone">
          <label>Telefone:</label>
          <div class="contact-value">
            <i class="fa fa-phone"></i>
            <span>{{ dadosLeadAtual?.telefone }}</span>
            <a [href]="'tel:' + dadosLeadAtual?.telefone" class="contact-action" title="Ligar">
              <i class="fa fa-external-link"></i>
            </a>
          </div>
        </div>

        <div class="contact-item" *ngIf="dadosLeadAtual?.email">
          <label>Email:</label>
          <div class="contact-value">
            <i class="fa fa-envelope"></i>
            <span>{{ dadosLeadAtual?.email }}</span>
            <a [href]="'mailto:' + dadosLeadAtual?.email" class="contact-action" title="Enviar email">
              <i class="fa fa-external-link"></i>
            </a>
          </div>
        </div>

        <div class="contact-item" *ngIf="dadosLeadAtual?.instagram">
          <label>Instagram:</label>
          <div class="contact-value">
            <i class="fa fa-instagram"></i>
            <span>{{ dadosLeadAtual?.instagram }}</span>
            <a [href]="getInstagramUrl(dadosLeadAtual?.instagram)" 
               target="_blank" 
               class="contact-action" 
               title="Abrir perfil no Instagram">
              <i class="fa fa-external-link"></i>
            </a>
          </div>
        </div>

        <div class="contact-item" *ngIf="dadosLeadAtual?.site">
          <label>Website:</label>
          <div class="contact-value">
            <i class="fa fa-globe"></i>
            <a [href]="getWebsiteUrl(dadosLeadAtual?.site)" target="_blank" class="website-link">
              {{ dadosLeadAtual?.site }}
            </a>
            <a [href]="getWebsiteUrl(dadosLeadAtual?.site)" 
               target="_blank" 
               class="contact-action" 
               title="Abrir website">
              <i class="fa fa-external-link"></i>
            </a>
          </div>
        </div>

        <div class="contact-item" *ngIf="dadosLeadAtual?.linkedin">
          <label>LinkedIn:</label>
          <div class="contact-value">
            <i class="fa fa-linkedin"></i>
            <a [href]="dadosLeadAtual?.linkedin" target="_blank" class="linkedin-link">
              {{ dadosLeadAtual?.linkedin }}
            </a>
            <a [href]="dadosLeadAtual?.linkedin" 
               target="_blank" 
               class="contact-action" 
               title="Abrir perfil no LinkedIn">
              <i class="fa fa-external-link"></i>
            </a>
          </div>
        </div>

        <div class="contact-item" *ngIf="dadosLeadAtual?.localizacao">
          <label>Localização:</label>
          <div class="contact-value">
            <i class="fa fa-map-marker"></i>
            <span>{{ dadosLeadAtual?.localizacao }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Links do Lead -->
    <div class="lead-links-section" *ngIf="dadosLeadAtual?.links && dadosLeadAtual?.links.length > 0">
      <h3 class="section-title">
        <i class="fa fa-link"></i>
        Links Importantes
      </h3>

      <div class="links-compact-list">
        <div class="link-compact-item" *ngFor="let link of dadosLeadAtual?.links">
          <div class="link-icon" [style.color]="getLinkColor(link.tipo)">
            <i [ngClass]="getLinkIcon(link.tipo)"></i>
          </div>
          <div class="link-info">
            <div class="link-type">{{ link.tipo }}</div>
            <a [href]="getLinkUrl(link)"
               target="_blank"
               class="link-url"
               [title]="link.descricao || 'Abrir ' + link.tipo">
              {{ link.url }}
            </a>
            <div class="link-description" *ngIf="link.descricao">{{ link.descricao }}</div>
          </div>
          <a [href]="getLinkUrl(link)"
             target="_blank"
             class="link-external-btn"
             [title]="'Abrir ' + link.tipo">
            <i class="fas fa-link"></i>
          </a>
        </div>
      </div>
    </div>

    <!-- Informações de Negócio -->
    <div class="lead-business-section">
      <h3 class="section-title">
        <i class="fa fa-briefcase"></i>
        Informações de Negócio
      </h3>
      
      <div class="business-grid">
        <div class="business-item" *ngIf="dadosLeadAtual?.dataPrimeiroContato">
          <label>Primeiro Contato:</label>
          <span>{{ dadosLeadAtual?.dataPrimeiroContato }}</span>
        </div>

        <div class="business-item" *ngIf="dadosLeadAtual?.ultimaInteracao">
          <label>Última Interação:</label>
          <span>{{ dadosLeadAtual?.ultimaInteracao }}</span>
        </div>

        <div class="business-item" *ngIf="dadosLeadAtual?.proximoFollowUp">
          <label>Próximo Follow-up:</label>
          <span class="follow-up-date">{{ dadosLeadAtual?.proximoFollowUp }}</span>
        </div>

        <div class="business-item" *ngIf="dadosLeadAtual?.tamanhoEmpresa">
          <label>Tamanho da Empresa:</label>
          <span>{{ dadosLeadAtual?.tamanhoEmpresa }}</span>
        </div>

        <div class="business-item" *ngIf="dadosLeadAtual?.interessesProdutos && dadosLeadAtual?.interessesProdutos.length > 0">
          <label>Interesses:</label>
          <div class="interests-list">
            <span class="interest-tag" *ngFor="let interesse of dadosLeadAtual?.interessesProdutos">
              {{ interesse }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Observações -->
    <div class="lead-notes-section" *ngIf="dadosLeadAtual?.observacoes || dadosLeadAtual?.notas || dadosLeadAtual?.historicoPropostas">
      <h3 class="section-title">
        <i class="fa fa-sticky-note"></i>
        Observações e Histórico
      </h3>
      
      <div class="notes-content">
        <div class="note-item" *ngIf="dadosLeadAtual?.observacoes">
          <label>Observações de Vendas:</label>
          <p>{{ dadosLeadAtual?.observacoes }}</p>
        </div>

        <div class="note-item" *ngIf="dadosLeadAtual?.notas">
          <label>Dados do Instagram:</label>
          <p class="instagram-notes">{{ dadosLeadAtual?.notas }}</p>
        </div>

        <div class="note-item" *ngIf="dadosLeadAtual?.historicoPropostas">
          <label>Histórico de Propostas:</label>
          <p>{{ dadosLeadAtual?.historicoPropostas }}</p>
        </div>
      </div>
    </div>

  </div>
</div>
