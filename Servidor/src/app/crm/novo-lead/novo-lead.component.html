<div class="wizard-container">
  <!-- Header com Progress Bar -->
  <div class="wizard-header">
    <div class="container-fluid">
      <div class="row">
        <div class="col-12">
          <div class="wizard-progress">
            <div class="step-progress">
              <div 
                *ngFor="let i of [1,2,3,4]" 
                class="step"
                [class]="getStepClass(i)"
                (click)="goToStep(i)">
                <div class="step-number">{{i}}</div>
                <div class="step-label">
                  <span *ngIf="i === 1">Extrair</span>
                  <span *ngIf="i === 2">Links</span>
                  <span *ngIf="i === 3">CNPJ</span>
                  <span *ngIf="i === 4">Finalizar</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- <PERSON><PERSON><PERSON><PERSON> do Passo Atual -->
  <div class="wizard-title">
    <div class="container-fluid">
      <div class="row">
        <div class="col-12">
          <h3 class="step-title">
            <i class="fas fa-user-plus" *ngIf="currentStep === 1"></i>
            <i class="fas fa-link" *ngIf="currentStep === 2"></i>
            <i class="fas fa-id-card" *ngIf="currentStep === 3"></i>
            <i class="fas fa-check-circle" *ngIf="currentStep === 4"></i>
            {{getCurrentStepTitle()}}
          </h3>
        </div>
      </div>
    </div>
  </div>

  <!-- Conteúdo dos Passos -->
  <div class="wizard-content">
    <div class="container-fluid">
      
      <!-- ===== PASSO 1: EXTRAIR DADOS ===== -->
      <div *ngIf="currentStep === 1" class="step-content step-1">
        
        <!-- Card Principal: Extração de Dados -->
        <div class="row">
          <div class="col-12">
            <div class="card extraction-main-card">
              <div class="card-body">
                <h5 class="card-title">
                  <i class="fab fa-instagram text-primary"></i>
                  Extrair Dados do Instagram
                </h5>
                <p class="card-text text-muted">
                  Informe o username do Instagram da empresa para extrair dados automaticamente
                </p>

                <!-- Input Instagram -->
                <div class="form-group">
                  <label for="instagramUsername">Username do Instagram</label>
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text">@</span>
                    </div>
                    <input
                      type="text"
                      id="instagramUsername"
                      class="form-control"
                      [(ngModel)]="username"
                      name="instagramUsername"
                      placeholder="restaurante_exemplo"
                      (keyup.enter)="solicitarDadosInstagram()">
                    <div class="input-group-append">
                      <button
                        type="button"
                        class="btn btn-primary"
                        (click)="solicitarDadosInstagram()"
                        [disabled]="carregando || !username">
                        <i class="fas fa-download" *ngIf="!carregando"></i>
                        <i class="fas fa-spinner fa-spin" *ngIf="carregando"></i>
                        <span *ngIf="!carregando">Extrair Dados</span>
                        <span *ngIf="carregando">Extraindo...</span>
                      </button>
                    </div>
                  </div>
                </div>

                <!-- OU Divisor -->
                <div class="divider-or">
                  <span>OU</span>
                </div>

                <!-- Botão Criar Manual -->
                <div class="text-center">
                  <button
                    type="button"
                    class="btn btn-outline-secondary"
                    (click)="mostrarFormularioManual()">
                    <i class="fas fa-edit"></i>
                    Criar Lead Manualmente
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Card: Dados do Instagram (se extraídos) -->
        <div class="row" *ngIf="dadosInstagram && dadosInstagram.user">
          <div class="col-12">
            <div class="card data-card">
              <div class="card-body">
                <h6 class="card-title">
                  <i class="fas fa-check text-success"></i>
                  Dados Extraídos do Instagram
                </h6>
                
                <div class="instagram-profile">
                  <div class="profile-header-simple">
                    <div class="profile-details">
                      <h6 class="username"><i class="fab fa-instagram"></i> @{{dadosInstagram.user.username}}</h6>
                      <p class="full-name" *ngIf="dadosInstagram.user.full_name">{{dadosInstagram.user.full_name}}</p>
                      <div class="stats">
                        <span class="stat">
                          <i class="fas fa-users"></i>
                          <strong>{{getSeguidoresFormatado()}}</strong> seguidores
                        </span>
                        <span class="stat">
                          <i class="fas fa-user-plus"></i>
                          <strong>{{dadosInstagram.user.edge_follow?.count || 0}}</strong> seguindo
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Biografia destacada -->
                  <div class="bio-section" *ngIf="dadosInstagram.user.biography">
                    <h6 class="bio-title">
                      <i class="fas fa-quote-left"></i>
                      Biografia do Perfil
                    </h6>
                    <div class="bio-content">
                      {{dadosInstagram.user.biography}}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Card: Formulário de Dados Básicos -->
        <div class="row" *ngIf="mostrarFormulario || (dadosInstagram && dadosInstagram.user)">
          <div class="col-12">
            <div class="card form-card">
              <div class="card-body">
                <h6 class="card-title">
                  <i class="fas fa-edit"></i>
                  Dados Básicos do Lead
                </h6>

                  <div class="row">
                    <!-- Nome do Responsável -->
                  <div class="col-md-6">
                    <div class="form-group">
                      <label for="nomeResponsavel">Nome do Responsável *</label>
                      <input
                        type="text"
                        id="nomeResponsavel"
                        class="form-control"
                        [(ngModel)]="lead.nomeResponsavel"
                        name="nomeResponsavel"
                        placeholder="João Silva"
                        required>
                    </div>
                  </div>

                  <!-- Nome da Empresa -->
                  <div class="col-md-6">
                    <div class="form-group">
                      <label for="empresa">Nome da Empresa *</label>
                      <input
                        type="text"
                        id="empresa"
                        class="form-control"
                        [(ngModel)]="lead.empresa"
                        name="empresa"
                        placeholder="Restaurante Exemplo"
                        required>
                    </div>
                  </div>

                  <!-- Cidade -->
                  <div class="col-md-6">
                    <div class="form-group">
                      <label for="cidade">Cidade *</label>
                      <input
                        type="text"
                        id="cidade"
                        class="form-control"
                        [(ngModel)]="lead.cidade"
                        name="cidade"
                        placeholder="São Paulo"
                        required>
                    </div>
                  </div>

                  <!-- Telefone -->
                  <div class="col-md-6">
                    <div class="form-group">
                      <label for="telefone">Telefone</label>
                      <input
                        type="tel"
                        id="telefone"
                        class="form-control"
                        [(ngModel)]="lead.telefone"
                        name="telefone"
                        placeholder="(11) 99999-9999">
                    </div>
                  </div>

                  <!-- Instagram Handle -->
                  <div class="col-md-6">
                    <div class="form-group">
                      <label for="instagramHandle">Username Instagram *</label>
                      <div class="input-group">
                        <div class="input-group-prepend">
                          <span class="input-group-text">@</span>
                        </div>
                        <input
                          type="text"
                          id="instagramHandle"
                          class="form-control"
                          [(ngModel)]="lead.instagramHandle"
                          name="instagramHandle"
                          placeholder="restaurante_exemplo"
                          required>
                      </div>
                    </div>
                  </div>

                  <!-- Website -->
                  <div class="col-md-6">
                    <div class="form-group">
                      <label for="website">Website</label>
                      <input
                        type="url"
                        id="website"
                        class="form-control"
                        [(ngModel)]="lead.website"
                        name="website"
                        placeholder="https://exemplo.com.br"
                        (blur)="onWebsiteBlur()">
                    </div>
                  </div>

                  <!-- Biografia Instagram -->
                  <div class="col-md-6" *ngIf="dadosInstagram && dadosInstagram.user && dadosInstagram.user.biography">
                    <div class="form-group">
                      <label for="bioInsta">Biografia do Instagram</label>
                      <textarea
                        id="bioInsta"
                        class="form-control"
                        [(ngModel)]="lead.bioInsta"
                        name="bioInsta"
                        rows="3"
                        placeholder="Biografia extraída do Instagram...">{{dadosInstagram.user.biography}}</textarea>
                    </div>
                  </div>

                  <!-- Observações -->
                  <div class="col-md-6" *ngIf="lead.observacoes">
                    <div class="form-group">
                      <label for="observacoesStep1">Observações</label>
                      <textarea
                        id="observacoesStep1"
                        class="form-control"
                        [(ngModel)]="lead.observacoes"
                        name="observacoesStep1"
                        rows="3"
                        placeholder="Observações sobre o lead..."></textarea>
                    </div>
                    </div>
                  </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- ===== PASSO 2: BUSCAR LINKS ===== -->
      <div *ngIf="currentStep === 2" class="step-content step-2">
        
        <!-- Resumo do Passo Anterior -->
        <div class="row">
          <div class="col-12">
            <div class="card summary-card">
              <div class="card-body">
                <h6 class="card-title">
                  <i class="fas fa-check text-success"></i>
                  Dados Básicos (Passo 1)
                </h6>
                <div class="summary-content">
                  <span class="badge badge-primary">@{{lead.instagramHandle}}</span>
                  <strong>{{lead.nomeResponsavel}}</strong> - {{lead.empresa}} - {{lead.cidade}}
                  <button class="btn btn-sm btn-outline-secondary float-right" (click)="goToStep(1)">
                    <i class="fas fa-edit"></i> Editar
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Card Principal: Análise de Website -->
        <div class="row">
          <div class="col-12">
            <div class="card main-card">
              <div class="card-body">
                <h5 class="card-title">
                  <i class="fas fa-link text-primary"></i>
                  Analisar Website da Empresa
                </h5>
                <p class="card-text text-muted">
                  Informe o website da empresa para descobrir links relevantes automaticamente
                </p>

                <!-- Website Input -->
                <div class="form-group">
                  <label for="websiteAnalysis">Website da Empresa</label>
                  <div class="input-group">
                    <input
                      type="url"
                      id="websiteAnalysis"
                      class="form-control"
                      [(ngModel)]="lead.website"
                      name="websiteAnalysis"
                      placeholder="https://restaurante-exemplo.com.br"
                      (blur)="onWebsiteBlur()">
                    <div class="input-group-append">
                      <button
                        type="button"
                        class="btn btn-primary"
                        (click)="carregarDadosDoLink()"
                        [disabled]="carregandoWebsite || !lead.website">
                        <i class="fas fa-search" *ngIf="!carregandoWebsite"></i>
                        <i class="fas fa-spinner fa-spin" *ngIf="carregandoWebsite"></i>
                        <span *ngIf="!carregandoWebsite">Analisar Website</span>
                        <span *ngIf="carregandoWebsite">Analisando...</span>
                      </button>
                    </div>
                  </div>
                  <small class="form-text text-muted">
                    A análise irá descobrir links para WhatsApp, Instagram, iFood, cardápios e muito mais
                  </small>
                </div>

                <!-- Aviso se não tiver website -->
                <div class="alert alert-info" *ngIf="!lead.website">
                  <i class="fas fa-info-circle"></i>
                  <strong>Etapa Opcional:</strong> Se a empresa não possui website, você pode pular esta etapa.
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Links Encontrados -->
        <div class="row" *ngIf="linksEncontrados && linksEncontrados.length > 0">
          <div class="col-12">
            <div class="card results-card">
              <div class="card-body">
                <h6 class="card-title">
                  <i class="fas fa-check text-success"></i>
                  Links Encontrados ({{linksEncontrados.length}})
                </h6>
                
                <div class="links-grid">
                  <div
                    *ngFor="let link of linksEncontrados"
                    class="link-item"
                    [class.link-whatsapp]="link.tipo === 'WhatsApp'"
                    [class.link-instagram]="link.tipo === 'Instagram'"
                    [class.link-ifood]="link.tipo === 'Ifood'"
                    [class.link-cardapio]="link.tipo === 'Site do Cardápio'"
                    [class.link-concorrente]="link.tipo === 'Concorrente'"
                    [class.link-localizacao]="link.tipo === 'Localização'"
                    [class.link-site]="link.tipo === 'Site'">
                    <div class="link-icon">
                      <i class="fab fa-whatsapp" *ngIf="link.tipo === 'WhatsApp'"></i>
                      <i class="fab fa-instagram" *ngIf="link.tipo === 'Instagram'"></i>
                      <i class="fas fa-utensils" *ngIf="link.tipo === 'Ifood'"></i>
                      <i class="fas fa-list-alt" *ngIf="link.tipo === 'Site do Cardápio'"></i>
                      <i class="fas fa-exclamation-triangle" *ngIf="link.tipo === 'Concorrente'"></i>
                      <i class="fas fa-map-marker-alt" *ngIf="link.tipo === 'Localização'"></i>
                      <i class="fas fa-globe" *ngIf="link.tipo === 'Site'"></i>
                      <i class="fas fa-link" *ngIf="!['WhatsApp', 'Instagram', 'Ifood', 'Site do Cardápio', 'Concorrente', 'Localização', 'Site'].includes(link.tipo)"></i>
                    </div>
                    <div class="link-content">
                      <div class="link-tipo">{{link.tipo}}</div>
                      <div class="link-descricao">{{link.descricao}}</div>
                      <div class="link-url">{{link.url | slice:0:40}}{{link.url.length > 40 ? '...' : ''}}</div>
                    </div>
                    <div class="link-actions">
                      <button
                        type="button"
                        class="btn btn-sm btn-outline-primary"
                        (click)="abrirLink(link.url)"
                        title="Abrir link">
                        <i class="fas fa-external-link-alt"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-sm btn-outline-secondary"
                        (click)="copiarLink(link.url)"
                        title="Copiar link">
                        <i class="fas fa-copy"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- ===== PASSO 3: DESCOBRIR CNPJ ===== -->
      <div *ngIf="currentStep === 3" class="step-content step-3">
        
        <!-- Resumo dos Passos Anteriores -->
        <div class="row">
          <div class="col-12">
            <div class="card summary-card">
              <div class="card-body">
                <h6 class="card-title">
                  <i class="fas fa-check text-success"></i>
                  Progresso Anterior
                </h6>
                <div class="summary-content">
                  <div class="summary-item">
                    <strong>Dados:</strong> {{lead.empresa}} - {{lead.cidade}} (@{{lead.instagramHandle}})
                  </div>
                  <div class="summary-item" *ngIf="linksEncontrados.length > 0">
                    <strong>Links:</strong> {{linksEncontrados.length}} encontrados
                  </div>
                  <button class="btn btn-sm btn-outline-secondary float-right" (click)="goToStep(1)">
                    <i class="fas fa-edit"></i> Editar
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Card Principal: Descoberta de CNPJ -->
        <div class="row">
          <div class="col-12">
            <div class="card main-card">
              <div class="card-body">
                <h5 class="card-title">
                  <i class="fas fa-id-card text-info"></i>
                  Descobrir CNPJ da Empresa
                </h5>
                <p class="card-text text-muted">
                  Encontre o CNPJ oficial da empresa através de busca inteligente
                </p>

                <!-- Inputs de Busca -->
                <div class="row">
                  <div class="col-md-5">
                    <div class="form-group">
                      <label for="empresaBusca">Nome da Empresa</label>
                      <input
                        type="text"
                        id="empresaBusca"
                        class="form-control"
                        [(ngModel)]="lead.empresa"
                        name="empresaBusca"
                        placeholder="Nome exato da empresa">
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <label for="cidadeBusca">Cidade</label>
                      <input
                        type="text"
                        id="cidadeBusca"
                        class="form-control"
                        [(ngModel)]="lead.cidade"
                        name="cidadeBusca"
                        placeholder="Cidade da empresa">
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-group">
                      <label>&nbsp;</label>
                      <button
                        type="button"
                        class="btn btn-primary btn-block"
                        (click)="descobrirCnpj()"
                        [disabled]="carregandoCnpj || !lead.empresa || !lead.cidade">
                        <i class="fas fa-search" *ngIf="!carregandoCnpj"></i>
                        <i class="fas fa-spinner fa-spin" *ngIf="carregandoCnpj"></i>
                        <span *ngIf="!carregandoCnpj">Buscar CNPJs</span>
                        <span *ngIf="carregandoCnpj">Buscando...</span>
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Aviso se campos não preenchidos -->
                <div class="alert alert-info" *ngIf="!lead.empresa || !lead.cidade">
                  <i class="fas fa-info-circle"></i>
                  <strong>Etapa Opcional:</strong> Se não souber o CNPJ da empresa, você pode pular esta etapa.
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- CNPJs Encontrados -->
        <div class="row" *ngIf="cnpjsEncontrados && cnpjsEncontrados.length > 0">
          <div class="col-12">
            <div class="card results-card">
              <div class="card-body">
                <h6 class="card-title">
                  <i class="fas fa-check text-success"></i>
                  CNPJs Encontrados ({{cnpjsEncontrados.length}})
                  <button 
                    type="button" 
                    class="btn btn-sm btn-outline-secondary float-right"
                    (click)="removerSelecaoCnpj()"
                    *ngIf="lead.cnpj">
                    <i class="fas fa-times"></i> Limpar Seleção
                  </button>
                </h6>
                
                <!-- Grid de CNPJs (3 colunas) -->
                <div class="cnpjs-grid">
                  <div
                    *ngFor="let cnpj of cnpjsEncontrados"
                    class="cnpj-card"
                    [class.selected]="cnpj.selecionado">
                    <div class="cnpj-header">
                      <span class="cnpj-badge" [class]="'badge-' + getCorConfianca(cnpj.confianca)">
                        {{cnpj.confianca | titlecase}}
                      </span>
                      <div class="cnpj-number">{{cnpj.cnpj}}</div>
                    </div>
                    
                    <div class="cnpj-info">
                      <!-- Nome Fantasia -->
                      <div class="cnpj-name" *ngIf="cnpj.nomeFantasia">
                        <i class="fas fa-store"></i>
                        {{cnpj.nomeFantasia}}
                      </div>
                      
                      <!-- Razão Social -->
                      <div class="cnpj-razao-social" *ngIf="cnpj.razaoSocial">
                        <i class="fas fa-building"></i>
                        <small>{{cnpj.razaoSocial}}</small>
                      </div>
                      
                      <!-- Endereço -->
                      <div class="cnpj-endereco" *ngIf="cnpj.endereco">
                        <i class="fas fa-map-marker-alt"></i>
                        <small>{{cnpj.endereco}}</small>
                      </div>
                      
                      <!-- Capital Social -->
                      <div class="cnpj-capital" *ngIf="cnpj.capitalSocial">
                        <i class="fas fa-dollar-sign"></i>
                        <small><strong>Capital:</strong> {{cnpj.capitalSocial}}</small>
                      </div>
                      
                      <!-- Atividade Principal -->
                      <div class="cnpj-atividade" *ngIf="cnpj.atividadePrincipal">
                        <i class="fas fa-briefcase"></i>
                        <small><strong>Atividade:</strong> {{cnpj.atividadePrincipal}}</small>
                      </div>
                      
                      <!-- Sócios -->
                      <div class="cnpj-socios" *ngIf="cnpj.socios && cnpj.socios.length > 0">
                        <i class="fas fa-users"></i>
                        <small><strong>Sócios:</strong> {{cnpj.socios.join(', ')}}</small>
                      </div>
                      
                      <!-- Status e Fonte -->
                      <div class="cnpj-footer-info">
                        <div class="cnpj-status" *ngIf="cnpj.situacao">
                          <span class="status-badge" [class.active]="cnpj.situacao === 'ATIVA'">
                            {{cnpj.situacao}}
                          </span>
                        </div>
                        <div class="cnpj-fonte" *ngIf="cnpj.fonte">
                          <small class="text-muted"><i class="fas fa-info-circle"></i> {{cnpj.fonte}}</small>
                        </div>
                      </div>
                    </div>
                    <div class="cnpj-actions">
                      <button
                        type="button"
                        class="btn btn-select"
                        [class.btn-success]="cnpj.selecionado"
                        [class.btn-primary]="!cnpj.selecionado"
                        (click)="selecionarCnpj(cnpj)">
                        <i class="fas fa-check" *ngIf="cnpj.selecionado"></i>
                        <i class="fas fa-plus" *ngIf="!cnpj.selecionado"></i>
                        {{cnpj.selecionado ? 'Selecionado' : 'Selecionar'}}
                      </button>
                      <button
                        type="button"
                        class="btn btn-outline-info btn-details"
                        (click)="abrirCnpjBiz(cnpj.cnpj)"
                        title="Ver detalhes">
                        <i class="fas fa-external-link-alt"></i>
                        Ver Detalhes
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- ===== PASSO 4: FINALIZAR ===== -->
      <div *ngIf="currentStep === 4" class="step-content step-4">
        
        <!-- Resumo Completo -->
        <div class="row">
          <div class="col-md-8">
            <div class="card summary-card">
              <div class="card-body">
                <h6 class="card-title">
                  <i class="fas fa-clipboard-list"></i>
                  Resumo do Lead
                </h6>
                
                <!-- Dados Básicos -->
                <div class="summary-section">
                  <h6><i class="fas fa-user"></i> Dados Básicos</h6>
                  <div class="summary-grid">
                    <div><strong>Responsável:</strong> {{lead.nomeResponsavel}}</div>
                    <div><strong>Empresa:</strong> {{lead.empresa}}</div>
                    <div><strong>Cidade:</strong> {{lead.cidade}}</div>
                    <div><strong>Telefone:</strong> {{lead.telefone || 'Não informado'}}</div>
                    <div><strong>Instagram:</strong> @{{lead.instagramHandle}}</div>
                    <div><strong>Website:</strong> {{lead.website || 'Não informado'}}</div>
                  </div>
                </div>

                <!-- Links Encontrados -->
                <div class="summary-section" *ngIf="linksEncontrados.length > 0">
                  <h6><i class="fas fa-link"></i> Links Encontrados ({{linksEncontrados.length}})</h6>
                  <div class="links-summary">
                    <span *ngFor="let link of linksEncontrados" class="link-tag">
                      {{link.tipo}}
                    </span>
                  </div>
                </div>

                <!-- CNPJ Selecionado -->
                <div class="summary-section" *ngIf="lead.cnpj">
                  <h6><i class="fas fa-id-card"></i> CNPJ Selecionado</h6>
                  <div class="cnpj-summary">
                    <div><strong>CNPJ:</strong> {{lead.cnpj}}</div>
                    <div><strong>Empresa:</strong> {{lead.empresa}}</div>
                    <div *ngIf="lead.endereco"><strong>Endereço:</strong> {{lead.endereco}}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Configurações Finais -->
          <div class="col-md-4">
            <div class="card form-card">
              <div class="card-body">
                <h6 class="card-title">
                  <i class="fas fa-cog"></i>
                  Configurações do Lead
                </h6>

                <!-- Etapa do Funil -->
                <div class="form-group">
                  <label for="etapa">Etapa do Funil</label>
                  <select
                    id="etapa"
                    class="form-control"
                    [(ngModel)]="lead.etapa"
                    name="etapa">
                    <option *ngFor="let etapa of etapas" [value]="etapa.valor">
                      {{etapa.texto}}
                    </option>
                  </select>
                </div>

                <!-- Origem -->
                <div class="form-group">
                  <label for="origem">Origem do Lead</label>
                  <select
                    id="origem"
                    class="form-control"
                    [(ngModel)]="lead.origem"
                    name="origem">
                    <option *ngFor="let origem of origens" [value]="origem.valor">
                      {{origem.texto}}
                    </option>
                  </select>
                </div>

                <!-- Segmento -->
                <div class="form-group">
                  <label for="segmento">Segmento</label>
                  <select
                    id="segmento"
                    class="form-control"
                    [(ngModel)]="lead.segmento"
                    name="segmento">
                    <option *ngFor="let segmento of segmentos" [value]="segmento.valor">
                      {{segmento.texto}}
                    </option>
                  </select>
                </div>

                <!-- Observações -->
                <div class="form-group">
                  <label for="observacoes">Observações</label>
                  <textarea
                    id="observacoes"
                    class="form-control"
                    [(ngModel)]="lead.observacoes"
                    name="observacoes"
                    rows="3"
                    placeholder="Observações sobre o lead..."></textarea>
                </div>

                <!-- Sincronizar com Bitrix -->
                <div class="form-check">
                  <input
                    type="checkbox"
                    class="form-check-input"
                    id="sincronizarBitrix"
                    [(ngModel)]="sincronizarBitrix"
                    name="sincronizarBitrix">
                  <label class="form-check-label" for="sincronizarBitrix">
                    Sincronizar com Bitrix24
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>

  <!-- Footer de Navegação -->
  <div class="wizard-footer">
    <div class="container-fluid">
      <div class="row">
        <div class="col-12">
          <div class="navigation-buttons">
            <!-- Botão Voltar -->
            <button
              type="button"
              class="btn btn-outline-secondary"
              (click)="prevStep()"
              *ngIf="currentStep > 1">
              <i class="fas fa-arrow-left"></i>
              Voltar
            </button>

            <!-- Espaçador -->
            <div class="flex-grow-1"></div>

            <!-- Mensagem de erro -->
            <div class="alert alert-danger mb-0 mr-3" *ngIf="erro">
              <i class="fas fa-exclamation-triangle"></i>
              {{erro}}
            </div>

            <!-- Botão Pular (apenas passos 2 e 3) -->
            <button
              type="button"
              class="btn btn-outline-warning mr-3"
              (click)="skipStep()"
              *ngIf="canSkipCurrentStep()">
              <i class="fas fa-forward"></i>
              Pular Etapa
            </button>

            <!-- Botão Próximo -->
            <button
              type="button"
              class="btn btn-primary"
              (click)="nextStep()"
              [disabled]="!canAdvance()"
              *ngIf="currentStep < totalSteps">
              Próximo
              <i class="fas fa-arrow-right"></i>
            </button>

            <!-- Botão Salvar Lead -->
            <button
              type="button"
              class="btn btn-success"
              (click)="salvarLead()"
              [disabled]="carregando"
              *ngIf="currentStep === totalSteps">
              <i class="fas fa-save" *ngIf="!carregando"></i>
              <i class="fas fa-spinner fa-spin" *ngIf="carregando"></i>
              {{carregando ? 'Salvando...' : 'Salvar Lead'}}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>