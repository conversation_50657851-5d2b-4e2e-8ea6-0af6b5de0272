import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { InstagramDataService } from '../services/instagram-data.service';
import { LeadService } from '../services/lead.service';
import { CrmEmpresaService } from '../services/crm-empresa.service';

@Component({
  selector: 'app-novo-lead',
  templateUrl: './novo-lead.component.html',
  styleUrls: ['./novo-lead.component.scss']
})
export class NovoLeadComponent implements OnInit {

  // Dados do Instagram
  dadosInstagram: any = null;
  username: string = '';

  // Lead processado pela API dadosig2
  leadProcessadoAPI: any = null;

  // Estado do componente
  carregando = false;
  carregandoWebsite = false;
  carregandoCnpj = false;
  erro = '';
  mostrarFormulario = false;
  sincronizarBitrix = false;

  // Links encontrados do website
  linksEncontrados: any[] = [];
  
  // Telefones múltiplos
  telefonesEncontrados: any[] = [];
  
  // CNPJs encontrados
  cnpjsEncontrados: any[] = [];

  // Wizard state
  currentStep = 1;
  totalSteps = 4;
  wizardData = {
    dadosBasicos: {},
    linksEncontrados: [],
    cnpjSelecionado: null,
    configuracoes: {}
  };

  // Controle de validação das etapas
  websiteAnalisado = false;
  cnpjBuscado = false;
  etapaFoiPulada = false;

  // Dados do formulário
  lead: any = {
    nomeResponsavel: '',
    empresa: '',
    cidade: '',
    endereco: '',
    cnpj: '',
    telefone: '',
    instagramHandle: '',
    website: '',
    linkCardapio: '',
    bioInsta: '',
    biografia: '',
    observacoes: '',
    origem: 'Instagram',
    etapa: 'Prospecção',
    segmento: 'Alimentação',
    crmEmpresaId: null
  };

  // Lista de empresas CRM para seleção
  crmEmpresas: any[] = [];

  // Opções para dropdowns
  etapas = [
    { valor: 'Prospecção', texto: 'Prospecção' },
    { valor: 'Qualificação', texto: 'Qualificação' },
    { valor: 'Objeção', texto: 'Objeção' },
    { valor: 'Fechamento', texto: 'Fechamento' },
    { valor: 'Ganho', texto: 'Ganho' },
    { valor: 'Perdido', texto: 'Perdido' }
  ];

  origens = [
    { valor: 'Instagram', texto: 'Instagram' },
    { valor: 'Site/Landing Page', texto: 'Site/Landing Page' },
    { valor: 'WhatsApp Direto', texto: 'WhatsApp Direto' },
    { valor: 'Indicação', texto: 'Indicação' },
    { valor: 'Evento/Feira', texto: 'Evento/Feira' },
    { valor: 'Outros', texto: 'Outros' }
  ];

  segmentos = [
    { valor: 'Alimentação', texto: 'Alimentação' },
    { valor: 'Varejo', texto: 'Varejo' },
    { valor: 'Serviços', texto: 'Serviços' },
    { valor: 'Saúde', texto: 'Saúde' },
    { valor: 'Educação', texto: 'Educação' },
    { valor: 'Outros', texto: 'Outros' }
  ];

  tiposTelefone = [
    { valor: 'WhatsApp', texto: 'WhatsApp', icone: 'fa-whatsapp', cor: '#25d366' },
    { valor: 'Telefone Fixo', texto: 'Telefone Fixo', icone: 'fa-phone', cor: '#6c757d' },
    { valor: 'Celular', texto: 'Celular', icone: 'fa-mobile-alt', cor: '#007bff' },
    { valor: 'Comercial', texto: 'Comercial', icone: 'fa-briefcase', cor: '#28a745' },
    { valor: 'Emergência', texto: 'Emergência', icone: 'fa-exclamation-triangle', cor: '#dc3545' }
  ];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private instagramDataService: InstagramDataService,
    private leadService: LeadService,
    private crmEmpresaService: CrmEmpresaService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    // Pega o username da URL
    this.route.queryParams.subscribe(params => {
      this.username = params['username'] || '';
      console.log('Username da URL:', this.username);
    });

    // Recupera dados do Instagram do service
    const dadosService = this.instagramDataService.getDados();
    if (dadosService) {
      this.dadosInstagram = dadosService.dados;
      this.username = dadosService.username;
      this.preencherFormularioComDadosInstagram();
    } else {
      console.warn('Nenhum dado do Instagram encontrado no service');

      // Se tem username, configura listener mas NÃO busca automaticamente
      if (this.username) {
        console.log('Username presente, aguardando ação do usuário para buscar dados');
        this.setupInstagramDataListener();
      } else {
        // Se não tem username, redireciona para home
        this.router.navigate(['/crm/home']);
      }
    }

    // Carrega lista de empresas CRM
    this.carregarCrmEmpresas();
  }

  /**
   * Preenche o formulário com dados do Instagram
   */
  private preencherFormularioComDadosInstagram(): void {
    if (!this.dadosInstagram || !this.dadosInstagram.user) {
      console.warn('Dados do Instagram não encontrados ou estrutura incorreta:', this.dadosInstagram);
      return;
    }

    const user = this.dadosInstagram.user;

    this.lead.nomeResponsavel = user.full_name || user.username || '';
    this.lead.empresa = user.full_name || user.username || '';
    this.lead.instagramHandle = user.username || '';
    this.lead.website = user.external_url || '';
    this.lead.bioInsta = user.biography || '';
    this.lead.biografia = user.biography || '';
    this.lead.telefone = user.business_phone_number || '';

    console.log('Formulário preenchido com dados do Instagram:', this.lead);
  }

  /**
   * Preenche o formulário com dados do lead processado pela API
   * Agora recebe um objeto Lead completo com CrmEmpresa
   */
  private preencherFormularioComLeadProcessado(leadProcessado: any): void {
    if (!leadProcessado) {
      console.warn('Lead processado não encontrado:', leadProcessado);
      return;
    }

    console.log('Preenchendo formulário com lead processado (objeto Lead completo):', leadProcessado);

    // Armazena o lead processado para uso posterior
    this.leadProcessadoAPI = leadProcessado;

    // Preenche campos do formulário com dados do Lead
    this.lead.nomeResponsavel = leadProcessado.nomeResponsavel || '';
    this.lead.empresa = leadProcessado.empresa || '';
    this.lead.cidade = leadProcessado.cidade || '';
    this.lead.endereco = leadProcessado.endereco || '';
    this.lead.telefone = leadProcessado.telefone || '';
    
    // Log de endereço extraído
    if (leadProcessado.endereco) {
      console.log('Endereço extraído e preenchido no formulário:', leadProcessado.endereco);
    }
    this.lead.instagramHandle = leadProcessado.instagramHandle || '';
    this.lead.website = leadProcessado.website || (leadProcessado.instagramData?.website || '');
    this.lead.bioInsta = leadProcessado.bioInsta || '';
    this.lead.biografia = leadProcessado.bioInsta || '';
    this.lead.origem = leadProcessado.origem || 'Instagram';
    this.lead.etapa = leadProcessado.etapa || 'Prospecção';
    this.lead.score = leadProcessado.score || 0;

    // Mapear segmento baseado na categoria do negócio
    if (leadProcessado.instagramData?.businessCategory) {
      this.lead.segmento = this.mapearSegmento(leadProcessado.instagramData.businessCategory);
    } else {
      this.lead.segmento = 'Alimentação'; // padrão
    }

    // Define empresa CRM se disponível
    if (leadProcessado.crmEmpresaId) {
      this.lead.crmEmpresaId = leadProcessado.crmEmpresaId;
    }

    // Processa telefones múltiplos se disponíveis
    if (leadProcessado.telefones && Array.isArray(leadProcessado.telefones)) {
      this.telefonesEncontrados = leadProcessado.telefones.map((telefone: any, index: number) => ({
        id: `temp_${index}`,
        tipo: telefone.tipo,
        numero: telefone.numero,
        descricao: telefone.descricao || '',
        numeroFormatado: this.formatarTelefone(telefone.numero),
        icone: this.getIconeTelefone(telefone.tipo),
        cor: this.getCorTelefone(telefone.tipo)
      }));
      console.log('Telefones processados no frontend:', this.telefonesEncontrados);
    }

    // Adiciona informações extras do Instagram nas notas se não estiverem presentes
    if (leadProcessado.notas) {
      this.lead.observacoes = leadProcessado.notas;
    }

    // Simula dados do Instagram para o template (se necessário)
    if (leadProcessado.instagramData) {
      this.dadosInstagram = {
        user: {
          username: leadProcessado.instagramHandle,
          full_name: leadProcessado.empresa,
          biography: leadProcessado.bioInsta,
          business_phone_number: leadProcessado.telefone,
          edge_followed_by: { count: leadProcessado.instagramData.followers },
          edge_follow: { count: leadProcessado.instagramData.following },
          is_business_account: leadProcessado.instagramData.accountType === 'Business',
          business_category_name: leadProcessado.instagramData.businessCategory,
          external_url: leadProcessado.instagramData.website,
          profile_pic_url: leadProcessado.avatarUrl
        }
      };
    }

    console.log('Formulário preenchido com lead processado:', this.lead);
    console.log('Dados do Instagram simulados para template:', this.dadosInstagram);
  }

  /**
   * Mapeia categoria de negócio para segmento
   */
  private mapearSegmento(categoria: string): string {
    if (!categoria) return 'Outros';

    const categoriaLower = categoria.toLowerCase();

    if (categoriaLower.includes('restaurante') ||
        categoriaLower.includes('comida') ||
        categoriaLower.includes('food') ||
        categoriaLower.includes('pizza') ||
        categoriaLower.includes('lanche') ||
        categoriaLower.includes('café') ||
        categoriaLower.includes('bar') ||
        categoriaLower.includes('japonês') ||
        categoriaLower.includes('delivery')) {
      return 'Alimentação';
    }

    if (categoriaLower.includes('loja') ||
        categoriaLower.includes('varejo') ||
        categoriaLower.includes('shop')) {
      return 'Varejo';
    }

    if (categoriaLower.includes('serviço') ||
        categoriaLower.includes('service')) {
      return 'Serviços';
    }

    if (categoriaLower.includes('saúde') ||
        categoriaLower.includes('health') ||
        categoriaLower.includes('médico') ||
        categoriaLower.includes('clínica')) {
      return 'Saúde';
    }

    if (categoriaLower.includes('educação') ||
        categoriaLower.includes('education') ||
        categoriaLower.includes('escola') ||
        categoriaLower.includes('curso')) {
      return 'Educação';
    }

    return 'Outros';
  }

  /**
   * Carrega lista de empresas CRM
   */
  private async carregarCrmEmpresas(): Promise<void> {
    try {
      const response = await this.crmEmpresaService.liste();
      if (response.sucesso) {
        this.crmEmpresas = response.dados || [];
      }
    } catch (error) {
      console.error('Erro ao carregar empresas CRM:', error);
    }
  }

  /**
   * Salva o novo lead
   */
  async salvarLead(): Promise<void> {
    if (!this.validarFormulario()) {
      return;
    }

    this.carregando = true;
    this.erro = '';

    try {
      // Se temos um lead processado pela API dadosig2, usa ele como base
      let leadParaSalvar: any;

      if (this.leadProcessadoAPI) {
        // Usa o lead processado pela API como base e aplica modificações do formulário
        leadParaSalvar = { ...this.leadProcessadoAPI };

        // Atualiza com dados modificados no formulário
        leadParaSalvar.nomeResponsavel = this.lead.nomeResponsavel;
        leadParaSalvar.empresa = this.lead.empresa;
        leadParaSalvar.cidade = this.lead.cidade;
        leadParaSalvar.endereco = this.lead.endereco;
        leadParaSalvar.telefone = this.lead.telefone;
        leadParaSalvar.instagramHandle = this.lead.instagramHandle;
        leadParaSalvar.website = this.lead.website;
        leadParaSalvar.linkCardapio = this.lead.linkCardapio;
        leadParaSalvar.bioInsta = this.lead.bioInsta;
        leadParaSalvar.origem = this.lead.origem;
        leadParaSalvar.etapa = this.lead.etapa;
        leadParaSalvar.segmento = this.lead.segmento;
        leadParaSalvar.crmEmpresaId = this.lead.crmEmpresaId;
        leadParaSalvar.observacoes = this.lead.observacoes;

        // Remove campos que não devem ser enviados na criação
        delete leadParaSalvar.id;
        delete leadParaSalvar.dataCriacao;
        delete leadParaSalvar.createdAt;
        delete leadParaSalvar.updatedAt;

        // Adiciona links categorizados se existirem
        if (this.linksEncontrados && this.linksEncontrados.length > 0) {
          leadParaSalvar.links = this.linksEncontrados;
          console.log('Adicionando links categorizados ao lead processado:', this.linksEncontrados);
        }

        // Adiciona telefones múltiplos se existirem
        if (this.telefonesEncontrados && this.telefonesEncontrados.length > 0) {
          leadParaSalvar.telefones = this.telefonesEncontrados.map(tel => ({
            tipo: tel.tipo,
            numero: tel.numero,
            descricao: tel.descricao
          }));
          console.log('Adicionando telefones ao lead processado:', leadParaSalvar.telefones);
        }

      } else {
        // Fallback: cria lead do zero (modo manual)
        leadParaSalvar = { ...this.lead };

        // Adiciona dados do Instagram de forma estruturada (se disponível)
        if (this.dadosInstagram && this.dadosInstagram.user) {
          const user = this.dadosInstagram.user;

          // Dados básicos
          leadParaSalvar.avatarUrl = user.profile_pic_url;

          // Estrutura instagramData
          leadParaSalvar.instagramData = {
            bio: user.biography,
            followers: user.edge_followed_by?.count,
            following: user.edge_follow?.count,
            accountType: user.is_business_account ? 'Business' : 'Pessoal',
            businessCategory: user.business_category_name || user.category_name,
            location: user.business_address_json ? JSON.stringify(user.business_address_json) : undefined,
            website: user.external_url
          };
        }

        // Adiciona links categorizados se existirem (modo manual)
        if (this.linksEncontrados && this.linksEncontrados.length > 0) {
          leadParaSalvar.links = this.linksEncontrados;
          console.log('Adicionando links categorizados ao lead manual:', this.linksEncontrados);
        }

        // Adiciona telefones múltiplos se existirem (modo manual)
        if (this.telefonesEncontrados && this.telefonesEncontrados.length > 0) {
          leadParaSalvar.telefones = this.telefonesEncontrados.map(tel => ({
            tipo: tel.tipo,
            numero: tel.numero,
            descricao: tel.descricao
          }));
          console.log('Adicionando telefones ao lead manual:', leadParaSalvar.telefones);
        }
      }

      console.log('Salvando lead:', leadParaSalvar);
      console.log('Sincronizar com Bitrix:', this.sincronizarBitrix);
      console.log('Total de links categorizados a serem enviados:', leadParaSalvar.links?.length || 0);

      if (leadParaSalvar.links && leadParaSalvar.links.length > 0) {
        console.log('Links categorizados detalhados:');
        leadParaSalvar.links.forEach((link: any, index: number) => {
          console.log(`  ${index + 1}. ${link.tipo}: ${link.url} (${link.descricao})`);
        });
      }

      // Adicionar flag de sincronização no objeto a ser salvo
      if (this.sincronizarBitrix) {
        leadParaSalvar.sincronizarBitrix = true;
      }

      const leadCriado = await this.leadService.salveLead(leadParaSalvar);

      console.log('Lead criado com sucesso:', leadCriado);

      // Limpa dados do service
      this.instagramDataService.clearDados();

      // Redireciona para a home do lead criado
      this.router.navigate(['/crm/home', this.username]);
    } catch (error) {
      console.error('Erro ao salvar lead:', error);
      this.erro = 'Erro ao criar lead. Tente novamente.';
    } finally {
      this.carregando = false;
    }
  }

  /**
   * Valida o formulário
   */
  private validarFormulario(): boolean {
    if (!this.lead.nomeResponsavel) {
      this.erro = 'Nome do responsável é obrigatório';
      return false;
    }
    if (!this.lead.empresa) {
      this.erro = 'Nome da empresa é obrigatório';
      return false;
    }
    if (!this.lead.cidade) {
      this.erro = 'Cidade da empresa é obrigatória';
      return false;
    }
    if (!this.lead.instagramHandle) {
      this.erro = 'Username do Instagram é obrigatório';
      return false;
    }
    return true;
  }

  /**
   * Cancela e volta para a home
   */
  cancelar(): void {
    this.instagramDataService.clearDados();
    this.router.navigate(['/crm/home', this.username]);
  }

  /**
   * Retorna a URL da foto do perfil do Instagram
   */
  getFotoPerfilInstagram(): string {
    if (this.dadosInstagram?.user?.profile_pic_url) {
      return this.dadosInstagram.user.profile_pic_url;
    }
    return '/assets/images/default-avatar.png'; // fallback
  }

  /**
   * Retorna o número de seguidores formatado
   */
  getSeguidoresFormatado(): string {
    const count = this.dadosInstagram?.user?.edge_followed_by?.count;
    if (!count) return '0';

    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  }

  /**
   * Solicita dados do Instagram via content script
   */
  solicitarDadosInstagram(): void {
    // Enviar mensagem para content script via iframe
    const message = {
      tipo: 'REQUEST_INSTAGRAM_DATA',
      username: this.username
    };

    // Comunicação via postMessage para parent window (content script)
    if (window.parent && window.parent !== window) {
      window.parent.postMessage({tipo: "NOVA_MENSAGEM", text: message}, "*");
    }

    console.log('Solicitação de dados enviada para content script:', message);
  }

  /**
   * Configura listener para eventos do Instagram
   */
  private setupInstagramDataListener(): void {
    window.addEventListener('message', (event) => {
      // Verifica se é um evento de resposta do Instagram
      if (event.data && event.data.tipo === 'INSTAGRAM_DATA_RESPONSE') {
        console.log('Dados do Instagram recebidos:', event.data);
        this.processarDadosInstagram(event.data);
      }

      // Verifica se houve erro na busca
      if (event.data && event.data.tipo === 'INSTAGRAM_DATA_ERROR') {
        console.error('Erro ao buscar dados do Instagram:', event.data);
        this.processarErroInstagram(event.data);
      }
    });
  }

  /**
   * Processa os dados recebidos do Instagram
   */
  private async processarDadosInstagram(dadosInstagram: any): Promise<void> {
    console.log('Processando dados do Instagram para username:', dadosInstagram.username);

    try {
      // Primeiro, envia texto do Instagram para a API dadosig2 para processamento
      console.log('Enviando texto do Instagram para API dadosig2...');
      this.carregando = true;

      const leadProcessado = await this.leadService.enviarDadosInstagram(dadosInstagram.textoInsta, null, dadosInstagram.username);

      console.log('Lead processado pela API dadosig2:', leadProcessado);

      if (leadProcessado) {
        // ServerService já extraiu o 'dados', então leadProcessado é o objeto Lead completo
        console.log('Objeto Lead processado com sucesso:', leadProcessado);

        // Define username
        this.username = dadosInstagram.username;

        // Preenche formulário com dados do lead processado pela API
        // A função preencherFormularioComLeadProcessado agora simula dadosInstagram para o template
        this.preencherFormularioComLeadProcessado(leadProcessado);

        // Salva dados do Instagram no service (se necessário para outras funcionalidades)
        this.instagramDataService.setDados(this.dadosInstagram, this.username);

        console.log('Lead processado e formulário preenchido:', this.lead);
        console.log('Dados simulados para template:', this.dadosInstagram);
      } else {
        console.error('Erro: resposta vazia da API dadosig2');
        this.erro = 'Erro ao processar dados do Instagram na API.';
      }
    } catch (error) {
      console.error('Erro ao enviar dados para API dadosig2:', error);
      this.erro = 'Erro ao processar dados do Instagram. Tente novamente.';
    } finally {
      this.carregando = false;
    }
  }

  /**
   * Envia dados do Instagram para a API (caso necessário futuramente)
   */
  private async enviarDadosInstagramParaAPI(dadosInstagram: any): Promise<void> {
    try {
      console.log('Enviando dados do Instagram para API:', dadosInstagram.data);

      const response = await this.leadService.enviarDadosInstagram(dadosInstagram.data, null, dadosInstagram.username);

      if (response.sucesso) {
        console.log('Lead criado/atualizado com sucesso:', response.dados);
      } else {
        console.error('Erro na resposta da API:', response.erro);
      }
    } catch (error) {
      console.error('Erro ao enviar dados do Instagram para API:', error);
    }
  }

  /**
   * Processa erros na busca de dados do Instagram
   */
  private processarErroInstagram(errorData: any): void {
    console.error('Erro ao buscar dados do Instagram:', errorData.error);
    this.erro = 'Erro ao buscar dados do Instagram. Tente novamente.';
  }

  /**
   * Exibe o formulário para criação manual de lead
   */
  mostrarFormularioManual(): void {
    this.mostrarFormulario = true;
    console.log('Formulário manual ativado');
  }

  /**
   * Normaliza URL do website quando o usuário sai do campo
   */
  onWebsiteBlur(): void {
    if (this.lead.website) {
      const urlOriginal = this.lead.website;
      this.lead.website = this.normalizarUrl(this.lead.website);
      
      if (urlOriginal !== this.lead.website) {
        console.log('URL normalizada:', urlOriginal, '->', this.lead.website);
      }
    }
  }

  /**
   * Carrega dados extras do website informado
   */
  async carregarDadosDoLink(): Promise<void> {
    if (!this.lead.website) {
      this.erro = 'Informe o website para carregar os dados';
      return;
    }

    // Normalizar URL antes da validação
    this.lead.website = this.normalizarUrl(this.lead.website);

    // Validar se é uma URL válida
    try {
      new URL(this.lead.website);
    } catch {
      this.erro = 'Informe uma URL válida (ex: https://exemplo.com.br)';
      return;
    }

    this.carregandoWebsite = true;
    this.erro = '';
    this.marcarWebsiteAnalisado(); // Marcar como tentativa executada

    try {
      console.log('Carregando dados do website:', this.lead.website);
      
      // Enviar URL para a API para análise
      const dadosWebsite = await this.leadService.analisarWebsite(this.lead.website);

      if (dadosWebsite) {
        console.log('Dados do website recebidos:', dadosWebsite);
        
        // Armazenar links categorizados
        if (dadosWebsite.linksCategorized && Array.isArray(dadosWebsite.linksCategorized)) {
          this.linksEncontrados = dadosWebsite.linksCategorized.sort((a: any, b: any) => a.ordem - b.ordem);
          console.log('Links categorizados recebidos da API:', this.linksEncontrados);
          console.log('Total de links categorizados:', this.linksEncontrados.length);

          // Log detalhado de cada link
          this.linksEncontrados.forEach((link: any, index: number) => {
            console.log(`Link ${index + 1}: ${link.tipo} = ${link.url} (${link.descricao})`);
          });
        } else {
          console.warn('Nenhum link categorizado recebido da API ou formato inválido:', dadosWebsite.linksCategorized);
          this.linksEncontrados = [];
        }

        // Preencher automaticamente telefone do primeiro WhatsApp encontrado
        const whatsappLink = this.linksEncontrados.find((link: any) => link.tipo === 'WhatsApp');
        if (whatsappLink && !this.lead.telefone) {
          // Extrair número do WhatsApp
          const numeroMatch = whatsappLink.url.match(/(\d{10,15})/);
          if (numeroMatch) {
            this.lead.telefone = numeroMatch[1];
          }
        }

        // Preencher automaticamente Instagram handle
        const instagramLink = this.linksEncontrados.find((link: any) => link.tipo === 'Instagram');
        if (instagramLink && !this.lead.instagramHandle) {
          // Extrair username do Instagram
          const usernameMatch = instagramLink.url.match(/instagram\.com\/([^\/\?]+)/);
          if (usernameMatch) {
            this.lead.instagramHandle = usernameMatch[1];
          }
        }

        // Preencher automaticamente link do cardápio (Site do Cardápio ou Concorrente)
        const cardapioLink = this.linksEncontrados.find((link: any) =>
          link.tipo === 'Site do Cardápio' || link.tipo === 'Concorrente'
        );
        if (cardapioLink) {
          this.lead.linkCardapio = cardapioLink.url;
          console.log(`Link de cardápio/concorrente encontrado: ${cardapioLink.tipo} = ${cardapioLink.url}`);
        }

        console.log('Formulário atualizado com dados dos links:', this.lead);
      } else {
        this.erro = 'Não foi possível extrair dados do website informado';
      }
    } catch (error) {
      console.error('Erro ao carregar dados do website:', error);
      this.erro = 'Erro ao analisar o website. Verifique a URL e tente novamente.';
    } finally {
      this.carregandoWebsite = false;
    }
  }

  /**
   * Normaliza URL adicionando https:// se necessário
   */
  private normalizarUrl(url: string): string {
    if (!url) return url;
    
    // Remove espaços extras
    url = url.trim();
    
    // Se já tem protocolo, retorna como está
    if (url.match(/^https?:\/\//i)) {
      return url;
    }
    
    // Se não tem protocolo, adiciona https://
    return `https://${url}`;
  }

  /**
   * Abre um link em nova aba (sempre com https://)
   */
  abrirLink(url: string): void {
    const urlNormalizada = this.normalizarUrl(url);
    window.open(urlNormalizada, '_blank');
    console.log('Abrindo link:', url, '-> normalizado:', urlNormalizada);
  }

  /**
   * Descobre múltiplos CNPJs da empresa usando busca no Google + IA
   */
  async descobrirCnpj(): Promise<void> {
    if (!this.lead.empresa) {
      this.erro = 'Informe o nome da empresa para descobrir o CNPJ';
      return;
    }

    if (!this.lead.cidade) {
      this.erro = 'Informe a cidade da empresa para descobrir o CNPJ';
      return;
    }

    this.carregandoCnpj = true;
    this.erro = '';
    this.cnpjsEncontrados = []; // Limpar resultados anteriores
    this.marcarCnpjBuscado(); // Marcar como tentativa executada

    try {
      console.log('Descobrindo CNPJs para empresa:', this.lead.empresa, 'em', this.lead.cidade);
      
      // ServerService retorna diretamente os dados (response.data) quando sucesso
      const response = await this.leadService.descobrirCnpj(this.lead.empresa, this.lead.cidade);
      
      console.log('Resposta completa do servidor:', response);
      console.log('Tipo da resposta:', typeof response);
      console.log('Estrutura da resposta:', Object.keys(response || {}));
      
      // ServerService já extraiu .data, então response É os dados
      const dados = response;
      
      console.log('Dados extraídos:', dados);
      console.log('CNPJs encontrados:', dados?.cnpjsEncontrados);
      console.log('Total encontrados:', dados?.totalEncontrados);
      
      // Debug detalhado
      console.log('dados existe?', !!dados);
      console.log('dados.cnpjsEncontrados existe?', !!dados?.cnpjsEncontrados);
      console.log('dados.cnpjsEncontrados é array?', Array.isArray(dados?.cnpjsEncontrados));
      console.log('length do array:', dados?.cnpjsEncontrados?.length);
      
      if (dados && dados.cnpjsEncontrados && dados.cnpjsEncontrados.length > 0) {
        this.cnpjsEncontrados = dados.cnpjsEncontrados.map((cnpj: any, index: number) => ({
          ...cnpj,
          id: `cnpj_${index}`,
          selecionado: false
        }));
        
        console.log('CNPJs encontrados:', dados.totalEncontrados);
        console.log('Lista de CNPJs processada:', this.cnpjsEncontrados);
        console.log('Propriedade cnpjsEncontrados.length:', this.cnpjsEncontrados.length);
        console.log('Primeiro CNPJ:', this.cnpjsEncontrados[0]);
        
        // Forçar detecção de mudanças
        this.cdr.detectChanges();
        
        setTimeout(() => {
          console.log('Após timeout - cnpjsEncontrados.length:', this.cnpjsEncontrados.length);
          this.cdr.detectChanges();
        }, 100);
        
        // Se encontrou apenas 1 CNPJ com alta confiança, pode selecionar automaticamente
        if (dados.totalEncontrados === 1 && dados.cnpjsEncontrados[0].confianca === 'alta') {
          this.selecionarCnpj(this.cnpjsEncontrados[0]);
          console.log('CNPJ único com alta confiança selecionado automaticamente');
        }
      } else {
        console.warn('Condição de CNPJs falhou:', {
          dados: !!dados,
          cnpjsEncontrados: !!dados?.cnpjsEncontrados,
          isArray: Array.isArray(dados?.cnpjsEncontrados),
          length: dados?.cnpjsEncontrados?.length,
          dadosCompletos: dados
        });
        
        this.erro = `Nenhum CNPJ encontrado para "${this.lead.empresa}" em ${this.lead.cidade}. Tente com um nome mais específico ou verifique se a empresa possui CNPJ.`;
        console.warn('Nenhum CNPJ encontrado para a busca realizada');
      }
    } catch (error) {
      console.error('Erro ao descobrir CNPJs:', error);
      this.erro = `Erro ao buscar CNPJs: ${error}`;
    } finally {
      this.carregandoCnpj = false;
    }
  }

  /**
   * Seleciona um CNPJ da lista de opções encontradas
   */
  selecionarCnpj(cnpjSelecionado: any): void {
    // Marcar apenas o selecionado
    this.cnpjsEncontrados.forEach(cnpj => {
      cnpj.selecionado = cnpj.id === cnpjSelecionado.id;
    });

    // Preencher dados no formulário
    this.lead.cnpj = cnpjSelecionado.cnpj;
    
    // Atualizar nome da empresa se necessário
    if (cnpjSelecionado.nomeFantasia && cnpjSelecionado.nomeFantasia !== this.lead.empresa) {
      console.log('Atualizando nome da empresa:', cnpjSelecionado.nomeFantasia);
      this.lead.empresa = cnpjSelecionado.nomeFantasia;
    }

    // Adicionar razão social nas observações se disponível
    if (cnpjSelecionado.razaoSocial) {
      const observacaoExtra = `Razão Social: ${cnpjSelecionado.razaoSocial}`;
      if (!this.lead.observacoes) {
        this.lead.observacoes = observacaoExtra;
      } else if (!this.lead.observacoes.includes('Razão Social:')) {
        this.lead.observacoes = `${observacaoExtra}\n\n${this.lead.observacoes}`;
      }
    }

    // Adicionar endereço se disponível e campo estiver vazio
    if (cnpjSelecionado.endereco && !this.lead.endereco) {
      this.lead.endereco = cnpjSelecionado.endereco;
      console.log('Endereço preenchido automaticamente:', cnpjSelecionado.endereco);
    }

    console.log('CNPJ selecionado:', cnpjSelecionado.cnpj);
    console.log('Dados atualizados no formulário:', {
      cnpj: this.lead.cnpj,
      empresa: this.lead.empresa,
      endereco: this.lead.endereco
    });
  }

  /**
   * Remove a seleção de CNPJ
   */
  removerSelecaoCnpj(): void {
    this.cnpjsEncontrados.forEach(cnpj => {
      cnpj.selecionado = false;
    });
    this.lead.cnpj = '';
    console.log('Seleção de CNPJ removida');
  }

  /**
   * Copia CNPJ para a área de transferência
   */
  async copiarCnpj(cnpj: string): Promise<void> {
    try {
      await navigator.clipboard.writeText(cnpj);
      console.log('CNPJ copiado:', cnpj);
    } catch (error) {
      console.error('Erro ao copiar CNPJ:', error);
    }
  }

  /**
   * Abre detalhes do CNPJ no site CNPJ.biz
   */
  abrirCnpjBiz(cnpj: string): void {
    // Remove formatação do CNPJ para usar na URL
    const cnpjLimpo = cnpj.replace(/[^\d]/g, '');
    const url = `https://cnpj.biz/${cnpjLimpo}`;
    window.open(url, '_blank');
    console.log('Abrindo CNPJ.biz para:', cnpj, '-> URL:', url);
  }

  /**
   * Obter cor do badge de confiança
   */
  getCorConfianca(confianca: string): string {
    switch (confianca) {
      case 'alta': return 'success';
      case 'media': return 'warning';
      case 'baixa': return 'danger';
      default: return 'secondary';
    }
  }

  /**
   * Obter ícone de confiança
   */
  getIconeConfianca(confianca: string): string {
    switch (confianca) {
      case 'alta': return 'fa-check-circle';
      case 'media': return 'fa-exclamation-circle';
      case 'baixa': return 'fa-times-circle';
      default: return 'fa-question-circle';
    }
  }

  /**
   * Copia link para a área de transferência (sempre com https://)
   */
  async copiarLink(url: string): Promise<void> {
    try {
      const urlNormalizada = this.normalizarUrl(url);
      await navigator.clipboard.writeText(urlNormalizada);
      console.log('Link copiado:', url, '-> normalizado:', urlNormalizada);
      // Opcional: mostrar notificação de sucesso
    } catch (error) {
      console.error('Erro ao copiar link:', error);
      // Fallback para navegadores antigos
      const urlNormalizada = this.normalizarUrl(url);
      const textArea = document.createElement('textarea');
      textArea.value = urlNormalizada;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
    }
  }

  /**
   * Formatar número de telefone
   */
  formatarTelefone(numero: string): string {
    if (!numero) return '';
    
    const numeroLimpo = numero.replace(/\D/g, '');
    
    if (numeroLimpo.length === 11) {
      // Celular: (XX) 9XXXX-XXXX
      return numeroLimpo.replace(/^(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
    } else if (numeroLimpo.length === 10) {
      // Fixo: (XX) XXXX-XXXX
      return numeroLimpo.replace(/^(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
    }
    
    return numero;
  }

  /**
   * Obter ícone por tipo de telefone
   */
  getIconeTelefone(tipo: string): string {
    const tipoInfo = this.tiposTelefone.find(t => t.valor === tipo);
    return tipoInfo ? tipoInfo.icone : 'fa-phone';
  }

  /**
   * Obter cor por tipo de telefone
   */
  getCorTelefone(tipo: string): string {
    const tipoInfo = this.tiposTelefone.find(t => t.valor === tipo);
    return tipoInfo ? tipoInfo.cor : '#6c757d';
  }

  /**
   * Abrir telefone (WhatsApp ou discador)
   */
  abrirTelefone(telefone: any): void {
    if (telefone.tipo === 'WhatsApp' || telefone.tipo === 'Celular') {
      const numeroLimpo = telefone.numero.replace(/\D/g, '');
      const whatsappUrl = `https://wa.me/55${numeroLimpo}`;
      window.open(whatsappUrl, '_blank');
    } else {
      // Para telefones fixos, apenas copiar o número
      this.copiarTelefone(telefone.numero);
    }
  }

  /**
   * Copiar telefone para a área de transferência
   */
  async copiarTelefone(numero: string): Promise<void> {
    try {
      const numeroFormatado = this.formatarTelefone(numero);
      await navigator.clipboard.writeText(numeroFormatado);
      console.log('Telefone copiado:', numeroFormatado);
      // Opcional: mostrar notificação de sucesso
    } catch (error) {
      console.error('Erro ao copiar telefone:', error);
    }
  }

  // ===== MÉTODOS DO WIZARD =====

  /**
   * Navegar para próximo passo
   */
  nextStep(): void {
    if (this.canAdvance()) {
      this.saveCurrentStepData();
      this.currentStep++;
      this.etapaFoiPulada = false; // Reset flag de pular
      console.log('Avançando para passo:', this.currentStep);
    } else {
      // Mostra mensagem específica do que precisa ser feito
      this.mostrarMensagemValidacao();
    }
  }

  /**
   * Voltar para passo anterior
   */
  prevStep(): void {
    if (this.currentStep > 1) {
      this.saveCurrentStepData();
      this.currentStep--;
      console.log('Voltando para passo:', this.currentStep);
    }
  }

  /**
   * Ir para passo específico
   */
  goToStep(step: number): void {
    if (step >= 1 && step <= this.totalSteps) {
      this.saveCurrentStepData();
      this.currentStep = step;
      console.log('Indo para passo:', this.currentStep);
    }
  }

  /**
   * Verificar se pode avançar para próximo passo
   */
  canAdvance(): boolean {
    switch (this.currentStep) {
      case 1: // Extrair Dados
        return !!(this.lead.nomeResponsavel && this.lead.empresa && this.lead.cidade && this.lead.instagramHandle);
      case 2: // Buscar Links
        // Deve ter analisado website OU não ter website OU ter pulado a etapa
        return this.websiteAnalisado || !this.lead.website || this.etapaFoiPulada;
      case 3: // Descobrir CNPJ
        // Deve ter buscado CNPJ OU não ter dados suficientes OU ter pulado a etapa
        return this.cnpjBuscado || !this.lead.empresa || !this.lead.cidade || this.etapaFoiPulada;
      case 4: // Finalizar
        return true;
      default:
        return false;
    }
  }

  /**
   * Verificar se passo foi completado
   */
  isStepCompleted(step: number): boolean {
    switch (step) {
      case 1:
        return !!(this.lead.nomeResponsavel && this.lead.empresa && this.lead.cidade);
      case 2:
        return this.linksEncontrados.length > 0;
      case 3:
        return !!this.lead.cnpj;
      case 4:
        return false; // Nunca completo até salvar
      default:
        return false;
    }
  }

  /**
   * Obter classe CSS do passo
   */
  getStepClass(step: number): string {
    if (step === this.currentStep) return 'active';
    if (step < this.currentStep || this.isStepCompleted(step)) return 'completed';
    return 'pending';
  }

  /**
   * Salvar dados do passo atual
   */
  saveCurrentStepData(): void {
    switch (this.currentStep) {
      case 1:
        this.wizardData.dadosBasicos = {
          nomeResponsavel: this.lead.nomeResponsavel,
          empresa: this.lead.empresa,
          cidade: this.lead.cidade,
          telefone: this.lead.telefone,
          instagramHandle: this.lead.instagramHandle,
          website: this.lead.website,
          bioInsta: this.lead.bioInsta
        };
        break;
      case 2:
        this.wizardData.linksEncontrados = [...this.linksEncontrados];
        break;
      case 3:
        this.wizardData.cnpjSelecionado = this.lead.cnpj ? {
          cnpj: this.lead.cnpj,
          empresa: this.lead.empresa,
          endereco: this.lead.endereco
        } : null;
        break;
      case 4:
        this.wizardData.configuracoes = {
          etapa: this.lead.etapa,
          origem: this.lead.origem,
          segmento: this.lead.segmento,
          observacoes: this.lead.observacoes,
          sincronizarBitrix: this.sincronizarBitrix
        };
        break;
    }
    console.log('Dados salvos do passo', this.currentStep, ':', this.wizardData);
  }

  /**
   * Pular etapa atual
   */
  skipStep(): void {
    if (this.currentStep < this.totalSteps && this.canSkipCurrentStep()) {
      console.log('Pulando passo:', this.currentStep);
      this.etapaFoiPulada = true;
      this.nextStep();
    }
  }

  /**
   * Obter título do passo atual
   */
  getCurrentStepTitle(): string {
    switch (this.currentStep) {
      case 1: return 'Extrair Dados do Instagram';
      case 2: return 'Buscar Links do Website';
      case 3: return 'Descobrir CNPJ da Empresa';
      case 4: return 'Finalizar Lead';
      default: return 'Passo Desconhecido';
    }
  }

  /**
   * Verificar se pode pular passo atual
   */
  canSkipCurrentStep(): boolean {
    return this.currentStep === 2 || this.currentStep === 3; // Apenas Links e CNPJ são opcionais
  }

  /**
   * Mostrar mensagem de validação específica
   */
  mostrarMensagemValidacao(): void {
    switch (this.currentStep) {
      case 2:
        if (this.lead.website && !this.websiteAnalisado) {
          this.erro = 'Você deve analisar o website antes de continuar ou pular esta etapa.';
        }
        break;
      case 3:
        if (this.lead.empresa && this.lead.cidade && !this.cnpjBuscado) {
          this.erro = 'Você deve buscar o CNPJ da empresa antes de continuar ou pular esta etapa.';
        }
        break;
      default:
        this.erro = 'Complete os dados obrigatórios para continuar.';
    }
  }

  /**
   * Marcar website como analisado
   */
  marcarWebsiteAnalisado(): void {
    this.websiteAnalisado = true;
    this.erro = ''; // Limpa erro se houver
  }

  /**
   * Marcar CNPJ como buscado
   */
  marcarCnpjBuscado(): void {
    this.cnpjBuscado = true;
    this.erro = ''; // Limpa erro se houver
  }

}
