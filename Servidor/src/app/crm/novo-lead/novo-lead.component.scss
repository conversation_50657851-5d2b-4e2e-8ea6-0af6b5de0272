// ===== WIZARD CONTAINER =====
.wizard-container {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

// ===== WIZARD HEADER =====
.wizard-header {
  background: white;
  border-bottom: 1px solid #dee2e6;
  padding: 20px 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.wizard-progress {
  .step-progress {
    display: flex;
    justify-content: center;
    align-items: center;
    
    .step {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 0 30px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      .step-number {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 18px;
        margin-bottom: 8px;
        border: 3px solid #dee2e6;
        background: white;
        color: #6c757d;
        transition: all 0.3s ease;
      }
      
      .step-label {
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: #6c757d;
        transition: all 0.3s ease;
      }
      
      // Estados do passo
      &.completed {
        .step-number {
          background: #28a745;
          border-color: #28a745;
          color: white;
        }
        .step-label {
          color: #28a745;
        }
      }
      
      &.active {
        .step-number {
          background: #007bff;
          border-color: #007bff;
          color: white;
          box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
        }
        .step-label {
          color: #007bff;
          font-weight: 600;
        }
      }
      
      &.pending {
        opacity: 0.6;
      }
      
      &:hover:not(.pending) {
        transform: translateY(-2px);
      }
    }
  }
}

// ===== WIZARD TITLE =====
.wizard-title {
  background: white;
  padding: 20px 0;
  border-bottom: 1px solid #dee2e6;
  
  .step-title {
    margin: 0;
    color: #2c3e50;
    font-size: 28px;
    font-weight: 600;
    display: flex;
    align-items: center;
    
    i {
      margin-right: 12px;
      font-size: 32px;
    }
  }
}

// ===== WIZARD CONTENT =====
.wizard-content {
  flex: 1;
  padding: 30px 0;
}

.step-content {
  animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// ===== CARDS =====
.card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  margin-bottom: 20px;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 8px 15px rgba(0,0,0,0.15);
  }
  
  .card-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    
    i {
      margin-right: 8px;
      font-size: 20px;
    }
  }
}

.main-card {
  border-left: 4px solid #007bff;
}

// ===== EXTRACTION MAIN CARD =====
.extraction-main-card {
  border: 2px solid #007bff;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.1);
}

// Instagram profile sem foto
.instagram-profile {
  .profile-header-simple {
    margin-bottom: 20px;
    
    .profile-details {
      .username {
        color: #007bff;
        font-weight: 600;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        
        i {
          margin-right: 8px;
          color: #e1306c;
        }
      }
      
      .full-name {
        color: #6c757d;
        font-weight: 500;
        margin-bottom: 12px;
      }
      
      .stats {
        display: flex;
        gap: 20px;
        flex-wrap: wrap;
        
        .stat {
          display: flex;
          align-items: center;
          color: #495057;
          font-size: 14px;
          
          i {
            margin-right: 6px;
            color: #007bff;
            font-size: 12px;
          }
          
          strong {
            margin-right: 4px;
          }
        }
      }
    }
  }
  
  .bio-section {
    background: rgba(0, 123, 255, 0.05);
    border: 2px solid rgba(0, 123, 255, 0.3);
    padding: 15px;
    border-radius: 8px;
    margin-top: 15px;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
    
    .bio-title {
      color: #007bff;
      font-weight: 600;
      margin-bottom: 10px;
      font-size: 14px;
      display: flex;
      align-items: center;
      
      i {
        margin-right: 8px;
        font-size: 12px;
      }
    }
    
    .bio-content {
      color: #495057;
      line-height: 1.6;
      font-style: italic;
      font-size: 14px;
      background: white;
      padding: 12px;
      border-radius: 6px;
      border: 1px solid rgba(0, 123, 255, 0.1);
    }
  }
}

.summary-card {
  background: #f8f9fa;
  border: 2px solid #28a745;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.1);
  
  .summary-content {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    
    .badge {
      margin-right: 10px;
    }
  }
  
  .summary-item {
    margin-bottom: 5px;
  }
}

.data-card {
  border: 2px solid #28a745;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.1);
}

.form-card {
  border: 2px solid #17a2b8;
  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.1);
}

.results-card {
  border: 2px solid #ffc107;
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.1);
}

// ===== FORMULÁRIOS =====
.form-group {
  margin-bottom: 20px;
  
  label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    display: block;
  }
  
  .form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 12px 16px;
    font-size: 14px;
    line-height: 1.4;
    transition: all 0.3s ease;
    
    &:focus {
      border-color: #007bff;
      box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
    }
  }
  
  // Ajuste específico para campos select
  select.form-control {
    min-height: 52px;
    padding: 16px 16px;
    line-height: 1.6;
    font-size: 15px;
    appearance: none;
    background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" width="4" height="5" viewBox="0 0 4 5"><path fill="%23666" d="M2 0L0 2h4z"/><path fill="%23666" d="M0 3l2 2 2-2z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 12px;
    padding-right: 40px;
  }
  
  .input-group-text {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-right: none;
    color: #6c757d;
    font-weight: 600;
  }
  
  .input-group .form-control {
    border-left: none;
  }
}

// ===== DIVISOR OU =====
.divider-or {
  text-align: center;
  margin: 30px 0;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #dee2e6;
  }
  
  span {
    background: white;
    padding: 0 20px;
    color: #6c757d;
    font-weight: 500;
    position: relative;
  }
}

// ===== PERFIL INSTAGRAM =====
.instagram-profile {
  .profile-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    
    .profile-pic {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      margin-right: 20px;
      border: 3px solid #e1306c;
    }
    
    .profile-info {
      h6 {
        margin: 0;
        color: #2c3e50;
        font-size: 18px;
        font-weight: 600;
      }
      
      .full-name {
        margin: 5px 0 10px 0;
        color: #6c757d;
        font-weight: 500;
      }
      
      .stats {
        display: flex;
        gap: 20px;
        
        .stat {
          text-align: center;
          
          .count {
            display: block;
            font-weight: 600;
            color: #2c3e50;
            font-size: 16px;
          }
          
          .label {
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
          }
        }
      }
    }
  }
  
  .bio {
    color: #495057;
    line-height: 1.5;
    font-style: italic;
  }
}

// ===== LINKS GRID =====
.links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
  
  .link-item {
    display: flex;
    align-items: center;
    padding: 15px;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #007bff;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .link-icon {
      margin-right: 15px;
      font-size: 24px;
      
      i {
        width: 30px;
        text-align: center;
      }
    }
    
    .link-content {
      flex: 1;
      
      .link-tipo {
        font-weight: 600;
        color: #2c3e50;
        font-size: 14px;
      }
      
      .link-descricao {
        color: #6c757d;
        font-size: 12px;
        margin: 2px 0;
      }
      
      .link-url {
        color: #007bff;
        font-size: 11px;
        font-family: monospace;
      }
    }
    
    .link-actions {
      display: flex;
      gap: 5px;
      
      .btn {
        padding: 5px 8px;
        font-size: 12px;
      }
    }
    
    // Cores por tipo
    &.link-whatsapp {
      border-left: 4px solid #25d366;
      .link-icon { color: #25d366; }
    }
    
    &.link-instagram {
      border-left: 4px solid #e1306c;
      .link-icon { color: #e1306c; }
    }
    
    &.link-ifood {
      border-left: 4px solid #ea1d2c;
      .link-icon { color: #ea1d2c; }
    }
    
    &.link-cardapio {
      border-left: 4px solid #28a745;
      .link-icon { color: #28a745; }
    }
    
    &.link-concorrente {
      border-left: 4px solid #dc3545;
      .link-icon { color: #dc3545; }
    }
    
    &.link-localizacao {
      border-left: 4px solid #17a2b8;
      .link-icon { color: #17a2b8; }
    }
    
    &.link-site {
      border-left: 4px solid #6f42c1;
      .link-icon { color: #6f42c1; }
    }
  }
}

// ===== CNPJS GRID =====
.cnpjs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 15px;
  
  .cnpj-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 15px;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #007bff;
      transform: translateY(-2px);
      box-shadow: 0 6px 12px rgba(0,0,0,0.15);
    }
    
    &.selected {
      border-color: #28a745;
      background: #f8fff9;
      box-shadow: 0 6px 12px rgba(40,167,69,0.2);
    }
    
    .cnpj-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      
      .cnpj-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
        
        &.badge-success {
          background: #d4edda;
          color: #155724;
        }
        
        &.badge-warning {
          background: #fff3cd;
          color: #856404;
        }
        
        &.badge-danger {
          background: #f8d7da;
          color: #721c24;
        }
      }
      
      .cnpj-number {
        font-family: monospace;
        font-weight: 600;
        color: #2c3e50;
        font-size: 16px;
      }
    }
    
    .cnpj-info {
      margin-bottom: 15px;
      
      .cnpj-name {
        font-weight: 600;
        color: #007bff;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        
        i {
          margin-right: 6px;
          font-size: 14px;
        }
      }
      
      .cnpj-razao-social {
        margin-bottom: 6px;
        display: flex;
        align-items: flex-start;
        color: #6c757d;
        
        i {
          margin-right: 6px;
          margin-top: 2px;
          font-size: 12px;
        }
        
        small {
          line-height: 1.3;
          font-weight: 500;
        }
      }
      
      .cnpj-endereco {
        margin-bottom: 6px;
        display: flex;
        align-items: flex-start;
        color: #6c757d;
        
        i {
          margin-right: 6px;
          margin-top: 2px;
          font-size: 12px;
          color: #dc3545;
        }
        
        small {
          line-height: 1.3;
        }
      }
      
      .cnpj-capital {
        margin-bottom: 6px;
        display: flex;
        align-items: center;
        color: #28a745;
        
        i {
          margin-right: 6px;
          font-size: 12px;
        }
        
        small {
          font-size: 11px;
        }
      }
      
      .cnpj-atividade {
        margin-bottom: 6px;
        display: flex;
        align-items: flex-start;
        color: #6c757d;
        
        i {
          margin-right: 6px;
          margin-top: 2px;
          font-size: 12px;
          color: #6f42c1;
        }
        
        small {
          line-height: 1.3;
          font-size: 11px;
        }
      }
      
      .cnpj-socios {
        margin-bottom: 8px;
        display: flex;
        align-items: flex-start;
        color: #fd7e14;
        
        i {
          margin-right: 6px;
          margin-top: 2px;
          font-size: 12px;
        }
        
        small {
          line-height: 1.3;
          font-size: 11px;
          font-weight: 500;
        }
      }
      
      .cnpj-footer-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
        padding-top: 8px;
        border-top: 1px solid #f1f3f4;
        
        .cnpj-status {
          .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            background: #f8d7da;
            color: #721c24;
            
            &.active {
              background: #d4edda;
              color: #155724;
            }
          }
        }
        
        .cnpj-fonte {
          flex: 1;
          text-align: right;
          
          small {
            font-size: 10px;
            
            i {
              margin-right: 4px;
            }
          }
        }
      }
    }
    
    .cnpj-actions {
      .btn-select {
        width: 100%;
        margin-bottom: 10px;
        padding: 12px 16px;
        font-size: 15px;
        font-weight: 600;
        min-height: 48px;
        border-radius: 8px;
      }
      
      .btn-details {
        width: 100%;
        padding: 10px 16px;
        font-size: 14px;
        font-weight: 500;
        min-height: 44px;
        border-radius: 8px;
      }
    }
  }
}

// ===== RESUMO FINAL =====
.summary-section {
  margin-bottom: 25px;
  
  h6 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    
    i {
      margin-right: 8px;
      color: #007bff;
    }
  }
  
  .summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 8px;
    
    div {
      padding: 5px 0;
      border-bottom: 1px solid #f8f9fa;
    }
  }
  
  .links-summary {
    .link-tag {
      display: inline-block;
      background: #e9ecef;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      margin: 2px 4px 2px 0;
      color: #495057;
    }
  }
  
  .cnpj-summary {
    div {
      margin-bottom: 8px;
      padding: 8px 0;
      border-bottom: 1px solid #f8f9fa;
      
      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }
    }
  }
}

// ===== WIZARD FOOTER =====
.wizard-footer {
  background: white;
  border-top: 1px solid #dee2e6;
  padding: 20px 0;
  position: sticky;
  bottom: 0;
  z-index: 100;
  box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
  
  .navigation-buttons {
    display: flex;
    align-items: center;
    
    .flex-grow-1 {
      flex: 1;
    }
    
    .btn {
      margin-left: 10px;
      font-weight: 600;
      padding: 12px 24px;
      border-radius: 8px;
      
      &:first-child {
        margin-left: 0;
      }
    }
    
    .alert {
      max-width: 300px;
    }
  }
}

// ===== RESPONSIVIDADE =====
@media (max-width: 768px) {
  .wizard-progress {
    .step-progress {
      .step {
        margin: 0 15px;
        
        .step-number {
          width: 40px;
          height: 40px;
          font-size: 16px;
        }
        
        .step-label {
          font-size: 10px;
        }
      }
    }
  }
  
  .wizard-title {
    .step-title {
      font-size: 24px;
      
      i {
        font-size: 28px;
      }
    }
  }
  
  .links-grid {
    grid-template-columns: 1fr;
  }
  
  .cnpjs-grid {
    grid-template-columns: 1fr;
  }
  
  .summary-grid {
    grid-template-columns: 1fr !important;
  }
  
  .navigation-buttons {
    flex-wrap: wrap;
    
    .alert {
      order: -1;
      width: 100%;
      margin: 0 0 15px 0 !important;
    }
  }
  
  .instagram-profile {
    .profile-header {
      flex-direction: column;
      text-align: center;
      
      .profile-pic {
        margin: 0 0 15px 0;
      }
    }
  }
}

@media (max-width: 576px) {
  .wizard-header,
  .wizard-title,
  .wizard-content,
  .wizard-footer {
    padding-left: 15px;
    padding-right: 15px;
  }
  
  .wizard-progress {
    .step-progress {
      .step {
        margin: 0 8px;
        
        .step-number {
          width: 35px;
          height: 35px;
          font-size: 14px;
        }
        
        .step-label {
          font-size: 9px;
        }
      }
    }
  }
  
  .card {
    margin-bottom: 15px;
  }
  
  .cnpj-card {
    padding: 15px;
    
    .cnpj-header {
      flex-direction: column;
      align-items: flex-start;
      
      .cnpj-number {
        margin-top: 8px;
        font-size: 14px;
      }
    }
  }
}

// ===== BOTÕES MELHORADOS =====
.btn {
  border-radius: 8px;
  font-weight: 600;
  padding: 14px 20px;
  font-size: 15px;
  min-height: 48px;
  transition: all 0.2s ease;
  border-width: 2px;
  
  &:hover {
    transform: none;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  }
  
  &:active {
    transform: translateY(1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  &:focus {
    box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
  }
}

// Botão primário
.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
  
  &:hover {
    background-color: #0056b3;
    border-color: #0056b3;
  }
}

// Botão success
.btn-success {
  background-color: #28a745;
  border-color: #28a745;
  
  &:hover {
    background-color: #1e7e34;
    border-color: #1e7e34;
  }
}

// Botões outline
.btn-outline-primary {
  border-color: #007bff;
  color: #007bff;
  
  &:hover {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
  }
}

.btn-outline-secondary {
  border-color: #6c757d;
  color: #6c757d;
  
  &:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
  }
}

.btn-outline-info {
  border-color: #17a2b8;
  color: #17a2b8;
  
  &:hover {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: white;
  }
}

.btn-outline-secondary {
  background-color: white;
  border-color: #6c757d;
  color: #6c757d;
  box-shadow: 0 4px 12px rgba(108,117,125,0.15);
  
  &:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
  }
}

.btn-outline-info {
  background-color: white;
  border-color: #17a2b8;
  color: #17a2b8;
  box-shadow: 0 4px 12px rgba(23,162,184,0.15);
  
  &:hover {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: white;
  }
}

// Botões de sucesso
.btn-success {
  background-color: #28a745;
  border-color: #28a745;
  color: white;
  box-shadow: 0 4px 12px rgba(40,167,69,0.3);
  
  &:hover {
    background-color: #1e7e34;
    border-color: #1e7e34;
  }
}

// Botões pequenos (para cards)
.btn-sm {
  min-height: 40px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 600;
  border-radius: 8px;
}

// Botões do wizard footer
.wizard-footer .btn {
  min-height: 55px;
  padding: 18px 30px;
  font-size: 17px;
  font-weight: 700;
  border-radius: 12px;
  box-shadow: 0 6px 18px rgba(0,0,0,0.15);
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
  }
}

.form-control:focus {
  animation: focusPulse 0.3s ease;
}

@keyframes focusPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0,123,255,0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(0,123,255,0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0,123,255,0);
  }
}