import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { TypebotService } from '../../chatbot/services/typebot.service';
import { CloudWhatsappService } from '../../services/cloud-whatsapp.service';

@Component({
  selector: 'app-fluxos-whatsapp',
  templateUrl: './fluxos-whatsapp.component.html',
  styleUrls: ['./fluxos-whatsapp.component.scss']
})
export class FluxosWhatsappComponent implements OnInit {
  configuracoes: any = {
    usarFluxoTypebot: false,
    typebotConfigurado: false,
    idFluxoTypebotWhatsapp: '',
    idFluxoTypebotInstagram: '',
    chaveApiTypebot: '',
    workspaceId: ''
  };
  
  msgSucesso: string = '';
  criandoFluxosTypebot: boolean = false;
  dadosIntegracao: any = {
    associado: false,
    telefones: []
  };

  constructor(
    private typebotService: TypebotService,
    private router: Router,
    private cloudWhatsapp: CloudWhatsappService
  ) { }

  ngOnInit(): void {
    // Verificar se o WhatsApp está associado
    this.cloudWhatsapp.carregueDados().then((dadosIntegracao) => {
      this.dadosIntegracao = dadosIntegracao;
      
      // Se o WhatsApp estiver associado, verificar o status do typebot
      if (this.dadosIntegracao.associado) {
        this.verificarStatusTypebot();
      }
    });
  }

  verificarStatusTypebot() {
    this.typebotService.verificarTypebotConfigurado().subscribe(
      (response: any) => {
        if (response && response.configurado) {
          this.configuracoes = response.configuracoes;
        }
      },
      (error) => {
        console.error('Erro ao verificar status do typebot:', error);
      }
    );
  }

  onUsarFluxoTypebotChange(event: any) {
    // Se o usuário desativar o fluxo, não é necessário fazer nada
    if (!event) {
      return;
    }
    
    // Se o typebot não estiver configurado, verificar se o usuário quer configurar
    if (!this.configuracoes.typebotConfigurado) {
      // Não faz nada, o botão para configurar será exibido
    }
  }

  abrirTelaConfigTypebot() {
    this.router.navigate(['/admin/chatbot/config-typebot']);
  }

  editarFluxoTypebot(tipo: string, nome: string) {
    if (tipo === 'whatsapp' && this.configuracoes?.idFluxoTypebotWhatsapp) {
      window.open(`https://promokit.app.br:8443/pt-BR/typebots/${this.configuracoes.idFluxoTypebotWhatsapp}/edit`, '_blank');
    } else if (tipo === 'instagram' && this.configuracoes?.idFluxoTypebotInstagram) {
      window.open(`https://promokit.app.br:8443/pt-BR/typebots/${this.configuracoes.idFluxoTypebotInstagram}/edit`, '_blank');
    } else {
      // Exibir mensagem de erro
      console.error(`Fluxo de ${nome} não configurado`);
    }
  }

  onSubmit() {
    // Salvar as configurações
    this.typebotService.salvarConfiguracoes(this.configuracoes).subscribe(
      () => {
        this.msgSucesso = 'Configurações salvas com sucesso!';
        setTimeout(() => {
          this.msgSucesso = '';
        }, 3000);
      },
      (error) => {
        console.error('Erro ao salvar configurações:', error);
      }
    );
  }

  formatTime(seconds: number): string {
    if (!seconds) return '0 segundos';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    
    let result = '';
    if (hours > 0) {
      result += `${hours} hora${hours > 1 ? 's' : ''} `;
    }
    if (minutes > 0) {
      result += `${minutes} minuto${minutes > 1 ? 's' : ''} `;
    }
    if (remainingSeconds > 0 || (hours === 0 && minutes === 0)) {
      result += `${remainingSeconds} segundo${remainingSeconds !== 1 ? 's' : ''}`;
    }
    
    return result.trim();
  }
}