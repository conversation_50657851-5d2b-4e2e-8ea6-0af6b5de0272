{"$schema": "../../node_modules/@angular/service-worker/config/schema.json", "index": "/index.html", "dataGroups": [{"name": "api-performance", "urls": ["/empresas", "/brindes", "/produtos", "/fotos", "/contatos", "/atividades", "/mensagem", "/planos", "/tiposDePontuacao", "/cartoes2", "/cartoes", "/campanha", "/api", "/l", "/notificacoes", "/auth", "/usuario", "/bots", "/upload", "/iugu", "/pagamentos", "/contratos", "/exportar"], "cacheConfig": {"maxSize": 0, "maxAge": "0u", "strategy": "freshness"}}], "assetGroups": [{"name": "app", "installMode": "prefetch", "resources": {"files": ["/favicon.ico", "/index.html", "/manifest.webmanifest", "/*.css", "/*.js"]}}, {"name": "assets", "installMode": "lazy", "updateMode": "prefetch", "resources": {"files": ["/assets/**", "/*.(eot|svg|cur|jpg|png|webp|gif|otf|ttf|woff|woff2|ani)"]}}]}