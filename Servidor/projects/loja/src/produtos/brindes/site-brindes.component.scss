h2{
  font-size: 1.25rem;
}

.font-12{
  font-size: 12px !important;
}

.reward-card {
  padding-top: 10px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  background: #f8f9fa;
  position: relative;

  .branco{
    background: #fff;
  }
}
.points-badge {
  background-color: #FFB700;
  border-radius: 25px;
  padding: 5px 15px;
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 4px;

  .points-icon{
    width: 16px;
    height: 16px;
    margin-right: 2px;
    top: -2px;
    position: relative;
    path{
      fill: #CB0A0A;
    }
  }

}


.nav-icon {
  width: 24px;
  height: 24px;
  display: block;
  margin: 0 auto 5px;
}
.bottom-nav {
  position: fixed;
  bottom: 0;
  width: 100%;
  background: white;
  border-top: 1px solid #eee;
  padding: 10px 0;
}
.logo-text {
  background: linear-gradient(45deg, #8B0000, #FF4500, #FFB700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
  font-size: 24px;
}

.resgatelogo {
  fill: #FFB700;
}

.product-icon {
  width: 100%;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.scroll_categoria {
  grid-auto-flow: column;
  display: grid;
  width: fit-content;
  grid-template-columns: inherit !important;
  gap: 20px;
  .brinde{
    width: 200px;
    display: block !important;
    img{
      height: 160px;
    }

    &.naodisponivel{
      .points-badge {
        background-color: #9e9e9e70;
        color: #000000a3;

        .points-icon path{
          fill: black !important;
        }
      }

      .product-icon path{
        fill: #dbdbdc !important
      }

      img{
        opacity: 0.5; /* Torna a imagem semitransparente */
        filter: grayscale(100%); /* Opcional: Deixa a imagem em preto e branco */
      }
    }
  }
}

.sombra{
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);

}


.boxinfo{
  border: 1px solid #cccccc3d;
  border-radius: 10px;
  padding: 10px;
  color: #343a40;
  background: #f8f9fa;
}

.saldo{
  color: #FFB700 !important;
}


@media only screen and (max-width: 768px) {

  .scroll_categoria {
    display: grid;
    grid-template-columns: 100%;
    .product-icon {
      height: 150px;
    }
    .brinde{
      width: 200px;
      img{
        height: 160px;
      }
    }
  }

}
