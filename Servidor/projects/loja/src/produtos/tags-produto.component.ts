import {Component, Input} from "@angular/core";


@Component({
  selector: 'app-tags-alimentar',
  template:  `
    <div class="tags-produto">
      <div class="tags-container">
        <ng-container *ngFor="let tag of tags">
          <app-produto-icone-alimentar  [tag]="tag" [exibirLabel]="exibirLabel"></app-produto-icone-alimentar>
        </ng-container>
      </div>

    </div>
  `,
  styles:  [`
    .tags-produto{
      border: 0;
      padding: 0;
      display: block;
    }

    .tags-container{
      display: flex;
      flex-wrap: wrap;
    }
    `
  ]
})
export class TagsProdutoComponent{
  @Input() tags: any = [];
  @Input() exibirLabel  = false;
}


