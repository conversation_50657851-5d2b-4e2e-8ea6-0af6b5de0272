.preco {
  color: #6db31b;
  display: inline-block;
  margin-right: 10px;

  &.antigo{
    color: #ccc;
    text-decoration: line-through;
  }
}


@media (min-width: 1100px) {
  .produto {
    width: calc(50% - 20px);
    display: inline-block;
    border: 1px solid #f2f2f2;
    box-shadow: 0px 1px 4px rgb(0 0 0 / 5%);
    border-radius: 4px;
    padding: 8px;
    background-color: var(--cor-fundo-elementos, #fff);

    &:hover {
      border: 1px solid #e3e3e3;
    }

    img {
      max-width: 128px;
      max-height: 128px;
    }

    .nome-produto {
      font-size: 16px;
    }

    .preco {
      font-size: 18px !important;

      &.antigo {
        font-size: 16px !important;
      }

    }

    .descricao {
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      min-height: 35px;
    }

    .descricao_menor {
      min-height: 25px !important;
    }
  }
}

@media only screen and (min-width: 992px) {
  .produto {
    padding: 8px;

    img {
      max-width: 140px !important;
    }

    .container_foto {
      width: 140px;
    }

    .media {
      width: calc(100% - 155px);
    }
  }
}

.produto {
  width: calc(100% - 20px) !important;
  align-items: flex-start;
  height: 100%;
  padding: 8px;

  display: flex !important;
  flex-direction: row-reverse !important;

  .media {
    flex-grow: 1;
    width: calc(100% - 165px);
  }

  img {
    width: 160px;
    height: auto;
    max-width: inherit !important;
    max-height: 200px;
  }

  .container_foto {
    width: 165px;
    display: flex;
    padding-top: 0rem !important;
  }

  @media only screen and (max-width: 768px) {
    width: calc(100%) !important;

    hr {
    }

    .scroll_categoria {
      display: grid;
      grid-template-columns: 100%;
    }

    .container_foto {
      width: 140px;
      display: flex;
    }

    img {
      width: 140px;
    }
  }

  .cashback-container {
    font-size: 11px;
    color: var(--cor-texto-secundaria, #666);
    margin-top: 4px;
    margin-bottom: 8px;
    background-color: var(--cor-fundo-elementos, #f5f5f5);
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;

    i {
      margin-right: 4px;
      color: #999;
    }
  }
}

::ng-deep .quiosque {
  .scroll_categoria {
    grid-template-columns: repeat(3, 1fr) !important;
  }

  .produto {
    width: calc(100% - 0px) !important;
    align-items: flex-start;
    height: 100%;

    display: flex !important;
    flex-direction: column !important;

    img {
      width: 100% !important;
      height: 250px !important;
      max-height: inherit !important;
      object-fit: cover !important;
    }

    .nome-produto {
      font-size: 18px !important;
    }

    .descricao {
      display: none !important;
    }

    .media {
      width: 100% !important;
    }

    @media only screen and (max-width: 768px) {
      width: 100% !important;
    }
  }

  .container_foto {
    width: 100% !important;
  }
}

.produto {
  border-bottom: solid 1px #e8e8e8;
  background-color: var(--cor-fundo-elementos, #fff);
  .descricao {
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    -webkit-line-clamp: 3;

  }


  cursor: pointer;

  .media-body{
    padding-top: 15px;
    min-width: 0px;
  }

  &.destacado {
    height: 98%;
    display: block !important;


    img {
      width: 100% !important;
    }

    .container_foto {
      width: 100% !important;
      margin-left: 0px !important;
      overflow: hidden !important;
      margin-bottom: 15px;
    }

    .media {
      width: 100%;
    }
  }

  img {
    object-fit: contain;
    display: block;
    margin: 0 auto;
  }

  &.destaque2 {
    box-shadow: 2px 2px 3px #0000003b;
    border: 1px solid #eeee;
    margin-bottom: 20px !important;
    width: 310px;
    min-height: 145px !important;

    .preco{
      margin-top: 10px;
    }
    .wrapper-img{
      width: 100px;
      img {
        width: 100px;
        height: 100px;
      }
    }
  }
}

::ng-deep .tema-personalizado{
  .text-muted{
    color: var(--cor-texto-secundaria, #999) !important;
  }

  .produtos_categoria .produto {
    background-color: var(--cor-fundo-elementos, #fff) !important;
    border-radius: 5px;
  }
}
