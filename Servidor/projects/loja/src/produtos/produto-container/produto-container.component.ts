import {
  AfterViewInit,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
  ViewEncapsulation
} from '@angular/core';
import {MyDetectorDevice} from "../../app/shared/MyDetectorDevice";
import {CarrinhoService} from "../../services/carrinho.service";
import {ConstantsService} from "../../services/ConstantsService";

@Component({
  selector: 'app-produto-container',
  templateUrl: './produto-container.component.html',
  styleUrls: ['./produto-container.component.scss']
})
export class ProdutoContainerComponent implements OnInit, AfterViewInit {
  @Input() produto: any;
  @Input() itemPedido: any;
  @Input() exibirPrecos: boolean
  @Input() layoutHorizontal: boolean
  @Input() botaoEscolher = false;
  @Input() semdestaque = false;
  @Input() exibirProdutoValorZerado = false;
  @Input() destaque2: boolean;
  @Output() onAbrirDetalhes = new EventEmitter();
  @Output() onAbrirDetalhesPrato = new EventEmitter();
  @ViewChild('divProduto', {static: false}) divProduto: any;

  larguraProdutos: number;
  isMobile: boolean;
  adicionaisImprirmir: any
  @Input()  tagsAlimentares = [];

  constructor( private detectorDevice: MyDetectorDevice,
               private carrinhoService: CarrinhoService) {
    this.isMobile = this.detectorDevice.isMobile();
  }

  ngOnInit(): void {
    if(this.itemPedido){
      this.adicionaisImprirmir = this.itemPedido.adicionaisImprirmir;
      this.itemPedido = this.carrinhoService.otenhaPedidoPrato(this.itemPedido);
      this.produto = this.itemPedido.produto;
    }


  }

  ngAfterViewInit(): void {
    if (this.layoutHorizontal && this.divProduto) {
      try{
        let largura = this.divProduto.nativeElement.closest('.produtos_categoria').clientWidth;

        if (this.isMobile) {
          this.larguraProdutos = ((largura - 20) / 2);
        } else {
          this.larguraProdutos = ((largura - 40) / 4);
        }
      } catch (e){

      }

    }
  }

  exibirUnidade(produto: any) {
    return produto.tipoDeVenda && produto.tipoDeVenda === 'Peso'
  }

  abrirDetalhes(produto: any){
    if(this.itemPedido){
      this.onAbrirDetalhesPrato.emit(this.itemPedido);
    } else {
      this.onAbrirDetalhes.emit(produto)
    }
  }
}
