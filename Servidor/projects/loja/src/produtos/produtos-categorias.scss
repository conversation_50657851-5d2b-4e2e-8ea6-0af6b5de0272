.scroll_categoria {
  display: grid;
  grid-template-columns: 50% 50%;
  gap: 15px;
}


@media only screen and (max-width: 768px) {
  .scroll_categoria {
    display: grid;
    grid-template-columns: 100%;
  }
  .ecommerce {
    .produtos_categoria {
      overflow-x: auto;
    }

    .arrow {
      display: none;
    }
  }
}



::ng-deep .natal {
  h4 {
    &.categoria {
      color: #730101 !important;
    }
  }

  .tab_categoria {
    color: #d39797 !important;
  }

  .tab_categoria {
    &.active {
      color: #730101 !important;
      border-bottom: 2px solid #730101 !important;
    }
  }

  .nome-produto {
    color: #730101 !important;
  }
}


::ng-deep .arraia {
  h4 {
    &.categoria {
      color: #CE2400 !important;
    }
  }

  .tab_categoria {
    color: #5B5764 !important;
  }

  .tab_categoria {
    &.active {
      color: #CE2400 !important;
      border-bottom: 2px solid #CE2400 !important;
    }
  }

  .nome-produto {
    color: #CE2400 !important;
  }
}

::ng-deep .copa_2022 {
  h4 {
    &.categoria {
      color: #1a7200 !important;
    }
  }

  .ver_todos {
    display: none;
    a {
      background: #fff;
      color: #1a7200;
      border: 1px solid #1a7200 !important;
      border-radius: 7px;
      padding-left: 5px;
      padding-right: 5px;
      font-size: 14px;
    }
  }

  .nav-tabs {
    border-bottom: solid 1px #ffd80e !important;
  }

  .tab_categoria {
    color: #9abe90 !important;
  }

  .tab_categoria {
    &.active {
      color: #1a7200 !important;
      border-bottom: 2px solid #ffd80e !important;
    }
  }

  .nome-produto {
    color: #1a7200 !important;
  }
}

::ng-deep .pascoa {
  h4 {
    &.categoria {
      color: #730101 !important;
    }
  }

  .tab_categoria {
    color: #8a6757 !important;
  }

  .tab_categoria {
    &.active {
      color: #FB1E41 !important;
      border-bottom: 2px solid #FB1E41 !important;
    }
  }

  .nome-produto {
    color: #730101 !important;
  }
}

::ng-deep .ano_novo {
  h4 {
    &.categoria {
      color: #030303 !important;
    }
  }

  .tab_categoria {
    color: #7f7f7f !important;
  }

  .tab_categoria {
    &.active {
      color: #030303 !important;
      border-bottom: 2px solid #f1dfa5 !important;
    }
  }

  .nav-bordered {
    border-bottom-width: 1px !important;
  }
}

::ng-deep .dia_maes {
  h4 {
    &.categoria {
      color: #FD6C67 !important;
    }
  }

  .tab_categoria {
    color: #8a6757 !important;
  }

  .tab_categoria {
    &.active {
      color: #FD6C67 !important;
      border-bottom: 2px solid #F3ADAC !important;
    }
  }

  .nome-produto {
    color: #730101 !important;
  }
}

$corFundo: #0e0e0e;

.carnaval{
  span.h4 , .nav-tabs .nav-link {
    color: #49b8b8 !important;
  }

  .nav-tabs .nav-link.active {
    color:  #D825A9 !important;
  }

  h4, a {
    &.categoria {
      color: #49b8b8 !important;
    }
  }
}

::ng-deep .black_friday_2022 {
  hr {
    border-top: solid 1px #676767;
  }

  .carrinho_desktop {
    background: $corFundo !important;
  }

  .header {
    background: $corFundo;
  }

  span.h4 {
    color: #b57e2b !important;
  }

  h4 {
    &.categoria {
      color: #b57e2b !important;
    }
  }

  .tab_categoria {
    color: #676767 !important;
  }

  .tab_categoria {
    &.active {
      color: #b57e2b !important;
      border-bottom: 2px solid #b57e2b !important;
      background-color: transparent !important;
    }
  }

  .produtos_categoria {
    .produto {
      background: transparent !important;
      border: solid 1px #1a1a1a !important;
    }
  }

  .nav-tabs {
    background: #0e0e0e;
    box-shadow: none;
  }

  .nav-tabs .nav-link.active, ::ng-deep .nav-tabs .nav-item.show .nav-link {
    background: #fff;
    font-weight: bold;
  }

  .nome-produto {
    color: #F6A844 !important;
  }
}

::ng-deep .cacau_show {
  hr {
    border-top: solid 1px var(--cor-borda);
  }

  .carrinho_desktop {
    background: var(--cor-fundo-site) !important;
  }

  .header {
    background: var(--cor-fundo-site);
  }

  span.h4 {
    color: var(--cor-texto-secundaria) !important;
  }

  h4 {
    &.categoria {
      color: var(--cor-texto-secundaria) !important;
    }
  }

  .tab_categoria {
    color: var(--cor-texto-secundaria) !important;
  }

  .tab_categoria {
    &.active {
      color: var(--cor-texto-secundaria) !important;
      border-bottom: 2px solid var(--cor-texto-secundaria) !important;
      background-color: transparent !important;
    }
  }

  .produtos_categoria {
    .produto {
      background: #1e1e1e !important;
      border-color: var(--cor-borda) !important;
    }
  }

  .nav-tabs {
    background: var(--cor-fundo-site);
    box-shadow: none;
  }

  .nav-tabs .nav-link.active, ::ng-deep .nav-tabs .nav-item.show .nav-link {
    background: var(--cor-fundo-site);
    font-weight: bold;
  }

  .nome-produto {
    color: var(--cor-texto) !important;
  }
}

::ng-deep .dia_pais {
  span.h4 {
    color: #10bde9;
  }

  h4 {
    &.categoria {
      color: #10bde9 !important;
    }
  }

  .tab_categoria {
    color: #10bde9 !important;
  }

  .tab_categoria {
    &.active {
      color: #0000dd !important;
      border-bottom: 2px solid #0000dd !important;
    }
  }

  .nome-produto {
    color: #10bde9 !important;
  }
}

::ng-deep .dia_namorados {
  h4 {
    &.categoria {
      color: #b264da !important;
    }
  }

  .tab_categoria {
    color: #b264da !important;
  }

  .tab_categoria {
    &.active {
      color: #d64860 !important;
      border-bottom: 2px solid #F3ADAC !important;
    }
  }

  .nome-produto {
    color: #730101 !important;
  }
}

.categoria_ia {
  padding: 10px 15px;
  background: #ccc;
  border: solid 1px #efefef;
  border-radius: 5px;
  background: #faf7f7;
}

::ng-deep .dialog-produto-ia {
  overflow-x: hidden;
  position: initial;
}

::ng-deep .mobile .dialog-produto-ia {
  padding: 12px;
}

::ng-deep .dialog-produto-ia {
  padding: 0px !important;
}


::ng-deep .tema-personalizado {
  hr {
    border-top: solid 1px var(--cor-borda);
  }

  .carrinho_desktop {
    background: $corFundo !important;
  }

  .header {
    background: var(--cor-fundo-site, #fff);
  }

  span.h4 {
    color: var(--cor-texto-primaria, #333) !important;
  }

  h4 {
    &.categoria {
      color: var(--cor-destaque, #333) !important;
    }
  }

  .tab_categoria {
    color: var(--cor-texto-secundaria, #999) !important;
  }

  .tab_categoria {
    &.active {
      color: var(--cor-destaque, #333) !important;
      border-bottom: 3px solid var(--cor-destaque, #333) !important;
      background-color: transparent !important;
    }
  }

  .produtos_categoria {
    .produto {
      background: transparent !important;
      border: solid 1px var(--cor-fundo-site, #999) !important;
    }
  }

  .nav-tabs {
    background: var(--cor-fundo-site, #fff);
    box-shadow: none;
  }

  .nav-tabs .nav-link.active, ::ng-deep .nav-tabs .nav-item.show .nav-link {
    background-color: var(--cor-fundo-site, #fff);
    font-weight: bold;
  }

  .nome-produto {
    color: var(--cor-texto-primaria, #333) !important;
  }

  .ver_todos {
    a {
      border-color: var(--cor-botao, #007bff) !important;
      color: var(--cor-texto-botao, #fff) !important;
      background-color: var(--cor-botao, #007bff) !important;

      &:hover {
        background-color: transparent !important;
        color: var(--cor-botao, #007bff) !important;
      }
    }
  }
}
