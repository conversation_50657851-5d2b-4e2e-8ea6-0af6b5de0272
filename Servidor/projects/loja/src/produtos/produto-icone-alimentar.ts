import {Component, Input} from "@angular/core";


@Component({
  selector: 'app-produto-icone-alimentar',
  template:  `
    <div class="produto-tag">
      <div class="icone">
        <i class="icon-restricao-alimentar {{getIconTag(tag.nome)}} " kendoTooltip title="{{tag.nome}}"></i>
      </div>

      <label *ngIf="exibirLabel">{{tag.nome}}</label>
    </div>



  `,
  styleUrls: ['./produto-icone-alimentar.scss']
})
export class ProdutoIconeAlimentarComponent{
  @Input() tag: any = {};
  @Input() exibirLabel  = false;

  getIconTag(nome: string){
    if(!nome) return '';

    return nome.toLowerCase().replace(' ', '').normalize('NFD').replace(/[\u0300-\u036f]/g, '');
  }
}


