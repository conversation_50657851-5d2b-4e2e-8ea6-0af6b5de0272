import {Component, EventEmitter, HostListener, Input, OnInit, Output, ViewEncapsulation} from "@angular/core";
import {Router} from "@angular/router";
import {DominiosService} from "../../services/dominios.service";

@Component({
  selector: 'app-site-categorias',
  templateUrl: './site-categorias.component.html',
  styleUrls: ['./site-categorias.component.scss']
})
export class SiteCategoriasComponent   implements OnInit{
  @Input()   categorias: any  = [];
  @Input()   isMobile: boolean ;
  @Input() produtosPorCategoria = {};
  @Output() verCategoria = new EventEmitter();
  categoriaVisivel: any;
  urlBack: string;
  tema: string
  constructor(private router: Router, private dominiosService: DominiosService) {
  }

  ngOnInit(): void {
     this.tema =  window['tema'];
  }

  selecionou(categoria) {

    this.dominiosService.obtenhaUrlHome();
   // let link = 'produtos/categoria/' +
   //   categoria.nome.toLowerCase().replace(/\s+/g, "-") + '/' + categoria.id + window.location.search;


    let nomeCategoria = categoria.nome.toLowerCase().replace(/\s+/g, "-");
    let link = String(`busca?cid=${categoria.id}`);

    let urlCompleta =  this.dominiosService.obtenhaUrlCompleta(link);

    let state: any = { categorias: [categoria] };

    let produtos = this.produtosPorCategoria[categoria.nome];

    if(produtos && produtos.length){
      let produtosPorCategoria: any  = { }

      produtosPorCategoria[categoria.nome ] = produtos;

      state.produtos =  produtos;
      state.produtosPorCategoria =  produtosPorCategoria;
    } else {
      state.aguardarCarregar = true;
    }

    this.router.navigateByUrl(urlCompleta, {state: state});
  }




}
