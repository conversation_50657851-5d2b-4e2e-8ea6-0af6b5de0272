.categories-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: stretch;
  max-width: 900px;
  margin: 0 auto; /* Centralizar o container */

}

.category {
  width: calc(50% - 10px); /* 2% de espaço entre as categorias */
  margin: 5px;
  box-sizing: border-box;
  position: relative;
  min-height: 150px; /* Altura mínima do contêiner */
  background-color: rgb(39 80 85 / 14%);
}

.category img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 4px 10px var(--cor-borda) !important;
  opacity: .9;
}

.category-description {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: rgb(29 29 27 / 75%);
  padding: 5px 10px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  text-align: center;
  color: #f5f5f5;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: .05em;
  overflow-wrap: break-word;
  max-width: 100%;
  font-size: 12px;
  white-space: pre-line;
  display: -webkit-box;
  overflow: hidden;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgb(7 7 7 / 50%);
  border-radius: 8px;
  pointer-events: none; /* Permite cliques passarem através da camada de sobreposição */
}
.black_friday_2022 , .black_friday, .black{
  .overlay{
    background-color: #ffffff9c;
  }
}

.navigation-header{
  border-bottom: 2px solid #ebebeb;

  padding: 5px 0px 5px 5px;
  background: #eaebeb;
  margin-left: -12px;
  margin-right: -12px;
  .flex{
    display: flex;
  }
  .items-center{
    align-items: center;
  }

  .k-icon{
    font-size: 24px;
  }
}

@media (min-width: 800px) {
  .category {
    width: calc(25% - 10px); /* 2% de espaço entre as categorias */
    margin-right: 10px;
    margin-bottom: 10px;
  }


}

.cacau_show {
  .categoria {
    background-color: var(--cor-fundo-elementos);
    border: none;
    border-radius: 8px;
    padding: 10px 15px;
    margin-bottom: 10px;

    .nome {
      color: var(--cor-texto-primaria);
      font-weight: 500;
    }

    &:hover {
      background-color: var(--cor-fundo-elementos);
      border: none;
    }

    &.ativo {
      background-color: var(--cor-destaque);

      .nome {
        color: var(--cor-texto-botao);
      }
    }
  }

  .subcategoria {
    background-color: var(--cor-fundo-site);
    border: none;
    border-radius: 8px;
    padding: 8px 15px;

    .nome {
      color: var(--cor-texto-secundaria);

      &:hover {
        color: var(--cor-texto-primaria);
      }
    }

    &.ativo {
      background-color: var(--cor-fundo-elementos);

      .nome {
        color: var(--cor-texto-primaria);
      }
    }
  }

  .destaques {
    color: var(--cor-texto-primaria);
    font-weight: 600;
    padding: 10px 15px;
    border-bottom: 1px solid var(--cor-borda);
    margin-bottom: 15px;
  }
}


