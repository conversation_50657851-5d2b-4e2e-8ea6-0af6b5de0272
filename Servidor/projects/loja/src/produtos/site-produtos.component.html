<ng-container *ngIf="multiPedido">
  <app-lista-empresas-multi-marca [empresas]="empresasDoGrupo"></app-lista-empresas-multi-marca>
</ng-container>


<div class="faixa-fidelidade"  *ngIf="fidelidade">
 <div class="conteudo">
   <div class="mb-3">
     <h5>
       <i class="fe-award"></i>
       Programa de Fidelidade</h5>
   </div>

   <div *ngIf="fidelidade.fazerLogin" class="mt-2 boxinfo mb-2">
     <a   (click)="irParaLogin()"  href="">
       <b>Identifique-se</b>
     </a>
     <br>
     <p class="font-12">
       <ng-container *ngIf="!fidelidade.cashback">
         Faça login para utilizar seus pontos e resgatar brindes.
       </ng-container>
       <ng-container *ngIf="fidelidade.cashback">
         Faça login para acumular até <b>{{fidelidade.cashback}}% de cashback</b> e usá-lo como desconto em suas próximas compras.
       </ng-container>
       Aproveite as vantagens do nosso programa de fidelidade!
     </p>

     <button (click)="irParaVerSaldo()" class="btn btn-blue btn-xs mt-1">Ver meu saldo</button>
     <br>
     <!--<button (click)="irParaVerSaldo()" class="btn btn-blue btn-xs mt-1">Ver meu saldo</button> -->
   </div>

   <ng-container *ngIf="!fidelidade.fazerLogin">

     <div>
       <p class="mb-0 texto2">Você tem disponível</p>
       <p class="pontos" *ngIf="!fidelidade.cashback">{{fidelidade.saldoDisponivel}} {{fidelidade.acumulo}}</p>
       <p class="pontos preco" *ngIf="fidelidade.cashback">{{fidelidade.saldoDisponivel | currency: "BRL"}}  </p>
     </div>


   </ng-container>
   <div class="header-grid">
     <p [hidden]="!brindes.length">Brindes</p>

     <a   [routerLink]="'/loja/brindes'"  class="link flex items-center gap-1" [hidden]="brindes.length <=2 ">Ver todos
       <span     class="text-lg">›</span>
     </a>

   </div>
   <div class="grid">

     <div class="k-i-loading ml-1 mr-1 centralizado" *ngIf="carregando" style="font-size: 40px;height: 90px;" ></div>
     <div *ngFor="let brinde of brindes | slice:0:2" class="brinde" (click)="abraDetalhesProduto(brinde)"
          [ngClass]="{'naodisponivel': brinde.naodisponivel}">
       <div class="flex">
         <div class="foto">
           <svg width="80" height="80" viewBox="0 0 100 100"  *ngIf="!brinde.imagens || !brinde.imagens[0]">
             <path fill="#CD8032" d="M20,40 L80,40 L75,90 L25,90 Z"/>
             <path fill="#8B4513" d="M30,30 L70,30 C75,30 75,40 70,40 L30,40 C25,40 25,30 30,30"/>
           </svg>

           <img  class="img img-fluid mb-1" *ngIf="brinde.imagens?.length"
                 [src]="'/images/empresa/' + brinde.imagens[0].linkImagem" alt="Imagem">
         </div>
         <div class="descricao">
            <span class="points-badge">
                        <svg class="points-icon" viewBox="0 0 24 24">
                            <circle cx="12" cy="12" r="10" fill="#FFF"/>
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.31-8.86c-1.77-.45-2.34-.94-2.34-1.67 0-.84.79-1.43 2.1-1.43 1.38 0 1.9.66 1.94 1.64h1.71c-.05-1.34-.87-2.57-2.49-2.97V5H10.9v1.69c-1.51.32-2.72 1.3-2.72 2.81 0 1.79 1.49 2.69 3.66 3.21 1.95.46 2.34 1.15 2.34 1.87 0 .53-.39 1.39-2.1 1.39-1.6 0-2.23-.72-2.32-1.64H8.04c.1 1.7 1.36 2.66 2.86 2.97V19h2.34v-1.67c1.52-.29 2.72-1.16 2.73-2.77-.01-2.2-1.9-2.96-3.66-3.42z"
                            />
                        </svg>
              {{brinde.valorResgate}} {{brinde.acumulo}}
             </span>
           <h5 class="mb-0">{{brinde.nome}}</h5>
         </div>
       </div>
     </div>

   </div>
 </div>
</div>


<div class="alert alert-danger mt-2 mb-2" role="alert" *ngIf="mensagemErro">
  <i class="fas fa-exclamation-triangle"></i> {{mensagemErro}}
</div>

<div style="max-width: 1024px;margin: 0 auto;" class="mt-2" *ngIf="banners.length > 0">
  <ng-image-slider #nav [id]="'banners'"  class="navImagem"
                   [images]="banners"
                   (imageClick)="clicouBanner($event)" [animationSpeed]="0.1" [imagePopup]="false"
                   [imageSize]="{width: '100%', height: alturaBanner}"  [autoSlide]="0" slideImage="1"></ng-image-slider>
</div>

<div *ngIf="mesaExpirada" class="mt-5" style="text-align: center">
  <i class="fas fa-qrcode mb-2 fa-4x" ></i><bR>
  <strong >Este código de mesa já expirou. Leia novamente o QR-CODE para continuar pedindo na mesa.</strong>
</div>

<div *ngIf="apiLoaded | async">
</div>


<ng-container *ngIf="exibirMenuCategorias">
  <div class="k-i-loading ml-1 mr-1 centralizado" *ngIf="carregando" style="font-size: 40px;height: 90px;" ></div>

  <app-site-categorias #siteCategorias [categorias]="categorias" [produtosPorCategoria]="produtosPorCategoria"
                       (verCategoria)="exibaProdutosCategoria($event)"  [isMobile]="isMobile"    >

  </app-site-categorias>

</ng-container>


<div *ngIf="!mesaExpirada && !exibirMenuCategorias" [class.ecommerce]="layoutHorizontal" class="{{empresa?.tema}}" >
  <ng-container *ngIf="ultimosPratos?.length  ">
    <div  #secaoCategoriaNovamente  class="mt-2 secaoCategoria ecommerce pacadenovo"  style="position: relative;">
      <h4   class="categoria ml-2 font-weight-bold">
        Peça de novo
      </h4>

      <div class="arrow esquerda" (click)="moverScrollEsquerda(divProdutosCategoriaNovamente, secaoCategoriaNovamente)">
        <i class="fas fa-chevron-left"></i>
      </div>
      <div class="arrow direita" (click)="moverScrollDireita(divProdutosCategoriaNovamente, secaoCategoriaNovamente)"
           [hidden]="ultimosPratos.length < 4">
        <i class="fas fa-chevron-right"></i>
      </div>

      <div #divProdutosCategoriaNovamente class="produtos_categoria vitrine"   >
        <div class="scroll_categoria">
          <ng-container *ngFor="let itemPedido of ultimosPratos ">
            <app-produto-container  [itemPedido]="itemPedido" [exibirPrecos]="exibirPrecos" [exibirProdutoValorZerado]="exibirProdutosValorZerado"
                                    [layoutHorizontal]="layoutHorizontal" [destaque2]="true"
                                    (onAbrirDetalhesPrato)="abraDetalhesPrato($event)"
                                    (onAbrirDetalhes)="abraDetalhesProduto($event)" >

            </app-produto-container>
          </ng-container>
        </div>
      </div>
    </div>

  </ng-container>

  <div class="header tirar_margem " *ngIf="!carregando">
    <div class="ml-2 wrapper-busca">
      <span class="h4 text-left" *ngIf="empresa && pedido">
        <span class="fa-stack" *ngIf="pedido.mesa">
          <i class="fas fa-square fa-stack-2x"></i>
          <i class="fas fa-chair fa-stack-1x fa-inverse"></i>
        </span>
        <strong>{{pedido.mesa   ? empresa.identificadorMesa  + " " + pedido.mesa.nome + " - " : ""}}</strong>
        Produtos</span>
      <button class="btn float-right" (click)="abraTelaDeBusca()" style="margin-top: -5px;margin-right: -10px;">
        <i class=" fas fa-search"  ></i>
      </button>
    </div>
    <div  *ngIf="pedido.mesa && empresa.avisosDeMesa" style="display: flex;justify-content: center">
      <button class="btn btn-primary mr-2" [disabled]="solicitandoGarcom"  (click)="solicitouGarcom()"><i class="fe-bell "></i> Chamar garçom
      {{timerSolicitarGarcomNovamente ? "(" + timerSolicitarGarcomNovamente + ")" : ""}}</button>
      <button class="btn btn-primary" [disabled]="solicitandoFechar" (click)="solicitouFecharMesa()" ><i class="fe-dollar-sign"></i> Pedir para fechar
        {{timerSolicitarFecharNovamente ? "(" + timerSolicitarFecharNovamente + ")" : ""}}</button>

    </div>
    <div class="alert alert-success mt-2 text-center" role="alert" *ngIf="mensagemSucessoGarcom">
      <i class="mdi mdi-check-all mr-2"></i> {{mensagemSucessoGarcom}}
      <button type="button" class="close" aria-label="Fechar" (click)="fecheMensagemSucessoGarcom()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>

    <div *ngIf="empresa && !empresa.layoutEcommerce">
      <ul id="tabs" class="nav-tabs nav-bordered nav" *ngIf="categorias && categorias.length > 0" role="tablist">
        <ng-container *ngFor="let categoria of categorias">
          <li [id]="'tab_' + categoria.nome" class="nav-item" (click)="selecionou(categoria)" *ngIf="!categoria.ocultar">
            <a ngbNavLink class="nav-link tab_categoria" [href]="'/categoria/' + categoria.nome + '/' + categoria.id" [ngClass]="{'active': categoria.nome === categoriaSelecionada}">{{categoria.nome}}</a>
            <ng-template ngbNavContent></ng-template>
          </li>
        </ng-container>

      </ul>
    </div>

    <div #megamenu class="megamenu  d-none d-sm-block" *ngIf="empresa && empresa.layoutEcommerce">
      <i class="k-icon k-i-arrow-chevron-right" (click)="scrollParaDireita()" ></i>
      <i class="k-icon k-i-arrow-chevron-left" (click)="scrollParaEsquerda()"   [ngClass]="{'exibir': deslocamento > 0}"></i>
      <div #wraper class="wraper" [ngStyle]="{'margin-left': marginLeft()}">
        <ul class="" >
          <li class="dropdown" *ngFor="let categoria of categoriasMegamenu"  >
            <button class="dropbtn" (click)="abraTelaBuscaCategoria(categoria)"   >{{categoria.nome}}  </button>

            <div class="dropdown-content" [ngStyle]="{'padding-left': paddingLeft()}">
              <div class="linha" >
                <div class="coluna" >

                  <div class="grupo" *ngFor="let categoriaFilha of getColunas(categoria).coluna1.subcategorias"
                       [ngClass]="{'esconder': getColunas(categoria).coluna1.total >= 12}">

                    <h4 class="cpointer" (click)="abraTelaBuscaCategoria(categoriaFilha)">{{categoriaFilha.nome}}</h4>

                    <a href="#" (click)="abraTelaBuscaCategoria(item)"   *ngFor="let item of  categoriaFilha.subcategorias">
                      {{item.nome}}
                    </a>
                  </div>
                </div>
                <div class="coluna">

                  <div class="grupo" *ngFor="let categoriaFilha of getColunas(categoria).coluna2.subcategorias"
                       [ngClass]="{'esconder': getColunas(categoria).coluna2.total >= 12}">

                    <h4 class="cpointer" (click)="abraTelaBuscaCategoria(categoriaFilha)">{{categoriaFilha.nome}}</h4>

                    <a href="#" (click)="abraTelaBuscaCategoria(item)"   *ngFor="let item of  categoriaFilha.subcategorias">
                      {{item.nome}}
                    </a>
                  </div>
                </div>
                <div class="coluna" >

                  <div class="grupo" *ngFor="let categoriaFilha of getColunas(categoria).coluna3.subcategorias"
                       [ngClass]="{'esconder': getColunas(categoria).coluna3.total >= 12}">

                    <h4 class="cpointer" (click)="abraTelaBuscaCategoria(categoriaFilha)">{{categoriaFilha.nome}}</h4>

                    <a href="#" (click)="abraTelaBuscaCategoria(item)"   *ngFor="let item of  categoriaFilha.subcategorias">
                      {{item.nome}}
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>

    </div>

  </div>

  <div class="tirar_margem" class="mt-3" #divProdutos>
    <div class="k-i-loading ml-1 mr-1 centralizado" *ngIf="carregando" style="font-size: 40px;height: 90px;" ></div>
    <ng-container  *ngFor="let categoria of categorias">
      <div class="mb-3 secaoCategoria" #secaoCategoria style="position: relative;" *ngIf="!categoria.ocultar" [ngClass]="{'categoria_ia': categoria.ia}"
           [class.ecommerce]="categoria.nome === categoriaDestaque.nome || categoria.vitrine || categoria.destaque">
        <h4 [id]="categoria.nome" class="categoria  font-weight-bold">
          <ng-container *ngIf="categoria.ia">
            <i class="fa fa-magic"></i>
          </ng-container>
          {{categoria.nome}}
        </h4>
        <ng-container *ngIf="categoria.ia">
          <h6>Por que você pediu esse produto na mensagem</h6>
        </ng-container>
        <hr>

        <div class="ver_todos" [hidden]="produtosPorCategoria[categoria.nome]?.length <= 3 || categoria.vitrine">
          <a href="" (click)="abraTelaBuscaPorCategoria(categoria)">Ver todos</a>
        </div>
        <div class="arrow esquerda" (click)="moverScrollEsquerda(divProdutosCategoria, secaoCategoria)">
          <i class="fas fa-chevron-left"></i>
        </div>
        <div class="arrow direita" (click)="moverScrollDireita(divProdutosCategoria, secaoCategoria)"
             [hidden]="produtosPorCategoria[categoria.nome]?.length < 3">
          <i class="fas fa-chevron-right"></i>
        </div>
        <div class="produtos_categoria" #divProdutosCategoria [class.vitrine]="categoria.vitrine">
          <div class="scroll_categoria">
            <ng-container *ngFor="let produto of produtosPorCategoria[categoria.nome] | slice:0: qtdeMaxima">

              <app-produto-container [produto]="produto" [exibirPrecos]="exibirPrecos" [layoutHorizontal]="layoutHorizontal"
                                (onAbrirDetalhes)="abraDetalhesProduto($event)" [exibirProdutoValorZerado]="exibirProdutosValorZerado"></app-produto-container>

            </ng-container>

            <div (click)="abraTelaBuscaPorCategoria(categoria)" class="produto pt-2 pb-2" [style.width.px]="larguraProdutos"
                 *ngIf="layoutHorizontal && produtosPorCategoria[categoria.nome].length > 3" [hidden]="categoria.vitrine">
              <div class="media">
                <div class="media-body pt-0 ml-2">
                  <h5 class="preco font-16" style="margin: 85px 15px">Ver todos desta categoria</h5>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </ng-container>


  </div>
</div>

<span class=" d-block d-sm-none">
  <div class="left-side-menu enlarged"  *ngIf="categorias.length" [ngClass]="{'exibir': sharedDataService.exibirMenuMobile}">
    <div class="slimScrollDiv">
      <div class="enlarged slimscroll-menu">
        <div class="nav-user titulo">
          <h4>Categorias</h4>
        </div>

        <div class="sidebar-menu mt-2">
          <ul class="metismenu">
            <li  *ngFor="let categoria of categoriasMegamenu" >
              <a href="" (click)="abraTelaBuscaCategoria(categoria)"> {{categoria.nome}}</a>
            </li>

             <li  *ngIf="false">
                <a  class="cpointer  text-muted" (click)="fecheMenuCategorias();">
                      <i class="fa fa-arrow-left ct-point"></i> fechar</a>
              </li>
          </ul>
        </div>
      </div>
    </div>
  </div>


  <div class="modal-backdrop" *ngIf="sharedDataService.exibirMenuMobile" (click)="sharedDataService.notifiqueExibirMenu()"></div>
</span>


