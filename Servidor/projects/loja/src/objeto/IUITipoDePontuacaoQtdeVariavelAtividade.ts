import {IUITipoDePontuacaoLoja} from "./IUITipoDePontuacaoLoja";
import {ProdutoCashbackUtilsLoja} from "./ProdutoCashbackUtilsLoja";


export class IUITipoDePontuacaoQtdeVariavelAtividade implements IUITipoDePontuacaoLoja {
  constructor(private empresa: any, private tipoDePontuacao: any, private tipoDeAcumulo: string, private atividade: any) {
  }

  calculePontos(valorVenda: number, itens: any) {
    if (this.atividade.plano.valorMinimoPontuar && this.atividade.plano.valorMinimoPontuar > valorVenda)
      return 0;

    if(itens && itens.length)
       return ProdutoCashbackUtilsLoja.calculeCashbakDosProdutos(valorVenda, itens, this.atividade)

    return this.atividade.pontosGanhos;
  }

  obtenhaPontosFormatado(valorVenda: number, itens: any) {
    let pontos = this.calculePontos(valorVenda, itens)

    if(this.tipoDeAcumulo === 'Reais')
      return    String(`R$ ${pontos.toFixed(2).replace('.', ',')}`)

    if(this.tipoDeAcumulo === 'Pontos')
      return String(`${pontos} ${pontos  <= 1 ? 'Ponto' : "Pontos" }`)

    return String(`${pontos > 0 ? pontos : ''} ${pontos  <= 1 ? 'Selo' : "Selos" }`)
  }

}
