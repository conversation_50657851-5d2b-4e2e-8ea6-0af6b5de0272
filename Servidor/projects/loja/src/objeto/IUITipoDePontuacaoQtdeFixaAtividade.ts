import {IUITipoDePontuacaoLoja} from "./IUITipoDePontuacaoLoja";

export class IUITipoDePontuacaoQtdeFixaAtividade implements IUITipoDePontuacaoLoja {
  constructor(private empresa: any, private tipoDePontuacao: any, private tipoDeAcumulo: string, private atividade: any) {
  }

  calculePontos(valorVenda: number) {
    return this.atividade.pontosGanhos;
  }

  obtenhaPontosFormatado(valorVenda: number) {
    let pontos = this.calculePontos(valorVenda)

    if(this.tipoDeAcumulo === 'Reais')
      return    String(`R$ ${pontos.toFixed(2).replace('.', ',')}`)

    if(this.tipoDeAcumulo === 'Pontos')
      return String(`${pontos} ${pontos  <= 1 ? 'Ponto' : "Pontos" }`)

    return String(`${pontos} ${pontos  <= 1 ? 'Selo' : "<PERSON><PERSON>" }`)
  }

}
