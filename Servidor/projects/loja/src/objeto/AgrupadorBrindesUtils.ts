export class AgrupadorBrindesUtils{

  static agrupeBrindes(brindes: any){

    const numeroDeFaixas = AgrupadorBrindesUtils.calcularNumeroDeFaixas(brindes);
    const faixas = AgrupadorBrindesUtils.gerarFaixas(brindes, numeroDeFaixas);

    return faixas;
  }

  static gerarFaixas(brindes: any[], numeroDeFaixas: number): number[] {
    const valores = brindes.map(brinde => brinde.valorResgate);
    const min = Math.min(...valores);
    const max = Math.max(...valores);

    const intervalo = (max - min) / numeroDeFaixas;
    const faixas = Array.from({ length: numeroDeFaixas }, (_, i) => Math.floor(min + intervalo * (i + 1)));

    return faixas;
  }

  static calcularNumeroDeFaixas(brindes: any[]): number {
    if(!brindes || !brindes.length  ) return 0;

    if(brindes.length < 3) return 1;

    const valores = brindes.map(brinde => brinde.valorResgate);
    const min = Math.min(...valores);
    const max = Math.max(...valores);
    const diferenca = max - min;

    if(diferenca === 0) return 1;

    // Base na quantidade de brindes
    const quantidadeDeBrindes = brindes.length;
    const baseQuantidade = Math.sqrt(quantidadeDeBrindes);

    // Base na amplitude
    const faixaDesejada = diferenca / 5; // Dividindo em faixas de 5 por padrão
    const baseAmplitude = diferenca / faixaDesejada;

    // Combinar e arredondar
    return Math.round(Math.max(1, Math.min(baseQuantidade, baseAmplitude)));
  }
}
