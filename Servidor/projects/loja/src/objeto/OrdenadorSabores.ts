declare var _;

export class OrdenadorSabores{
  static ordene(sabores, campo, agruparCategorias: boolean = false){
    let listaOrdenada = this.ordenePorCampo(sabores, campo)

    if(!agruparCategorias)
      return listaOrdenada

    listaOrdenada = _.sortBy(listaOrdenada, (_sabor: any) => _sabor.categoriaPosicao)

    return listaOrdenada
  }

  static ordenePorCampo(sabores, campo) {
    if(!campo || campo === 'valor')
      return _.sortBy(sabores , (_sabor: any) => _sabor.preco);

    return _.sortBy(sabores , (_sabor: any) => _sabor.nome);

  }
}
