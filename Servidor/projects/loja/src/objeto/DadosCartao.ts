export class DadosCartao {
  numero: string;
  ultimosNumeros: string;
  cvv: string;
  nome: string;
  validade: Date;
  email: string;
  cpf: string;
  dataNascimento: string;
  senderHash: string;
  token: string;
  endereco: any;
  usarEnderecoDeEntrega: boolean;
  bandeira: string;
  autenticacao3ds: any;
  valorPagamento: number;
  parcela: number
  tokenize: any;
  tipoDoCartao: any;
  deviceInfo: any;
  tokenSession: string;
  externalProvider: string;
  descricaoPagamento: string;
  constructor() {
    this.cvv = ''
  }

  setRetornoTokenCartaoPagarme(res: any, tokenize: any ){
    this.token = res.id;
    this.bandeira = res.card.brand;
    this.tokenize = tokenize
  }

  outroSemDadosSensitivos() {
    const outro = new DadosCartao();

    outro.nome = this.nome;
    outro.cpf = this.cpf;
    outro.dataNascimento = this.dataNascimento;
    outro.senderHash = this.senderHash;
    outro.email = this.email;
    outro.token = this.token;
    outro.bandeira = this.bandeira;
    outro.endereco = this.endereco;
    outro.usarEnderecoDeEntrega = this.usarEnderecoDeEntrega;
    outro.ultimosNumeros = this.ultimosNumeros || (this.numero ? this.numero.slice(-4) : '****' );
    outro.autenticacao3ds = this.autenticacao3ds;
    outro.valorPagamento = this.valorPagamento;
    outro.parcela = this.parcela;
    outro.tipoDoCartao = this.tipoDoCartao;
    if(this.tokenize) outro.tokenize = this.tokenize;
    if(this.deviceInfo) outro.deviceInfo = this.deviceInfo
    if(this.tokenSession) outro.tokenSession = this.tokenSession;
    if(this.externalProvider)
      outro.externalProvider = this.externalProvider;

    if(this.descricaoPagamento)
      outro.descricaoPagamento = this.descricaoPagamento;


    return outro;
  }
}
