import {Endereco} from "./Endereco";
import {FormaDeEntrega} from "./FormaDeEntrega";

export class Entrega {
  formaDeEntrega: FormaDeEntrega;
  endereco: Endereco;
  taxaDeEntrega: number;
  permiteEntrega: boolean;
  hash: string;
  taxaCalculadaId: any;
  cepObrigatorio: boolean;
  complementoObrigatorio: boolean;
  comerNoLocal: boolean;
  valorComRetorno: number;
  constructor() {
    this.permiteEntrega = true;
  }

  foiPreenchida() {
    if( !this.formaDeEntrega)
      return false;

    if(this.ehRetirada()) return true;

    return this.endereco != null;
  }

  ehRetirada(){
    return this.formaDeEntrega && (this.formaDeEntrega === FormaDeEntrega.RETIRAR || this.formaDeEntrega === FormaDeEntrega.COMERNOLOCAL)
  }

  ehDelivery() {
    return this.formaDeEntrega && this.formaDeEntrega === FormaDeEntrega.RECEBER_EM_CASA
  }

  obtenhaForma(){
    if(this.formaDeEntrega === FormaDeEntrega.RETIRAR)
      return { id: 1} ;

    if(this.formaDeEntrega === FormaDeEntrega.COMERNOLOCAL)
      return { id: 1, comerNoLocal: true} ;

    if(this.formaDeEntrega === FormaDeEntrega.RECEBER_EM_CASA)
      return { id: 2 };
  }

  setTaxaEntrega(endereco: any, dados: any) {
    if(dados)  Object.assign(endereco, dados)

    this.endereco = endereco;
    this.taxaDeEntrega = endereco.taxaDeEntrega;
    this.hash = endereco.hash;
    this.taxaCalculadaId = endereco.taxaCalculadaId;

    if(endereco.taxaRetorno)
      this.valorComRetorno =  Number((this.taxaDeEntrega + endereco.taxaRetorno).toFixed(2))
  }


}
