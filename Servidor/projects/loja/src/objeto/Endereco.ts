import {ZonaDeEntrega} from "./ZonaDeEntrega";

export class Endereco {
  id: number;
  descricao: string;
  cep: string;
  logradouro: string;
  complemento: string;
  bairro: string;
  localizacao: any;
  numero: any;
  cidade: any;
  estado: any;
  taxaDeEntrega: number;
  nome: string;
  zonaDeEntrega: ZonaDeEntrega;
  descricaoCompleta: string
  pontoDeReferencia: string
  gps = false;
  hash = '';

  constructor(id: number, cidade: any, cep: string, logradouro: string,
              complemento: string, bairro: string, numero: any, descricao: string = null, localizacao: any,
              zonaDeEntrega: ZonaDeEntrega = null) {
    this.id  = id;
    this.cidade = cidade;
    this.cep = cep ? cep.replace(/\D/g, "").trim() : '';
    this.logradouro = logradouro;
    this.complemento = complemento;
    this.bairro = bairro;
    this.numero = numero;
    this.descricao = descricao;
    this.localizacao = localizacao;
  }

  obtenhaEnderecoCompleto() {
    if(this.descricao)  return  this.descricao;

   return  this.obtenhaEndereco();
  }

  obtenhaNomeAutocomplete() {
    let endereco = this.logradouro || '' ;

    if(this.complemento)  endereco  = endereco + ', ' + this.complemento;

    if(this.cep) endereco = endereco + ' ' + this.cep

    endereco = endereco[0].toUpperCase() + endereco.slice(1).toLocaleLowerCase()

    return endereco;
  }

  obtenhaEndereco() {
    let endereco = this.logradouro || '' ;

    if(this.complemento)  endereco  = endereco + ', ' + this.complemento;

    if(this.numero) endereco = endereco + ' ' + this.numero

    if(endereco !== '')
      endereco = endereco[0].toUpperCase() + endereco.slice(1).toLocaleLowerCase()

    if(this.bairro) endereco = endereco +  ' ' + this.bairro[0].toUpperCase() + this.bairro.slice(1).toLocaleLowerCase();

    if(this.pontoDeReferencia) endereco  = endereco + ' Ponto de referência: ' + this.pontoDeReferencia;

    if(this.cidade)  endereco = endereco + ", " + this.cidade.nome + "-" + this.cidade.estado.nome;

    return endereco;
  }

  // tslint:disable-next-line:member-ordering
  static novo(){
    return new Endereco(null , null, null, null, null,  null, null,
      null, null, null);
  }

  informadoManualmente() {
    return this.localizacao != null;
  }

  calculouTaxaDeEntrega() {
    return this.taxaDeEntrega != null && this.taxaDeEntrega !== undefined;
  }
}
