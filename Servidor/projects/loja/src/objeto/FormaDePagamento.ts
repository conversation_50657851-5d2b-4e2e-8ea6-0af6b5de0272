import {ConfigMeioDePagamento} from "./ConfigMeioDePagamento";
import {TaxaCobranca} from "./TaxaCobranca";
import {Bandeira} from "../../../../server/domain/pdv/Bandeira";


export class FormaDePagamento {
  id: number;
  nome: string;
  descricao: string;
  exibirCardapio: boolean;
  taxaCobranca: TaxaCobranca
  possuiDesconto: boolean;
  desconto: number;
  habilitarRetirada: boolean;
  habilitarEntrega: boolean;
  cobrarTaxaRetorno: boolean;

  configMeioDePagamento: ConfigMeioDePagamento;
  pix: boolean;
  chavePix: string;
  bandeira: Bandeira;
  formaDePagamentoPdv: any;
  online: boolean;
  carteiraDigital: string;
  estaHabilitadoPara(entrega: any) {
    if(!entrega) return true;

    if(entrega.ehDelivery() && this.habilitarEntrega) return true;

    return entrega.ehRetirada() && this.habilitarRetirada;

  }
}
