<app-header-tela titulo="{{!cliente.id ? 'Identifique-se' : 'Complete seu cadastro'}}" [retorno]="'carrinho'" ></app-header-tela>

<div   style="max-width: 800px;" class="{{empresa.tema}}">
  <form [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"
        novalidate #frm="ngForm" (ngSubmit)="onSubmit()"    class="mt-2" >

    <h4 class="mt-2 mb-1">Bem vindo de volta!</h4>

    <p>Para continuar, insira seus dados abaixo:</p>

    <!--<p class="mt-2 mb-0" *ngIf="empresa.integracaoPedidoFidelidade"  >
      Informando seu nome e telefone você terá descontos exclusivos

      <span *ngIf="fidelidadeExterna">e até 20% de cashback</span>
    </p> -->

    <div class="form-group mb-3 ">
      <label for="telefone">WhatsApp</label>
      <div class="input-group">
        <app-seletor-codigo-pais
          [selectedCountryCode]="cliente.codigoPais ? cliente.codigoPais : '+55' "
          (phoneMaskChange)="onPhoneMaskChange($event)"
          (selectedCountryCodeChange)="onCountrySelected($event)"></app-seletor-codigo-pais>
        <kendo-maskedtextbox  id="telefone" #telefone="ngModel" name="telefone"  [mask]="phoneMask" (change)="informouTelefone()"
                              #kendoMaskedTextBox="kendoMaskedTextBox"
                              [readonly]="cliente.informarCpf || cliente.informarNomeCompleto"
                              class="form-control ml-1"  [disabled]="buscando"
                              [(ngModel)]="cliente.telefone"   required>
        </kendo-maskedtextbox>


        <div class="invalid-feedback">
          <p *ngIf="telefone.errors?.required">WhatsApp é obrigatório</p>
          <p *ngIf="telefone.errors?.mask">WhatsApp inválido</p>
        </div>

      </div>

    </div>

    <div class="form-group mb-3">
      <label for="nome">Nome </label>
      <input kendoTextBox id="nome" name="nome"  placeholder="Nome completo"
             class="form-control"   #nome="ngModel" nomeCompleto [validarNome]="empresa.vendeOnline"
             [(ngModel)]="cliente.nome" required/>

      <div class="invalid-feedback">
        <p *ngIf="nome.errors?.required">Nome é obrigatório</p>
        <p *ngIf="nome.errors?.nomeCompleto">Informe seu nome completo</p>
      </div>
    </div>

    <div class="form-group mb-3" *ngIf="informarCpf">
      <label  >CPF</label>
      <input type="text" class="form-control" autocomplete="off" (change)="informouCpf()"
             name="cpf" [(ngModel)]="cliente.cpf" #cpf="ngModel" mask="000.000.000-00" cpfValido appAutoFocus
             placeholder="___.___.___-__"   [required]="cpfObrigatorio"  [autoFocus]="!cliente.cpf && cliente.telefone" />
      <div class="invalid-feedback">
        <p *ngIf="cpf.errors?.required">CPF é obrigatório</p>
        <p *ngIf="!cpf.errors?.required && cpf.errors?.cpfInvalido">CPF inválido</p>
      </div>
    </div>

    <div class="form-group mb-3   " *ngIf="informarDataNascimento">
      <label  >Data de nascimento</label>
      <kendo-datepicker #dataNascimento='ngModel' [(ngModel)]="cliente.dataNascimento"
                        class="form-control "   format="dd/MM/yyyy"
                        name="dataNascimento"
                        [required]="dataNascimentoObrigatorio"     >
        <kendo-datepicker-messages
          today="Hoje"
          toggle="Trocar calendário"
        ></kendo-datepicker-messages>

      </kendo-datepicker>

      <div class="invalid-feedback">
        <p *ngIf="dataNascimento.errors?.required">Data de nascimento é obrigatória</p>
      </div>

    </div>

    <div class="form-group mb-3 aceite-fidelidade" *ngIf="fidelidadeExterna && fidelidadeExterna.aceitarFidelidade" [ngClass]="{'menor': empresa?.idRede === 2}">
      <input  id="optin" name="optin" type="checkbox"
              [(ngModel)]="fidelidadeExterna.optin"  kendoCheckBox class="k-checkbox" (change)="alterouOptinFidelidade()" />
      <label for="optin" class="ml-1 usar-saldo"  >
        Quero ganhar cashback com   {{empresa.idRede === 1 ? 'Meu China in Box' : 'Gendai Vip'}}

        <span  > <br>Estou de acordo com o  <a href=""  href="{{fidelidadeExterna.linkRegras}}" target="_blank">Regulamento</a>.</span>
      </label>

      <img src="{{fidelidadeExterna?.logo}}" class="ml-2"
           *ngIf="fidelidadeExterna?.logo">

    </div>

    <ng-container *ngIf="criarConta">
      <fieldset >
        <legend>Dados de acesso</legend>
        <div class="form-group mb-3">
          <label for="email">E-mail</label>

          <div class="input-group" style="position: relative">
            <input type="email" class="form-control" autocomplete="off" [required]=" true"
                   id="email" name="email"  [(ngModel)]="cliente.email" #email="ngModel"
                   placeholder="Email do usuário"  (change)="valideEmail()" >
            <div class="invalid-feedback">
              <p *ngIf="email.errors?.required" >Email é obrigatório</p>
            </div>

            <i class="k-icone k-i-loading" *ngIf="validandoEmail"></i>
          </div>

          <div class="text-danger   " *ngIf="emailExistente"><b>Email já está em uso</b></div>
        </div>

        <div class="form-group mb-3" >

          <label for="senha">Senha</label>
          <label   class="col-form-label float-right cpointer text-blue" (click)="exibirSenhaTela()">
            <i class="fa fa-eye fa-lg   " *ngIf="!exibirSenha"></i>
            <i class="fa fa-eye-slash fa-lg   " *ngIf="exibirSenha"></i>
          </label>
          <span *ngIf="!exibirSenha">
                  <input class="form-control" type="password"  id="senha" name="senha"  #senha="ngModel" minlength="5"
                         [(ngModel)]="cliente.senha" placeholder="Informe sua senha"  [required]=" true">
                  <div  class="invalid-feedback">
                    <p  *ngIf="senha.errors?.required">Senha é obrigatório</p>
                    <p  *ngIf="senha.errors?.minlength">Senha deve ter no mínimo 5 caracteres</p>
                  </div>
              </span>

          <span *ngIf="exibirSenha">
                  <input class="form-control" type="text"  id="senhaTexto" name="senhaTexto"  #senhaTexto="ngModel"
                         [(ngModel)]="cliente.senha" placeholder="Informe sua senha"  [required]=" true">
                  <div  class="invalid-feedback">
                    <p  *ngIf="senhaTexto.errors?.required">Senha é obrigatório</p>
                  </div>
              </span>
        </div>

      </fieldset>

      <div  class="mt-2">
        <ng-container  *ngIf="!cliente.temConta">
          <button   class="btn btn-success btn-block  " type="submit" [disabled]="validandoEmail || emailExistente"
          >Salvar Meus dados</button>

          <button   class="btn btn-light btn-block mb-2 mt-2 " type="button" (click)="canceleCriarConta()"
                    [disabled]="validandoEmail || emailExistente">Cancelar</button>
        </ng-container>


        <ng-container *ngIf="cliente.temConta">
          <p  class="text-danger  mb-2 ">
            <b> Já existe uma conta com esse número de telefone</b>
          </p>

          <button class="btn btn-success btn-block" type="button"  (click)="vaParaLogin()"  >Entrar</button>
        </ng-container>
      </div>

    </ng-container>

    <button   class="btn btn-success btn-block " type="submit"    *ngIf="atualizarMeuCadastro()"
              [disabled]="validandoEmail || emailExistente || buscando">
      Salvar</button>


    <ng-container *ngIf="!atualizarMeuCadastro() &&  !this.criarConta">
      <button   class="btn btn-success btn-block " type="button"  (click)="continuarSemConta()"
                [disabled]="validandoEmail || emailExistente || buscando">
        Continuar sem senha</button>

      <hr>

      <ng-container *ngIf="!cliente.id && !cliente.novo">
        <h4 class="mb-0">Já tem uma conta?</h4>
        <button  class="btn btn-blue btn-block mt-2" type="button" (click)="vaParaLogin()" [disabled]="buscando">
          Continuar com senha</button>
      </ng-container>

      <ng-container *ngIf="cliente.novo">
        <h4 class="mb-0">Primeira visita?</h4>

        <button  class="btn btn-blue btn-block mt-2" type="button" (click)="exibirCriarConta()" [disabled]="buscando">
          Cadastrar senha</button>


      </ng-container>

      <ng-container *ngIf="cliente.id && !cliente.temConta">
        <button  class="btn btn-blue btn-block mt-2" type="button" (click)="exibirCriarConta()" [disabled]="buscando">
          Cadastrar uma senha</button>
      </ng-container>

      <ng-container *ngIf="cliente.temConta">
        <button  class="btn btn-blue btn-block mt-2" type="button" (click)="vaParaLogin()" [disabled]="buscando">
          Entrar com minha senha</button>
      </ng-container>

    </ng-container>

    <ng-container *ngIf="empresa.pedidoMesaNaoIdentificado && pedido.mesa">
      <h4 class="text-center">    ou </h4>
      <button   class="btn btn-blue btn-block " type="button"  (click)="escolheuNaoIndenficar()">
        Não quero me identificar</button>
    </ng-container>

  </form>

</div>

