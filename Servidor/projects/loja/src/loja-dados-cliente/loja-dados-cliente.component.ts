import {Component, OnInit, ViewChild} from '@angular/core';
import {ClienteService} from "../services/cliente.service";
import {PedidoLoja} from "../objeto/PedidoLoja";
import {NgForm} from "@angular/forms";
import {CarrinhoService} from "../services/carrinho.service";
import {Location} from "@angular/common";
import {ITela} from "../objeto/ITela";
import {ConstantsService} from "../services/ConstantsService";
import {DominiosService} from "../services/dominios.service";
import {AutorizacaoLojaService} from "../services/autorizacao-loja.service";
import {ActivatedRoute} from "@angular/router";
import {MaskedTextBoxComponent} from "@progress/kendo-angular-inputs";

declare var fbq;

@Component({
  selector: 'app-loja-dados-cliente',
  templateUrl: './loja-dados-cliente.component.html',
  styleUrls: ['./loja-dados-cliente.component.scss']
})
export class LojaDadosClienteComponent implements OnInit, ITela {
  @ViewChild('frm')  frm: NgForm;
  empresa: any;
  cliente: any = {};
  pedido: PedidoLoja;
  criarConta: boolean;
  informarDados: boolean;
  buscando: boolean;
  exibirSenha: boolean;
  informarCpf: boolean;
  cpfObrigatorio: boolean;
  informarDataNascimento: boolean;
  dataNascimentoObrigatorio: boolean;
  validandoEmail: boolean;
  emailExistente: boolean;
  erro: any;
  fidelidadeExterna: any;
  target: string
  phoneMask: string;
  @ViewChild('kendoMaskedTextBox', {static: false}) txtTelefone: MaskedTextBoxComponent;
  constructor(private clienteService: ClienteService, private carrinhoService: CarrinhoService,
              private location: Location, private constantsService: ConstantsService,
              private autorizacao: AutorizacaoLojaService, private activatedRoute: ActivatedRoute,
              private dominiosService: DominiosService) { }

  ngOnInit() {
    this.pedido = this.carrinhoService.obtenhaPedido();
    this.cliente =  Object.assign({}, this.pedido.contato);
    this.target = this.activatedRoute.snapshot.queryParams.target;

    if(this.cliente.naoIdentificado) this.cliente = {};

    this.constantsService.empresa$.subscribe( empresa => {
      if(!empresa) return;
      this.empresa = empresa;

      if(this.empresa.integracaoFidelidade){
        this.fidelidadeExterna = Object.assign({},  this.empresa.integracaoFidelidade);
        this.fidelidadeExterna.aceitarFidelidade = true;
        this.fidelidadeExterna.optin = false;
      }
      let usuario: any = this.autorizacao.getUsuario();

      if(window.history.state.informarNomeCompleto){
        this.cliente.informarNomeCompleto = true;
        this.cliente.completarCadastro = true;
      } else {
        this.cliente.completarCadastro = usuario && (usuario.completarCadastro || usuario.informarCpf || usuario.informarDataNascimento);
      }

      if(usuario && usuario.id){
        this.criarConta = false;
        this.informarDados = this.cliente.completarCadastro || (this.empresa.integracaoFidelidade && !usuario.saldoFidelidade)
        if(this.informarDados){
          this.busqueContatoPeloTelefone();
        } else {
          return this.dominiosService.vaParaCarrinho();
        }
      } else {
        if(this.cliente.telefone)
          this.busqueContatoPeloTelefone()
      }

      if(!this.cliente.id){
        if(this.cliente.email && this.cliente.senha)
          this.criarConta = true;
        else if(this.cliente.telefone){
          this.informarDados = true;
        }
      }

    });

    this.constantsService.campoCpf$.subscribe( campoCpf => {
      this.informarCpf = campoCpf != null;
      this.cpfObrigatorio = campoCpf && !campoCpf.opcional;
    });

    this.constantsService.campoDataNascimento$.subscribe( campoDataNascimento => {
      this.informarDataNascimento = campoDataNascimento != null;
      this.dataNascimentoObrigatorio = campoDataNascimento && !campoDataNascimento.opcional;
    });


    if (typeof fbq !== 'undefined')
      fbq('track', 'Lead');

  }

  atualizarMeuCadastro(){
    if(this.criarConta ) return false;

    if(this.cliente.completarCadastro || this.cliente.atualizarCadastro )
      return true;

    if(this.cliente.id && this.informarDados) return true;

    return false;
  }

  async continuarSemConta(){
    this.frm.onSubmit(null);
  }

  async onSubmit() {
    if( !this.frm.valid || this.validandoEmail)
      return;

    let usuario = this.autorizacao.getUsuario();

    if(usuario && ( this.cliente.completarCadastro ||  this.informarDados)){
      usuario.nome = this.cliente.nome;
      usuario.cpf = this.cliente.cpf;
      usuario.email = this.cliente.email;
      usuario.dataNascimento = this.cliente.dataNascimento;
      this.autorizacao.salveUsuario(usuario);
    }

    await this.salveCliente();
  }
  private async salveCliente(){
    this.pedido.contato = this.cliente;

    if(this.fidelidadeExterna){
      if(this.fidelidadeExterna.optin){
        this.pedido.fidelidadeGcom = { id_cliente: this.fidelidadeExterna.id_cliente}
      } else {
        delete   this.pedido.fidelidadeGcom
      }
    }

    if( this.cliente.atualizarCadastro){
      let erroAtualizar;
      this.buscando = true;
      await this.autorizacao.atualizeConta(this.cliente).catch( erro => {
        erroAtualizar = erro;
      })

      this.buscando = false;
      if(erroAtualizar) {
        this.erro = erroAtualizar;
        return;
      }
    }

    this.carrinhoService.salvePedido(this.pedido);

    if(!this.target)
      this.dominiosService.vaParaCarrinho();
    else
      this.dominiosService.navegueParaUrl(this.target)

  }


  async informouCpf(){
    if(this.cliente.id && !this.cliente.completarCadastro) return;


    this.buscando = true;
    let resposta: any = await this.clienteService.obtenhaCadastroCompletarPeloCpf(this.cliente.cpf).catch((e) => {
      console.error(e);
    });

    this.buscando = false;
    if (resposta && resposta.id) {
      if(this.cliente.id && this.cliente.id !== resposta.id){
        this.erro = 'CPF já está em uso'
      } else {
        this.cliente.id = resposta.id;
        this.cliente.completarCadastro = true;
        this.criarConta = true;
      }
    }
  }

  busqueContatoPeloTelefone(){
    this.buscando = true;
    delete this.emailExistente;

    delete this.cliente.novo;

    this.clienteService.obtenhaClientePorTelefone(this.cliente.telefone).then( resposta => {
      this.buscando   = false;
      if( resposta.id){
        Object.assign(this.cliente , resposta)
        if(this.cliente.dataNascimento)
          this.cliente.dataNascimento = new Date(this.cliente.dataNascimento)

        if(this.informarCpf && !this.cliente.cpf)
          this.cliente.atualizarCadastro = true;

        if(this.cliente.informarNomeCompleto)
          this.cliente.atualizarCadastro = true;

      } else {
        this.cliente.atualizarCadastro = false;
        this.cliente.novo = true;
      }

      if(resposta.fidelidadeExterna){
        this.fidelidadeExterna.aceitarFidelidade = resposta.fidelidadeExterna.aceitarFidelidade;
        if(resposta.fidelidadeExterna.id_cliente){
          this.fidelidadeExterna.id_cliente = resposta.fidelidadeExterna.id_cliente;
          this.fidelidadeExterna.aceitarFidelidade = false;
          this.fidelidadeExterna.optin = false;
        }
      }
    }).catch( () => {
      this.buscando   = false;
    })
  }

  informouTelefone() {
    if(this.cliente.id)
      this.cliente = { telefone: this.cliente.telefone,  nome: this.cliente.nome}

     if(this.fidelidadeExterna)
      delete this.fidelidadeExterna.id_cliente;

     this.busqueContatoPeloTelefone();
  }

  deveExibirTopo() {
    return false;
  }

  deveExibirMenu(){
    return false;
  }

  exibirCriarConta() {
    if(this.fidelidadeExterna) this.fidelidadeExterna.optin = true;
    this.criarConta = true;
  }


  vaParaLogin(){
     this.dominiosService.vaParaLogin('cliente', this.cliente.telefone);
  }

  exibirSenhaTela() {
    this.exibirSenha = ! this.exibirSenha ;
  }

  valideEmail() {
    this.validandoEmail = true;
    this.clienteService.existeClienteComEmail(this.cliente.email).then( (existe: any) => {
        this.validandoEmail = false;
        this.emailExistente = existe;

    }).catch(() => {
      this.validandoEmail = false;
    })

  }

  canceleCriarConta() {
    this.criarConta = false;
  }

  escolheuNaoIndenficar() {
    this.cliente = {
      telefone: '00000000000',
      codigoPais: '+55',
      nome: 'Consumidor não identificado',
      naoIdentificado: true
    }
    this.salveCliente();
  }

  deveTerBordas() {
    return true;
  }

  deveExibirBannerTema() {
    return false;
  }

  alterouOptinFidelidade(){
    if(this.atualizarMeuCadastro() || this.cliente.temConta) return;

    if( this.fidelidadeExterna.optin )
      this.criarConta = true;
    else
      this.criarConta = false

  }

  onPhoneMaskChange($event: string) {
    this.phoneMask = $event;
    if(this.txtTelefone)
      (this.txtTelefone as any).updateValue(this.txtTelefone.input.nativeElement.value);

  }

  onCountrySelected($event: string) {
    this.cliente.codigoPais = $event;
    console.log("setou código país. Contato: ", this.cliente)

  }
}
