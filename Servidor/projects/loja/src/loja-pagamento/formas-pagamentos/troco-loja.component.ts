import {Component, Input} from "@angular/core";
import {ControlContainer, NgForm} from "@angular/forms";

@Component({
  selector: 'app-troco-loja',
  templateUrl: './troco-loja.component.html',
  styleUrls: ['./troco-loja.component.scss'],
  viewProviders: [ { provide: ControlContainer, useExisting: NgForm } ]
})

export class TrocoLojaComponent{
  @Input() pagamento: any = {};
  @Input() pedido: any = {};
  @Input() obtenhaTotalPagar: Function;


  precisaDeTroco(temTroco: boolean) {
    if( !temTroco ) {
      this.pagamento.trocoPara = null;
    }
  }

  calculeTroco(valor: any) {
    this.pedido.pagamento = this.pagamento;

    setTimeout( () => {
      this.pedido.calculeTroco();
    }, 0);
  }

}
