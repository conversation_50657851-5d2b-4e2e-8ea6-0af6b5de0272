<kendo-tabstrip class="nav-bordered" id="tabs" name="tabs" (tabSelect)="alterouTipoPagamento($event)">

  <kendo-tabstrip-tab [title]="'Pagar pelo site'"   [selected]="(tabSelect  === 0)" *ngIf="temPagamentoOnline()">
    <ng-template kendoTabContent>
      <div class="row escolher row  mt-2 mobile ">
        <div class="form-group col-12 mb-0"  *ngIf="formasDePagamentoPix.length > 0 && estaHabilitado(formasDePagamentoPix[0], pedido)">
           <span class="mr-3 radio radio-blue mb-1  w-100"  *ngFor="let formaDePagamento of formasDePagamentoPix"
                 [ngClass]="{'selecionado': pagamentoOnlineEstaSelecionado(formaDePagamento)}">

            <input [id]="'formaPagamento' + formaDePagamento.id" name="formaDePagamento" type="radio"
                   [(ngModel)]="pedido.pagamento.formaDePagamento" [value]="formaDePagamento" class="k-radio" kendoRadioButton
                   (ngModelChange)="alterouFormaDePagamentoParaPix()"
            />
            <img src="https://user-images.githubusercontent.com/33992396/99478353-00e4d600-2933-11eb-8228-4bafe8571507.png"
                 style="width: 32px;" class="ml-1"/>


            <label for="formaPagamento{{formaDePagamento.id}}" class="ml-1 d-inline">
              PIX  <span class="text-muted float-right" *ngIf="formaDePagamento.taxaCobranca && formaDePagamento.taxaCobranca.ativa">
                         <i>+{{formaDePagamento.taxaCobranca.percentual}}% taxa</i></span>
            </label>
          </span>

          <div class="invalid-feedback" *ngIf="!pedido.pagamento.formaDePagamento">
            <p>Escolha a forma de pagamengo do seu pedido</p>
          </div>

          <div class="row mt-2" *ngIf="pedido.pagamento.formaDePagamento && pedido.pagamento.formaDePagamento.pix">
            <div class="ml-3 col" >

              <div class="form-group"  style="max-width: 200px" *ngIf="pedirCpfNoPix()">
                <h5  >Informe seu Cpf</h5>
                <div class="form-group">
                  <input type="text" class="form-control" autocomplete="off" required  #txtCpfPix
                         id="cpf" name="cpf" [(ngModel)]="pedido.pagamento.dadosPix.cpf" #cpf="ngModel"
                         mask="000.000.000-00" cpfValido placeholder="___.___.___-__" value="">
                  <div class="invalid-feedback">
                    <p *ngIf="cpf.errors?.required">Obrigatório</p>
                    <p *ngIf="cpf.errors?.cpfInvalido">CPF é invalido</p>
                  </div>
                </div>
              </div>
              <div class="form-group" style="max-width: 350px" >
                <h5  >Informe seu Email</h5>
                <div class="input-group">
                  <input id="emailPix" name="emailPix"  class="form-control" #txtEmailPix  email="true" type="email"
                         #emailPix="ngModel" placeholder="Informe seu email" [required]="true"
                         [(ngModel)]="pedido.pagamento.dadosPix.email"  />
                  <div class="invalid-feedback">
                    <p *ngIf="emailPix.errors?.required">
                      Para pagamentos pix você deve informar seu email. Caso aconteça algum problema, usaremos esse email para te contactar.
                    </p>

                    <p *ngIf="emailPix.errors?.email">Informe um email válido.</p>

                  </div>
                </div>

              </div>
            </div>
          </div>
        </div>

        <div class="form-group   col-12  cartao-online mb-0"  *ngFor="let formaDePagamento of formasDePagamentoOnline">
          <div class="mt-2">
            <span class="radio radio-blue w-100" [hidden]="!estaHabilitado(formaDePagamento, pedido)"
                  [ngClass]="{'selecionado': pagamentoOnlineEstaSelecionado(formaDePagamento) }">
                        <input id="formaPagamento{{formaDePagamento.id}}" name="formaDePagamento" type="radio"
                               [(ngModel)]="pedido.pagamento.formaDePagamento" [value]="formaDePagamento" class="k-radio" kendoRadioButton
                               [required]="true" (ngModelChange)="escolheuFormaDePagamentoOnline(formaDePagamento)"
                               #rdFormaDePagamento="ngModel"
                               [pagseguro]="{pedido: pedido, exigeCartao: exigeCartao, dadosCartao: pagamento.dadosCartao}"/>

                       <i class="fa fa-credit-card text-blue fa-2x ml-1" aria-hidden="true" style="    top: 5px;position: relative;"></i>

                        <label for="formaPagamento{{formaDePagamento.id}}" class="ml-1 d-inline">
                                       Cartão de Crédito

                          <span class="text-muted font-11 ml-1 d-inline" *ngIf="formaDePagamento.taxaCobranca && formaDePagamento.taxaCobranca.ativa"
                          >(+{{formaDePagamento.taxaCobranca.percentual}}% taxa)</span>

                        </label>
             </span>

            <label class="mt-1 acao-cartao " *ngIf="pagamentoOnlineEstaSelecionado(formaDePagamento)">
              <ng-container *ngIf="rdFormaDePagamento.errors">
                <div class="invalid-feedback" *ngIf="rdFormaDePagamento?.errors?.endereco_obrigatorio">
                  <p>Informe o endereço de cobrança da fatura do cartão de crédito</p>
                </div>
              </ng-container>
              <div class="invalid-feedback" *ngIf="rdFormaDePagamento?.errors?.dados_cartao_obrigatorio">
                <p>Informe os dados do cartão de cŕedito</p>
              </div>

              <div class="text-right" >
                <ng-container *ngIf="pagamento.dadosCartao">
                  <div class="">
                    <i class="fas fa-credit-card font-16"></i>
                    <span class="" *ngIf="!pagamento.dadosCartao.descricaoPagamento">
                          ****-{{pagamento.dadosCartao.ultimosNumeros}}
                        </span>
                    <span class="ml-2" *ngIf="pagamento.dadosCartao.descricaoPagamento">
                          {{pagamento.dadosCartao.descricaoPagamento}}
                        </span>
                    <button type="button" class="btn btn-blue ml-2" (click)="trocarCartao()">
                      <i class="far fa-credit-card mr-1 fa-lg"></i>
                      Alterar Cartão
                    </button>
                  </div>
                </ng-container>
                <ng-container *ngIf="!pedido.pagamento.dadosCartao  ">
                  <button class="btn btn-blue" type="button" (click)="trocarCartao()">
                    <i class="far fa-credit-card mr-1 fa-lg"></i>
                    Adicionar Cartão
                  </button>
                </ng-container>
              </div>
            </label>
          </div>

        </div>

        <div class="col-12"  *ngIf="formaPagamentoCarteirasDigitais">
          <app-tunnapay-digitais [formaDePagamento]="formaPagamentoCarteirasDigitais" [pedido]="pedido"
                                 (onDefiniuCartao)="setCartaoEscolhido($event)" (onEscolheuFormaPagamento)="escolheuFormaDePagamentoOnline($event)">

          </app-tunnapay-digitais>
        </div>
      </div>


    </ng-template>

  </kendo-tabstrip-tab>

  <kendo-tabstrip-tab [title]="'Pagar na entrega'" [selected]="(tabSelect  === 1)"  *ngIf="formasDePagamento.length">
    <ng-template kendoTabContent>

      <div class="form-group mb-2 mt-2 escolher mobile">
        <ng-container *ngFor="let formaDePagamento of formasDePagamento">
          <div [hidden]="!estaHabilitado(formaDePagamento, pedido)" class="  radio radio-blue mb-2"
          >
            <input [id]="'formaPagamento' + formaDePagamento.id" name="formaDePagamento" type="radio"
                   [(ngModel)]="pagamento.formaDePagamento" [value]="formaDePagamento" class="k-radio" kendoRadioButton
                   (ngModelChange)="alterouFormaDePagamento()"
                   [required]="true"/>
            <label for="formaPagamento{{formaDePagamento.id}}" class="ml-1 d-inline">{{formaDePagamento.descricao}}

              <span class="text-muted float-right" *ngIf="formaDePagamento.taxaCobranca && formaDePagamento.taxaCobranca.ativa">
             <i>+{{formaDePagamento.taxaCobranca.percentual}}% taxa</i></span>

              <span class="text-muted float-right" *ngIf="formaDePagamento.possuiDesconto && formaDePagamento.desconto > 0">
             <i>-{{formaDePagamento.desconto}}% de desconto</i></span>
            </label>
          </div>
        </ng-container>


        <div class="invalid-feedback" *ngIf="!pagamento.formaDePagamento">
          <p>Escolha a forma de pagamengo do seu pedido</p>
        </div>
      </div>

      <div *ngIf="pedido.pagamento?.formaDePagamento?.nome === 'dinheiro'" class="mt-2" >
        <h5 class="d-inline">Vai precisar de troco?  </h5>

        <div class="form-group mb-2  ml-3 mt-2 radio radio-blue">

          <input id="trocoSim" name="temTroco" type="radio"    kendoRadioButton  class="k-radio"
                 [(ngModel)]="pedido.pagamento.temTroco" value="sim"
                 [required]="pedido.pagamento?.formaDePagamento.nome === 'dinheiro'"/>
          <label for="trocoSim" class="mr-4">&nbsp;Sim</label>

          <input id="trocoNao" name="temTroco" type="radio"    kendoRadioButton  class="k-radio"
                 [(ngModel)]="pedido.pagamento.temTroco" value="nao" (ngModelChange)="precisaDeTroco(false)"
                 [required]="pedido.pagamento?.formaDePagamento.nome === 'dinheiro'"/>
          <label for="trocoNao" class="ml-1">&nbsp;Não</label>


          <div class="invalid-feedback" *ngIf="!pedido.pagamento.temTroco">
            Informe se vai precisar de troco.
          </div>
        </div>
      </div>

      <div id="divTroco"  *ngIf="pedido.pagamento?.formaDePagamento?.nome === 'dinheiro' && pedido.pagamento.temTroco ==='sim'">
        <div class="form-group ">
          <h5 for="trocoParaM">Troco para quanto? </h5>
          <input id="trocoParaM" name="trocoParaM" type="text" inputmode="decimal" class="form-control troco"
                 #txtTrocoM="ngModel" [trocoMinimo]="obtenhaTotalPagar()"
                 (ngModelChange)="calculeTroco($event)"
                 [(ngModel)]="pedido.pagamento.trocoPara" appSelecionarNoFoco
                 currencyMask [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',', align: 'left' }"/>
          <div class="invalid-feedback">
            <p *ngIf="txtTrocoM.errors?.trocoMinimo">
              A nota tem que ser maior que  {{obtenhaTotalPagar() | currency: "R$"}}</p>
          </div>
        </div>
      </div>

      <div class="produto pt-0 pb-2" *ngIf="pedido.troco && pagamento.formaDePagamento.nome === 'dinheiro'">

        <div class="media mt-0" >
          <div class="media-body">
            <h5 class="mt-0 mb-1"><span>dinheiro</span></h5>
          </div>
          <h5 class="mt-0">{{pagamento.trocoPara | currency: 'BRL'}}</h5>
        </div>
        <div class="media mt-1" *ngIf="pagamento.trocoPara > 0">
          <div class="media-body">
            <h4 class="mt-0 mb-1"><span>Troco</span></h4>
          </div>
          <h4 class="mt-0 preco" [class.negativo]="pedido.troco < 0">{{pedido.troco | currency: 'BRL'}}</h4>
        </div>

        <hr class="linha">
      </div>
    </ng-template>

  </kendo-tabstrip-tab>
</kendo-tabstrip>

