
<kendo-tabstrip #tabFormas class="nav-bordered" id="tabs" name="tabs" (tabSelect)="alterouTipoPagamento($event)"
                *ngIf="pedido.pagamento" style="background-color: var(--cor-fundo-site, #f7f8f8); border-color: var(--cor-borda, #dee2e6);">

  <kendo-tabstrip-tab [title]=" 'Pagar pelo site'"    [selected]=" (tabSelect  === 0)" *ngIf="temPagamentoOnline()" style="color: var(--cor-texto-primaria, inherit);">
    <ng-template kendoTabContent>

      <div class="row escolher row  mt-2 " style="background-color: var(--cor-fundo-elementos, #f7f8f8);">
        <div class="form-group col-12 mb-0"
             *ngIf="formasDePagamentoPix.length > 0 && estaHabilitado(formasDePagamentoPix[0], pedido)">
          <span class="mr-3 radio radio-blue mb-1 w-100" *ngFor="let formaDePagamento of formasDePagamentoPix"
                [ngClass]="{'selecionado': pagamentoOnlineEstaSelecionado(formaDePagamento)}"
                style="background-color: var(--cor-fundo-elementos, #f7f8f8);">

            <input [id]="'formaPagamento' + formaDePagamento.id" name="formaDePagamento" type="radio"
                   [(ngModel)]="pedido.pagamento.formaDePagamento" [value]="formaDePagamento" class="k-radio" kendoRadioButton
                   (ngModelChange)="alterouFormaDePagamentoParaPix()"
                    />
            <img src="https://user-images.githubusercontent.com/33992396/99478353-00e4d600-2933-11eb-8228-4bafe8571507.png"
                 style="width: 32px;" class="ml-3"/>


            <label for="formaPagamento{{formaDePagamento.id}}" class="ml-1 d-inline" style="color: var(--cor-texto-primaria, inherit);">
              PIX  <span class="text-muted float-right" *ngIf="formaDePagamento.taxaCobranca && formaDePagamento.taxaCobranca.ativa" style="color: var(--cor-texto-secundaria, #6c757d) !important;">
                         <i>+{{formaDePagamento.taxaCobranca.percentual}}% taxa</i></span>
            </label>
          </span>

          <div class="ml-3 col" *ngIf="pedido.pagamento.formaDePagamento?.pix">

            <div class="form-group" style="max-width: 200px" *ngIf="pedirCpfNoPix()">
              <h5 style="color: var(--cor-texto-primaria, inherit);">Informe seu Cpf</h5>
              <div class="form-group">
                <input type="text" class="form-control" autocomplete="off" required  #txtCpfPix
                       id="cpf" name="cpf" [(ngModel)]="pedido.pagamento.dadosPix.cpf" #cpf="ngModel"
                       mask="000.000.000-00" cpfValido placeholder="___.___.___-__" value=""
                       style="background-color: var(--cor-fundo-elementos, white); color: var(--cor-texto-primaria, inherit); border-color: var(--cor-borda, #ced4da);">
                <div class="invalid-feedback">
                  <p *ngIf="cpf.errors?.required">Obrigatório</p>
                  <p *ngIf="cpf.errors?.cpfInvalido">CPF é invalido</p>
                </div>
              </div>
            </div>
            <div class="form-group" style="max-width: 350px" >
              <h5 style="color: var(--cor-texto-primaria, inherit);">Informe seu Email</h5>
              <div class="input-group">
                <input id="emailPix" name="emailPix" class="form-control" #txtEmailPix email="true" type="email"
                       #emailPix="ngModel" placeholder="Informe seu email" [required]="true"
                       [(ngModel)]="pedido.pagamento.dadosPix.email"
                       style="background-color: var(--cor-fundo-elementos, white); color: var(--cor-texto-primaria, inherit); border-color: var(--cor-borda, #ced4da);" />
                <div class="invalid-feedback">
                  <p *ngIf="emailPix.errors?.required">
                    Para pagamentos pix você deve informar seu email. Caso aconteça algum problema, usaremos esse email para te contactar.
                  </p>

                  <p *ngIf="emailPix.errors?.email">Informe um email válido.</p>

                </div>
              </div>

            </div>
          </div>

        </div>

        <div class="form-group col-12 cartao-online mb-0" *ngFor="let formaDePagamento of formasDePagamentoOnline">
          <div class="mt-1">
            <span class="radio radio-blue w-100" [hidden]="!estaHabilitado(formaDePagamento, pedido)"
                  [ngClass]="{'selecionado': pagamentoOnlineEstaSelecionado(formaDePagamento)}"
                  style="background-color: var(--cor-fundo-elementos, #f7f8f8);">

              <input id="formaPagamento{{formaDePagamento.id}}"
                     name="formaDePagamento"
                     type="radio"
                     [(ngModel)]="pedido.pagamento.formaDePagamento"
                     [value]="formaDePagamento"
                     class="k-radio"
                     kendoRadioButton
                     (ngModelChange)="escolheuFormaDePagamentoOnline(formaDePagamento)"
                     #rdFormaDePagamento="ngModel"
                     [pagseguro]="{pedido: pedido, exigeCartao: exigeCartao, dadosCartao: pagamento.dadosCartao}"/>

             <i class="fa fa-credit-card text-blue fa-2x ml-3" aria-hidden="true" style="color: var(--cor-botao, #007bff);"></i>
             <label for="formaPagamento{{formaDePagamento.id}}" class="mb-0 ml-1 d-inline" style="color: var(--cor-texto-primaria, inherit);">
                      Cartão de Crédito
                        <span class="text-muted font-11 ml-1"
                              *ngIf="formaDePagamento.taxaCobranca && formaDePagamento.taxaCobranca.ativa"
                              style="color: var(--cor-texto-secundaria, #6c757d) !important;">
                          (+{{formaDePagamento.taxaCobranca.percentual}}% taxa)
                        </span>

                      <small class="font-11 text-muted" style="color: var(--cor-texto-secundaria, #6c757d) !important;" [hidden]="true">
                        <br>{{formaDePagamento.configMeioDePagamento.meioDePagamento}}
                      </small>
             </label>
            </span>

            <ng-container *ngIf="rdFormaDePagamento.errors">
              <div class="invalid-feedback" *ngIf="rdFormaDePagamento?.errors?.endereco_obrigatorio">
                <p>Informe o endereço de cobrança da fatura do cartão de crédito</p>
              </div>
            </ng-container>
            <div class="invalid-feedback" *ngIf="rdFormaDePagamento?.errors?.dados_cartao_obrigatorio">
              <p>Informe os dados do cartão de cŕedito</p>
            </div>
          </div>

          <label class="ml-1 d-inline acao-cartao ">
            <div class="text-right" *ngIf="pagamentoOnlineEstaSelecionado(formaDePagamento)">
              <ng-container *ngIf="pedido.pagamento.dadosCartao">
                <div class="d-flex align-items-center">
                  <i class="fas fa-credit-card font-16" style="color: var(--cor-botao, #007bff);"></i>
                  <span class="ml-2" *ngIf="!pedido.pagamento.dadosCartao.descricaoPagamento" style="color: var(--cor-texto-primaria, inherit);">
                          ****-{{pedido.pagamento.dadosCartao.ultimosNumeros}}
                        </span>
                  <span class="ml-2" *ngIf="pedido.pagamento.dadosCartao.descricaoPagamento" style="color: var(--cor-texto-primaria, inherit);">
                          {{pedido.pagamento.dadosCartao.descricaoPagamento}}
                        </span>
                  <button type="button" class="btn btn-blue ml-2" (click)="trocarCartao()"
                          style="background-color: var(--cor-botao, #007bff); border-color: var(--cor-botao, #007bff); color: var(--cor-texto-botao, white);">
                    <i class="far fa-credit-card mr-1 fa-lg"></i>
                    Alterar Cartão
                  </button>
                </div>
              </ng-container>
              <ng-container *ngIf="!pedido.pagamento.dadosCartao && pedido.entrega.formaDeEntrega">
                <button class="btn btn-blue" type="button" (click)="trocarCartao()"
                        style="background-color: var(--cor-botao, #007bff); border-color: var(--cor-botao, #007bff); color: var(--cor-texto-botao, white);">
                  <i class="far fa-credit-card mr-1 fa-lg"></i>
                  Adicionar Cartão
                </button>
              </ng-container>
            </div>
          </label>
        </div>

        <div class="col-12"  *ngIf="formaPagamentoCarteirasDigitais">
          <app-tunnapay-digitais [formaDePagamento]="formaPagamentoCarteirasDigitais" [pedido]="pedido"
           (onDefiniuCartao)="setCartaoEscolhido($event)" (onEscolheuFormaPagamento)="escolheuFormaDePagamentoOnline($event)">

          </app-tunnapay-digitais>
        </div>
      </div>


      <div class="invalid-feedback ml-3" *ngIf="!pedido.pagamento.formaDePagamento">
        <p>Escolha a forma de pagamengo do seu pedido</p>
      </div>
    </ng-template>
  </kendo-tabstrip-tab>

  <kendo-tabstrip-tab [title]="'Pagar na entrega'" [selected]="tabSelect  === 1"  *ngIf="formasDePagamento.length" style="color: var(--cor-texto-primaria, inherit);">
    <ng-template kendoTabContent>
      <div class="form-group mb-2 mt-2 escolher row cartoes" style="background-color: var(--cor-fundo-elementos, #f7f8f8);">
        <ng-container *ngFor="let formaDePagamento of formasDePagamento;  let i = index;">
          <div class="col-6 radio radio-blue mt-1 cpointer"  [hidden]="!estaHabilitado(formaDePagamento, pedido)">
            <span class="radio radio-blue cpointer" [ngClass]="{'selecionado': pedido.pagamento?.formaDePagamento?.id === formaDePagamento.id }"
                  style="background-color: var(--cor-fundo-elementos, #f7f8f8);">
                     <input [id]="'formaPagamento' + formaDePagamento.id" name="formaDePagamento" type="radio"
                            [(ngModel)]="pagamento.formaDePagamento" [value]="formaDePagamento" class="k-radio" kendoRadioButton
                            (ngModelChange)="selecioneFormaPagamento()"
                            [required]="true"/>
                              <label for="formaPagamento{{formaDePagamento.id}}" class="ml-1 d-inline" style="color: var(--cor-texto-primaria, inherit);">

                               <app-loja-bandeira-logo [bandeira]=" formaDePagamento.bandeira" [descricao]="formaDePagamento.descricao"  >

                              </app-loja-bandeira-logo>


                              </label>
                              <label class="text-muted font-11 ml-1 d-inline"
                                    *ngIf="formaDePagamento.taxaCobranca && formaDePagamento.taxaCobranca.ativa"
                                    style="color: var(--cor-texto-secundaria, #6c757d) !important;"
                              >(+{{formaDePagamento.taxaCobranca.percentual}}%)</label>

                            <label class="text-muted font-11 ml-1 d-inline"
                                  *ngIf="formaDePagamento.possuiDesconto && formaDePagamento.desconto > 0"
                                  style="color: var(--cor-texto-secundaria, #6c757d) !important;"
                            >(-{{formaDePagamento.desconto}}% taxa)</label>
          </span>
          </div>

          <div *ngIf="i===0" class="col-12">
            <app-troco-loja [obtenhaTotalPagar]="this.obtenhaTotalPagar" [pagamento]="pagamento"
                            [pedido]="pedido"></app-troco-loja>
          </div>

        </ng-container>

      </div>

    </ng-template>
  </kendo-tabstrip-tab>

  <kendo-tabstrip-tab [title]="'Pix (Chave)'"  *ngIf="formasDePagamentoManual.length " [selected]="tabSelect===2" style="color: var(--cor-texto-primaria, inherit);">
    <ng-template kendoTabContent>

      <div class="form-group mb-2 mt-2 escolher row cartoes" style="background-color: var(--cor-fundo-elementos, #f7f8f8);">
        <ng-container *ngFor="let formaDePagamento of formasDePagamentoManual">
          <div class="col-12 radio radio-blue mt-1 cpointer"  [hidden]="!estaHabilitado(formaDePagamento, pedido)">
                <span class="radio radio-blue cpointer" [ngClass]="{'selecionado': pedido.pagamento?.formaDePagamento?.id === formaDePagamento.id }"
                      style="background-color: var(--cor-fundo-elementos, #f7f8f8);">

                  <input [id]="'formaPagamento' + formaDePagamento.id" name="formaDePagamento" type="radio"    kendoRadioButton
                         [(ngModel)]="pagamento.formaDePagamento" [value]="formaDePagamento" class="k-radio"
                         (ngModelChange)="selecioneFormaPagamento()"
                         [required]="true"/>

                              <label for="formaPagamento{{formaDePagamento.id}}" class="ml-1 d-inline" style="color: var(--cor-texto-primaria, inherit);">

                               <app-loja-bandeira-logo [bandeira]=" formaDePagamento.bandeira" [descricao]="formaDePagamento.descricao"  >

                              </app-loja-bandeira-logo>


                              </label>
                              <label class="text-muted font-11 ml-1 d-inline"
                                    *ngIf="formaDePagamento.taxaCobranca && formaDePagamento.taxaCobranca.ativa"
                                    style="color: var(--cor-texto-secundaria, #6c757d) !important;"
                              >(+{{formaDePagamento.taxaCobranca.percentual}}%)</label>

                            <label class="text-muted font-11 ml-1 d-inline"
                                  *ngIf="formaDePagamento.possuiDesconto && formaDePagamento.desconto > 0"
                                  style="color: var(--cor-texto-secundaria, #6c757d) !important;"
                            >(-{{formaDePagamento.desconto}}% taxa)</label>
                             </span>

            <div class="ml-2 font-12 mt-1" *ngIf="formaDePagamento.chavePix" style="color: var(--cor-texto-primaria, inherit);">
              Chave para transferência do PIX: <b>{{formaDePagamento.chavePix}}</b>


              <div class="alert alert-info mt-2" style="background-color: var(--cor-fundo-elementos, #d1ecf1); color: var(--cor-texto-primaria, #0c5460); border-color: var(--cor-borda, #bee5eb);">
                <i class="k-icon k-i-information mr-1 text-blue" style="color: var(--cor-destaque, #007bff);"></i>
                Após realizar o pedido, faça o pagamento envie o comprovante para o lojista
              </div>

            </div>

          </div>
        </ng-container>
      </div>
    </ng-template>
  </kendo-tabstrip-tab>

</kendo-tabstrip>

