import {ApplicationRef, Component, OnInit} from "@angular/core";

import {DialogService} from "@progress/kendo-angular-dialog";
import {MyDetectorDevice} from "../../app/shared/MyDetectorDevice";
import {CarrinhoService} from "../../services/carrinho.service";
import {FormasPagamentoLoja} from "./formas-pagamento-loja";
import {ControlContainer, NgForm} from "@angular/forms";
import {ConstantsService} from "../../services/ConstantsService";

@Component({
  selector: 'app-loja-formaspagamento-nova',
  templateUrl: './formas-pagamento-loja-desktop-nova.html',
  styleUrls: ['../../app/finalizar-pedido/finalizar-pedido.component.scss'],
  viewProviders: [ { provide: ControlContainer, useExisting: NgForm } ]
})
export class FormasPagamentoLojaDesktopNovaComponent extends FormasPagamentoLoja implements OnInit {
  constructor(protected dialogService: DialogService,   protected detectorDevice: MyDetectorDevice,
              protected carrinhoService: CarrinhoService, protected app: ApplicationRef,
              protected constantsService: ConstantsService) {
    super(dialogService, detectorDevice, carrinhoService, app, constantsService)
  }
  ngOnInit(): void { }



}
