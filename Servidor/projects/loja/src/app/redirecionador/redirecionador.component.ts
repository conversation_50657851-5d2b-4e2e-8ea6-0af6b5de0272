import { Component, OnInit } from '@angular/core';
import {ConstantsService} from "../../services/ConstantsService";
import {ITela} from "../../objeto/ITela";
import {ActivatedRoute} from "@angular/router";

@Component({
  selector: 'app-redirecionador',
  templateUrl: './redirecionador.component.html',
  styleUrls: ['./redirecionador.component.scss']
})
export class RedirecionadorComponent implements OnInit, ITela {
  deveExibirBannerTema() {
    return true;
  }

  deveExibirMenu() {
    return true;
  }

  deveExibirTopo() {
    return true;
  }

  deveTerBordas() {
    return true;
  }

  constructor(private route: ActivatedRoute, private constantsService: ConstantsService) { }

  ngOnInit(): void {
    this.constantsService.empresa$.subscribe((empresa) => {
      if (!empresa) {
        return
      }

      const telefone = '+55' + empresa.numeroWhatsapp.whatsapp;
      const msg = this.route.snapshot.queryParamMap.get('m');
      const urlWhatsApp = `https://api.whatsapp.com/send?phone=${telefone}&text=${msg}`;
      window.location.href = urlWhatsApp;
    });
  }

}
