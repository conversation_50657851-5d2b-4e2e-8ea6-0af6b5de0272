.card {
  border: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  .card-body {
    padding: 2rem;
    position: relative;
    padding-bottom: 0rem;
  }
}

.bloqueio {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255,255,255,0.9);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;

  .k-icon {
    color: #007bff;
    font-size: 2rem;
  }
}

.form-group {
  margin-bottom: 1.5rem;

  label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #495057;
  }
}

kendo-textbox {
  width: 100%;

  ::ng-deep .k-textbox {
  //  padding: 0.65rem 1rem;
    border-radius: 4px;

    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    }
  }
}

kendo-dropdownlist {
  width: 100%;

  ::ng-deep .k-dropdown-wrap {
    border-radius: 4px;

    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    }
  }
}

.card-header-custom {
  background: #f8f9fa;
  padding: 1rem 2rem;
  border-bottom: 1px solid #dee2e6;

  h5 {
    margin: 0;
    color: #495057;
    font-weight: 500;
  }
}

.card-icon {
  margin-right: 0.5rem;
  color: #6c757d;
}

.bandeiras-aceitas {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 4px;

  img {
    height: 25px;
    opacity: 0.7;
    transition: opacity 0.2s;

    &:hover {
      opacity: 1;
    }
  }
}

.invalid-feedback {
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.alert {
  border-radius: 4px;

  i {
    margin-right: 0.5rem;
  }
}

// Animação de loading
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.k-i-loading {
  animation: spin 1s linear infinite;
}

.centralizado{
  margin: 0 auto;
  display: block;
}

@media (max-width: 950px) {
  form{
    padding: 12px;
    padding-bottom: 0;

    .form-control{
      font-size: 16px;
      font-stretch: 100%;
      line-height: normal;
      font-family: Poppins, sans-serif;
    }
  }
}

.card-preview {
  position: relative;
  width: 85%;
  height: 175px;
  background: linear-gradient(45deg, #1a1f71, #0066eb);
  border-radius: 16px;
  margin-bottom: 0;
  padding: 20px;
  padding-bottom: 0;
  color: white;
  font-family: 'Courier New', monospace;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  overflow: hidden;
  margin: 0 auto;
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSg0NSkiPjxyZWN0IHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgZmlsbD0icmdiYSgyNTUsMjU1LDI1NSwwLjEpIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI3BhdHRlcm4pIi8+PC9zdmc+');
    opacity: 0.1;
  }

  .chip {
    width: 50px;
    height: 40px;
    background: linear-gradient(135deg, #ffd700 0%, #b8860b 100%);
    border-radius: 8px;
    margin-bottom: 20px;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 10px;
      right: 10px;
      height: 1px;
      background: rgba(0,0,0,0.3);
    }
  }

  .card-number {
    font-size: 1.5rem;
    letter-spacing: 2px;
    margin-bottom: 20px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
  }

  .card-info {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;

    .holder {
      text-transform: uppercase;
      font-size: 0.9rem;

      .label {
        font-size: 0.7rem;
        opacity: 0.8;
        margin-bottom: 4px;
      }
    }

    .expiry {
      text-align: right;
      font-size: 0.9rem;

      .label {
        font-size: 0.7rem;
        opacity: 0.8;
        margin-bottom: 4px;
      }
    }
  }
}

// Responsividade para o cartão
@media (max-width: 768px) {
  .card-preview {
    height: 180px;

    .card-number {
      font-size: 1.2rem;
    }

    .card-info {
      font-size: 0.8rem;

      .label {
        font-size: 0.6rem;
      }
    }
  }

  form{
    padding: 0 !important;
    padding-top: 10px !important;
    .card-body{
      padding: 1rem !important;
    }
  }
}

.btn-google-pay {
  background: #000000;
  color: #ffffff;
  border: none;
  border-radius: 4px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  min-width: 240px;

  &:hover:not(:disabled) {
    background: #1a1a1a;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: none;
  }

  &:disabled {
    background: #cccccc;
    cursor: not-allowed;
    opacity: 0.8;
  }

  .google-pay-logo {
    height: 24px;
    width: auto;
  }
}

// Animação de loading para o botão
.btn-google-pay.loading {
  position: relative;
  color: transparent;

  &::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
  }
}

#google-pay-container {
  display: flex;
  justify-content: center;
  margin: 1rem 0;
  min-height: 40px;
}

#frame-container-3ds {
  width: 100%;
  min-height: 0;
  opacity: 0;
  position: absolute;
}

#modal-container-3ds {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: none;

  &.active {
    display: block;
  }
}

// Ajuste do loading durante autenticação 3DS
.bloqueio.loading-3ds {
  background: rgba(255,255,255,0.95);
  z-index: 10000;

  .loading-text {
    margin-top: 10px;
    color: #007bff;
    font-size: 14px;
  }
}
