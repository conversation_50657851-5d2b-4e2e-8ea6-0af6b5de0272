<div class="alert alert-danger mb-2" role="alert" *ngIf="erroInicializacao">
  <i class="fas fa-times"></i>
  {{erroInicializacao}}
</div>

<div class="alert alert-danger mb-2" role="alert" *ngIf="msgErro" #erroDiv>
  <i class="fas fa-exclamation-triangle"></i> {{msgErro}}
</div>



<div class="card-preview">
  <div class="chip"></div>
  <div class="card-number">
    {{cartao.cardNumber || '•••• •••• •••• ••••'}}
  </div>
  <div class="card-info">
    <div class="holder">
      <div class="label">TITULAR DO CARTÃO</div>
      <div>{{cartao.nome || 'NOME DO TITULAR'}}</div>
    </div>
    <div class="expiry">
      <div class="label">VALIDADE</div>
      <div>
        {{(cartao.validade ? (cartao.validade | date: 'MM')  : 'MM' )}}/{{cartao.validade ? (cartao.validade | date: 'YY'): 'AA'}}
      </div>
    </div>
  </div>
</div>

<form [ngClass]="{'needs-validation': !formCartao.submitted, 'was-validated': formCartao.submitted}"
      novalidate #formCartao="ngForm">
  <div class="card  mt-2">
    <div class="card-body">
      <div class="bloqueio" *ngIf="(!this.definiuSessao || carregando3ds)">
        <div class="k-icon k-i-loading" [hidden]="erroInicializacao"></div>
      </div>

      <div class="alert alert-danger" *ngIf="erroCartao">
        <i class="fas fa-exclamation-circle"></i>
        {{erroCartao}}
      </div>


      <div class="row">
        <div class="col-12">
          <div class="form-group">
            <label>
              <i class="fas fa-credit-card card-icon"></i>
              Número do Cartão
            </label>
            <input type="text" class="form-control input-background" autocomplete="off"  [mask]="'0000-0000-0000-0009'"
                   id="numero" name="numero" [(ngModel)]="cartao.numero" #numero="ngModel" #numeroElem appAutoFocus
                   required   placeholder="0000-0000-0000-000" >

            <div class="invalid-feedback">
              <p *ngIf="formCartao.submitted && formCartao.form.get('cardNumber')?.errors?.required">
                Número do cartão é obrigatório
              </p>
            </div>
          </div>
        </div>

        <div class="col-sm-8 col-12">
          <div class="form-group">
            <label>
              <i class="fas fa-user card-icon"></i>
              Nome do Titular
            </label>
            <kendo-textbox class="form-control  "
              [(ngModel)]="cartao.nome"
              name="cardHolderName"  id="cardHolderName"
              required
              placeholder="Nome como está no cartão">
            </kendo-textbox>
            <div class="invalid-feedback">
              <p *ngIf="formCartao.submitted && formCartao.form.get('cardHolderName')?.errors?.required">
                Nome do titular é obrigatório
              </p>
            </div>
          </div>
        </div>


        <div class=" col-sm-4 col-6">
          <div class="form-group">
            <label>
              <i class="fas fa-calendar-alt card-icon"></i>
              Validade
            </label>
            <div  >
              <kendo-dateinput name="validade" id="validade" format="MM/yy" class="form-control"  required #validade="ngModel"
                               [(ngModel)]="cartao.validade" data-pagarmecheckout-input>

              </kendo-dateinput>
              <div class="invalid-feedback">
                <p *ngIf="formCartao.submitted && !cartao.expirationMonth">
                  Mês é obrigatório
                </p>
              </div>
            </div>
          </div>
        </div>

        <div class="col-sm-6 col-6">
          <div class="form-group">
            <label>
              <i class="fas fa-lock card-icon"></i>
              CVV
            </label>
            <kendo-textbox   [(ngModel)]="cartao.cvv"
              name="securityCode" id="securityCode"  class="form-control  "
              required placeholder="000" [maxlength]="4">
            </kendo-textbox>
            <div class="invalid-feedback">
              <p *ngIf="formCartao.submitted && !cartao.cvv">
                CVV é obrigatório
              </p>
            </div>
          </div>
        </div>

        <div class="col-sm-6 col-12">
          <div class="form-group">
            <label>
              <i class="fas fa-id-card card-icon"></i>
              CPF do Titular
            </label>
            <kendo-textbox  [(ngModel)]="cartao.cpf"  class="form-control  "
                            name="cpf"    id="cpf" required #cpf="ngModel"  cpfValido
                            mask="000.000.000-00" cpfValido placeholder="___.___.___-__"  >

            </kendo-textbox>
            <div class="invalid-feedback">
              <p *ngIf="cpf.errors?.required">Obrigatório</p>
              <p *ngIf="cpf.errors?.cpfInvalido">CPF é invalido</p>
            </div>
          </div>
        </div>

      </div>

      <!--<div class="bandeiras-aceitas">
        <img src="assets/img/visa.png" alt="Visa">
        <img src="assets/img/mastercard.png" alt="Mastercard">
        <img src="assets/img/amex.png" alt="American Express">
        <img src="assets/img/elo.png" alt="Elo">
      </div> -->
    </div>
  </div>
</form>

<div class="card mt-3" *ngIf="false">
  <div class="card-body">
    <!-- Container para o frame de coleta de dados 3DS -->
    <div id="frame-container-3ds"></div>

    <!-- Container para o modal de desafio 3DS -->
    <div id="modal-container-3ds"></div>
  </div>
</div>

