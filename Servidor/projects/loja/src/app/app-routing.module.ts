import { NgModule } from '@angular/core';
import {Routes, RouterModule, UrlSegment, UrlSegmentGroup, Route} from '@angular/router';
import {SiteProdutosComponent} from "../produtos/site-produtos.component";
import {SiteProdutoComponent} from "../site-produto/site-produto.component";
import {LojaCarrinhoComponent} from "../loja-carrinho/loja-carrinho.component";
import {EscolherEnderecoComponent} from "../escolher-endereco/escolher-endereco.component";
import {LojaPagamentoComponent} from "../loja-pagamento/loja-pagamento.component";
import {LojaDadosClienteComponent} from "../loja-dados-cliente/loja-dados-cliente.component";
import {EntregaComponent} from "../entrega/entrega.component";
import {PaginaComponent} from "../pagina/pagina.component";
import {FinalizarPedidoComponent} from "./finalizar-pedido/finalizar-pedido.component";
import {TelaBuscaProdutosComponent} from "./tela-busca-produtos/tela-busca-produtos.component";
import {LojaPerfilComponent} from "./loja-perfil/loja-perfil.component";
import {PainelLoginComponent} from "./painel-login/painel-login.component";
import {CadContatoLojaComponent} from "./cad-contato/cad-contato-loja.component";
import {PedidosLojaComponent} from "./loja-pedidos/pedidos-loja.component";
import {TelaPedidoComponent} from "./tela-pedido/tela-pedido.component";
import {MeusDadosComponent} from "./meus-dados/meus-dados.component";
import {ValidarContatoComponent} from "./cad-contato/validar-contato/validar-contato.component";
import {ConfirmarContatoComponent} from "./cad-contato/confirmar-contato/confirmar-contato.component";
import {LojaRecuperarComponent} from "./loja-recuperar/loja-recuperar.component";
import {TelaAcompanharPedidoComponent} from "./tela-acompanhar-pedido/tela-acompanhar-pedido.component";
import {PaginaFranquiaComponent} from "./franquia/pagina-franquia/pagina-franquia.component";
import {LojaConfiguracoesComponent} from "./loja-configuracoes/loja-configuracoes.component";
import {TelaPosicaoMapaComponent} from "./tela-posicao-mapa/tela-posicao-mapa.component";
import {TelaBuscaEcommerceComponent} from "./tela-busca-ecommerce/tela-busca-ecommerce.component";
import { TelaMultiLojaComponent } from './tela-multi-loja/tela-multi-loja.component';
import {PaginaMultiLojaComponent} from "./pagina-multi-loja/pagina-multi-loja.component";
import {TelaAvaliarPedidoComponent} from "./tela-avaliar-pedido/tela-avaliar-pedido.component";
import {RedirecionadorComponent} from "./redirecionador/redirecionador.component";
import {FinalizarPedidoTotemComponent} from "./finalizar-pedido-totem/finalizar-pedido-totem.component";
import {ValidarContatoZapComponent} from "./cad-contato/validar-contato-zap/validar-contato-zap.component";
import {ThreeDSRetornoComponent} from "./tela-acompanhar-pedido/three-ds-retorno";
import {SiteBrindesComponent} from "../produtos/brindes/site-brindes.component";
import {TabletPedidosComponent} from "./tablet-pedidos/tablet-pedidos.component";
import {TabletPedidosConfigurarComponent} from "./tablet-pedidos/associar-tablet/tablet-pedidos-configurar";

export function MatchPaginaGrupoListaLojas(url: UrlSegment[], group: UrlSegmentGroup, route: Route) {
  let link = window.location.hostname

  if( window['telaMultiLoja'] ) {
    return null;
  }

  if(link.indexOf('chinainboxrj.meucardapio.ai') >= 0 || link.indexOf('chinainboxrj.zapkit.com.br') >= 0 ||
    link.indexOf('teste.meucardapio.ai') >= 0 || window['agregador'] ) {

    if(url && url[0])
      if (url[0].path === 'loja' || url[0].path === 'cardapio') return {consumed: [url[0]]}

    return {consumed: []}
  }

  return null;
}

export function MatchLojaCardapioOuRaiz(url: UrlSegment[], group: UrlSegmentGroup, route: Route) {
  let link = window.location.hostname

  if(link.indexOf('meucardapio') >= 0 || link.indexOf('promokit') < 0) {

    if(url && url[0]) {
      if (url[0].path === 'loja' || url[0].path === 'cardapio')
        return {consumed: [url[0]]}
    }

    return {consumed: []}
  }

  if(url.length === 0) return null;

  if(url[0].path === 'loja' || url[0].path === 'cardapio') return {consumed: [url[0]]}

  return null;
}

export function MatchCardapioRede(url: UrlSegment[], group: UrlSegmentGroup, route: Route) {
  let link = window.location.hostname

  return {consumed: []};
}

export function MatchGrupoTelaMultiLoja(url: UrlSegment[], group: UrlSegmentGroup, route: Route) {
  let link = window.location.hostname

  if( window['telaMultiLoja'] ) {
    if(url && url[0])
      if (url[0].path === 'l' || url[0].path === 'cardapio') {
        return {consumed: [url[0]]}
      }

    return {consumed: []}
  }

  return null;
}

const rotas: Routes = [
  {path: 'index', pathMatch: 'full', component: SiteProdutosComponent},
  {path: 'lojas', pathMatch: 'full', component: TelaMultiLojaComponent},
  {path: 'avaliar-pedido/:guid', pathMatch: 'full', component: TelaAvaliarPedidoComponent},
  {path: 'categoria/:nome/:idc', pathMatch: 'full', component: SiteProdutosComponent},
  {path: 'local/:hm', pathMatch: 'full', component: SiteProdutosComponent},
  {path: 'desconto/:codigopromo', pathMatch: 'full', component: SiteProdutosComponent},
  {path: 'produto/:id', pathMatch: 'full', component: SiteProdutosComponent},
  {path: 'produto/:id/editar/:posicao', pathMatch: 'full', component: SiteProdutoComponent},
  {path: 'carrinho', pathMatch: 'full', component: LojaCarrinhoComponent},
  {path: 'busca', pathMatch: 'full', component: TelaBuscaProdutosComponent},
  {path: 'brindes', pathMatch: 'full', component: SiteBrindesComponent},
  {path: 'busca/ecommerce', pathMatch: 'full', component: TelaBuscaEcommerceComponent},
  {path: 'busca/ecommerce/:categoria', pathMatch: 'full', component: TelaBuscaEcommerceComponent},
  {path: 'criar-endereco', pathMatch: 'full', component: EscolherEnderecoComponent},
  {path: 'posicionar', pathMatch: 'full', component: TelaPosicaoMapaComponent},
  {path: 'pagamento', pathMatch: 'full', component: LojaPagamentoComponent},
  {path: 'cliente', pathMatch: 'full', component: LojaDadosClienteComponent},
  {path: 'forma-entrega', pathMatch: 'full', component: EntregaComponent},
  {path: 'pedido', pathMatch: 'full', component: FinalizarPedidoComponent},
  {path: 'pedido-totem', pathMatch: 'full', component: FinalizarPedidoTotemComponent},
  {path: 'pedido-tablet', pathMatch: 'full', component: TabletPedidosComponent},
  {path: 'pedido-tablet/configurar', pathMatch: 'full', component: TabletPedidosConfigurarComponent},
  {path: 'r/w', pathMatch: 'full', component: RedirecionadorComponent},
  {path: 'meusPedidos', pathMatch: 'full', component: PedidosLojaComponent},
  {path: 'perfil', pathMatch: 'full', component: LojaPerfilComponent},
  {path: 'configuracoes', pathMatch: 'full', component: LojaConfiguracoesComponent},
  {path: 'login', pathMatch: 'full', component: PainelLoginComponent},
  {path: 'recuperar', pathMatch: 'full', component: LojaRecuperarComponent},
  {path: 'recuperar/:token', pathMatch: 'full', component: LojaRecuperarComponent},
  {path: 'cadastro', pathMatch: 'full', component: CadContatoLojaComponent},
  {path: 'cadastro/validar/:token', pathMatch: 'full', component: ValidarContatoComponent},
  {path: 'cadastro/confirmar/:token', pathMatch: 'full', component: ConfirmarContatoComponent},
  {path: 'login/validar/whatszapp', pathMatch: 'full', component: ValidarContatoZapComponent},
  {path: 'meus-dados', pathMatch: 'full', component: MeusDadosComponent},
  {path: 'exibir-pedido/:guid', pathMatch: 'full', component: TelaPedidoComponent},
  {path: 'pedido/acompanhar/:guid', pathMatch: 'full', component: TelaAcompanharPedidoComponent},
  {path: 'pedido/acompanhar/:guid/3dsretorno/:status3ds', pathMatch: 'full', component: TelaAcompanharPedidoComponent},
  {path: '3ds/retorno/:status', pathMatch: 'full', component: ThreeDSRetornoComponent},
  { path: '', pathMatch: 'full', component: SiteProdutosComponent},
];

const routes: Routes = [
  {
    component: PaginaComponent,
    matcher: MatchGrupoTelaMultiLoja,
    children: [
      {path: 'index', pathMatch: 'full', component: TelaMultiLojaComponent},
      {path: 'pedido', pathMatch: 'full', component: FinalizarPedidoComponent},
      {path: '', pathMatch: 'full', component: TelaMultiLojaComponent},
    ]
  },
  {
    component: PaginaFranquiaComponent,
    matcher: MatchPaginaGrupoListaLojas,
  },
  {
    path: 'marca/:dominio',
    component: PaginaMultiLojaComponent,
    children: rotas
  },
  {
    matcher: MatchLojaCardapioOuRaiz,
    component: PaginaComponent,
    children: rotas
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes, { relativeLinkResolution: 'legacy' })],
  exports: [RouterModule]
})
export class AppRoutingModule { }
