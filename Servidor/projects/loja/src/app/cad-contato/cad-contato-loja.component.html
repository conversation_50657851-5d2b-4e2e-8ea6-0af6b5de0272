<app-header-loja #headerLoja [titulo]="'Cadastro'" [retorno]="'login'" *ngIf="!this.abriuComoModal"></app-header-loja>
<app-header-tela [titulo]="'Cadastro'" [exibirFechar]="true" (fechou)="fecheTela()" *ngIf="this.abriuComoModal" ></app-header-tela>

<form [ngClass]="{'needs-validation': !frmt.submitted, 'was-validated': frmt.submitted}"
      novalidate #frmt="ngForm" (ngSubmit)="validarTelefone(frmt)" class="mt-3"  *ngIf="!validouTelefone">

  <div class="form-group mb-2 col">
    <label for="telefone">Telefone com DDD</label>
    <div class="input-group ">
      <input kendoTextBox    name="telefone"  #telefone="ngModel" mask="(00)0-0000-0000"
             appAutoFocus
             type="text" class="form-control" autocomplete="off"   [disabled]="buscando"
             [(ngModel)]="contato.telefone"  placeholder="Informe seu telefone com DDD"  required/>


      <div class="invalid-feedback">
        <p *ngIf="telefone.errors?.required">Telefone é obrigatório</p>
        <p *ngIf="telefone.errors?.mask">Telefone inválido</p>
      </div>

    </div>

  </div>


  <div class="text-danger mt-2 mb-2   text-center" role="alert" *ngIf="erro"  >
    <b> {{erro}}</b>
  </div>
  <div class="mt-3 mb-3 col"   >

    <button class="btn btn-block mb-1 btn-success " type="button" *ngIf="exibirLogin"  (click)="vaParaLogin()" >
         Fazer login
    </button>

    <button class="btn btn-blue btn-block " type="submit" [disabled]="aguardeProcessar" (click)="validarTelefone(frmt)">
      <i class="k-icon k-i-loading mr-1" *ngIf="aguardeProcessar"></i>
      Continuar </button>

  </div>

</form>

<form [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"
      novalidate #frm="ngForm" (ngSubmit)="submitForm()" class="mt-3"  *ngIf="validouTelefone">

  <div class="form-group mb-2 col">
    <label for="telefone">Telefone</label>
    <div class="input-group ">
      <input kendoTextBox  id="telefone" name="telefone"  #telefone="ngModel" mask="(00)0-0000-0000"
             [readonly]="this.contato.fazerOptin || this.contato.atualizarCadastro"
             (change)="validarTelefone(frm)"
             type="text" class="form-control" autocomplete="off"   [disabled]="buscando"
             [(ngModel)]="contato.telefone"  placeholder="Telefone com DDD"  required/>


      <div class="invalid-feedback">
        <p *ngIf="telefone.errors?.required">Telefone é obrigatório</p>
        <p *ngIf="telefone.errors?.mask">Telefone inválido</p>
      </div>

    </div>

  </div>


  <div class="form-group mb-2 col">
    <label for="nome">Nome </label>
    <input kendoTextBox id="nome" name="nome"  placeholder="Nome completo"   nomeCompleto nomeValido
           class="form-control"   #nome="ngModel" appAutoFocus
           [(ngModel)]="contato.nome" required/>

    <div class="invalid-feedback">
      <p *ngIf="nome.errors?.required">Nome é obrigatório</p>
      <p *ngIf="nome.errors?.nomeCompleto">Informe seu nome completo</p>
      <p *ngIf="nome.errors?.nomeValido">Informe um nome válido</p>
    </div>
  </div>



  <div class="form-group mb-2 col" *ngIf="informarCpf">
    <label  >CPF</label>
    <input type="text" class="form-control" autocomplete="off" (change)="informouCpf()"
           name="cpf" [(ngModel)]="contato.cpf" #cpf="ngModel" mask="000.000.000-00" cpfValido
           placeholder="___.___.___-__"   [required]="cpfObrigatorio"    >
    <div class="invalid-feedback">
      <p *ngIf="cpf.errors?.required">CPF é obrigatório</p>
      <p *ngIf="!cpf.errors?.required && cpf.errors?.cpfInvalido">CPF inválido</p>
    </div>
  </div>

  <div class="form-group mb-2 col " *ngIf="informarDataNascimento">
    <label  >Data Nascimento</label>
    <kendo-datepicker #dataNascimento='ngModel' [(ngModel)]="contato.dataNascimento"
                      class="form-control "   format="dd/MM/yyyy"
                      name="dataNascimento" activeView="decade"
                      [required]="dataNascimentoObrigatorio"     >
      <kendo-datepicker-messages
        today="Hoje"
        toggle="Trocar calendário"
      ></kendo-datepicker-messages>

    </kendo-datepicker>

    <div class="invalid-feedback">
      <p *ngIf="dataNascimento.errors?.required">Data de nascimento é obrigatória</p>
    </div>

  </div>

  <div class="form-group mb-2 col" *ngIf="this.contato.fazerOptin">
    <label for="email">E-mail </label>
    <input type="email" class="form-control" autocomplete="off"  required
           name="email"  [(ngModel)]="contato.email" #email="ngModel"
           placeholder="Email do usuário"   >
    <div class="invalid-feedback">
      <p *ngIf="email.errors?.required" >Email é obrigatório</p>
    </div>
  </div>


  <div class="form-group mb-2 col aceite-fidelidade" *ngIf="fidelidadeExterna?.aceitarFidelidade"
       [ngClass]="{'menor': empresa?.idRede === 2}" >
    <input  id="optin" name="optin" type="checkbox"    kendoCheckBox [(ngModel)]="fidelidadeExterna.optin"      class="k-checkbox"   />
    <label for="optin" class="ml-1 usar-saldo"  >
      Quero ganhar cashback com  {{empresa.idRede === 1 ? 'Meu China in Box' : 'Gendai Vip'}}
      <img src="{{fidelidadeExterna?.logo}}" class="ml-2" *ngIf="fidelidadeExterna?.logo">

      <br>
      <span  > Estou de acordo com o  <a href="{{fidelidadeExterna.linkRegras}}" target="_blank"  >Regulamento</a>.</span>
    </label>

  </div>



  <fieldset *ngIf="!this.contato.fazerOptin && !this.contato.atualizarCadastro">
    <legend>Dados de acesso</legend>
    <div class="form-group mb-2 col">
      <label for="email">E-mail </label>
      <input type="email" class="form-control" autocomplete="off"  required
             id="email" name="email"  [(ngModel)]="contato.email" #email="ngModel"
             placeholder="Email do usuário"   >
      <div class="invalid-feedback">
        <p *ngIf="email.errors?.required" >Email é obrigatório</p>
      </div>
    </div>

    <div class="form-group mb-2 col"  >
      <label for="senha">Senha</label>
      <label   class="col-form-label float-right cpointer text-blue" (click)="exibirSenhaTela()">
        <i class="fa fa-eye fa-lg   " *ngIf="!exibirSenha"></i>
        <i class="fa fa-eye-slash fa-lg   " *ngIf="exibirSenha"></i>
      </label>
      <span *ngIf="!exibirSenha">
            <input class="form-control" type="password"  id="senha" name="senha"  #senha="ngModel"
                   [(ngModel)]="contato.senha" placeholder="Informe sua senha"   required >
            <div  class="invalid-feedback">
              <p  *ngIf="senha.errors?.required">Senha é obrigatório</p>
            </div>
     </span>

      <span *ngIf="exibirSenha">
          <input class="form-control" type="text"  id="senhaTexto" name="senhaTexto"  #senhaTexto="ngModel"
                 [(ngModel)]="contato.senha" placeholder="Informe sua senha" required>
          <div  class="invalid-feedback">
            <p  *ngIf="senhaTexto.errors?.required">Senha é obrigatório</p>
          </div>
     </span>

    </div>
  </fieldset>

  <div class="text-danger mt-2 mb-2 text-center" role="alert" *ngIf="erro"  >
    <b> {{erro}}</b>
  </div>

  <div class="mt-3 mb-3 col"   >
    <button class="btn btn-blue btn-block "  type="submit"  [disabled]="aguardeProcessar">
      <i class="k-icon k-i-loading mr-1" *ngIf="aguardeProcessar"></i>
       {{this.contato.fazerOptin ? 'Quero Participar' : (this.contato.atualizarCadastro ? 'Atualizar Cadastro' : 'Cadastrar')}} </button>
 </div>


</form>
