<app-header-loja [titulo]="'Código Validação'"></app-header-loja>

<form  novalidate #frm="ngForm" (ngSubmit)="validarCodigo()" class="mt-3"    >

  <p class="text-center text-muted">Digite o código de  5 digitos  enviado via <b>{{meioDeEnvio}}</b>  para:</p>

  <p class="text-center preto font-18"> <b>{{this.contato.telefone  | telefone}}</b></p>
  <div class="input-group mb-2 col">

    <input class="form-control codigo-validacao" type="text" minlength="5" mask="0-0-0-0-0"
           id="codigo" name="codigo"  #codigo="ngModel" (ngModelChange)="alterouCodigo($event)"
           [(ngModel)]="contato.codigo" placeholder="0-0-0-0-0" required  appAutoFocus>


    <div class="invalid-feedback">
      <p *ngIf="codigo.errors?.required">Informe o código de validação de 5 digitos</p>

    </div>
  </div>

  <div class="text-danger mt-2 mb-2  text-center" role="alert" *ngIf="erro"  >
    <b> {{erro}}</b>
  </div>

  <div class="mb-3 col"   >
    <button class="btn btn-blue btn-block " type="submit" [disabled]="aguardeProcessar || frm.invalid"  >
      <i class="k-icon k-i-loading mr-1" *ngIf="aguardeProcessar"></i>
      Continuar </button>

  </div>



</form>
