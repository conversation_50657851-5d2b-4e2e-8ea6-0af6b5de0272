import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { ValidarContatoComponent } from './validar-contato.component';

describe('ValidarContatoComponent', () => {
  let component: ValidarContatoComponent;
  let fixture: ComponentFixture<ValidarContatoComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ ValidarContatoComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ValidarContatoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
