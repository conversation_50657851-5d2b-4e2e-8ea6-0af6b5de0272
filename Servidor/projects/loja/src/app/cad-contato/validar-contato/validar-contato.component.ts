import {Component, OnInit, ViewChild} from '@angular/core';
import {ActivatedRoute, Router} from "@angular/router";
import {DominiosService} from "../../../services/dominios.service";
import {AutorizacaoLojaService} from "../../../services/autorizacao-loja.service";
import {NgForm} from "@angular/forms";
import {ITela} from "../../../objeto/ITela";

@Component({
  selector: 'app-validar-contato',
  templateUrl: './validar-contato.component.html',
  styleUrls: ['./validar-contato.component.scss']
})
export class ValidarContatoComponent implements OnInit , ITela  {
  @ViewChild('frm')  frm: NgForm;
  contato: any = {};
  erro: string;
  aguardeProcessar: boolean;
  token: any;
  meioDeEnvio: any;
  constructor(private router: Router,  private dominiosService: DominiosService,
              private  activatedRoute: ActivatedRoute,
              private autorizacao: AutorizacaoLojaService) { }

  ngOnInit(): void {
    this.token = this.activatedRoute.snapshot.params.token;

    this.autorizacao.obtenhaDadosToken(this.token).then( resposta => {
      this.contato = resposta.contato;
      this.meioDeEnvio = resposta.meioDeEnvio;
    }).catch(erro => {
      this.erro = erro;
    })
  }

  validarCodigo() {


  }

  alterouCodigo(codigo) {

    if(this.frm.controls.codigo.valid){
      delete this.erro;
      this.aguardeProcessar = true;
      this.autorizacao.valideContato(this.token, codigo).then( erro => {
        if(!erro){
          this.dominiosService.navegueParaUrl('cadastro/confirmar/' + this.token)
        } else {
          this.aguardeProcessar = false;
        }
      }).catch(erro => {
        this.aguardeProcessar = false;
        this.erro = erro;
      })
    }

  }

  deveExibirTopo() {
    return false;
  }

  deveExibirMenu(){
    return true;
  }

  deveTerBordas() {
    return true;
  }

  deveExibirBannerTema() {
    return false;
  }
}
