import {Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild} from '@angular/core';
import {NgForm} from "@angular/forms";
import {ActivatedRoute, Router} from "@angular/router";
import {DominiosService} from "../../../services/dominios.service";
import {AutorizacaoLojaService} from "../../../services/autorizacao-loja.service";
import {ConstantsService} from "../../../services/ConstantsService";
import {ITela} from "../../../objeto/ITela";
import { interval, Subscription } from 'rxjs';
import {MaskedTextBoxComponent} from "@progress/kendo-angular-inputs";
import {CarrinhoService} from "../../../services/carrinho.service";
import {DialogRef, DialogService} from "@progress/kendo-angular-dialog";
import {PopupUtils} from "../../../objeto/PopupUtils";

@Component({
  selector: 'app-validar-contato-zap',
  templateUrl: './validar-contato-zap.component.html',
  styleUrls: ['./validar-contato-zap.component.scss']
})
export class ValidarContatoZapComponent implements   OnInit , OnDestroy, ITela  {
  @ViewChild('frm')  frm: NgForm;
  @ViewChild('kendoMaskedTextBox', {static: false}) txtTelefone: MaskedTextBoxComponent;
  paisPadrao =  '+55';
  contato: any = { codigoPais: this.paisPadrao};
  erro: string;
  processando = false;
  validarCodigo = false;
  retorno: string;
  empresa: any = {}
  codigoValido: boolean;
  countdownSeconds = 60 * 5;
  timerSubscription: Subscription;
  formattedTime = '01:00';
  encodedText: string;
  phoneMask = "(00) 00000-0000";
  modal = false;
  window: any
  constructor(private router: Router,  private dominiosService: DominiosService, private carrinhoService: CarrinhoService,
              private  activatedRoute: ActivatedRoute, private constantsService: ConstantsService,
              private autorizacao: AutorizacaoLojaService) {

    this.encodedText = encodeURIComponent("Não recebi meu codigo de login pelo whatsApp");
  }

  static abraComoPopup(router,  location, activatedRoute, dialogService: DialogService,
                       contato: any = null, onClose: any = null){

    let dimensao = PopupUtils.calculeAlturaLargura(false)

    const windowRef: DialogRef = dialogService.open({
      title: null,
      content: ValidarContatoZapComponent,
      minWidth: 200,
      width: dimensao.largura,
      height: 500
    });

    let telaValidar: ValidarContatoZapComponent = windowRef.content.instance;

    telaValidar.setModal(windowRef, contato ? contato.telefone : null )

    PopupUtils.abraJanela(router,  location, activatedRoute, windowRef, {}, onClose);



  }

  ngOnInit(): void {
    if(window.history.state.contato )
      this.contato = window.history.state.contato;
    else {
      let telefone = this.activatedRoute.snapshot.queryParams.tel;

      if(telefone)
        this.contato.telefone = telefone
    }

    this.retorno = this.activatedRoute.snapshot.queryParams.t
    if(!this.contato.codigoPais)
       this.contato.codigoPais = this.paisPadrao;

    this.constantsService.empresa$.subscribe( empresa => {
      if (!empresa) return;
      this.empresa = empresa;
    });
  }

  setModal(window, telefone: string = null){
    this.window = window;
    this.modal = true;
    if(telefone) this.contato.telefone = telefone;
  }

  informarNovoTelefone($event){
    this.contato.telefone = null;
    this.validarCodigo = false;
    if(this.timerSubscription)
      this.timerSubscription.unsubscribe();

    if($event) $event.stopPropagation();
    if($event) $event.preventDefault();

    return false;
  }

  startTimer(): void {
    const source = interval(1000); // Emitir um valor a cada segundo
    this.countdownSeconds = 60 * 5;
    this.timerSubscription = source.subscribe(() => {
      if (this.countdownSeconds > 0) {
        this.countdownSeconds--;
        this.updateFormattedTime();
      } else {
        this.timerSubscription.unsubscribe();
      }
    });
  }


  gereCodigoValidacaoWhatsapp(){
    delete this.erro;
    if(this.frm.valid  ){
      this.processando  = true;
      this.autorizacao.gereCodigoValidacao(this.contato).then((reposta: any) => {
        this.processando = false;
        this.validarCodigo = true;
        this.startTimer();
        this.contato.id = reposta.id;
        this.frm.resetForm({ telefone: this.contato.telefone})
      }).catch( erro => {
        this.processando = false;
        this.erro = erro;
      })
    }

  }

  alterouCodigo(codigo) {
    if(this.frm.controls.codigo.valid){
      delete this.erro;
      this.processando = true;
      this.autorizacao.valideContatoWhatsapp(this.contato, codigo).then( (contato: any) => {
        this.codigoValido = true;
        this.autorizacao.salveUsuario(contato);

        let pedido = this.carrinhoService.obtenhaPedido();

        pedido.contato = contato;

        this.carrinhoService.salvePedido(pedido)

        if(!this.modal){
          setTimeout(() => {
            this.dominiosService.navegueParaUrl(this.retorno)
          }, 1500)
        } else {
          this.window.close({login: true});
        }


      }).catch(erro => {
        this.processando = false;
        this.erro = erro;
      })
    }
  }

  falarComLoja($event: any){
   // await this.autorizacao.canceleCodigoValidacao(this.contato);
    let url = 'http://wa.me/55' + this.empresa.numeroWhatsapp?.whatsapp + '?text=' + this.encodedText;

    window.open(url);
  }

  updateFormattedTime(): void {
    const minutes = Math.floor(this.countdownSeconds / 60);
    const seconds = this.countdownSeconds % 60;

    // Formatar os minutos e segundos com zero à esquerda, se necessário
    const minutesStr = minutes.toString().padStart(2, '0');
    const secondsStr = seconds.toString().padStart(2, '0');

    // Atualizar o tempo formatado
    this.formattedTime = `${minutesStr}:${secondsStr}`;
  }

  onPhoneMaskChange($event: string) {
    this.phoneMask = $event;
    if(this.txtTelefone)
      (this.txtTelefone as any).updateValue(this.txtTelefone.input.nativeElement.value);
  }

  onCountrySelected($event: any) {
    this.contato.codigoPais = $event;
  }

  fecheTela(){
    this.window.close();
  }

  deveExibirTopo() {
    return false;
  }

  deveExibirMenu(){
    return true;
  }

  deveTerBordas() {
    return true;
  }

  deveExibirBannerTema() {
    return false;
  }

  ngOnDestroy(): void {
    // Limpar a assinatura do timer ao destruir o componente
    if(this.timerSubscription)
      this.timerSubscription.unsubscribe();
  }

}
