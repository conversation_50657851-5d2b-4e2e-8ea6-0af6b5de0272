<app-header-loja [titulo]="'Confirmar <PERSON>'"></app-header-loja>

<form [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"
      novalidate #frm="ngForm" (ngSubmit)="confirmarContato()" class="mt-3"   >

  <div class="form-group mb-2 col">
    <label for="telefone" class="label-disabled"> Telefone</label>
    <div class="input-group ">
      <input kendoTextBox  id="telefone" name="telefone"   mask="(00)0-0000-0000"

             type="text" class="form-control" autocomplete="off"   [disabled]="true"
             [(ngModel)]="contato.telefone"  placeholder="Telefone com DDD"  required/>

    </div>

  </div>

  <div class="form-group mb-2 col">
    <label for="nome">Nome </label>
    <input kendoTextBox id="nome" name="nome"  placeholder="Nome completo"   nomeCompleto nomeValido
           class="form-control"   #nome="ngModel" appAutoFocus
           [(ngModel)]="contato.nome" required/>

    <div class="invalid-feedback">
      <p *ngIf="nome.errors?.required">Nome é obrigatório</p>
      <p *ngIf="nome.errors?.nomeCompleto">Informe seu nome completo</p>
      <p *ngIf="nome.errors?.nomeValido">Informe um nome válido</p>
    </div>
  </div>

  <div class="form-group mb-2 col">
    <label for="email">E-mail </label>
    <input type="email" class="form-control" autocomplete="off"  required

           id="email" name="email"  [(ngModel)]="contato.email" #email="ngModel"
           placeholder="Email do usuário"   >
    <div class="invalid-feedback">
      <p *ngIf="email.errors?.required" >Email é obrigatório</p>
    </div>
  </div>

  <div class="form-group mb-2 col"  >
    <label for="senha">Senha</label>
    <label   class="col-form-label float-right cpointer text-blue" (click)="exibirSenhaTela()">
      <i class="fa fa-eye fa-lg   " *ngIf="!exibirSenha"></i>
      <i class="fa fa-eye-slash fa-lg   " *ngIf="exibirSenha"></i>
    </label>
    <span *ngIf="!exibirSenha">
            <input class="form-control" type="password"  id="senha" name="senha"  #senha="ngModel"
                   [(ngModel)]="contato.senha" placeholder="Informe sua senha"    required >
            <div  class="invalid-feedback">
              <p  *ngIf="senha.errors?.required">Senha é obrigatório</p>
            </div>
     </span>

    <span *ngIf="exibirSenha">
          <input class="form-control" type="text"  id="senhaTexto" name="senhaTexto"  #senhaTexto="ngModel"
                 [(ngModel)]="contato.senha" placeholder="Informe sua senha" required>
          <div  class="invalid-feedback">
            <p  *ngIf="senhaTexto.errors?.required">Senha é obrigatório</p>
          </div>
     </span>

  </div>
  <div class="text-danger mt-2 mb-2  text-center" role="alert" *ngIf="erro"  >
    <b> {{erro}}</b>
  </div>
  <div class="mt-3 mb-3 col"   >
    <button class="btn btn-blue btn-block "  type="submit"  [disabled]="aguardeProcessar">
      <i class="k-icon k-i-loading mr-1" *ngIf="aguardeProcessar"></i>
      Confirmar Cadastro </button>
  </div>


</form>

