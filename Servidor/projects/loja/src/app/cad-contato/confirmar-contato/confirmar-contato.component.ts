import {Component, OnInit, ViewChild} from '@angular/core';
import {NgForm} from "@angular/forms";
import {ActivatedRoute, Router} from "@angular/router";
import {DominiosService} from "../../../services/dominios.service";
import {AutorizacaoLojaService} from "../../../services/autorizacao-loja.service";
import {ITela} from "../../../objeto/ITela";

@Component({
  selector: 'app-confirmar-contato',
  templateUrl: './confirmar-contato.component.html',
  styleUrls: ['./confirmar-contato.component.scss']
})
export class ConfirmarContatoComponent implements OnInit , ITela  {
  @ViewChild('frm')  frm: NgForm;
  contato: any = {};
  erro: string;
  exibirSenha: boolean;
  aguardeProcessar: boolean;
  token: any;
  constructor(private router: Router,  private dominiosService: DominiosService,
              private  activatedRoute: ActivatedRoute,
              private autorizacao: AutorizacaoLojaService) { }

  ngOnInit(): void {
    this.contato =  window.history.state.contato || {}
    this.token = this.activatedRoute.snapshot.params.token;

    if(!this.contato.id){
      this.autorizacao.obtenhaDadosToken(    this.token ).then( (resposta) => {
        this.contato = resposta.contato;
      }).catch(erro => {
        this.erro = erro;
      })
    }
  }

  confirmarContato(){
    delete this.erro;
    if(this.frm.valid  ){
      this.aguardeProcessar = true;
      this.autorizacao.confirmeConta(this.token, this.contato).then(reposta => {
        this.aguardeProcessar = false;
        this.dominiosService.navegueParaUrl('login', { email: this.contato.email})

      }).catch( erro => {
        this.aguardeProcessar = false;
        this.erro = erro;
      })

    }
  }


  exibirSenhaTela() {
    this.exibirSenha = ! this.exibirSenha ;
  }


  deveExibirTopo() {
    return false;
  }

  deveExibirMenu(){
    return true;
  }

  deveTerBordas() {
    return true;
  }

  deveExibirBannerTema() {
    return false;
  }
}
