import {Component, EventEmitter, OnInit, Output, ViewChild} from '@angular/core';
import {NgForm} from "@angular/forms";
import {ActivatedRoute, Router} from "@angular/router";
import {AutorizacaoLojaService} from "../../services/autorizacao-loja.service";
import {DominiosService} from "../../services/dominios.service";
import {ITela} from "../../objeto/ITela";
import {ConstantsService} from "../../services/ConstantsService";
import {ClienteService} from "../../services/cliente.service";
import {HeaderLojaComponent} from "../topo-menu/header-loja.component";
import {DialogRef, DialogService} from "@progress/kendo-angular-dialog";
import {PopupUtils} from "../../objeto/PopupUtils";
import {PainelLoginComponent} from "../painel-login/painel-login.component";
import {Location} from "@angular/common";

@Component({
  selector: 'app-cad-usuario',
  templateUrl: './cad-contato-loja.component.html',
  styleUrls: ['./cad-contato-loja.component.scss']
})
export class CadContatoLojaComponent implements OnInit, ITela {
  @ViewChild('frm')  frm: NgForm;
  @ViewChild('frmt')  frmt: NgForm;
  @ViewChild('headerLoja')  headerLoja: HeaderLojaComponent;
  @Output() cadastrou = new EventEmitter();
  contato: any = {};
  exibirSenha: boolean;
  aguardeProcessar: any;
  buscando: any;
  erro: string;
  validouTelefone = false;
  novoContato: boolean;
  urlDestino: string
  informarCpf: boolean;
  cpfObrigatorio: boolean;
  informarDataNascimento: boolean;
  dataNascimentoObrigatorio: boolean;
  fidelidadeExterna: any;
  empresa: any
  exibirLogin
  window: any
  abriuComoModal = false;

  static abraComoPopup(router,  location, activatedRoute, dialogService: DialogService,
                       contato: any = null, onClose: any = null){
    let dimensao = PopupUtils.calculeAlturaLargura(false)

    const windowRef: DialogRef = dialogService.open({
      title: null,
      content: CadContatoLojaComponent,
      minWidth: 300,
      width: dimensao.largura,
      height: dimensao.altura
    });

    let tela: CadContatoLojaComponent = windowRef.content.instance;

    tela.setModal(windowRef, contato);

    PopupUtils.abraJanela(router,  location, activatedRoute, windowRef, {}, onClose);

  }
  constructor(private router: Router,  private route: ActivatedRoute,
              private clienteService: ClienteService,
              private activatedRoute: ActivatedRoute,      private _location: Location,
              private dialogService: DialogService,
              private dominiosService: DominiosService, private constantsService: ConstantsService,
              private autorizacao: AutorizacaoLojaService) { }

  ngOnInit(): void {
    this.validouTelefone = false;
    this.novoContato = false;
    this.urlDestino =    this.dominiosService.obtenhaRaizCardapio();
    if(this.route.snapshot.queryParams['t']) {
      this.urlDestino = this.route.snapshot.queryParams['t'];
       setTimeout(() => {
         this.headerLoja.retorno = this.urlDestino
       })
    }

    this.constantsService.campoCpf$.subscribe( campoCpf => {
      this.informarCpf = campoCpf != null;
      this.cpfObrigatorio = campoCpf && !campoCpf.opcional;
    });

    this.constantsService.campoDataNascimento$.subscribe( campoDataNascimento => {
      this.informarDataNascimento = campoDataNascimento != null;
      this.dataNascimentoObrigatorio = campoDataNascimento && !campoDataNascimento.opcional;
    });


    this.constantsService.empresa$.subscribe( empresa => {
      if(!empresa) return;
      this.empresa = empresa;
      if( this.empresa.integracaoFidelidade){
        this.fidelidadeExterna = Object.assign({},  this.empresa.integracaoFidelidade);
        this.fidelidadeExterna.optin = true;

        if(window.history.state && window.history.state.contato)
          this.contato = window.history.state.contato;

        if(this.contato.fazerOptinUsarSaldo) this.contato.fazerOptin = true;

        if(this.contato.id && this.contato.fazerOptin){
          this.validouTelefone = true;
          this.fidelidadeExterna.aceitarFidelidade = true;
        }   else {
          this.verifiqueContatoPeloTelefone()
        }

      } else {
        if(this.contato.atualizarCadastro)
          this.verifiqueContatoPeloTelefone()
      }
    })
  }

  submitForm() {
    delete this.erro;
    if(this.frm.valid && this.validouTelefone){
      this.aguardeProcessar = true;
      if(this.fidelidadeExterna && this.fidelidadeExterna.optin){
        this.contato.adicionarFidelidadeGcom = true;
        this.contato.idCliente = this.fidelidadeExterna.id_cliente;
      }

      if(this.contato.fazerOptin) {
        this.facaOptinFidelidade();
      } else if(this.contato.atualizarCadastro){
        this.atualizeCadastro();
      } else {
        this.crieConta();
      }
    }
  }

  crieConta(){
    this.autorizacao.crieConta(this.contato).then(reposta => {
      this.autorizacao.login(this.contato.telefone, this.contato.senha).then(() => {
        this.aguardeProcessar = false;
        this.finalizouSubmit();
      })
    }).catch( erro => {
      this.aguardeProcessar = false;
      this.erro = erro;
    })
  }

  atualizeCadastro(){
    this.autorizacao.atualizeConta(this.contato).then( async reposta => {
      this.finalizouSubmit();
    }).catch( erro => {
      this.aguardeProcessar = false;
      this.erro = erro;
    })
  }

  facaOptinFidelidade(){
    if(!this.fidelidadeExterna.optin){
      this.erro = 'Marque e opção "Quero ganhar cashback"'
      this.aguardeProcessar = false;
      return;
    }

    this.clienteService.facaOptinFidelidade(this.contato).then(reposta => {
        localStorage.recarregarSaldo =  true;
        this.finalizouSubmit();
    }).catch( erro => {
      this.aguardeProcessar = false;
      this.erro = erro;
    })
  }

  finalizouSubmit(){
    this.aguardeProcessar = false;
    if(!this.abriuComoModal){
      this.router.navigateByUrl(this.urlDestino)
    } else {
      this.fecheTela();
    }
  }

  verifiqueContatoPeloTelefone(){
    if(!this.contato.telefone) return;

    this.aguardeProcessar = true;
    this.exibirLogin = false;

    this.autorizacao.verifiqueContato(this.contato.telefone).then( resposta => {
      this.aguardeProcessar = false;
      if(resposta.tokenValidacao){
        this.fecheTela();
        this.dominiosService.navegueParaUrl('cadastro/validar/' + resposta.tokenValidacao)
      } else if (resposta.existente){
        this.exibirLogin = true;
        this.erro = 'Já existe um usuário com esse numero de telefone';
      } else {
        if( resposta.id){
          this.contato.id =  resposta.id;
          this.contato.nome =  resposta.nome;
          this.contato.cpf =  resposta.cpf;
          this.contato.email =  resposta.email;
        }

        this.validouTelefone = true;
        if(resposta.fidelidadeExterna){
          if(resposta.fidelidadeExterna.aceitarFidelidade){
            this.fidelidadeExterna.aceitarFidelidade = true;
            this.fidelidadeExterna.optin = true;
            this.fidelidadeExterna.id_cliente = resposta.fidelidadeExterna.id_cliente;
          } else {
            this.fidelidadeExterna.aceitarFidelidade = false;
            this.fidelidadeExterna.optin = false;
          }
        }
      }
    }).catch(erro => {
      this.aguardeProcessar = false;
      this.erro = erro;
    })
  }

  validarTelefone(frm) {
    if(this.aguardeProcessar) return;

    delete  this.erro;
    if(this.contato.id){
      this.validouTelefone = false;
      this.contato = { telefone: this.contato.telefone}
    }

    if(frm.controls.telefone.valid)
       this.verifiqueContatoPeloTelefone();


  }

  vaParaLogin() {
    if(!this.abriuComoModal){
      this.dominiosService.vaParaLogin(this.urlDestino, this.contato.telefone);
    } else {
      this.fecheTela();
      PainelLoginComponent.abraComoPopup(this.router, this._location, this.activatedRoute,  this.dialogService );
    }

    return false;
  }

  exibirSenhaTela() {
    this.exibirSenha = ! this.exibirSenha ;
  }

  alterouTelefone() {
    if(this.frm.controls.telefone.invalid){
      this.validouTelefone = false;
    }
  }

  async informouCpf(){
    if(this.contato.id && !this.contato.completarCadastro) return;

    this.buscando = true;
    let resposta: any = await this.clienteService.obtenhaCadastroCompletarPeloCpf(this.contato.cpf).catch((e) => {
      console.error(e);
    });

    this.buscando = false;
    if (resposta && resposta.id !== this.contato.id)
      this.erro = 'CPF já está em uso'
  }

  deveExibirTopo() {
    return false;
  }

  deveExibirMenu(){
    return true;
  }

  deveTerBordas() {
    return true;
  }

  deveExibirBannerTema() {
    return false;
  }

  setModal(window, contato: any = null){
    this.window = window;
    this.abriuComoModal = true;
    if(contato)  this.contato = contato;
  }

  fecheTela() {
    if(this.window)  this.window.close(this.contato);
  }

}
