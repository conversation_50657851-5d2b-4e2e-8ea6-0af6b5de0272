import {Component, HostListener, OnInit, ViewChild} from '@angular/core';
import {SiteProdutoComponent} from "../../site-produto/site-produto.component";
import {SiteMontarpizzaComponent} from "../../site-montarpizza/site-montarpizza.component";
import {ActivatedRoute, Router} from "@angular/router";
import {DomSanitizer} from "@angular/platform-browser";
import {ClienteService} from "../../services/cliente.service";
import {ProdutoService} from "../../services/produto.service";
import {CarrinhoService} from "../../services/carrinho.service";
import {AutorizacaoLojaService} from "../../services/autorizacao-loja.service";
import {SharedDataService} from "../../services/shared.data.service";
import {ConstantsService} from "../../services/ConstantsService";
import {DominiosService} from "../../services/dominios.service";
import {MyDetectorDevice} from "../shared/MyDetectorDevice";
import {DialogRef, DialogService} from "@progress/kendo-angular-dialog";
import {Location} from "@angular/common";
import {BannerService} from "../../services/banner.service";
import {HttpClient} from "@angular/common/http";
import {catchError, map} from "rxjs/operators";
import {Observable, of, Subscription} from "rxjs";
import {PedidoLoja} from "../../objeto/PedidoLoja";
import {ITela} from "../../objeto/ITela";

declare var _;
declare var $;

@Component({
  selector: 'app-tela-multi-loja',
  templateUrl: './tela-multi-loja.component.html',
  styleUrls: ['./tela-multi-loja.component.scss']
})
export class TelaMultiLojaComponent implements OnInit, ITela {
  @ViewChild('tabs', {static: false}) tabs: any;
  @ViewChild('divProdutos', {static: false}) divProdutos: any;

  carregando: boolean;
  categorias: any  = [];
  empresas: any = [];
  produtosPorCategoria: any = {}

  nomeCategoriaDestaque = 'DESTAQUES';
  categoriaDestaque = {
    id: 2,
    nome: this.nomeCategoriaDestaque
  };
  window: DialogRef;
  isMobile: any;
  movendo: boolean;
  larguraProdutos: number;
  qtdeMaxima = 10;
  empresa: any
  exibirPrecos = true;
  layoutHorinzontal: boolean;
  exibirMenuMobile = false;
  codigopromo: any;
  codigoProduto: any;
  hashMesa: string;
  nomePagina: string;
  alturaBanner = 230;
  apiLoaded: Observable<boolean>;
  mesaExpirada: boolean;
  idTamanho: any;
  pedido: PedidoLoja;
  indiceProduto: any;
  exibirMegaMenu: boolean
  categoriasMegamenu: any;
  colunasMenu: any = {};
  vitrines = [];
  produtos = [];

  categoriaMonteSuaPizza = {
    id: 1,  nome: ''
  };
  destaques =  [];
  assinatura: Subscription;
  estaFazendoScroll: boolean;
  isDesktop = false;

  constructor(private router: Router, private sanitizer: DomSanitizer, private clienteService: ClienteService,
              private produtoService: ProdutoService, private carrinhoService: CarrinhoService,
              private autorizacaoLojaService: AutorizacaoLojaService, public sharedDataService: SharedDataService,
              private activatedRoute: ActivatedRoute, private constantsService: ConstantsService,
              private dominiosService: DominiosService, private detectorDevice: MyDetectorDevice,
              private dialogService: DialogService, private location: Location, private bannerService: BannerService,
              private httpClient: HttpClient) {
    this.nomePagina = dominiosService.obtenhaRaizCardapio();
    this.isMobile = this.detectorDevice.isMobile();
    this.hashMesa = this.activatedRoute.snapshot.params.hm;
    this.codigopromo = this.activatedRoute.snapshot.params.codigopromo;
    this.dominiosService.hashMesa = this.hashMesa

    if( this.isMobile ) {
      this.alturaBanner = 100;
    }

    this.apiLoaded = this.httpClient.jsonp('https://maps.googleapis.com/maps/api/js?' +
      'key=AIzaSyBlPtg98e0hBo0JJSYMB3R7iakhFe_iT4o&callback=initMap&libraries=&v=weekly', 'callback').pipe(
      map(() => {
        console.log('chamando');
        return true
      }),
      catchError((error) => {
        console.log(error);
        return of(false);
      }),
    );

    this.inicializePedido().then(() => {
      this.activatedRoute.queryParams.subscribe(params => {

        if( params['nl']  )
          this.autorizacaoLojaService.atualizeUsuarioLogado().then(  () => {});

        if( params['c'] ) {
          this.codigoProduto = parseInt(params['c'], 10);
          if( isNaN(this.codigoProduto) )
            this.codigoProduto = null;
        }

        if( params['t'] ) {
          this.idTamanho = parseInt(params['t'], 10);
          if( isNaN(this.idTamanho) )
            this.idTamanho = null;
        }
        if( params['e'] ) {

          this.indiceProduto = parseInt(params['e'], 10);

          if( isNaN(this.indiceProduto) ) {
            this.indiceProduto = null;
          } else {
            this.indiceProduto = this.indiceProduto + '';
          }
        }
      });

      if (this.codigopromo)
        this.carrinhoService.atualizeValorDesconto(this.codigopromo, this.empresa, true).then( () => {});
    });

    this.sharedDataService.exibirMenu.subscribe(exibir => {
      this.exibirMenuMobile = exibir;
    });
  }


  ngOnInit() {
    this.carregando = true;
    this.exibirMegaMenu = false;
    this.layoutHorinzontal = false;

    const  tipoCardapio = (this.hashMesa || (this.pedido && this.pedido.mesa )) ? 'MESA' : 'DELIVERY';

    if(  this.exibirMegaMenu ){
      this.produtoService.listeCategoriasMegamenu().then( (categoriasMegamenu) => {
        this.categoriasMegamenu = categoriasMegamenu;
        this.setColunasMegaMenu();
      }).catch(erro => {
        console.log(erro)
      })
    }

    this.vitrines = [];

    if(this.vitrines.length &&  this.exibirMegaMenu ) {
      this.carregando = false;
      this.definaCategoriasFixas();
      return;
    }

    this.produtoService.listeAhVendaGrupo(tipoCardapio, this.layoutHorinzontal).subscribe( (empresas: any) => {
      this.empresas = empresas;
      for( let empresaCorrente of this.empresas ) {
        this.produtoService.listeAhVenda(empresaCorrente.empresa, this.obtenhaTipoCardapio()).subscribe( (resp) => {
          console.log(resp);
        });
      }
      this.carregando = false;
      //this.produtos = produtos;
      //this.constantsService.produtosLoja = produtos;

      /*
      if( this.layoutHorinzontal ) {
        for (let i = 0; i < this.produtos.length; i++) {
          const produto = this.produtos[i];
          produto.destacado = true;
        }
      }

      if(this.produtos && this.produtos.length > 0) {
        this.produtos.sort((produtoA: any, produtoB: any) => {
          if(produtoA.ordem == null || produtoB.ordem == null) return 0;

          return produtoA.ordem - produtoB.ordem
        })
      }
      this.produtosPorCategoria = _.groupBy(this.produtos, produto => produto.categoria ? produto.categoria.nome.trim() : 'Outros')

      const mapCategoriasPorId = {};

      this.produtos.map(produto => {
        if( produto.categoria ) {
          produto.categoria.nome = produto.categoria.nome.trim(); ///
          mapCategoriasPorId[produto.categoria.id] = produto.categoria;
        }
      });

      this.categorias = Object.values(mapCategoriasPorId).sort( (a: any, b: any) => a.posicao - b.posicao);

      this.definaCategoriasFixas();

      this.carregando = false;

      if( this.codigoProduto ) {
        const produto = this.obtenhaProduto(this.codigoProduto);
        this.abraDetalhesProduto(produto, this.indiceProduto);
      } else if (this.idTamanho){
        let produtoCategoria =
          this.produtosPorCategoria[this.categoriaMonteSuaPizza.nome].find( produtoCategogia => produtoCategogia.id === this.idTamanho);

        this.abraDetalhesProduto(produtoCategoria, this.indiceProduto);
      }

      this.assinatura = this.activatedRoute.params.subscribe( (params2) => {
        const idCategoria = params2.idc;

        const categoria = mapCategoriasPorId[idCategoria];

        if( !categoria ) {
          return;
        }
        setTimeout( () => {
          this.selecionou(categoria);
          this.assinatura.unsubscribe();
        }, 1000);
      });
       */
    });
  }

  obtenhaTipoCardapio(){
    return (this.hashMesa || (this.pedido && this.pedido.mesa )) ? 'MESA' : 'DELIVERY' ;
  }

  selecionou(categoria) {
    const idTab = 'tab_' + categoria.nome;
    const nomeCategoria = categoria.nome;

    this.posicioneTab(idTab);

    this.posicioneConteudo(nomeCategoria);

    this.tabs.select(idTab);

    /* Removed unsupported properties by Angular migration: relativeTo, queryParams, queryParamsHandling. */
    this.router.navigateByUrl('/categoria/' + categoria.nome.toLowerCase() + '/' + categoria.id,
      {
        replaceUrl: false }).then( () => {});
  }

  private definaCategoriasFixas() {
    let pizzasMontar = this.produtos.filter( produto => produto.template != null && produto.template.montarPizza);

    if(pizzasMontar.length > 0){

      this.categorias.splice(0, 0,  this.categoriaMonteSuaPizza);
      this.produtosPorCategoria[this.categoriaMonteSuaPizza.nome] = []

      pizzasMontar.forEach( (produtoPizza: any) => {
        produtoPizza.tamanhos.forEach( (tamanhoPizza: any) => {

          if(tamanhoPizza.qtdeSabores > 1){

            let nome = String(`${produtoPizza.template.nome} ${tamanhoPizza.descricao} com até  ${tamanhoPizza.qtdeSabores} sabores`);
            let preco = tamanhoPizza.novoPreco ? tamanhoPizza.novoPreco : tamanhoPizza.preco;

            let produtoCategoria =
              this.produtosPorCategoria[this.categoriaMonteSuaPizza.nome].find(
                produtoCategogia => produtoCategogia.id === tamanhoPizza.template.id);

            if(!produtoCategoria){
              let descricao = String(`${produtoPizza.template.nome} ${tamanhoPizza.descricao}`);

              if(tamanhoPizza.qtdeSabores > 1) descricao = String(`${descricao}  com até  ${tamanhoPizza.qtdeSabores} sabores`)

              produtoCategoria =  {
                id: tamanhoPizza.template.id, nome: nome,  montar: true,
                descricao: descricao,
                template: produtoPizza.template, tamanho: tamanhoPizza, valorMinimo: preco,
                sabores: [], camposAdicionais: produtoPizza.camposAdicionais, tamanhos: [],
              }
              this.produtosPorCategoria[this.categoriaMonteSuaPizza.nome].push( produtoCategoria);
            }

            produtoCategoria.sabores.push(produtoPizza);

            if(produtoCategoria.preco < preco)
              produtoCategoria.valorMinimo = preco
          }


        })
      })

    }
    this.destaques = [];

    this.produtos.forEach( (produto: any) => {
      if(produto.destaque) this.destaques.push(Object.assign({}, produto))

      if(produto.tamanhos && produto.tamanhos.length){
        if(produto.tamanhos.find( tamanho => tamanho.destaque) != null)
          this.destaques.push(Object.assign({}, produto))
      }
    })

    this.destaques.forEach( produto => produto.destacado = true);

    if( this.destaques.length) {
      this.categorias.splice(0, 0,  this.categoriaDestaque);
      this.produtosPorCategoria[this.categoriaDestaque.nome] =  this.destaques;
    }

    for(let i = this.vitrines.length - 1; i >= 0 ; i--){
      let vitrine: any =  this.vitrines[i];
      this.categorias.splice(0, 0,   { nome: vitrine.nome, id: vitrine.id, vitrine: true });
      let produtosVitrine = vitrine.produtosNaVitrine.map((pnv: any) => pnv.produto);
      produtosVitrine.forEach( produto => produto.destacado = true);
      this.produtosPorCategoria[vitrine.nome] = produtosVitrine;
    }
  }

  abraDetalhesProduto(empresaDoGrupo: any, produto: any, indiceProduto: any) {
    if(window['multipedido']){
      this.router.navigateByUrl('/marca/' + empresaDoGrupo.empresa.dominio + '?c=' + produto.id);
    } else {
      const url = this.calculeUrl(empresaDoGrupo);
      window.location.href = url + '?c=' + produto.id;
      /*
        if(!produto.montar){
          this.window = SiteProdutoComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
            this.dialogService, this.isMobile, produto, indiceProduto);
        } else {
          this.window =  SiteMontarpizzaComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
            this.dialogService, this.isMobile, produto, indiceProduto);
        }
   */
    }
  }

  moverScrollDireita(scroll: HTMLDivElement, categoria: HTMLDivElement) {
    if( this.movendo ) return;

    const posicao = scroll.scrollLeft;

    let largura = categoria.clientWidth;

    if( this.isMobile ) {
      largura = this.larguraProdutos + 10;
    }

    const novaPosicao = posicao + largura;

    this.movendo = true;
    scroll.scroll({
      left: novaPosicao,
      behavior: 'smooth'
    });

    categoria.classList.add('moveu');

    setTimeout( () => {
      this.movendo = false;

      const scrollWidth = scroll.scrollWidth;
      if (scrollWidth - largura === novaPosicao) {
        categoria.classList.add('scroll_fim');
      }
    }, 500);
  }

  moverScrollEsquerda(scroll: HTMLDivElement, categoria: HTMLDivElement) {
    if( this.movendo ) return;

    categoria.classList.remove('scroll_fim');

    const posicao = scroll.scrollLeft;

    let largura = categoria.clientWidth;

    if( this.isMobile ) {
      largura = this.larguraProdutos + 10;
    }


    let novaPosicao = posicao - largura;
    if( novaPosicao < 0 ) {
      novaPosicao = 0;
    }

    this.movendo = true;
    scroll.scroll({
      left: novaPosicao,
      behavior: 'smooth'
    });

    setTimeout( () => {
      if( novaPosicao === 0 ) {
        categoria.classList.remove('moveu');
      }
      this.movendo = false;
    }, 500);
  }

  abraTelaCardapio(empresaDoGrupo) {
    const url = this.calculeUrl(empresaDoGrupo);

    window.location.href = url;

    return false;
  }

  exibirUnidade(produto: any) {
    return produto.tipoDeVenda && produto.tipoDeVenda === 'Peso'
  }

  private inicializePedido() {
    this.carrinhoService.setContexto(window['multipedido']);
    return new Promise((resolve) => {
      if(this.hashMesa) {
        this.carrinhoService.obtenhaMesa(this.hashMesa).then((mesa: any) => {
          if(!mesa){
            this.mesaExpirada = true;
          } else {
            mesa.hash  = this.hashMesa;
            this.pedido = this.carrinhoService.obtenhaPedido(mesa)
            resolve(null);
          }
        })
      } else {
        this.pedido = this.carrinhoService.obtenhaPedido();
        resolve(null);
      }


    })
  }

  private setColunasMegaMenu() {

    for(let categoria of this.categoriasMegamenu){
      this.colunasMenu[categoria.id] = {
        coluna1: { total: 0, subcategorias: []},
        coluna2: { total: 0, subcategorias: []},
        coluna3: { total: 0, subcategorias: []}
      }

      for(let i = 0 ; i <  categoria.subcategorias.length; i++){
        let item: any = this.colunasMenu[categoria.id];

        let coluna: any = this.obtenhaMaisVazia(item.coluna1, item.coluna2, item.coluna3)
        let subcategoria = categoria.subcategorias[i];
        coluna.subcategorias.push(subcategoria)

        let total = 1 + subcategoria.subcategorias.length;

        coluna.total += total; //so exibi 8 itens da subcategoria na tela
      }
    }
  }

  obtenhaProduto(id: number) {
    return this.produtos.find( produto => produto.id === id)
  }

  private obtenhaMaisVazia(categoriasColuna1: any, categoriasColuna2: any, categoriasColuna3: any){
    if(categoriasColuna1.total <= categoriasColuna2.total &&  categoriasColuna1.total <= categoriasColuna3.total )
      return categoriasColuna1;

    if(categoriasColuna2.total <= categoriasColuna3.total)
      return categoriasColuna2;


    return categoriasColuna3;
  }

  posicioneTab(categoria) {
    categoria = categoria.trim();
    const tab = document.getElementById(categoria);

    const $tabs = $("#tabs.nav-tabs");
    const posicao = tab.offsetLeft - ($tabs.width() / 2) + (tab.getBoundingClientRect().width / 2);

    $tabs.animate({scrollLeft: posicao}, 100);
  }

  getOffset(el) {
    const rect = el.getBoundingClientRect();
    return {
      left: rect.left + window.scrollX,
      top: rect.top + window.scrollY
    };
  }

  @HostListener('window:scroll', ['$event']) onScrollEvent($event){
    if( this.estaFazendoScroll ) {
      return;
    }

    const tabs = document.querySelectorAll('.categoria');

    for( let i = tabs.length - 1; i >= 0; i-- ) {
      const tab: any = tabs[i];

      const topElemento = this.getOffset(tab).top;
      const posicaoY = topElemento - document.querySelector('.header').clientHeight - 50;

      if( window.scrollY >= posicaoY ) {
        const idTab = 'tab_' + tab.innerText;

        if( this.tabs && this.tabs.activeId !== idTab ) {
          this.tabs.select(idTab);
          this.posicioneTab('tab_' + tab.innerText);
        }
        break;
      }
    }
  }

  posicioneConteudo(categoria) {
    categoria = categoria.trim();
    const topElemento = this.getOffset(document.getElementById(categoria)).top;
    const y = topElemento - document.querySelector('.header').clientHeight - 20;

    this.estaFazendoScroll = true;

    let posicao = -1;

    const chequeScroll = () => {
      if( posicao !== window.scrollY ) {
        posicao = window.scrollY;

        setTimeout(chequeScroll, 100);
        return;
      }

      this.estaFazendoScroll = false;
    }

    setTimeout(chequeScroll, 100);

    window.scroll({
      top: y,
      behavior: 'smooth'
    });
  }

  deveExibirBannerTema() {
    return false;
  }

  deveExibirMenu() {
    return true;
  }

  deveExibirTopo() {
    return true;
  }

  deveTerBordas() {
    return true;
  }

  topoDeveSerReduzido() {
    return true;
  }


  abraCardapio(circulo: any, empresaDoGrupo: any, event = null) {
    let svg = null;
    if( circulo ) {
      svg = circulo.getElementsByTagName('svg')[0];
      svg.classList.add('svg_animacao');
    }

    setTimeout( () => {
      if (window['multipedido']) {
        const empresa = empresaDoGrupo.empresa;
        this.router.navigateByUrl('/marca/' + empresa.dominio);
      } else {
        const url = this.calculeUrl(empresaDoGrupo);

        window.location.href = url;
      }

      if( svg ) {
        setTimeout(() => {
          svg.classList.remove('svg_animacao');
        }, 500);
      }
    }, 300);

    // Impede que a ação padrão do link seja executada
    if (event)
      event.preventDefault();


  }

  calculeUrl(empresaDoGrupo: any) {
    const empresa = empresaDoGrupo.empresa;
    const urlDaEmpresa = empresa.urlDaEmpresa;

    if(urlDaEmpresa) return "https://" + urlDaEmpresa.hostname

    return this.obtenhaHostLoja(empresa);
  }

  obtenhaHostLoja(empresa: any) {
    return 'https://' + empresa.dominio + '.' +
      (empresa.tipoDeLoja === 'Catalogo' ? 'meucatalogo' : 'meucardapio') + '.ai'
  }
}
