<div class="row mt-2 flex-nowrap" [class.scrollx]="isMobile" style="margin: 0px;">
  <div *ngFor="let empresaDoGrupo of empresas" class="mb-3 col-auto col-empresa" #secaoCategoria style="position: relative;"
       [class.ecommerce]="true">

    <div #circulo class="circle" (click)="abraCardapio(circulo, empresaDoGrupo, $event)">
      <img src="/images/empresa/{{empresaDoGrupo.empresa.logo}}"/>

      <div class="div_label_empresa">
          <div class="label_empresa">{{empresaDoGrupo.empresa.nome}}</div>
      </div>
      <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg" style="enable-background:new -580 439 577.9 194;"
           xml:space="preserve">
        <circle cx="50" cy="50" r="40" />
      </svg>
    </div>
  </div>
</div>

<div class="tirar_margem" class="mt-3" #divProdutos>
  <div class="k-i-loading ml-1 mr-1 centralizado" *ngIf="carregando" style="font-size: 40px;height: 90px;" ></div>

  <div *ngFor="let empresaDoGrupo of empresas" class="mb-3 secaoCategoria" #secaoCategoria style="position: relative;"
       [class.ecommerce]="true">

    <img class="imagem_empresa2 mr-1 mb-1" src="/images/empresa/{{empresaDoGrupo.empresa.logo}}"/>
    <p [id]="'emp_' + empresaDoGrupo.empresa.id" class="categoria ml-2 font-weight-bold font-14">
      {{empresaDoGrupo.empresa.nome}}
    </p>

    <div class="ver_todos">
      <a href="" (click)="abraCardapio(null, empresaDoGrupo, $event)">Ver todos</a>
    </div>
    <div class="arrow esquerda" (click)="moverScrollEsquerda(divProdutosCategoria, secaoCategoria)">
      <i class="fas fa-chevron-left"></i>
    </div>
    <div class="arrow direita" (click)="moverScrollDireita(divProdutosCategoria, secaoCategoria)"
         [hidden]="empresaDoGrupo.produtos.length < 3">
      <i class="fas fa-chevron-right"></i>
    </div>

    <div class="produtos_categoria" #divProdutosCategoria [class.vitrine]="true">
      <div class="scroll_categoria">
        <ng-container *ngFor="let produto of empresaDoGrupo.produtos">
          <div *ngIf="produto.preco > 0 || produto.valorMinimo > 0" (click)="abraDetalhesProduto(empresaDoGrupo, produto, null)" class="produto pt-2 pb-2 destacado" [style.width.px]="larguraProdutos"
               [class.destacado]="true" >
            <div class="container_foto" *ngIf="((produto.imagemCodigoDeBarras)|| (produto.imagens && produto.imagens.length > 0))">
              <img *ngIf="!produto.imagemCodigoDeBarras" class="img img-fluid mb-2" [src]="'/images/empresa/' + produto.imagens[0].linkImagem" alt="Imagem"/>
              <img *ngIf="produto.imagemCodigoDeBarras" class="img img-fluid mb-2" [src]="'/images/produtos/' + (produto.imagemCodigoDeBarras.linkImagem ? produto.imagemCodigoDeBarras.linkImagem : 'carrinho-produtos.svg' )" alt="Imagem"/>
            </div>

            <div class="media">
              <div class="media-body pt-0 ml-1">
                <h5 class="mt-0 mb-1 font-14 nome-produto">{{produto.nome}}  </h5>
                <span class="text-muted descricao mb-1 font-13">{{produto.descricao}}</span>

                <div class="div_preco">
                  <ng-container *ngIf="!produto.indisponivel && produto.exibirPrecoNoCardapio">
                  <ng-container *ngIf="!produto.template?.exibirPrecosTamanhos ">
                    <span class=" text-muted font-12 mr-1"  *ngIf="produto.valorMinimo"><i>A partir de</i></span>
                    <h5 class="preco font-14 mb-0" *ngIf="exibirPrecos " >
                      {{ (produto.valorMinimo ? produto.valorMinimo : produto.preco) | currency: 'BRL'}}

                      <span class=" text-muted font-11"  *ngIf="exibirUnidade(produto)" > /  Kg </span>

                    </h5>

                    <h5 class="preco font-14 antigo mb-0"   *ngIf="produto.precoAntigo && exibirPrecos">
                      {{(produto.precoAntigo) | currency: 'BRL'}}
                    </h5>
                  </ng-container>

                  <ng-container *ngIf="produto.template?.exibirPrecosTamanhos ">
                    <div *ngFor="let tamanho of produto.tamanhos" class="mt-2">
                      {{tamanho.descricao}}

                      <span class=" text-muted font-12 mr-1"   *ngIf="tamanho.valorMinimo"><i>A partir de</i></span>

                      <h5 class="preco font-14 mt-0 mb-0" *ngIf="exibirPrecos " >
                        {{ (tamanho.novoPreco ? tamanho.novoPreco : tamanho.preco) | currency: 'BRL'}}

                      </h5>
                      <h5 class="preco font-14 antigo mb-0"   *ngIf="tamanho.precoAntigo && exibirPrecos">
                        {{(tamanho.precoAntigo) | currency: 'BRL'}}
                      </h5>
                    </div>

                  </ng-container>


                  <i *ngIf="produto.qtdMaxima"><label class="text-muted" style="font-size: 10px">*Até {{produto.qtdMaxima}} por pedido</label></i>
                </ng-container>
                </div>

                <h5 *ngIf="produto.indisponivel" class="text-danger">Indisponível!</h5>


              </div>
              <div style="width: 128px;" class="justify-content-center align-items-center ml-1"
                   *ngIf="((produto.imagemCodigoDeBarras)|| (produto.imagens && produto.imagens.length > 0)) && !produto.destacado && false">
                <img  *ngIf="!produto.imagemCodigoDeBarras" class="img img-fluid" [src]="'/images/empresa/' + produto.imagens[0].linkImagem" alt="Imagem" >
                <img *ngIf="produto.imagemCodigoDeBarras" class="img img-fluid" [src]="'/images/produtos/' + (produto.imagemCodigoDeBarras.linkImagem ? produto.imagemCodigoDeBarras.linkImagem : 'carrinho-produtos.svg' )" alt="Imagem"/>

              </div>
            </div>
          </div>
        </ng-container>

        <div (click)="abraTelaCardapio(empresaDoGrupo)" class="produto pt-2 pb-2" [style.width.px]="larguraProdutos"
             *ngIf="layoutHorinzontal" [hidden]="false">
          <div class="media">
            <div class="media-body pt-0 ml-1">
              <h5 class="preco font-16" style="margin: 85px 15px">Ver Mais</h5>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</div>
