import {Component, OnInit, ViewChild} from '@angular/core';
import {ITela} from "../../objeto/ITela";
import {PainelLoginComponent} from "../painel-login/painel-login.component";
import {Router} from "@angular/router";
import {DominiosService} from "../../services/dominios.service";
import {AutorizacaoLojaService} from "../../services/autorizacao-loja.service";
import {CarrinhoService} from "../../services/carrinho.service";
import {ConstantsService} from "../../services/ConstantsService";


@Component({
  selector: 'app-loja-perfil',
  templateUrl: './loja-perfil.component.html',
  styleUrls: ['./loja-perfil.component.scss']
})
export class LojaPerfilComponent implements  OnInit, ITela {
  @ViewChild('painelLogin') painelLogin: PainelLoginComponent;
  empresa: any = {}
  usuario: any = {};
  carregandoSaldo = false;
  constructor(private autorizacao: AutorizacaoLojaService , private carrinhoService: CarrinhoService,
              private router: Router, private dominiosService: DominiosService,
              private constantsService: ConstantsService) {
    this.usuario = autorizacao.getUsuario() || {}

    if(!this.usuario.telefone)
      this.dominiosService.vaParaLogin(location.pathname);

    if(localStorage.recarregarSaldo)
      this.recarregueSaldo(this.usuario);


    this.constantsService.empresa$.subscribe( (empresa) => {
      if (empresa) this.empresa = empresa;
    });


  }

  ngOnInit(): void {
  }

  deveExibirTopo() {
    return false;
  }

  deveExibirMenu(){
    return true;
  }

  facaLogout(event) {
    event.stopPropagation();
    event.preventDefault();

    this.autorizacao.logout().then( (erro) => {
      if(!erro){
        this.carrinhoService.limpeContatoPedido();
        this.dominiosService.vaParaHome();
      } else {
        alert(erro)
      }
    })

  }

  deveTerBordas() {
    return true;
  }

  deveExibirBannerTema() {
    return false;
  }

  async recarregueSaldo(usuario: any, event: any = null){
    if(event){
      event.stopPropagation();
      event.preventDefault();
    }

    this.carregandoSaldo = true;
    await this.autorizacao.atualizeSaldoFidelidade(usuario);
    this.carregandoSaldo = false;

    delete localStorage.recarregarSaldo


  }

  verDetalhesSaldo(usuario: any) {
    if(usuario.atualizarCadastro || usuario.fazerOptinUsarSaldo || usuario.fazerOptin){
      this.dominiosService.vaParaCadastro(window.location.pathname, { contato: usuario});
    } else {
      if(usuario.idCartao)
        window.open(String(`/cliente/${usuario.telefone}/extrato/${usuario.token}`))
    }

    return false;

  }

}
