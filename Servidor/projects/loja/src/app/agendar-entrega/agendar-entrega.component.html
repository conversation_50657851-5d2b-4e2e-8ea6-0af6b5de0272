
<div *ngIf="pedido.permiteAgendamento(pedido.entrega.formaDeEntrega, empresa.formasDeEntrega)">
  <div class="form-group mb-0 escolher " *ngIf="!apenasAgendamento">
    <label class="mt-2 k-checkbox-label"  >
      <input type="checkbox" id="agendarEntrega" name="agendarEntrega" class="k-checkbox " kendoCheckBox   [(ngModel)]="agendarEntrega"
      (ngModelChange)="clicouAgendarEntrega()" />
      Agendar {{getLabel()}}

    </label>
  </div>
  <div class="form-group mt-2" *ngIf="agendarEntrega">
    <label>Data   {{getLabel()}}: </label><br>
    <kendo-datepicker #dataEntrega='ngModel' [(ngModel)]="pedido.dataEntrega" [min]="diaMiminio" [max]="dataMaximaAgendamento"
                      class="form-control data_entrega mr-2 k-display-inline-block" id="dataEntrega" format="dd/MM/yy"
                      name="dataEntrega" style="width: 160px"  [disabledDates]="disabledDates"
                      [required]="agendarEntrega" (afterValueChanged)="alterouDataDeEntrega()"   >
      <kendo-datepicker-messages
        today="Hoje"
        toggle="Trocar calendário"
      ></kendo-datepicker-messages>

    </kendo-datepicker>

    <div style="width: 150px;display: inline-flex">
    <kendo-combobox id="horarioEntrega" name="horarioEntrega" [(ngModel)]="horarioAgendado"  [data]="horariosDisponiveis"
                    *ngIf="!pedido.naoInformarHorarioNoAgendamento(pedido.entrega.formaDeEntrega, empresa.formasDeEntrega)"
                    [required]="agendarEntrega"    [itemDisabled]="itemDisabled" textField="descricao" valueField="valor"
                    [allowCustom]="horariosDisponiveis.length == 0" [clearButton]="false"   placeholder="hh:mm" class="form-control"
                     (selectionChange)="alterouHorarioAgendado($event)"
                    [kendoDropDownFilter]="filterSettings"
                    [readonly]="!pedido.dataEntrega || naoFunciona || naoDaTempo ">
    </kendo-combobox>
    </div>

    <label *ngIf="naoFunciona" class="mt-2 text-danger">
      <i class="fas fa-exclamation-triangle"></i>
      Não fazemos entrega na data selecionada.</label>
    <label *ngIf="naoDaTempo" class="mt-2 text-danger">
      <i class="fas fa-exclamation-triangle"></i>
      Não há tempo hábil para fazermos a entrega na data selecionada.</label>

  </div>
</div>
