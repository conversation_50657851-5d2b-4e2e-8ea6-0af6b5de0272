import { ApplicationRef, Component, Input, OnChanges, OnInit} from '@angular/core';
import {PedidoLoja} from "../../objeto/PedidoLoja";
import {ControlContainer, NgForm} from "@angular/forms";
import {DropDownFilterSettings} from "@progress/kendo-angular-dropdowns";
import {ClienteService} from "../../services/cliente.service";

@Component({
  selector: 'app-agendar-entrega',
  templateUrl: './agendar-entrega.component.html',
  styleUrls: ['./agendar-entrega.component.scss'],
  viewProviders: [ { provide: ControlContainer, useExisting: NgForm } ]
})
export class AgendarEntregaComponent implements OnInit, OnChanges {
  @Input()
  pedido: PedidoLoja;

  @Input()
  empresa: any;

  @Input()
  formasDeEntrega: any;

  @Input()
  apenasAgendamento: boolean;

  @Input()
  tela: any;

  @Input()
  agendarEntrega: boolean;

  naoFunciona = false;
  naoDaTempo = false;

  horariosDisponiveis: string[] = [];
  public filterSettings: DropDownFilterSettings = {
    caseSensitive: false,
    operator: 'contains'
  };
  inicializouHorarios = false;
  dataMinimaAgendamento: Date;
  dataMaximaAgendamento: Date;
  horariosCheios = [];
  horarioAgendado: any;
  diaMiminio: Date;
  constructor( private clienteService: ClienteService,
               private app: ApplicationRef) {

   this.setDataMinima(new Date())
  }


  async ngOnInit() {
    await this.executeCarregamentoDeHorarios()
  }

  setDataMinima(data){
    this.dataMinimaAgendamento =  new Date(data)
    this.diaMiminio = new Date(data);
    this.setInicioDia(  this.diaMiminio);
  }

  setInicioDia(date: Date){
    date.setHours(0);
    date.setMinutes(0);
    date.setSeconds(0);
    date.setMilliseconds(0)
  }


  addHours(data: Date, h: number){
    data.setTime(data.getTime() + (h * 60 * 60 * 1000));

    return data;
  }

  getLabel(){
   return this.pedido.obtenhaLabelFormaSelecionada();
  }


  async ngOnChanges() {
    if(this.formasDeEntrega && this.formasDeEntrega.length)
      this.setDatasMinimaseMaximas();

    await this.executeCarregamentoDeHorarios()

  }

  private setDatasMinimaseMaximas() {
    let formaDeEntregaSelecionada: any = this.pedido.obtenhaFormaEscolhida(this.formasDeEntrega)

    if(!formaDeEntregaSelecionada) return;

    let agora = new Date();

    if(formaDeEntregaSelecionada.naoPerguntarHorario)
      this.setInicioDia(agora)

    if(formaDeEntregaSelecionada.agendamentoLimiteMinimo)
      this.setDataMinima( this.addHours( agora, formaDeEntregaSelecionada.agendamentoLimiteMinimo))

    if(formaDeEntregaSelecionada.agendamentoLimiteMaximo)
       this.dataMaximaAgendamento =  this.addHours( agora, formaDeEntregaSelecionada.agendamentoLimiteMaximo)

  }

  async alterouDataDeEntrega() {
    this.pedido.horarioEntrega = null;

    if(this.pedido.dataEntrega )
      await this.carregueHorariosDisponiveis();


    if(this.horarioAgendado)
      if(!this.horariosDisponiveis.find((item: any) => item.valor === this.horarioAgendado.valor ))
       this.horarioAgendado = null;
  }

  diferencaMaiorQueMinima(dataA, dataB, horas) {
    // Subtrai as duas datas para obter a diferença em milissegundos
    const diferencaEmMilissegundos = Math.abs(dataB - dataA);

    // Converte a diferença em milissegundos para horas
    const diferencaEmHoras = diferencaEmMilissegundos / (1000 * 60 * 60);

    // Verifica se a diferença em horas é maior que 72
    return diferencaEmHoras >= horas;
  }

  obtenhaTempoFuncionamentoMinutos(abertura, fechamento) {
    // Separamos horas e minutos
    const [aberturaHoras, aberturaMinutos] = abertura.split(':').map(Number);
    const [fechamentoHoras, fechamentoMinutos] = fechamento.split(':').map(Number);

    // Criamos objetos Date para os horários de abertura e fechamento
    const agora = new Date();
    const dataAbertura = new Date(agora.getFullYear(), agora.getMonth(), agora.getDate(), aberturaHoras, aberturaMinutos);
    const dataFechamento = new Date(agora.getFullYear(), agora.getMonth(), agora.getDate(), fechamentoHoras, fechamentoMinutos);

    // Se o fechamento for na madrugada do dia seguinte, adicionamos um dia ao objeto dataFechamento
    if (dataFechamento <= dataAbertura)
      dataFechamento.setDate(dataFechamento.getDate() + 1);


    // Calculamos a diferença em milissegundos
    // @ts-ignore
    const diferencaEmMilissegundos = dataFechamento - dataAbertura;

    // Convertendo a diferença para minutos
    const diferencaEmMinutos = diferencaEmMilissegundos / (1000 * 60);

    return diferencaEmMinutos;
  }

  private async carregueHorariosDisponiveis() {
    this.naoFunciona = false;
    this.naoDaTempo = false;

    let diaDaSemana = this.pedido.dataEntrega.getDay()
    if(!this.empresa.horariosFuncionamento) return;

    this.horariosDisponiveis = [];
    let turnos = []

    let diaAnterior =  new Date(this.pedido.dataEntrega)

    diaAnterior.setDate(diaAnterior.getDate() - 1)

    let diaDaSemanaAnterior = diaAnterior.getDay()

    for (let horarioDaEmpresa of this.empresa.horariosFuncionamento) {
      horarioDaEmpresa.tempoAbertoEmMinutos =
        this.obtenhaTempoFuncionamentoMinutos(horarioDaEmpresa.horarioAbertura, horarioDaEmpresa.horarioFechamento )

      if ((horarioDaEmpresa.diaDaSemana === diaDaSemana) && horarioDaEmpresa.funciona)
        turnos.push(horarioDaEmpresa)
      else if(horarioDaEmpresa.diaDaSemana === diaDaSemanaAnterior &&
        horarioDaEmpresa.funciona &&
        parseInt(horarioDaEmpresa.horarioFechamento.split(":")[0], 10) <
        parseInt(horarioDaEmpresa.horarioAbertura.split(":")[0], 10) ) {
        horarioDaEmpresa.ehMadrugada = true
        turnos.push(horarioDaEmpresa)
      }
    }

    let formaDeEntregaSelecionada: any = this.pedido.obtenhaFormaEscolhida(this.formasDeEntrega);

    let tempoMaximoPreparo = this.obtenhaTempoMaximo(formaDeEntregaSelecionada);


    if (turnos.length === 0) this.naoFunciona = true
    else {
      for(let horarioFuncionamento of turnos) {
          let partesAbertura = horarioFuncionamento.horarioAbertura.split(":"),
            partesFechamento = horarioFuncionamento.horarioFechamento.split(":")

          if(horarioFuncionamento.diaDaSemana === diaDaSemanaAnterior)
            partesAbertura = ['00', '00']

          if(tempoMaximoPreparo > 0 && horarioFuncionamento.tempoAbertoEmMinutos <= tempoMaximoPreparo) continue;

          if (!this.ehHoje(this.pedido.dataEntrega)) {
            let horaAbertura = new Date(this.pedido.dataEntrega.getFullYear(), this.pedido.dataEntrega.getMonth(),
              this.pedido.dataEntrega.getDate(), partesAbertura[0], partesAbertura[1], 0, 0)

            if(!horarioFuncionamento.ehMadrugada)
              horaAbertura = this.adidioneTempoPreparo(horaAbertura, formaDeEntregaSelecionada)

            let horaFechamento = new Date(this.pedido.dataEntrega.getFullYear(), this.pedido.dataEntrega.getMonth(),
              this.pedido.dataEntrega.getDate(), partesFechamento[0], partesFechamento[1], 0, 0)

            //para os dias que passou da meia-noite
            if (horaFechamento < horaAbertura) horaFechamento =
              new Date(this.pedido.dataEntrega.getFullYear(), this.pedido.dataEntrega.getMonth(),
                this.pedido.dataEntrega.getDate(), 23, 59)


           await this.gereIntervaloHorarios(horaAbertura,
              horaFechamento, this.pedido.obtenhaIntervaloAgendamento(this.pedido.entrega.formaDeEntrega, this.empresa.formasDeEntrega))

          } else if (!this.empresa.fechadoTemporariamente) {
            //ehHoje -
            let horaMinimaQueFicaPronto = this.adidioneTempoPreparo(new Date(), formaDeEntregaSelecionada)

            let horaAbertura = this.adidioneTempoPreparo(new Date(this.pedido.dataEntrega.getFullYear(), this.pedido.dataEntrega.getMonth(),
              this.pedido.dataEntrega.getDate(), partesAbertura[0], partesAbertura[1], 0), formaDeEntregaSelecionada)

            let horaFechamento = new Date(this.pedido.dataEntrega.getFullYear(), this.pedido.dataEntrega.getMonth(),
              this.pedido.dataEntrega.getDate(), partesFechamento[0], partesFechamento[1], 0)

            //para os dias que passou da meia-noite
            if (horaFechamento < horaAbertura) horaFechamento =
              new Date(this.pedido.dataEntrega.getFullYear(), this.pedido.dataEntrega.getMonth(),
                this.pedido.dataEntrega.getDate(), 23, 59)

            if (horaMinimaQueFicaPronto > horaAbertura) horaAbertura = horaMinimaQueFicaPronto

            if (horaAbertura < horaFechamento)
              await this.gereIntervaloHorarios(horaAbertura,
                horaFechamento, this.pedido.obtenhaIntervaloAgendamento(this.pedido.entrega.formaDeEntrega, this.empresa.formasDeEntrega))
        }
      }

      console.log("Horarios disponíveis calculados", this.horariosDisponiveis)
      if(this.horariosDisponiveis.length === 0) this.naoDaTempo = true

      // Ordena o array em ordem crescente pelo valor
      this.horariosDisponiveis.sort((a: any, b: any) => {
        return this.converterParaMinutos(a.valor) - this.converterParaMinutos(b.valor);
      });
    }
  }

  // Função para converter o horário "HH:mm" em minutos após a meia-noite
  converterParaMinutos(horario: string) {
    const [horas, minutos] = horario.split(':').map(Number);
    return horas * 60 + minutos;
  }

  ehMesmoDia (umaData, outraData){
    return umaData.getDate() === outraData.getDate() &&
      umaData.getMonth() === outraData.getMonth() &&
      umaData.getFullYear() === outraData.getFullYear()

  }

  ehHoje(umaData) {
    const hoje = new Date()

    return this.ehMesmoDia(umaData, hoje)
  }

  ehAmanha(umaData) {
    const amanha = new Date()
    amanha.setTime(amanha.getTime() +  1000 * 60 * 60 * 24)

    return this.ehMesmoDia(umaData, amanha)

  }

  clicouAgendarEntrega() {
    this.tela.agendarEntrega = this.agendarEntrega;
    this.setDatasMinimaseMaximas();
  }

  selecionouHorario() {

  }

  private async gereIntervaloHorarios(horaInicial: Date, horaFinal: Date, intervaloEmMinutos: number = 30) {
    let formaDeEntrega =  this.pedido.obtenhaFormaEscolhida(this.formasDeEntrega);
    if(formaDeEntrega.limitePedidosAgendados){
      let dataSelecioanda = this.pedido.dataEntrega;
      let ano = dataSelecioanda.getFullYear(),
          mes = dataSelecioanda.getMonth() + 1,
          dia = dataSelecioanda.getDate();

      let dataEntrega = String(`${ano}${mes.toString().padStart(2, '0')}${dia}`)

      let horariosCheios = await this.clienteService.obtenhaHorariosIndisponiveis(formaDeEntrega, dataEntrega);

      this.horariosCheios = horariosCheios || [];

    }

    while (horaInicial.getMinutes() % intervaloEmMinutos !== 0)
      horaInicial.setMinutes ( horaInicial.getMinutes() + 1 );

    horaInicial.setSeconds(0)


    let horarioAInserir = horaInicial

    while (horarioAInserir.getTime() <= horaFinal.getTime()) {

      if(this.estaDentroHorarioMaximoEMinino(horarioAInserir)){
        let horario =  horarioAInserir.toLocaleTimeString('pt-BR').substring(0, 5);
        let item: any = {
           valor: horario,
           descricao: horario,
           bloqueado: this.horariosCheios.indexOf(horario) >= 0
        }

        if(item.bloqueado) item.descricao = String(`${horario} (indisponível)`)

        this.horariosDisponiveis.push(item);
      }

      horarioAInserir = new Date(horarioAInserir)
      horarioAInserir.setMinutes ( horarioAInserir.getMinutes() + intervaloEmMinutos);
    }
  }


  public itemDisabled(itemArgs: { dataItem: any; index: number }) {
    return itemArgs.dataItem.bloqueado
  }


  private estaDentroHorarioMaximoEMinino(horarioAInserir: Date) {
    let estaEntreAsDatas  = true;

    if(this.dataMinimaAgendamento)
       estaEntreAsDatas = horarioAInserir.getTime() >= this.dataMinimaAgendamento.getTime()

    if(this.dataMaximaAgendamento && estaEntreAsDatas)
      estaEntreAsDatas = horarioAInserir.getTime() <=  this.dataMaximaAgendamento.getTime()

    return  estaEntreAsDatas;
  }

  alterouHorarioAgendado(horarioAgendado) {
    if(horarioAgendado && !horarioAgendado.bloqueado)
      this.pedido.horarioEntrega = horarioAgendado.valor;
  }

  public disabledDates = (date: Date): boolean => {
    let naoFunciona = false;
    let diaDaSemana = date.getDay();

    if(!this.empresa.horariosFuncionamento) return;

   // console.log('Chamando disabledDates');
    //this.horariosDisponiveis = [];
    let turnos = []

    let diaAnterior =  date;

   //console.log(date);

    diaAnterior.setDate(diaAnterior.getDate() - 1)

    let diaDaSemanaAnterior = diaAnterior.getDay()

    for (let horarioDaEmpresa of this.empresa.horariosFuncionamento) {
      if ((horarioDaEmpresa.diaDaSemana === diaDaSemana) && horarioDaEmpresa.funciona)
        turnos.push(horarioDaEmpresa)
      else if(horarioDaEmpresa.diaDaSemana === diaDaSemanaAnterior &&
          horarioDaEmpresa.funciona &&
          parseInt(horarioDaEmpresa.horarioFechamento.split(":")[0], 10) <
          parseInt(horarioDaEmpresa.horarioAbertura.split(":")[0], 10) )
        turnos.push(horarioDaEmpresa)

    }

    if (turnos.length === 0) {
      naoFunciona = true
    }

    return naoFunciona;
  };


  private obtenhaTempoMaximo(formaDeEntregaSelecionada: any){
    if(!formaDeEntregaSelecionada) return null;
    return formaDeEntregaSelecionada.tempoMaximo || formaDeEntregaSelecionada.tempoMaximoRetirada
  }

  private adidioneTempoPreparo(date: Date, formaDeEntregaSelecionada): Date {
    let horaAbertura = date;

    let tempoMaximo = this.obtenhaTempoMaximo(formaDeEntregaSelecionada)

    if(tempoMaximo)
      horaAbertura.setTime(horaAbertura.getTime() + tempoMaximo * 60 * 1000)

    horaAbertura.setSeconds(0)
    horaAbertura.setMilliseconds(0)

    return horaAbertura
  }

  private async executeCarregamentoDeHorarios() {
    if(this.empresa && this.pedido &&  this.pedido.dataEntrega && this.formasDeEntrega) {
      await this.carregueHorariosDisponiveis()
      this.inicializouHorarios = true;
      this.horarioAgendado = {
        valor: this.pedido.horarioEntrega,
        descricao: this.pedido.horarioEntrega,
        bloqueado: false
      };
      this.app.tick();
      console.log("Horários disponiveis", this.horariosDisponiveis)
    }

  }
}
