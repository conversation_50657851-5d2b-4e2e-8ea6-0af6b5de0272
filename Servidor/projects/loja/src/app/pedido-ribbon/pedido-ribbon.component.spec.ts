import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { PedidoRibbonComponent } from './pedido-ribbon.component';

describe('PedidoRibbonComponent', () => {
  let component: PedidoRibbonComponent;
  let fixture: ComponentFixture<PedidoRibbonComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ PedidoRibbonComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PedidoRibbonComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
