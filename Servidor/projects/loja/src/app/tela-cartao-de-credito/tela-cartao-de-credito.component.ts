import {ApplicationRef, Component, ElementRef, NgZone, OnInit, ViewChild} from '@angular/core';
import {DialogRef, DialogService} from "@progress/kendo-angular-dialog";
import {CadCartaocreditoPagSeguroComponent} from "../cad-cartaocredito/cad-cartaocredito-pag-seguro.component";
import {FormEnderecoComponent} from "../form-endereco/form-endereco.component";

import {PedidoLoja} from "../../objeto/PedidoLoja";
import {DadosCartao} from "../../objeto/DadosCartao";
import {FormUtils} from "../../objeto/FormUtils";
import {CadCartaoMercadopagoComponent} from "../cad-cartao-mercadopago/cad-cartao-mercadopago.component";
import {ICartaoCreditoGateway} from "../../objeto/ICartaoCreditoGateway";
import {CadCartaoCieloComponent} from "../cad-cartao-cielo/cad-cartao-cielo.component";
import {CadCartaoPagarmeComponent} from "../cad-cartao-pagarme/cad-cartao-pagarme.component";
import {
  CadCartaoPagseguroConnectComponent
} from "../cad-cartao-pagseguro-connect/cad-cartao-pagseguro-connect.component";
import {CadCartaoEredeComponent} from "../cad-cartao-erede/cad-cartao-erede.component";
import {CadCartaoTunapayComponent} from "../cad-cartao-tunapay/cad-cartao-tunapay.component";
import {AutorizacaoLojaService} from "../../services/autorizacao-loja.service";
import {Endereco} from "../../objeto/Endereco";
import {NgForm} from "@angular/forms";


@Component({
  selector: 'app-tela-cartao-de-credito',
  templateUrl: './tela-cartao-de-credito.component.html',
  styleUrls: ['./tela-cartao-de-credito.component.scss']
})
export class TelaCartaoDeCreditoComponent implements OnInit {
  msgErro: string;
  usarEnderecoDeEntrega = true;
  processandoDados = false;
  @ViewChild('frmEscolherEndereco', {static: false}) frmEscolherEndereco: NgForm;
  @ViewChild('dadosCartao', {static: false}) ctlDadosCartao: CadCartaocreditoPagSeguroComponent;
  @ViewChild('dadosCartaoPagaseguroConnect', {static: false}) ctlDadosCartaoPagaseguroConnect: CadCartaoPagseguroConnectComponent;
  @ViewChild('dadosCartaoMercadoPago', {static: false}) ctlDadosCartaoMercadoPago: CadCartaoMercadopagoComponent;
  @ViewChild('ctlDadosCartaoCielo', {static: false}) ctlDadosCartaoCielo: CadCartaoCieloComponent;
  @ViewChild('ctlDadosCartaoPagarme', {static: false}) ctlDadosCartaoPagarme: CadCartaoPagarmeComponent;
  @ViewChild('ctlDadosCartaoRede', {static: false}) ctlDadosCartaoRede: CadCartaoEredeComponent;
  @ViewChild('ctlDadosCartaoTuna', {static: false}) ctlDadosCartaoTuna: CadCartaoTunapayComponent;
  @ViewChild('formEndereco', {static: false}) formEndereco: FormEnderecoComponent;
  @ViewChild('erroDiv', { static: false})  erroDiv: ElementRef;
  window: DialogRef;
  pedido: PedidoLoja;
  obrigarEndereco: boolean;
  gateway: string
  numeroParcelasFixas: number;
  mensagemAlerta: string;
  enderecos = [];
  enderecoCobranca: any;
  informarNovo: boolean;
  constructor(private app: ApplicationRef, private ngZone: NgZone,
              private autorizacao: AutorizacaoLojaService,) {
  }

  static abraComoPopup(dialogService: DialogService, isMobile: boolean,
                       pedido: PedidoLoja, pagamento: any, dadosCartao: DadosCartao): DialogRef {
    let altura: any = window.innerHeight - 100;
    if( isMobile ) {
      altura = '100%';
    }

    let largura: any = '100%';

    if( window.innerWidth > 600 ) {
      largura = 'calc(100% - ' + (window.innerWidth - 600) + 'px)';
    }

    let configJanela: any = {
      title: null,
      content: TelaCartaoDeCreditoComponent,
      minWidth: 250,
      width: largura
    };

    if( isMobile ) {
      configJanela.height = altura;
    } else {
      configJanela.maxHeight = altura;
    }

    const windowRef = dialogService.open(configJanela);

    const $divDialog = windowRef.dialog.location.nativeElement;

    if( $divDialog ) {
      const $divDialogContent = $divDialog.getElementsByClassName('k-dialog-content');

      if( $divDialogContent.length > 0 ) {
        $divDialogContent[0].classList.add('dialog-pagamento');
      }
    }

    const telaCartaoDeCreditoComponent: TelaCartaoDeCreditoComponent = windowRef.content.instance;
    telaCartaoDeCreditoComponent.inicialize(windowRef, pedido, pagamento, dadosCartao);

    return windowRef;
  }

  ngOnInit(): void {
    if(this.informarEnderecoCobranca()){
      this.autorizacao.obtenhaEnderecos().then( (enderecos) => {
        this.setEnderecos(enderecos)
      })
    }
  }

  setEnderecos(enderecos: any = []){
    this.enderecos = []
    enderecos.forEach( (dadosEndereco: any) => {
      let endereco = Endereco.novo();

      Object.assign(endereco, dadosEndereco)

      if(dadosEndereco.cidade){
        endereco.cidade = dadosEndereco.cidade;
        endereco.estado = dadosEndereco.cidade.estado;
      }

      if( !endereco.descricaoCompleta ) {
        endereco.descricaoCompleta = endereco.obtenhaEnderecoCompleto();
      }
      this.enderecos.push(endereco)
    });

    if(!enderecos.length) this.novoEndereco();

  //  if(this.enderecos.length && !this.enderecoEscolhido)
     // this.endereco =  this.enderecos[0]
  //  else if (!this.enderecos.length &&    !this.enderecoEscolhido){
    //  this.enderecoEscolhido = true;
   // }
  }

  private obtenhaCtlDados(): ICartaoCreditoGateway {
    if (this.gatewayTunaPay()) {
      return this.ctlDadosCartaoTuna;
    }
    if(this.gatewayPagSeguro()) return this.ctlDadosCartao;
    if(this.gatewayPagSeguroConnect()) return this.ctlDadosCartaoPagaseguroConnect;
    if(this.gatewayMercadoPago()) return this.ctlDadosCartaoMercadoPago;
    if(this.gatewayPagCielo()) return this.ctlDadosCartaoCielo;
    if(this.gatewayPagarme()) return this.ctlDadosCartaoPagarme;
    if(this.gatewayERede()) return this.ctlDadosCartaoRede;
  }

  gatewayPagSeguro(){
    return this.gateway === 'pagseguro';
  }

  gatewayPagSeguroConnect(){
    return this.gateway === 'pagseguroconnect';
  }

  gatewayPagCielo(){
    return this.gateway === 'cielocheckout';
  }


  gatewayPagarme(){
    return this.gateway === 'pagarme' ||  this.gateway === 'pagarmehub';
  }

  gatewayERede(){
    return this.gateway === 'erede' ;
  }

  gatewayMercadoPago(){
    return this.gateway === 'mercadopago';
  }

  gatewayTunaPay() {
    return this.gateway === 'tunapay';
  }

  inicialize(window: DialogRef, pedido: any, pagamento: any, dadosCartao: DadosCartao) {
    this.window = window;
    this.pedido = pedido;
    this.gateway  = pagamento.formaDePagamento.configMeioDePagamento.meioDePagamento;

    if( pedido.retirar || (pedido.entrega && pedido.entrega.ehRetirada()) ) {
      this.obrigarEndereco = true;
      this.usarEnderecoDeEntrega = false;
    }

    setTimeout( () => {
      if(this.gatewayPagarme())
        this.ctlDadosCartaoPagarme.setParcelamento(pagamento.formaDePagamento.numeroParcelasFixas)
      this.obtenhaCtlDados().exibaCartao(dadosCartao)
    }, 0)


  }

  fecheTela() {
    this.window.close(null);
  }

  alterouUsarEnderecoDeEntrega() {

  }

  obtenhaDadosSemDadosSensitivos() {
  }

  obtenhaEnderecoCobranca(){
    if(this.usarEnderecoDeEntrega)
      return this.pedido.entrega ? this.pedido.entrega.endereco : (this.pedido as any).endereco;

    if(!this.informarNovo) return this.enderecoCobranca;

    return this.formEndereco ? this.formEndereco.obtenhaEndereco() :  null;

  }

  completeDadosCartao(dadosCartao: DadosCartao) {
    dadosCartao.usarEnderecoDeEntrega = this.usarEnderecoDeEntrega;

    if( this.formEndereco )
      dadosCartao.endereco = this.formEndereco.obtenhaEndereco();
    else dadosCartao.endereco = this.enderecoCobranca

  }

  getOffset(el: any) {
    const rect = el.getBoundingClientRect();
    return {
      left: rect.left + window.scrollX,
      top: rect.top + window.scrollY
    };
  }


  salvarCartao() {
    let valido = true;
    const respValidadeCartao = this.obtenhaCtlDados().ehValido();

    if( !respValidadeCartao.valido ) {
      const $janela = document.querySelector('.k-content');
      FormUtils.foqueInvalido($janela, respValidadeCartao.controle)

      valido = false;
    }


    if( !this.usarEnderecoDeEntrega ){
      if(this.informarNovo){
        if(this.formEndereco){
          const respValidadeEndereco = this.formEndereco.ehValido();

          if( valido && !respValidadeEndereco.valido ) {
            const $janela = document.querySelector('.k-content');
            FormUtils.foqueInvalido($janela, respValidadeEndereco.controle)

            valido = false;
          }
        } else {

        }
      } else {
        if( this.frmEscolherEndereco)
          ( this.frmEscolherEndereco as {submitted: boolean}).submitted = true;

         if(!this.enderecoCobranca )
           valido  = false;
      }
    }

    if( !valido )    return;

    this.processandoDados = true;

    this.obtenhaCtlDados().crieTokenCartao(this.obtenhaEnderecoCobranca()).then( (dadosCartao: DadosCartao) => {
      this.ngZone.run( () => {
        // Callback para todas chamadas.
        this.processandoDados = false;
      });
      this.gerouTokenCartao(dadosCartao);
    }).catch( (erro) => {
      this.ngZone.run( () => {
        // Callback para todas chamadas.
        this.processandoDados = false;
        this.msgErro = erro;
        setTimeout(() => {
          this.scrollToErro();
        }, 0)

      });
    })
  }

  scrollToErro(){
    if( this.erroDiv && this.erroDiv.nativeElement)
      this.erroDiv.nativeElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }

  gerouTokenCartao(dadosCartao: DadosCartao){
    this.completeDadosCartao(dadosCartao);
    this.window.close(dadosCartao);
    this.app.tick();
  }

  informarEnderecoCobranca() {
    return this.gatewayPagSeguro() ||
      this.gatewayPagarme() ||
      this.gatewayPagCielo() ||
      this.gatewayPagSeguroConnect() ||
      this.gatewayERede() ||
      this.gatewayTunaPay()
  }

  novoEndereco() {
    this.informarNovo = true;
    return false;
  }
}
