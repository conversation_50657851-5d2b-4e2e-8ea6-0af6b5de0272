::ng-deep .dialog-pagamento {
  overflow-x: hidden;
  padding-top: 0px !important;
  position: initial;
}

::ng-deep .mobile .dialog-pagamento {
  padding: 12px;
}

::ng-deep .k-dialog-wrapper {
  z-index: 100001;
}

.footer {
  background: #fff;
  padding: 0px;
  position: initial;
  margin-top: 20px;
  z-index: 2;
}

.card {
  border: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  .card-body {
    padding: 2rem;
    position: relative;
  }
}
h4{
  font-size: 16px;
}

@media (min-width: 1025px) {

  .footer {
    margin-top: 25px;
    position: initial !important;
  }
}

@media (max-width: 900px) {
  .card-body {
    padding: 1rem !important;
    position: relative;
  }
  .card-body .card-body{
    padding: 0 !important;
  }

}

.label{
  color: #495057;
  font-weight: normal;
}



.custom-control-label {
  font-size: 1rem;
  padding-top: 0.2rem;
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid rgba(0,0,0,.125);
}

.btn-outline-primary {
  &:hover {
    color: #fff;
  }
}


::ng-deep kendo-dateinput .k-input-inner {
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
}
