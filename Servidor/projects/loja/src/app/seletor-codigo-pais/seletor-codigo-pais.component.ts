import {Component, OnInit, Input, Output, EventEmitter, ApplicationRef} from '@angular/core';
import {CountryService} from "../../services/country.service";
import * as libphonenumber from 'google-libphonenumber';

@Component({
  selector: 'app-seletor-codigo-pais',
  templateUrl: './seletor-codigo-pais.component.html',
  styleUrls: ['./seletor-codigo-pais.component.scss']
})
export class SeletorCodigoPaisComponent implements OnInit {
  @Input() selectedCountryCode: string;
  @Output() selectedCountryCodeChange = new EventEmitter<string>();
  @Output() phoneMaskChange = new EventEmitter<string>();

  countries: any[] = [];
  selectedCountry: any = {};
  dados: any[] = [];


  constructor(private app: ApplicationRef,
              private  countryService: CountryService) {

  }

  ngOnInit(): void {
    this.countryService.getCountries().then((countries: any) => {
      this.countries = countries.data;

      if(this.selectedCountryCode) {
        this.selectedCountry = this.countries.find(country => country.phone === this.selectedCountryCode)
        this.phoneMaskChange.emit(this.getPhoneMask(this.selectedCountry.alpha2))
      }

      this.dados = this.countries;

    });

  }

  onCountryChange(pais: any) {
    if(!pais)
      return;

    this.selectedCountryCodeChange.emit(pais.phone);
    this.phoneMaskChange.emit(this.getPhoneMask(pais.alpha2))


  }

  getPhoneMask(countryCode: string): string {
    const phoneUtil = libphonenumber.PhoneNumberUtil.getInstance();
    const exampleNumber = phoneUtil.getExampleNumberForType(countryCode, libphonenumber.PhoneNumberType.MOBILE);

    let format = phoneUtil.format(exampleNumber, libphonenumber.PhoneNumberFormat.NATIONAL);

    if(format)
      format = format.replace(/[0-9]/g, '0')

    console.log("Máscara telefone (" + countryCode + "): " + format )
    return format
  }

  handleFilter(value: any) {
    this.dados = this.countries.filter(
      (s) => s.phone.toLowerCase().indexOf(value.toLowerCase()) !== -1 || s.alpha2.toLowerCase().indexOf(value.toLowerCase()) !== -1
    );
  }
}
