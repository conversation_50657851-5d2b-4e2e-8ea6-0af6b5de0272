import { Component, OnInit } from '@angular/core';
import {AutorizacaoLojaService} from "../../services/autorizacao-loja.service";
import {ITela} from "../../objeto/ITela";
import {MyDetectorDevice} from "../shared/MyDetectorDevice";

@Component({
  selector: 'app-loja-configuracoes',
  templateUrl: './loja-configuracoes.component.html',
  styleUrls: ['./loja-configuracoes.component.scss']
})
export class LojaConfiguracoesComponent implements OnInit, ITela {
  configuracoes: any = {
    msgsMarketingAtivas: false
  };
  msgSucesso = '';
  ehDesktop = false;

  constructor(private autorizacao: AutorizacaoLojaService, private detectorDevice: MyDetectorDevice) {
    this.ehDesktop = this.detectorDevice.isDesktop();
  }

  ngOnInit(): void {
    this.autorizacao.obtenhaConfiguracoesUsuario().then((configuracoes) => {
      this.configuracoes = configuracoes;
    });
  }

  salvar() {
    this.autorizacao.atualizeConfiguracoesUsuario(this.configuracoes).then((resposta) => {
      this.msgSucesso = resposta;
    })
  }

  deveExibirTopo() {
    return false;
  }

  deveExibirMenu() {
    return true;
  }

  deveTerBordas() {
    return true;
  }

  deveExibirBannerTema() {
    return false;
  }
}
