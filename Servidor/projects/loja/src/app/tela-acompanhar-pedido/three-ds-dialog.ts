import {Component, Inject, Input, OnInit} from '@angular/core';
import { DialogRef, DialogService } from '@progress/kendo-angular-dialog';
import {DomSanitizer} from "@angular/platform-browser";

@Component({
  selector: 'app-three-ds-dialog',
  template: `
     <h4>
       <i class="icone-autenticacao"></i>
       Para autenticar o cartão, você será redirecionado. Por favor aguarde e não feche a página

       <i class="k-i-loading k-icon mr-1"> </i></h4>

     <p  class="mt-4">
       Caso não for redirecionado  <a href="{{url}}"  > clique aqui</a>
     </p>

  `,
  styles: [`
    .icone-autenticacao{
      width: 50px;
      height: 50px;
      background-size: 50px;
      margin-bottom: 5px;
      top: 13px;
      position: relative;
      display: inline-block;
      background-image: url("/assets/icons/autenticacao-de-pagamento.png");
    }
    .k-i-loading{
      font-size: 30px;
    }
  `]
})
export class ThreeDSDialogComponent implements OnInit {
  url;
  sanitizedUrl;

  constructor(  private dialogRef: DialogRef,  private sanitizer: DomSanitizer) {

  }

  ngOnInit(): void {
    window.addEventListener('message', this.receiveMessage.bind(this), false);
    setTimeout(() => {
      window.location.href =  this.url;
    }, 1500)
  }

  setUrl(url: string){
    this.url = url;
    this.sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);
  }

  receiveMessage(event: MessageEvent): void {
    if (event.data === '3ds-auth-success')
      this.dialogRef.close({retornou: true});
  }


  sanitizeUrl(url: string): string {
    return url;
    // Você pode usar DomSanitizer se necessário para segurança:
    // return this.sanitizer.bypassSecurityTrustResourceUrl(url) as string;
  }

  close(): void {
    this.dialogRef.close();
  }
}
