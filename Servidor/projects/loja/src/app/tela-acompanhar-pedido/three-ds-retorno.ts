import {Component, Inject, OnInit, Renderer2} from "@angular/core";
import {DOCUMENT} from "@angular/common";
import {ActivatedRoute} from "@angular/router";

@Component({
  selector: 'app-three-ds-dialog',
  template: `
   <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Autenticação Concluída</title>
      </head>
        <body>
           <h5 class="text-center mt-3" *ngIf="autenticou">
                 <i class="k-icon k-i-check-circle fa-2x text-success mr-2"></i>
                 Autenticação do cartão concluido com sucesso!</h5>

         <ng-container *ngIf="!autenticou">
           <h5 class="text-center mt-3" >
             <i class="k-icon k-i-times-circle fa-2x text-danger mr-2"></i>
             Autenticação do cartão  falhou: "{{msgErro}}" <br>
           </h5>
         </ng-container>

        </body>
      </html>
  `
})
export class ThreeDSRetornoComponent implements OnInit {
  autenticou: boolean;
  msgErro: string;
  constructor(    private _renderer2: Renderer2,  @Inject(DOCUMENT) private  _document: Document,
                  private  activatedRoute: ActivatedRoute) {

    this.autenticou = this.activatedRoute.snapshot.params.status === 'success-page';
    this.msgErro =  this.activatedRoute.snapshot.queryParams.err;
  }

  ngOnInit(): void {

    if( this.autenticou){
      setTimeout(() => {
        this.noitiqueFecharModal();
      }, 5000)
    }
  }

  noitiqueFecharModal(){
    let script = this._renderer2.createElement('script');
    script.type = 'text/javascript';
    script.text = `
       window.parent.postMessage('3ds-auth-success', '*');
    `;
    this._renderer2.appendChild(this._document.body, script);
  }

  deveExibirTopo(){
    return false;
  }

  deveExibirMenu(){
    return false;
  }

  deveTerBordas(){
    return false;
  }

  deveExibirBannerTema(){
    return false;
  }
}
