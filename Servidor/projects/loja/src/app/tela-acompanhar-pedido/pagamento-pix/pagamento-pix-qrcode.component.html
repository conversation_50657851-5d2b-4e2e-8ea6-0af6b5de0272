<div class="row">
  <div class="col-12"  [hidden]="!pedido.aguardandoPagamentoOnline || pedido.novaTentativaPix">
    <div class="card pix-pagar">
      <div class="card-body p-2">
        <div *ngIf="pedido.tempoRestantePagar != null && pedido.tempoRestantePagar <= 0" class="text-center">
          <div class="alert alert-danger mb-3 font-20">
            <i class="fas fa-exclamation-circle fa-lg mr-2"></i>
            O tempo para realizar o pagamento expirou!
          </div>
          <button class="btn btn-warning btn-lg btn-block"
                  (click)="gerarNovoPix()"
                  [disabled]="tentandoNovoPagamento">
            <i class="fas fa-sync-alt mr-2" [hidden]="tentandoNovoPagamento"></i>
            <i class="k-i-loading k-icon mr-2" [hidden]="!tentandoNovoPagamento"></i>

            {{tentandoNovoPagamento ? 'Gerando novo PIX...' : 'Gerar novo código PIX'}}
          </button>
        </div>

        <ng-container *ngIf="pedido.tempoRestantePagar > 0 || pedido.tempoRestantePagar == null">
          <div class="text-center mb-3"  *ngIf="pedido.tempoRestantePagar > 0">
            <div class="alert alert-warning">
              <i class="fas fa-clock fa-lg mr-1"></i>
              O prazo para finalizar o pagamento termina em:

              <span style="width: 95px; display: inline-block; font-size: 18px">
                  <strong>{{tempoRestante.minutos}}m:{{tempoRestante.segundos | number:'2.0-0'}}s</strong>
              </span>

            </div>
          </div>

          <div class="row">
            <div class="col-auto mb-2" *ngIf="desktop">
              <img [src]="'/api/qrcode?c=' + pagamento.codigoQrCode" style="width: 200px;"/>
              <div style="width: 190px;margin-top: -10px;" class="ml-2">
                <span class="small help font-11">Você também pode ler o QrCode com o App do seu Banco.</span>
              </div>
            </div>
            <div class="col">
              <h4>Copie esse código para pagar</h4>
              <p><strong>1.</strong> Acesse seu internet banking ou app de pagamentos</p>
              <p><strong>2.</strong> Escolha pagar via PIX</p>
              <p><strong>3.</strong> <span *ngIf="desktop"> Leia o QrCode ou</span> Copie e cole o seguinte código</p>
            </div>
          </div>

          <input type="text" class="form-control font-14" [readOnly]="true"
                 style="padding: 20px 15px;border-radius: 5px;background: #fffcfc !important;color: #000;font-weight: bold;"
                 [(ngModel)]="pagamento.codigoQrCode"/>

          <div class="mt-2"></div>
          <button class="btn btn-blue btn-block btn-lg" (click)="copiarCodigoPix()">Copiar Código</button>
          <div class="alert alert-success mt-2 font-13" role="alert" *ngIf="msgCopiar">
            <i class="mdi mdi-check-all mr-2"></i> {{msgCopiar}}
          </div>
        </ng-container>
      </div>
    </div>
  </div>
</div>

