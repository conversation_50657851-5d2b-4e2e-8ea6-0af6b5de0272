.pedido-finalizado{
  padding-top: 25px;
  padding-bottom: 25px;
  .alert-success{
    background: var(--cor-fundo-elementos, #fff);
    font-size: 20px;
  }
}

.k-checkbox:disabled{
  border-color: rgba(0, 0, 0, 0.1) !important;
}

.usar-saldo{
  display: inline;
  top: 2px;
  position: relative;
}

.k-form .k-label{
  display: inline !important;
}

.panel-finalizar{
  background: var(--cor-fundo-site, #fff);
  min-height: 700px;
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;

  .card-body{
    >div{
      max-width: 600px;
    }
  }

}

.panel-pedido{
  position: absolute;
  right: 15px;
  top: 30px;
  max-width: 400px;
  background: var(--cor-fundo-elementos, #f7f8f8);
  border: 1px solid var(--cor-borda, #ccc3);
 /*  box-shadow: 0 4px 10px -2px #E2E3E3; */
}


::ng-deep body{
  background-color: var(--cor-fundo-site, #f7f8f8) !important;
}

input.troco {
  max-width: 215px;
}

.k-dateinput-wrap {
    padding: 0 !important;
}

::ng-deep.escolher {
  span.radio-blue {
    position: relative;
    min-width: 230px;
    display: inline-block;
    min-height: 40px;
    padding: 10px;
    background: var(--cor-fundo-elementos, #f7f8f8);
    /* border-right: 1px solid #ccc; */
    margin-left: 10px;

    &.auto{
      min-width: inherit;
    }

    &.cartao{
      width: 100%;
    }
  }

  span.selecionado:before {
    border-left: 5px solid var(--cor-destaque, #3483fa);
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
  }

  &.cartoes{
    span {
      width: 100%;
      background: var(--cor-fundo-elementos, #f7f8f870);

      &.selecionado{
        background: var(--cor-fundo-elementos, #f7f8f8) !important;
      }

    }


  }

  &.menor{
    span {
      width: 190px;
    }
  }

  &.troco{
    span{
      width: 120px;
      label{
        display: inline;
        top: 2px;
        position: relative;
      }
    }
  }

  .cartao-online{
    .fa{
      top: 7px;
      position: relative;
    }
    label small{
      left: 85px;
      position: relative;
    }

    .acao-cartao{
      position: absolute;
      right: 10px;
      top: 15px;
    }
  }

}



.icone.whatsapp {
  width: 16px;
  display: inline-block;
  margin: 0
}

.icone_voltar {
  display: inline-block;
  fill: var(--cor-botao, #4a81d4);
  vertical-align: middle;
}


fieldset {
  border: 1px solid var(--cor-borda, #ccc);
  padding: 20px;
  margin-top: 20px;
  padding-top: 10px;
  margin-left: 12px;
  margin-right: 12px;
  legend{
    max-width: 140px;
    font-size: 1.0em;
  }
}

::ng-deep .k-dialog-titlebar {
  background: var(--cor-botao, #3A44B9);
  color: var(--cor-texto-botao, #fff);
}

.preco {
  color: var(--cor-preco, #6db31b);
  font-weight: 700;
}

h5{
  font-size: 1em !important;
}
.pagarfidelidade{
  background: var(--cor-fundo-elementos, #f7f8f8);
  padding: 10px;
  border-left: 5px solid var(--cor-destaque, #367ef6);
  margin-left: 10px;
  &.gendai{
    img{
      top: 13px;
      position: relative;
    }
  }

}

.chinainbox{
  .btn-blue {
    background-color: var(--cor-botao, #e52a28de) !important;
    border-color: var(--cor-botao, #e52a28) !important;;
  }
}

.carnaval {
  .btn-blue, .btn-primary {
    background-color: var(--cor-botao, #5b1da6) !important;
    border-color: var(--cor-botao, #5b1da6) !important;
  }
}

::ng-deep  .k-tabstrip > .k-content  {
  overflow: hidden !important;
}

