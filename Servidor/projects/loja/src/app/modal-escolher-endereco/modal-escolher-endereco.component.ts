import {Component, EventEmitter, OnInit, Output, ViewChild} from '@angular/core';
import {FormEnderecoComponent} from "../form-endereco/form-endereco.component";
import {AutorizacaoLojaService} from "../../services/autorizacao-loja.service";
import {Endereco} from "../../objeto/Endereco";
import {EnderecoService} from "../../services/endereco.service";
import {FormaDeEntrega} from "../../objeto/FormaDeEntrega";
import {ConstantsService} from "../../services/ConstantsService";
import {GeoService} from "../../services/geo.service";

@Component({
  selector: 'app-modal-escolher-endereco',
  templateUrl: './modal-escolher-endereco.component.html',
  styleUrls: ['./modal-escolher-endereco.component.scss']
})
export class ModalEscolherEnderecoComponent implements OnInit {
  @ViewChild('telaEndereco', {static: false}) telaEndereco:  FormEnderecoComponent;
  pedido: any;
  window: any;
  carregando = false;
  carregou = false;
  enderecos = [];
  enderecoEscolhido: any;
  endereco: any;
  calculandoTaxa = false;
  msgErro = '';
  formaReceberEmCasa: any;
  empresa: any;
  entregaPorZona = false;
  calculandoLocalizacao = false;

  constructor(private autorizacao: AutorizacaoLojaService, private enderecoService: EnderecoService,
              private constantsService: ConstantsService, private geoService: GeoService) { }

  ngOnInit() {
    let endereco = this.pedido && this.pedido.entrega ?  this.pedido.entrega.endereco : {}

    this.enderecoEscolhido =  this.endereco != null;

    this.constantsService.empresa$.subscribe( (empresa) => {
      if( !empresa ) return;

      this.empresa = empresa;

      this.formaReceberEmCasa =  this.empresa.formasDeEntrega.find((forma) => forma.nome === FormaDeEntrega.RECEBER_EM_CASA )

      if (this.formaReceberEmCasa && this.formaReceberEmCasa.tipoDeCobranca === 'zona')
        this.entregaPorZona = true;


      setTimeout(() => {
        if(this.empresa.enderecoCompleto && ! this.formaReceberEmCasa.naoUsarCidadePadrao)
          this.telaEndereco.setCidadePadrao(this.empresa.enderecoCompleto.cidade)
      }, 0)

    });

    this.autorizacao.obtenhaEnderecos().then( (enderecos) => {
      this.carregando = false;
      this.carregou = true;

      if( endereco && !endereco.id) {
        enderecos.unshift(endereco);
      }

      this.setEnderecos(enderecos)
    }).catch( (erro) => {
      this.carregou = true;
    });
  }


  cadastrarNovo(){
    this.enderecoEscolhido = true
  }


  salveEndereco(endereco) {
    if( endereco.calculouTaxaDeEntrega() ) {
      this.window.close(endereco)
    }
  }

  setEnderecos(enderecos: any = []){
    this.enderecos = []
    enderecos.forEach( (dadosEndereco: any) => {
      let endereco = Endereco.novo();

      Object.assign(endereco, dadosEndereco)

      if(dadosEndereco.cidade){
        endereco.cidade = dadosEndereco.cidade;
        endereco.estado = dadosEndereco.cidade.estado;
      }

      if( !endereco.descricaoCompleta ) {
        endereco.descricaoCompleta = endereco.obtenhaEnderecoCompleto();
      }
      this.enderecos.push(endereco)
    });

    if(this.enderecos.length && !this.enderecoEscolhido)
      this.endereco =  this.enderecos[0]
    else if (!this.enderecos.length &&    !this.enderecoEscolhido){
      this.enderecoEscolhido = true;
    }
  }

  calculeTaxaEntrega() {
    if( this.endereco.faltamInformacoes ) {
      this.enderecoEscolhido = true;
      this.telaEndereco.exibaEndereco(this.endereco);
      return;
    }

    this.calculandoTaxa = true;
    this.enderecoService.calculeTaxaDeEntrega('Receber em casa', this.endereco, this.pedido.obtenhaSubTotal()).then( (dados) => {
      this.calculandoTaxa = false;
      Object.assign(this.endereco, dados)
      this.salveEndereco(this.endereco)

      this.window.close(this.endereco);
    }).catch( erro => {
      this.calculandoTaxa = false;
      this.msgErro = erro;
    })
  }

  editarEndereco(){
    this.telaEndereco.exibaEndereco(this.endereco);
    this.enderecoEscolhido = true;
  }

  usarLocalizacao() {
    this.calculandoLocalizacao = true;
    navigator.geolocation.getCurrentPosition((posicao) => {
      const latlng = {
        lat: posicao.coords.latitude,
        lng: posicao.coords.longitude,
      };

      this.geoService.obtenhaEndereco(latlng).then((respEndereco) => {
        this.calculandoLocalizacao = false;
        const endereco = Endereco.novo();

        Object.assign(endereco, respEndereco);
        endereco.localizacao = null;
        this.pedido.entrega.formaDeEntrega = FormaDeEntrega.RECEBER_EM_CASA;
        endereco.gps = true;
        this.pedido.entrega.endereco = endereco;

        this.endereco = endereco;
        this.telaEndereco.exibaEndereco(this.endereco);
        //this.carrinhoService.salvePedido(this.pedido);

        this.enderecoEscolhido = true;

        this.telaEndereco.exibaMapa(latlng);
      });
    }, (erro) => {

      this.msgErro = 'Você precisa ativar a geolocalização para poder utilizar essa funcionalidade.';
    }, {maximumAge: 0, enableHighAccuracy: true, timeout: 5000 });
  }

  voltarDoCadastro() {
    this.enderecoEscolhido = false
    if(this.telaEndereco)
      this.telaEndereco.volteEndereco();
  }
}
