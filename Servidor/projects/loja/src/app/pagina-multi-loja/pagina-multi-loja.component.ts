import { Component, OnInit } from '@angular/core';
import {MyDetectorDevice} from "../shared/MyDetectorDevice";
import {Router} from "@angular/router";
import {ConstantsService} from "../../services/ConstantsService";

@Component({
  selector: 'app-pagina-multi-loja',
  templateUrl: './pagina-multi-loja.component.html',
  styleUrls: ['./pagina-multi-loja.component.scss']
})
export class PaginaMultiLojaComponent implements OnInit {
  isMobile: any;
  empresa: any = {};

  constructor(private router: Router, private detectorDevice: MyDetectorDevice,
              private constantsService: ConstantsService) {
    this.isMobile = this.detectorDevice.isMobile();
  }

  ngOnInit(): void {
    this.constantsService.empresa$.subscribe( (empresa) => {
      if( !empresa ) { return; }

      this.empresa = empresa || {};
    });
  }

  voltar() {
    this.router.navigateByUrl('/');
  }
}
