header {
  display: flex;
  position: relative;
  height: 92px;
  flex-direction: column;
  width: 100vw;
  background-color: #fff;
  box-shadow: inset 0 -1px 0 #dcdcdc;
  top: 0;
  z-index: 1001;
  padding: 0;

  &.quiosque {
    height: auto;
  }
  .conteudo{
    flex-grow: 1;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin: auto;

    .coluna-principal{
      padding-right: 0;
      position: relative;
      max-width: 1300px !important;
      margin: 0 auto;
    }

    .coluna-links{
      max-width: 400px ;
      height: 80%;
      display: inline-block;
    }
  }

  .forma_entrega {
    color: rgba(0, 0, 0, .5);

    .endereco {
      color: #3e3e3e;
    }
  }

  .fa-instagram {
    color: #B92468;
  }

  .fa-facebook-f {
    color: #0D8BF1;
  }

  &.tema-personalizado {
    background: var(--cor-fundo) !important;

    h1,.enderecos, .text-blue, .descricao, .fa-instagram, .fa-facebook-f,
    .tempos_entrega, .horario, .endereco {
      color: var(--cor-texto-topo)  !important;
    }

    .whatsapp, .fa-whatsapp {
      color: var(--cor-texto-topo) !important;
    }

    span, label, p {
      color: var(--cor-texto-topo) !important;
    }

    .descricao-empresa {
      color: var(--cor-texto-topo) !important;

      .status {
        color: var(--cor-texto-topo) !important;

        &.fechado {
          color: var(--cor-texto-topo) !important;
        }
      }
    }

    .btn-outline-blue {
      border-color: var(--cor-botao)  !important;
      color:  var(--cor-texto-botao) !important;
    }
  }

  &.carnaval{
    background: #D825A9;

    h1,.enderecos, .text-blue,  .descricao,   .fa-instagram, .fa-facebook-f ,
    .tempos_entrega{
      color: #fff000  !important;
    }

    .conteudo {
      .whatsapp {
        color: #fff000  !important;
      }
    }

    .btn-outline-blue {
      border-color: #fff000  !important;
      color:  #fff000;
    }

    .descricao-empresa {
      .status {
        color: #fff000 !important;
      }
    }
  }

  &.black_friday_2022 {
    background: #000;
    box-shadow: inset 0 -1px 0 #000;

    h1 {
      color: #F6A844;
    }

    .enderecos {
      color: #999;
    }

    .forma_entrega {
      color: #999;

      .endereco {
        color: #999;
      }
    }

    .text-blue {
      color: #999 !important;
    }

    .descricao {
      color: #999;
    }

    .fa-instagram {
      color: #fff;
    }

    .fa-facebook-f {
      color: #fff;
    }

    .btn-outline-blue {
      border-color: #999;
      color: #fff;
    }

    .conteudo {
      .whatsapp {
        color: #999;
      }
    }

    .tempos_entrega {
      color: #999;
    }
  }

  &.black_friday, &.black {
    background: #000;
    box-shadow: inset 0 -1px 0 #333;

    h1 {
      color: #fff;
    }

    .enderecos {
      color: #e5e5e5;
    }

    .forma_entrega {
      color: #e5e5e5;

      .endereco {
        color: #e5e5e5;
      }
    }

    .text-blue {
      color: #fff !important;
    }

    .descricao {
      color: #e5e5e5;
    }

    .fa-instagram {
      color: #fff;
    }

    .fa-facebook-f {
      color: #fff;
    }

    .btn-outline-blue {
      border-color: #fff;
      color: #fff;
    }

    .conteudo {
      .whatsapp {
        color: #fff;
      }
    }

    .tempos_entrega {
      color: #e5e5e5;
    }
  }

  &.arraia {
    background: #730101;
    box-shadow: none;

    h1 {
      color: #ffffff;
    }

    .enderecos {
      color: #ffffff;
    }

    .forma_entrega {
      color: #ffffff;

      .endereco {
        color: #ffffff;
      }
    }

    .tempos_entrega {
      color: #ffffff;
    }

    .text-blue {
      color: #ffffff !important;
    }

    .descricao {
      color: #ffffff;
    }

    .fa-instagram {
      color: #ffffff;
    }

    .fa-facebook-f {
      color: #ffffff;
    }

    .btn-outline-blue {
      border-color: #fff;
      color: #ffffff;
    }

    .conteudo {
      .whatsapp {
        color: #ffffff;
      }
    }
  }

  &.natal {
    background: #730101;
    box-shadow: none;

    h1 {
      color: #f5cb89;
    }

    .enderecos {
      color: #f5cb89;
    }

    .forma_entrega {
      color: #f5cb89;

      .endereco {
        color: #f5cb89;
      }
    }

    .tempos_entrega {
      color: #f5cb89;
    }

    .text-blue {
      color: #f5cb89 !important;
    }

    .descricao {
      color: #f5cb89;
    }

    .fa-instagram {
      color: #f5cb89;
    }

    .fa-facebook-f {
      color: #f5cb89;
    }

    .btn-outline-blue {
      border-color: #fff;
      color: #f5cb89;
    }

    .conteudo {
      .whatsapp {
        color: #f5cb89;
      }
    }
  }

  &.chinainbox{
    background: #e52f26;
    color: #fff;
    box-shadow: none !important;

    .bolinha.fechado {
      background: #FF9800 !important;
    }

    .status.fechado{
      color:  #FF9800 !important;
    }

    h1, .enderecos , .forma_entrega, .tempos_entrega,
    .text-blue,  .icone-whatsapp,  .fa-instagram ,  .fa-facebook-f{
      color: #fff !important;

      .endereco {
        color: #fff;
      }
    }

    .conteudo {
      .whatsapp {
        color: #fff;
      }
    }

    .btn-outline-blue {
      border-color: #fff;
      color: #fff;
    }


  }

  &.dia_namorados {
    background: #b264da;
    box-shadow: none;

    h1 {
      color: #fff;
    }

    .enderecos {
      color: #fff;
    }

    .forma_entrega {
      color: #fff;

      .endereco {
        color: #fff;
      }
    }

    .tempos_entrega {
      color: #fff;
    }

    .text-blue {
      color: #fff !important;
    }

    .descricao {
      color: #fff;
    }

    .fa-instagram {
      color: #fff;
    }

    .fa-facebook-f {
      color: #fff;
    }

    .btn-outline-blue {
      border-color: #fff;
      color: #fff;
    }

    .conteudo {
      .whatsapp {
        color: #fff;
      }
    }
  }

  &.dia_maes {
    background: #BD3C37;
    box-shadow: none;

    h1 {
      color: #f5cb89;
    }

    .enderecos {
      color: #f5cb89;
    }

    .forma_entrega {
      color: #f5cb89;

      .endereco {
        color: #f5cb89;
      }
    }

    .tempos_entrega {
      color: #f5cb89;
    }

    .text-blue {
      color: #f5cb89 !important;
    }

    .descricao {
      color: #f5cb89;
    }

    .fa-instagram {
      color: #f5cb89;
    }

    .fa-facebook-f {
      color: #f5cb89;
    }

    .btn-outline-blue {
      border-color: #fff;
      color: #fff;
    }

    .conteudo {
      .whatsapp {
        color: #fff;
      }
    }
  }

  &.dia_pais {
    background: #0000dd;
    box-shadow: none;

    h1 {
      color: #ffffff;
    }

    .enderecos {
      color: #ffffff;
    }

    .forma_entrega {
      color: #ffffff;

      .endereco {
        color: #ffffff;
      }
    }

    .tempos_entrega {
      color: #ffffff;
    }

    .text-blue {
      color: #ffffff !important;
    }

    .descricao {
      color: #ffffff;
    }

    .fa-instagram {
      color: #ffffff;
    }

    .fa-facebook-f {
      color: #ffffff;
    }

    .btn-outline-blue {
      border-color: #fff;
      color: #fff;
    }

    .conteudo {
      .whatsapp {
        color: #fff;
      }
    }
  }

  &.ano_novo {
    background: #030303;
    box-shadow: none;

    h1 {
      color: #f1dfa5;
    }

    .enderecos {
      color: #f1dfa5;
    }

    .forma_entrega {
      color: #f1dfa5;

      .endereco {
        color: #f1dfa5;
      }
    }

    .tempos_entrega {
      color: #f1dfa5;
    }

    .text-blue {
      color: #f1dfa5 !important;
    }

    .descricao {
      color: #f1dfa5;
    }

    .fa-instagram {
      color: #f1dfa5;
    }

    .fa-facebook-f {
      color: #f1dfa5;
    }

    .btn-outline-blue {
      border-color: #f6d888;
      color: #f1dfa5;
    }

    .conteudo {
      .whatsapp {
        color: #f1dfa5;
      }
    }
  }

  &.pascoa {
    background: #775748;
    box-shadow: none;

    h1 {
      color: #fff;
    }

    .enderecos {
      color: #fff;
    }

    .forma_entrega {
      color: #fff;

      .endereco {
        color: #fff;
      }
    }

    .tempos_entrega {
      color: #fff;
    }

    .text-blue {
      color: #fff !important;
    }

    .descricao {
      color: #fff;
    }

    .fa-instagram {
      color: #fff;
    }

    .fa-facebook-f {
      color: #fff;
    }

    .btn-outline-blue {
      border-color: #fff;
      color: #fff;
    }

    .conteudo {
      .whatsapp {
        color: #fff;
      }
    }
  }

  &.copa_2022 {
    background: #1a7200;
    box-shadow: none;

    h1 {
      color: #ffd80e;
    }

    .enderecos {
      color: #ffd80e;
    }

    .forma_entrega {
      color: #ffd80e;

      .endereco {
        color: #ffd80e;
      }
    }

    .tempos_entrega {
      color: #ffd80e;
    }

    .text-blue {
      color: #ffd80e !important;
    }

    .descricao {
      color: #ffd80e;
    }

    .fa-instagram {
      color: #ffd80e;
    }

    .fa-facebook-f {
      color: #ffd80e;
    }

    .btn-outline-blue {
      border-color: #ffd80e;
      color: #ffd80e;
    }

    .conteudo {
      .whatsapp {
        color: #ffd80e;
      }
    }
  }
}

.fidelidade-info{
  >div{
    width: 230px;
    background: #f7f8f8;
    padding: 10px;
    height: 72px;
    border-radius: 15px;
    border: 1px solid #343a4078;
    text-align: center;
  }
  p,a{
    color: #343a40
  }
  a{
    font-weight: bold;
    text-decoration: underline;
  }
  .saldo{
    color: #199d0f;
  }
}

.faixa.fidelidade{
  height: 200px;
  background: #000;
}

.saldo-info{
  border: 1px solid #cccccc5c;
  background: #f7f7f7;
  margin-left: -25px;
  padding-left: 25px;
  margin-right: -25px;
  border-left: none;
  border-right: none;
  padding-top: 5px;
  padding-bottom: 5px;
  position: relative;
  img{
    height: 35px;
    position: absolute;
    right: 15px;
    top: 15px;
  }

  &.menor{
    img{
      height: 20px;
      top: 25px;
    }
  }
}

.descricao-empresa{
  display: block;
  width: 100%;

  p{margin-bottom: 0};
  .logo-empresa{
    width: 70px;
    height: 70px;
    float: left;
    margin-right: 20px;
    display: inline-block;
    margin-top: 10px;
  }

  .info {
    right: 15px;
    max-width: 230px;
    position: relative;

    .whatsapp{
      font-size: 12px;
      font-weight: 600;
      color: #199d0f;
      padding-bottom: 5px;
      display: block;
    }

    .horario{

      padding-left: 0px;
      width: 100%!important;

      .bolinha{
        margin: 5px;
        width: 8px;
        height: 8px;
        background: #6db31b;
        border-radius: 100px;
        float: left;
      }

      .status{
        color: #6db31b;
      }

      .bolinha.fechado  {
        background: red;
      }
      .status.fechado{
        color: red;
      }


    }

    .fa-whatsapp{
      top: 3px;
      position: relative;
    }
  }
}

.nav-user .fe-user{
  top: 3px;
  position: relative;
}

h1, .h1 {
  font-size: 2rem !important;
}

@media (min-width: 1600px){
  header{
    .conteudo{
      margin: 0 auto;
    }
  }

}

@media (max-width: 1560px) {
  header{
    .logo-empresa{
      margin-left: 20px;
    }
  }
}

@media (max-width: 1250px) {
  header {
    .conteudo {
      .coluna-links {
        width: 250px;
      }
      .coluna-principal{
      }
    }
  }
}

@media (max-width: 1050px) {
  header{
    .conteudo{
      .coluna-links{
        width: 130px;
        a{
          margin-right: 25px !important;
        }
      }

      .coluna-principal{
      }

      .info{

      }

    }

  }

}

@media (max-width: 900px) {
 .descricao-empresa{
   .info{
     top:0px;
     min-width: 165px;
     text-align: center;

     .horario{
       .bolinha{
         position: relative;
         left: 20px;
       }
     }

     .descricao{
       span{
         display: block;
       }
     }
   }
 }
  a.whatsapp{
    span{ display: none}
  }
}

.div_natal {
  background: #7e0101;
  text-align: center;
  border-top: solid 2px #e0bd6c;
  border-bottom: solid 2px #e0bd6c;
}

.div_arraia {
  background: url("/assets/images/banner_arraia.png");
  height: 100px;
  text-align: center;
  border-top: solid 2px #e0bd6c;
  border-bottom: solid 2px #e0bd6c;
}

.div_dia_maes {
  background: #FB6C66;
  text-align: center;
  border-top: solid 2px #e0bd6c;
  border-bottom: solid 2px #e0bd6c;
}

.div_dia_pais {
  background: #10b3e9;
  text-align: center;
  border-top: solid 2px #10b3e9;
  border-bottom: solid 2px #10b3e9;
}

.div_dia_namorados {
  background: #d64860;
  text-align: center;
  border-top: solid 2px transparent;
  border-bottom: solid 2px transparent;
}

.div_ano_novo {
  background: #1c1c1c;
  text-align: center;
  border-top: solid 1px #f1dfa5;
  border-bottom: solid 2px #f1dfa5;
}

.div_copa_2022 {
  background: #ffd80e;
  text-align: center;
  border-top: solid 1px #ffd80e;
  border-bottom: solid 2px #ffd80e;
}

.div_cacau_show {
  background: var(--cor-fundo-site);
  text-align: center;
  padding: 0;
  margin: 0;

  img {
    max-width: 100%;
    height: auto;
  }
}

header.cacau_show {
  background-color: var(--cor-fundo-site);
  color: var(--cor-texto-primaria);

  .conteudo {
    background-color: var(--cor-fundo-elementos);
    border-bottom: 1px solid var(--cor-borda);
  }

  .descricao-empresa {
    h1 {
      color: var(--cor-texto-primaria);
      font-weight: 600;
    }

    p {
      color: var(--cor-texto-secundaria);
    }
  }

  .menu-principal {
    background-color: var(--cor-fundo-elementos);

    .item-menu {
      color: var(--cor-texto-secundaria);

      &:hover, &.active {
        color: var(--cor-destaque);
      }
    }
  }

  .busca {
    input {
      background-color: var(--cor-fundo-site);
      border: 1px solid var(--cor-borda);
      color: var(--cor-texto-primaria);
    }

    button {
      background-color: var(--cor-destaque);
      color: var(--cor-texto-botao);
    }
  }

  .carrinho-resumo {
    background-color: var(--cor-fundo-site);
    border: 1px solid var(--cor-borda);

    .total {
      color: var(--cor-texto-primaria);
    }

    .icone {
      color: var(--cor-destaque);
    }
  }

  .whatsapp {
    color: var(--cor-texto);
    &:hover {
      color: var(--cor-texto-secundaria);
    }
  }

  .horario {
    color: var(--cor-texto);
    .status {
      color: var(--cor-texto-secundaria);
      &.fechado {
        color: var(--cor-texto-secundaria);
      }
    }
  }

  .forma_entrega {
    color: var(--cor-texto);
    .endereco {
      color: var(--cor-texto-secundaria);
    }
  }

  .fidelidade-info {
    .saldo {
      color: var(--cor-texto);
    }
    a {
      color: var(--cor-texto-secundaria);
      &:hover {
        color: color-mix(in srgb, var(--cor-texto-secundaria) 80%, white 20%);
      }
    }
  }

  .btn-outline-blue {
    color: var(--cor-texto);
    border-color: var(--cor-borda);
    &:hover {
      background-color: var(--cor-botao);
      color: var(--cor-texto-botao);
    }
  }
}

::ng-deep .quiosque {
  header {
    height: 605px;
  }
}

.font11{
  font-size: 11px !important;
}
