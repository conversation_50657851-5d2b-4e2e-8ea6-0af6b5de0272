import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { TopoDesktopComponent } from './topo-desktop.component';

describe('TopoDesktopComponent', () => {
  let component: TopoDesktopComponent;
  let fixture: ComponentFixture<TopoDesktopComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ TopoDesktopComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TopoDesktopComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
