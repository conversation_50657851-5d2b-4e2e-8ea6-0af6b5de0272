<header #header  *ngIf="carregou"  class="{{empresa.tema}}">
  <div class="conteudo">
    <div *ngIf="empresa.tema === 'quiosque'">
      <img id="imagemPrincipal" name="imagemPrincipal" #imagemPrincipal src="/assets/fidelidade/alsultan-tela-totem-horizontal.jpg" style="width: 100%;"/>
    </div>

    <div class="coluna-principal" *ngIf="empresa.tema !== 'quiosque'">
      <div class="descricao-empresa ">
        <div class="row">
          <div class="col cpointer" (click)="dominiosService.vaParaHome()">
            <img class="logo-empresa " [src]="'https://promokit.com.br/images/empresa/' + empresa?.logo"/>
            <h1 class="abreviar mb-0" style="line-height: 40px;">  {{empresa.nome}}</h1>
            <p class="enderecos">
              {{empresa.descricaoEndereco ? empresa.descricaoEndereco : empresa.endereco}}
            </p>
            <div *ngIf="empresa.descricaoTempoEntrega || empresa.descricaoTempoRetirada">
                                        <span class="tempos_entrega  mt-1" *ngIf="empresa.descricaoTempoEntrega"  >
                <i class="fas fa-motorcycle mr-1 fa-lg"></i>{{empresa.descricaoTempoEntrega}}
             </span>
              <span class="tempos_entrega  mt-1" *ngIf="empresa.descricaoTempoRetirada"  >
                <i class="ml-1 fas fa-store mr-1"></i>{{empresa.descricaoTempoRetirada}}
             </span>
            </div>
          </div>
          <div class="col-auto d-flex align-items-center">
            <div class="info d-none d-lg-block" style="min-height: 45px;">
              <a class="whatsapp" [href]="'http://wa.me/55' + empresa?.numeroWhatsapp?.whatsapp"
                 [hidden]="!empresa?.numeroWhatsapp ||  empresa?.numeroWhatsapp?.ocultar">
                <i class="fab fa-whatsapp fa-2x mr-1 "></i>

                <span >
                  <ng-container *ngIf="empresa && empresa.numeroWhatsapp && empresa?.numeroWhatsapp?.whatsapp.length === 11">
                    {{empresa?.numeroWhatsapp?.whatsapp | mask: '(99) 9-9999-9999'}}
                  </ng-container>
                  <ng-container *ngIf="empresa && empresa?.numeroWhatsapp && empresa?.numeroWhatsapp?.whatsapp.length === 10">
                    {{empresa?.numeroWhatsapp?.whatsapp | mask: '(99) 9999-9999'}}
                  </ng-container>
                </span>

              </a>

              <div class="horario cpointer" (click)="verHorarios(horario)" *ngFor="let horario of empresa.horariosDoServico; let i = index;" style="display: table;">
                <div class="descricao float-left" [class.font11] = "horario.turnos">
                  <i class="{{horario.icone}} status" kendoTooltip title="{{horario.descricao}}" [ngClass]="{ 'fechado': !horario.estaAberta}" ></i>
                  <span  > {{horario.label}}:</span>
                  <span  >    {{horario.descricaoHorario}}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="col-auto d-flex align-items-center">
            <div class="d-none d-lg-block" style="max-height: 45px;" *ngIf="empresa.instagram || empresa.facebook">
              <p class="text-black-50 font-14 d-none d-xl-block" *n>NOSSAS REDES</p>
              <a [href]="'https://instagram.com/' + empresa.instagram" target="_blank" *ngIf="empresa.instagram">
                <i class="fab fa-instagram font-24"></i>&nbsp;&nbsp;
              </a>
              <a [href]="'https://facebook.com/' + empresa.facebook" target="_blank" *ngIf="empresa.facebook">
                <i class="fab fa-facebook-f font-24"></i>
              </a>
            </div>
          </div>
          <div class="forma_entrega col-auto d-flex align-items-center" *ngIf="exibirFormaDeEntrega() && pedido.entrega && pedido.entrega.formaDeEntrega">
            <div style="height: 45px;" class="cpointer" (click)="mudarFormaDeEntrega()">
              <p class="font-14">FORMA DE ENTREGA</p>
              <div class="mt-1 font-14" *ngIf="pedido.entrega.formaDeEntrega === 'Retirar'">
                <div class="ml-0 d-inline-block abreviar" style="max-width: 170px;">
                  <i class="fas fa-map-marker-alt"></i> Retirar Na Loja &nbsp;
                </div>
                <i class="fas fa-chevron-down" style="top: -5px;position: relative"></i>
              </div>

              <div class="mt-1 font-14 endereco" *ngIf="pedido.entrega.formaDeEntrega === 'Receber em casa'">
                      <div class="ml-0 d-inline-block abreviar" style="max-width: 150px;">
                       <i class="fas fa-map-marker-alt"></i> {{pedido.entrega.endereco.obtenhaEnderecoCompleto()}} &nbsp;
                      </div>
                      <i class="fas fa-chevron-down" style="top: -5px;position: relative"></i>
              </div>
            </div>
          </div>
          <div class="col-auto fidelidade-info" *ngIf="fidelidade  ">
            <div class="mt-2 "   >
                    <p>
                      <span  >{{fidelidade.descricao}}</span>
                    </p>

                  <ng-container *ngIf="!usuario">
                    <p class="saldo">Saldo 0  </p>
                    <a (click)="abraModaLogin($event)"   href="#">entrar</a>
                  </ng-container>

                  <ng-container *ngIf="usuario">
                    <p >Saldo <span class="saldo">{{usuario.saldoDescricao}}</span></p>
                    <a [routerLink]="'/loja/brindes'">resgatar brindes</a>
                  </ng-container>

            </div>
          </div>
          <div class="col-auto" *ngIf="true">
            <div class="mt-2" >
              <a class="btn btn-outline-blue float-right mr-5 mt-2" (click)="abraModaLogin($event)"  *ngIf="!usuario"> Entrar</a>

              <ul class="list-unstyled topnav-menu float-right  mt-2" *ngIf="usuario">

                <li class="dropdown notification-list" >
                  <a class="nav-link dropdown-toggle nav-user mr-0 waves-effect waves-light text-blue"
                     data-toggle="dropdown" href="#" role="button" aria-haspopup="false" aria-expanded="false" (click)="clicouDropDown()">
                    <i class="fe-user fa-2x"    alt="user-image"> </i>
                    <span class="pro-user-name ml-1">
                   {{usuario.nome ? usuario.nome?.split(' ')[0] : 'Cliente'}} <i class="k-icon k-i-chevron-down"></i>
                  </span>
                  </a>
                  <div class="dropdown-menu dropdown-menu-right profile-dropdown " style="min-width: 350px">
                    <!-- item-->
                    <div class="dropdown-header noti-title">
                      <h5 class="text-overflow m-0">Olá,
                      </h5>

                      <h5 class="text-overflow  "> <b>{{usuario.nome ? usuario.nome.split(' ')[0] : ''}}</b>
                      </h5>

                      <div *ngIf="usuario.saldoFidelidade >= 0"  class="saldo-info" [ngClass]="{'menor': empresa.idRede === 2}">
                        <a (click)="verDetalhesSaldo(usuario)"  href="" >
                          <h5 class="">
                            <ng-container *ngIf="!usuario.atualizarCadastro &&  !usuario.fazerOptinUsarSaldo && !usuario.fazerOptin ">
                              {{usuario.idCartao ? 'Saldo' : 'Cashback'}}
                              <span class="font-18"><b> {{usuario.saldoDescricao}}</b></span>

                              <span class="ml-1" style="    top: -4px; position: relative">
                                 <i class="k-icon k-i-reload   cpointer"  kendoTooltip title="atualizar saldo"
                                  (click)="recarregueSaldo(usuario, $event)" [hidden]="carregandoSaldo" *ngIf="!usuario.erroFidelidade"></i>

                                  <i class="k-icon k-i-loading" *ngIf="carregandoSaldo"></i>

                                 <i  class="fas fa-exclamation-triangle text-warning"
                                     *ngIf="usuario.erroFidelidade" kendoTooltip title="{{usuario.erroFidelidade}}" > </i>
                              </span>

                              <button class="btn btn-blue btn-sm btn-small ml-1" *ngIf="usuario.idCartao">extrato</button>
                            </ng-container>

                            <span class="text-primary"  *ngIf="usuario.atualizarCadastro"><b>Atualize seu cadastro</b></span>
                            <span class="text-primary"  *ngIf="usuario.fazerOptinUsarSaldo || usuario.fazerOptin"><b>Quero ganhar cashback</b></span>

                            <small class="text-muted mt-1 d-block"> {{usuario.descricaoProgramaFidelidade}}</small>

                            <img src="{{empresa.integracaoFidelidade?.logo}}" class="ml-2"
                                 *ngIf="empresa.integracaoFidelidade?.logo">

                          </h5>
                        </a>
                      </div>

                    </div>

                    <!-- item-->
                    <a  routerLink="/loja/meusPedidos" class="dropdown-item notify-item">
                      <i class="fe-shopping-bag fa-lg"></i>
                      <span> Pedidos </span>
                    </a>
                    <div class="dropdown-divider"></div>
                    <a  routerLink="/loja/meus-dados" class="dropdown-item notify-item">
                      <i class="fe-settings fa-lg"></i>
                      <span>Meu dados</span>
                    </a>
                    <div class="dropdown-divider"></div>

                    <!-- item-->
                    <a   (click)="logout($event)" href="" class="dropdown-item notify-item">
                      <i class="fe-log-out fa-lg"></i>
                      <span>Sair</span>
                    </a>

                  </div>
                </li>

              </ul>

            </div>
          </div>
        </div>
      </div>


    </div>


  </div>


</header>


<div style="background: #593F33;text-align: center;" *ngIf="empresa.tema && empresa.tema === 'pascoa'">
  <img src="/assets/images/banner_pascoa.png"/>
</div>
<div style="background: #000015;text-align: center;" *ngIf="empresa.tema && empresa.tema === 'black_friday'">
<img src="/assets/images/banner-blackfriday.jpg"/>
</div>
<div style="background: #000015;text-align: center;" *ngIf="empresa.tema && empresa.tema === 'black_friday_2022'">
  <img src="/assets/images/banner_blackfriday2022.jpg"/>
</div>

<div style="background: #fde436;text-align: center;" *ngIf="empresa.tema && empresa.tema === 'carnaval'">
  <img src="/assets/images/banner_carnaval_2025.jpeg"/>
</div>

<div class="div_natal" *ngIf="empresa.tema && empresa.tema === 'natal'">
  <img src="/assets/images/banner_natal.png"/>
</div>
<div class="div_ano_novo" *ngIf="empresa.tema && empresa.tema === 'ano_novo'">
  <img src="/assets/images/banner_ano_novo.png?v=4"/>
</div>
<div class="div_dia_maes" *ngIf="empresa.tema && empresa.tema === 'dia_maes'">
  <img src="/assets/images/banner_dia_maes.png"/>
</div>
<div class="div_dia_pais" *ngIf="empresa.tema && empresa.tema === 'dia_pais'">
  <img src="/assets/images/banner_dia_dos_pais.png"/>
</div>
<div class="div_arraia" *ngIf="empresa.tema && empresa.tema === 'arraia'">
</div>
<div class="div_dia_namorados" *ngIf="empresa.tema && empresa.tema === 'dia_namorados'">
  <img src="/assets/images/banner_dia_namorados.png?v=1"/>
</div>
<div class="div_copa_2022" *ngIf="empresa.tema && empresa.tema === 'copa_2022'">
  <img src="/assets/images/banner_copa_2022.png"/>
</div>

<div class="div_cacau_show" *ngIf="empresa.tema && empresa.tema === 'cacau_show'">
  <img src="/assets/images/banner_cacau_show.png"/>
</div>
