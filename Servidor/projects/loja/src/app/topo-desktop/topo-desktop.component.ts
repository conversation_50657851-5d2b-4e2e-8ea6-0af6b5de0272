import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {ConstantsService} from "../../services/ConstantsService";
import {AutorizacaoLojaService} from "../../services/autorizacao-loja.service";
import {DominiosService} from "../../services/dominios.service";
import {CarrinhoService} from "../../services/carrinho.service";
import {PedidoLoja} from "../../objeto/PedidoLoja";
import {SiteProdutosComponent} from "../../produtos/site-produtos.component";
import {ActivatedRoute, Router} from "@angular/router";
import {DialogService} from "@progress/kendo-angular-dialog";
import {Location} from "@angular/common";
import {ClienteService} from "../../services/cliente.service";
import {PainelLoginComponent} from "../painel-login/painel-login.component";
import {CadContatoLojaComponent} from "../cad-contato/cad-contato-loja.component";
import {PainelHorariosComponent} from "../painel-horarios/painel-horarios.component";

@Component({
  selector: 'app-topo-desktop',
  templateUrl: './topo-desktop.component.html',
  styleUrls: ['./topo-desktop.component.scss']
})
export class TopoDesktopComponent implements OnInit {
  @ViewChild("header") header: ElementRef;
  empresa: any = {};
  usuario: any;
  carregou = false;
  pedido: PedidoLoja;
  formaReceberEmCasa: any = {};
  carregandoSaldo = false;
  fidelidade: any;
  constructor(private constantsService: ConstantsService, public dominiosService: DominiosService,
              private carrinhoService: CarrinhoService, private autorizacao: AutorizacaoLojaService,
              private clienteService: ClienteService,
              private router: Router, private location: Location, private activatedRoute: ActivatedRoute,
              private dialogService: DialogService) {
   this.carregaDados();

   this.pedido = this.carrinhoService.obtenhaPedido();
  }

  ngOnInit(): void {
    this.constantsService.empresa$.subscribe( (data) => {
      if( !data ) return;

      this.carregou = true;
      this.empresa  = data;

      this.formaReceberEmCasa = this.obtenhaFormaDeEntregaReceberEmCasa(this.empresa);
    });

    this.autorizacao.usuario$.subscribe( (usuario) => {
      if(!usuario) return;

      if(!this.usuario)  this.carregaDados()
    });

  }



  carregaDados(){
    this.autorizacao.estaLogado().then( (resposta: any) => {
      if(resposta.logado){
        this.usuario = this.autorizacao.getUsuario();
        /* if(this.empresa.integracaoPedidoFidelidade){
          this.clienteService.obtenhaSaldoCartao(this.empresa.integracaoPedidoFidelidade.plano).then( resp => {
          })
        } */
      }
    })
  }

  obtenhaAltura(){
    return this.header.nativeElement.offsetHeight;
  }

  logout(event) {
    event.stopPropagation();
    event.preventDefault();
    this.autorizacao.logout().then( (erro) => {
      if(!erro){
        delete  this.usuario;
        this.carrinhoService.limpeContatoPedido();
        this.dominiosService.vaParaHome();
      } else {
        alert(erro)
      }
    })
  }

  exibirFormaDeEntrega() {
    return this.formaReceberEmCasa && this.formaReceberEmCasa.perguntarEnderecoInicio;
  }

  mudarFormaDeEntrega() {
    SiteProdutosComponent.abraPedindoEndereco(null, this.router, this.location, this.activatedRoute,
      this.dialogService, false);
  }

  obtenhaFormaDeEntregaReceberEmCasa(empresa: any) {
    return empresa.formasDeEntrega.find( (forma) => {
      return forma.formaDeEntrega.nome === 'Receber em casa'
    });
  }

  verDetalhesSaldo(usuario: any) {
    if(usuario.atualizarCadastro || usuario.fazerOptinUsarSaldo || usuario.fazerOptin){
      CadContatoLojaComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
        this.dialogService, usuario, (result) => {
          if(result && result.id){  }
        }) ;
    } else {
      if(usuario.idCartao)
        window.open(String(`/cliente/${usuario.telefone}/extrato/${usuario.token}`))
    }

    return false;

  }

  async recarregueSaldo(usuario: any, event: any = null){
    if(event){
      event.stopPropagation();
      event.preventDefault();
    }

    this.carregandoSaldo = true;
    await this.autorizacao.atualizeSaldoFidelidade(usuario);
    this.carregandoSaldo = false;
  }

  async clicouDropDown(){
    if(localStorage.recarregarSaldo){
      await this.recarregueSaldo(this.usuario);
      delete localStorage.recarregarSaldo
    }
  }

  abraModaLogin(event) {
    PainelLoginComponent.abraComoPopup(this.router, this.location, this.activatedRoute,  this.dialogService );
    return false;
  }


  verHorarios(horarioServico: any){
    PainelHorariosComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
      this.dialogService, this.empresa, horarioServico)
  }
}
