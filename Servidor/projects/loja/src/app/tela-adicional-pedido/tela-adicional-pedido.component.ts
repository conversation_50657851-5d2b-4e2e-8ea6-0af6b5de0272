import {Component, OnInit, QueryList, ViewChildren} from '@angular/core';
import {AdicionalUtils} from "../../objeto/AdicionalUtils";
import {PedidosService} from "../../services/pedidos.service";
import {PedidoLoja} from "../../objeto/PedidoLoja";
import {ConstantsService} from "../../services/ConstantsService";
import {SiteCampoAdicionalComponent} from "../site-campo-adicional/site-campo-adicional.component";

@Component({
  selector: 'app-tela-adicional-pedido',
  templateUrl: './tela-adicional-pedido.component.html',
  styleUrls: ['./tela-adicional-pedido.component.scss']
})
export class TelaAdicionalPedidoComponent implements OnInit {
  window: any;
  camposAdicionais = [];
  pedido: PedidoLoja;
  erro: any;
  @ViewChildren('adicionalComponent') ctrlAdicionais: QueryList<SiteCampoAdicionalComponent>;

  constructor(private pedidosService: PedidosService, private constantsService: ConstantsService) {
  }

  ngOnInit(): void {
  }

  inicialize(windowRef: any, pedido: PedidoLoja) {
    this.window = windowRef;
    this.pedido = pedido;

    this.constantsService.empresa$.subscribe( (empresa) => {
      if( !empresa ) { return; }

      this.pedidosService.obtenhaAdicionaisPedido(empresa).then( (adicionais) => {
        this.camposAdicionais = adicionais;
        this.pedido.adicionais = {};
        this.pedido.camposAdicionais = adicionais;

        AdicionalUtils.prepareAdicionais(this.pedido, adicionais);
      });
    });
  }

  fecheTela() {
    this.window.close();
  }

  finalizar() {
    if( this.pedido.camposAdicionais ) {
      for( let i = 0; i < this.pedido.camposAdicionais.length; i++ ) {
        const campoAdicional = this.pedido.camposAdicionais[i];

        if(!this.pedido.valideCampoAdicional(campoAdicional)) {
          this.ctrlAdicionais.toArray()[i].exibaErro('Complemento é obrigatório');
          this.posicioneNoComplemento(campoAdicional);
          return
        }
      }
    }

    this.window.close(true);
  }

  posicioneNoComplemento(campoAdicional) {
    /*
    const $controleAdicional = document.getElementById('adicional_' + campoAdicional.id);

    if( !$controleAdicional ) {
      return;
    }

    const topo = $controleAdicional.offsetTop - 10;

    document.querySelector('.k-dialog-content ').scrollTo(0, topo);
     */
  }

  desmarcouNovaOpcao(opcao: any){
    this.ctrlAdicionais.forEach((ctlAdicioanal: SiteCampoAdicionalComponent) => {
      ctlAdicioanal.desmarcouOpcaoQueDepende(opcao)
    });
  }


  escolheuNovaOpcao(opcao: any){
    if(this.ctrlAdicionais){
      this.ctrlAdicionais.forEach((ctlAdicioanal: SiteCampoAdicionalComponent) => {
        ctlAdicioanal.exibaOuEscondaOpcaoQueDepende(opcao)
      });
    }
  }
}
