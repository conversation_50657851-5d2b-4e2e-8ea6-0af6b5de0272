import {
  ApplicationRef,
  Component, ElementRef,
  EventEmitter,
  Inject, Input,
  NgZone,
  OnInit,
  Output,
  Renderer2,
  ViewChild
} from '@angular/core';
import {DOCUMENT} from "@angular/common";
import {NgForm} from "@angular/forms";
import {PedidoLoja} from "../../objeto/PedidoLoja";
import {DadosCartao} from "../../objeto/DadosCartao";
import {FormUtils} from "../../objeto/FormUtils";
import {ICartaoCreditoGateway} from "../../objeto/ICartaoCreditoGateway";
import {IFormCartaoRespostaValido} from "../../objeto/IFormCartaoRespostaValido";
import {PagseguroService} from "../../services/pagseguro.service";

declare let PagSeguroDirectPayment;

@Component({
  // tslint:disable-next-line:component-selector
  selector: 'cad-cartaocredito-pag-seguro',
  templateUrl: './cad-cartaocredito-pag-seguro.component.html',
  styleUrls: ['./cad-cartaocredito-pag-seguro.component.scss']
})
export class CadCartaocreditoPagSeguroComponent implements OnInit, ICartaoCreditoGateway {
  @ViewChild('frm', { static: false})  frm: NgForm;
  @ViewChild('frm', { read: ElementRef }) public frmElement: ElementRef;
  @Input() parcelamento: any;
  @Input() pedido: PedidoLoja;
  @Output() onCriou = new EventEmitter();
  empresa: any;
  processando: any;
  mensagemSucesso: any;
  mensagemErro: any;
  cartao: any = new DadosCartao();
  carregandoScript = true;
  bandeira: any = null;
  definiuSessaoPagSeguro = false;
  parcelas: any;
  msgErro: string;
  tentativas = 1;
  erroInicializacao: string;
  constructor(private _renderer2: Renderer2, private ngZone: NgZone,  private pagSeguroService: PagseguroService,
              private app: ApplicationRef,
              @Inject(DOCUMENT) private _document: Document, private _ngZone: NgZone) {
    this.cartao.numero = '';
    this.cartao.cvv = '';
    this.cartao.nome = '';
    this.cartao.cpf = '';
    this.cartao.validade = null;
    this.cartao.dataNascimento = null;
  }

  ngOnInit() {
   this.addScriptPagseguro();
   this.inicializePagSeguro();
  }

  private addScriptPagseguro() {
    let script = this._renderer2.createElement('script');
    script.type = 'text/javascript';
    if(window['pagseguroprod']){
      script.src =  'https://stc.pagseguro.uol.com.br/pagseguro/api/v2/checkout/pagseguro.directpayment.js';
    } else {
      script.src =  'https://stc.sandbox.pagseguro.uol.com.br/pagseguro/api/v2/checkout/pagseguro.directpayment.js';
    }

    this._renderer2.appendChild(this._document.body, script);
  }


  inicializePagSeguro(){
    if(typeof PagSeguroDirectPayment === 'object'){
      this.carregandoScript = false;
      this.crieSessaoPagSeguro();
    } else {
      if(this.tentativas <= 10){
        this.tentativas++;
        setTimeout(() => {
          this.inicializePagSeguro();
        }, 1000)
      } else {
        this.erroInicializacao =  'Não foi possivel carreguar os scripts do PagSeguro'
      }
    }
  }

  canceleProcessando(){
    this._ngZone.run(() => {
      this.processando = false;
    });

  }

  fecheMensagemSucesso() {
    delete this.mensagemSucesso;
  }

  fecheMensagemErro() {
    delete this.mensagemErro;
  }

  alterouNumeroCartao() {
    if( this.cartao.numero.length <= 6 || !this.definiuSessaoPagSeguro ) {
      return;
    }

    PagSeguroDirectPayment.getBrand({
      cardBin: this.cartao.numero.substr(0, 6),
      success: (response) => {
        //bandeira encontrada
        this.bandeira = response;

        this.calculeParcelas().then( (parcelas) => {
          this.parcelas = parcelas;
        }).catch( (erro: Error) => {
        });
      },
      error: (response) => {
        //tratamento do erro
        console.log(response);
      },
      complete: (response) => {
        //tratamento comum para todas chamadas
      }
    });
  }

  ativeAssinatura() {

  }

  obtenhaBandeira() {
    if( !this.bandeira ) { return null; }

    return this.bandeira.brand.name;
  }

  ehValido(): IFormCartaoRespostaValido {
    (this.frm as {submitted: boolean}).submitted = true;

    const ctrlInvalido = FormUtils.obtenhaControleInvalido(this.frm, this.frmElement);

    if( ctrlInvalido ) {
      return {
        valido: false,
        controle: ctrlInvalido
      }
    }

    return {
      valido: this.frm.valid,
      controle: null
    };
  }

  crieTokenCartao(enderecoCobranca: any): Promise<DadosCartao> {
    const mesValidade = (this.cartao.validade.getMonth() + 1).toString().padStart(2, '0');

    return new Promise((resolve, reject ) => {
      PagSeguroDirectPayment.createCardToken({
        cardNumber: this.cartao.numero, // Número do cartão de crédito
        brand: this.obtenhaBandeira(), // Bandeira do cartão
        cvv: this.cartao.cvv, // CVV do cartão
        expirationMonth: mesValidade, // Mês da expiração do cartão
        expirationYear: this.cartao.validade.getFullYear(), // Ano da expiração do cartão, é necessário os 4 dígitos.
        success: (response: any) => {
          this.ngZone.run( () => {
            this.setTokenCartao(response.card.token);
            resolve(this.cartao)
          });
        },
        error: (response: any) => {
          this.ngZone.run( () => {
            if (response.errors[30400]) {
              reject('Dados do cartão de crédito estão incorretos')
            } else {
              reject(Object.values(response.errors).join(" "))
            }
          });
        }
      });
    })

  }

  calculeParcelas() {
    return new Promise( (resolve, reject) => {
      PagSeguroDirectPayment.getInstallments({
        amount: this.pedido.total,
        maxInstallmentNoInterest: 1,
        brand: this.obtenhaBandeira(),
        success: (response) => {
          // Retorna as opções de parcelamento disponíveis
          resolve(response);
        },
        error: (response) => {
          // callback para chamadas que falharam.
          reject(response);
        },
        complete: (response) => {
          // Callback para todas chamadas.
        }
      });
    });
  }

  setTokenCartao(token: string) {
    this.cartao.senderHash = PagSeguroDirectPayment.getSenderHash();
    this.cartao.token = token;
  }

  crieSessaoPagSeguro(){
    this.pagSeguroService.crieSessao().then( (idSessao: any) => {
      if(idSessao){
        try{
          PagSeguroDirectPayment.setSessionId(idSessao);
          PagSeguroDirectPayment.getPaymentMethods({
            amount: this.pedido.total,
            success: (response: any) => {
              this.definiuSessaoPagSeguro = true;
              this.app.tick();
            },
            error:  (response) => {
              this.erroInicializacao = 'Erro ao criar sessão de pagamento com PagSeguro'
              if(response && Object.keys(response.errors).length){
                Object.keys(response.errors).forEach( key => {
                  this.erroInicializacao =  String(`${this.msgErro}: ${response.errors[key]}`)
                })
              }
              this.app.tick();
              // Callback para chamadas que falharam.
            }
          });
        } catch (err){
          console.error(err)
          this.app.tick();
        }

      } else {
        this.erroInicializacao = 'Não foi possível criar sessão de pagamento.'
        this.app.tick();
      }

    }).catch( erro => {
      console.log(erro)
      this.erroInicializacao = 'Não foi possível criar sessão de pagamento.'
      this.app.tick();
    });

    PagSeguroDirectPayment.onSenderHashReady((response) => {
      if(response && response.status === 'error') {
        this.msgErro = response.message;
        this.app.tick();
        return false;
      }
    });

    if(this.parcelamento){
      this.parcelamento.parcelas = []
      for(let i = 1; i <= this.parcelamento.numeroParcelas; i++){
        let valor: any = this.parcelamento.total / i ;

        valor = valor.toFixed(2).replace('.', ',')

        this.parcelamento.parcelas.push( { numeroParcelas: i, descricao: String(`${i}x de R$ ${valor} sem juros`)});
      }
    }
  }

  exibaCartao(dadosCartao: DadosCartao) {
    this.cartao = new DadosCartao();

    Object.assign(this.cartao, dadosCartao);
  }
}
