import {
  Component,
  ElementRef,
  EventEmitter, HostListener,
  Inject,
  Input,
  NgZone,
  OnInit,
  Output,
  Renderer2,
  ViewChild
} from '@angular/core';
import {ICartaoCreditoGateway} from "../../objeto/ICartaoCreditoGateway";
import {NgForm} from "@angular/forms";
import {PedidoLoja} from "../../objeto/PedidoLoja";
import {DadosCartao} from "../../objeto/DadosCartao";
import {DOCUMENT} from "@angular/common";
import {IFormCartaoRespostaValido} from "../../objeto/IFormCartaoRespostaValido";
import {FormUtils} from "../../objeto/FormUtils";
import {HttpClient} from "@angular/common/http";


@Component({
  selector: 'app-cad-cartao-cielo',
  templateUrl: './cad-cartao-cielo.component.html',
  styleUrls: ['./cad-cartao-cielo.component.scss']
})
export class CadCartaoCieloComponent implements OnInit, ICartaoCreditoGateway {
  @ViewChild('frm', {static: true}) frm: NgForm;
  @ViewChild('frm', {read: ElementRef}) public frmElement: ElementRef;
  @Input() parcelamento: any;
  @Input() pedido: any;
  // tslint:disable-next-line:no-output-on-prefix
  @Output() onCriou = new EventEmitter();
  empresa: any;
  processando: any;
  mensagemSucesso: any;
  mensagemErro: any;
  cartao: any = new DadosCartao();
  bandeira: any = null;
  definiuSessaoPagSeguro = false;
  parcelas: any;
  msgErro: string
  paymentMethod
  timerVerificar
  carregandoScript: boolean;
  token: string;
  host: string;
  inicializouMdi3ds: boolean;
  autenticacao3ds: any;
  erroAutenticacao3ds: string;
  enderecoCobranca: any;
  enderecoEntrega: any;
  contato: any = {};
  constructor(private _renderer2: Renderer2, private ngZone: NgZone,
              protected http: HttpClient,
              @Inject(DOCUMENT) private _document: Document, private _ngZone: NgZone) {
    this.cartao.numero = '';
    this.cartao.cvv = '';
    this.cartao.nome = '';
    this.cartao.cpf = '';
    this.cartao.email = '';
    this.cartao.validade = null;
    this.cartao.dataNascimento = null;
    this.host = window.location.origin;
  }

  ngOnInit(): void {
    this.gereTokenMpi();

    this.contato =  this.pedido.contato || (this.pedido as any).cliente;
  }

  onCarregou3ds(alvo: any) {
     this.inicializouMdi3ds = true;
  }

  onAuntenticou3ds($event: any){
     this.autenticacao3ds = $event.detail;
    //Cavv, Xid, Eci, Version, ReferenceId
  }

  onFalhouAutenticou3ds($event: any){

    if(!this.inicializouMdi3ds){
      this.erroAutenticacao3ds = 'Não foi possivel iniciar processo de autenticação do cartão'
      if($event.ReturnCode){
        this.erroAutenticacao3ds = String(`${ this.erroAutenticacao3ds}: ${$event.ReturnCode} - ${$event.ReturnMessage}`)
      }

    } else {
      this.erroAutenticacao3ds =
        ($event.detail && $event.detail.erro) ? $event.detail.erro : 'Não foi possível fazer autenticação do cartão';

    }
  }

  gereTokenMpi(){
    this.http.post('/cielo/mpi/token', {}).toPromise().then( (res: any) => {
      this.token = res.data.token;
      localStorage.setItem('EnvironmentMpi',  res.data.environment);
      this.pedido.guid = res.data.guid;
      this.addScriptConfigMpi();
      this.addScriptMpi();
    }).catch((fail: any) => {
        console.error(fail)
        alert(fail)
    })
  }

  private addScriptConfigMpi() {
      let script = this._renderer2.createElement('script');
      script.type = 'text/javascript';
      script.src =  '/assets/js/mpi.config.js?v=1';
      this._renderer2.appendChild(this._document.body, script);
  }

  private addScriptMpi() {
    let script = this._renderer2.createElement('script');
    script.type = 'text/javascript';
    script.src =  '/assets/js/BP.Mpi.3ds20.min.js';
    this._renderer2.appendChild(this._document.body, script);

  }

  ehValido(): IFormCartaoRespostaValido {
    (this.frm as { submitted: boolean }).submitted = true;

    const ctrlInvalido = FormUtils.obtenhaControleInvalido(this.frm, this.frmElement);

    if (ctrlInvalido) {
      return {
        valido: false,
        controle: ctrlInvalido
      }
    }

    return {
      valido: this.frm.valid,
      controle: null
    };
  }

  crieTokenCartao(enderecoCobranca: any): any {
    this.cartao.mes =   (this.cartao.validade.getMonth() + 1).toString().padStart(2, '0');
    this.cartao.ano =  this.cartao.validade.getFullYear().toString();
    this.enderecoCobranca = enderecoCobranca;
    this.enderecoEntrega = this.pedido.entrega ? this.pedido.entrega.endereco : (this.pedido as any).endereco;

    delete this.erroAutenticacao3ds;
    this.autenticacao3ds = false;

    return new Promise( async (resolve, reject) => {
      let erroBandeira = await this.valideBin();

      if(erroBandeira)  reject(erroBandeira);

      if(!this.inicializouMdi3ds)
         await this.aguardeInicializacao3ds();

      setTimeout(() => {
        (window as any).bpmpi_authenticate();
      }, 100);

      let cartaoValidado: any =
        await this.gereTokenCartao().catch((erro) => reject(erro));

      await this.aguardeRetorno3ds();

      if(this.erroAutenticacao3ds)
        return reject(this.erroAutenticacao3ds);

      if(cartaoValidado){
        this.cartao.autenticacao3ds = this.autenticacao3ds;
        resolve(this.cartao)
      }
    })
  }

  valideBin(): Promise<any>{
    return new Promise((resolve, reject ) => {
      let bin = this.cartao.numero.substr(0, 6);

      this.http.get(String(`https://api.pagar.me/bin/v1/${bin}?appId=pk_test_BQ0NVj8C47hbrNL5`), {})
        .toPromise().then( (resbandeira: any) => {

        this.cartao.bandeira = resbandeira.brandName;

        resolve(null)

      }).catch((fail: any) => {
        let erro = 'Cartão inválido';
        if(fail.error && fail.error.errors && fail.error.errors['request.card'])
          erro += ":" +   fail.error.errors['request.card'][0]
        resolve(erro)
      } )
    });
  }

  gereTokenCartao(): Promise<any>{
    return new Promise((resolve, reject ) => {
      let dadosCartao: any = {
        holder: this.cartao.nome,
        validade: this.cartao.validade,
        numero: this.cartao.numero,
        bandeira:  this.cartao.bandeira,
        codigoSeguranca: this.cartao.cvv
      }

      if( (this.pedido as any).cliente){  //dtoPedidoServer
        dadosCartao.nome = (this.pedido as any).cliente.nome;
        dadosCartao.formaPagamento =  (this.pedido as any).pagamentos[0].formaDePagamento.id
      } else {
        dadosCartao.nome = this.pedido.contato.nome;
        dadosCartao.formaPagamento = this.pedido.pagamento.formaDePagamento.id
      }

      this.http.post('/cielo/cartao/token', dadosCartao).toPromise().then( (res: any) => {
        if(res.erro)  return reject(res.erro)
        this.cartao.token = res.data;
        resolve(this.cartao)
      }).catch(() => {
        reject('Não foi possível processar cartão')
      });
    })
  }

  async aguarde(tempo){
    return new Promise( async (resolve) => {
        setTimeout(() => {
           resolve('');
        }, tempo)
    });
  }

  async aguardeInicializacao3ds(count: number = 1){
    return new Promise( async (resolve) => {
      console.log('verificando inicializou 3ds:' + count);
      count = count + 1;
      if(!this.inicializouMdi3ds){
        await this.aguarde(1000);
        await this.aguardeInicializacao3ds(count);
      }
      resolve(true);
    });
  }

  async aguardeRetorno3ds(count: number = 1){
    return new Promise( async (resolve) => {
      console.log('verificando retorno 3ds:' + count);
      count = count + 1;
      if(!this.autenticacao3ds && !this.erroAutenticacao3ds){
        await this.aguarde(1000);
        await this.aguardeRetorno3ds(count);
      }
      resolve(true);
    });
  }



  exibaCartao(dadosCartao: DadosCartao) {
    this.cartao = new DadosCartao();

    Object.assign(this.cartao, dadosCartao);
  }

  alterouNumeroCartao() {

  }

  fecheMensagemSucesso() {
    delete this.mensagemSucesso;
  }

  fecheMensagemErro() {
    delete this.mensagemErro;
  }

  obtenhaEndereco1(endereco: any) {
    //Av Marechal Camara 160
    let rua = endereco.logradouro || '';
    let numero = endereco.numero || '';

    return String(`${rua} ${numero}`).trim()

  }
  obtenhaEndereco2(endereco: any) {
    //Sala 934 Centro
    let complemento = endereco.complemento || '';
    let bairro = endereco.bairro || '';

    return String(`${complemento} ${bairro}`).trim()

  }

  obtenhaValorUnitario(item: any) {
    if(item.valor)
      return this.convertaCentavos(item.valor) * 100

    return  this.convertaCentavos(Number((item.total / item.qtde).toFixed(2)));
  }

  convertaCentavos(numero: number){
    const totalPedido =  this.pedido.totalPagar != null ? this.pedido.totalPagar : this.pedido.obtenhaValorAhPagar() ;

    return Number((totalPedido * 100).toFixed(0));
  }
}
