import {AfterViewInit, Component, OnInit, ViewChild} from '@angular/core';
import {SiteProdutoComponent} from "../../../site-produto/site-produto.component";
import {SiteMontarpizzaComponent} from "../../../site-montarpizza/site-montarpizza.component";
import {ActivatedRoute, Router} from "@angular/router";
import {Location} from "@angular/common";
import {MyDetectorDevice} from "../../shared/MyDetectorDevice";
import {DialogRef, DialogService} from "@progress/kendo-angular-dialog";
import {StepperComponent} from "@progress/kendo-angular-layout";
import {ITela} from "../../../objeto/ITela";
import {string} from "blockly/core/utils";
import {CarrinhoComponent} from "../../../carrinho/carrinho.component";
import {LojaCarrinhoComponent} from "../../../loja-carrinho/loja-carrinho.component";
import {CarrinhoService} from "../../../services/carrinho.service";

declare var _;

@Component({
  selector: 'app-site-wizard-produtos-ia',
  templateUrl: './site-wizard-produtos-ia.component.html',
  styleUrls: ['./site-wizard-produtos-ia.component.scss']
})
export class SiteWizardProdutosIaComponent implements OnInit, ITela, AfterViewInit {
  ids: string;
  categorias: Array<any> = [];
  produtos: Array<any>;
  isMobile: boolean;
  window: DialogRef;
  novasCategorias: any[] = [];
  produtosPorCategoria;
  exibirCarrinho = false;

  todos: any[] = []

  posicao = 0;

  //pegar referencia ao #wizard
  @ViewChild('wizard') wizard: StepperComponent;
  @ViewChild('carrinho') carrinho: CarrinhoComponent; //apenas a faixa
  @ViewChild('telaCarrinho', {static: false}) telaCarrinho: LojaCarrinhoComponent;

  temProdutosNoCarrinho: boolean = false;

  alturaPopup: string = 'calc(100vh - 100px)';

  constructor(private router: Router, private location: Location, private detectorDevice: MyDetectorDevice,
              private dialogService: DialogService, private activatedRoute: ActivatedRoute,
              private carrinhoService: CarrinhoService) {
    this.isMobile = this.detectorDevice.isMobile();
  }

  ngOnInit(): void {
    const pedido = this.carrinhoService.obtenhaPedido();
    this.temProdutosNoCarrinho = pedido.qtde > 0;

    if( this.temProdutosNoCarrinho ) {
      this.alturaPopup = 'calc(100vh - 265px)';
    } else {
      this.alturaPopup = 'calc(100vh - 215px)';
    }

    this.carrinhoService.alterouPedido.subscribe( (novoPedido) => {
      const pedido = this.carrinhoService.obtenhaPedido();
      this.temProdutosNoCarrinho = pedido.qtde > 0;

      if( this.temProdutosNoCarrinho ) {
        this.alturaPopup = 'calc(100vh - 265px)';
      } else {
        this.alturaPopup = 'calc(100vh - 215px)';
      }
    });
  }

  processe() {
    const items = this.ids.split('|');

    this.novasCategorias = [];

    let produtosEncontrados = []

    items.forEach((item) => {
      // Dividir cada item pela ":" para separar o nome do produto e os IDs
      const [product, idsStr] = item.split(':');

      // Dividir os IDs pela "," para separar os IDs
      const ids = idsStr.split(',');

      const categoriaEncontrados = {
        id: ids.join(':'),
        nome: product,
        destaque: true,
        ia: true
      };

      produtosEncontrados = ids.map((id) => {
        return this.produtos.find((produto => produto.id.toString() === id));
      }).filter((produto) => produto != null);

      produtosEncontrados.forEach(produtoEncontrado => {
        const index = this.produtos.indexOf(produtoEncontrado);
        if (index !== -1) {
          this.produtos.splice(index, 1);
        }
      });

      this.produtos.push(...produtosEncontrados);

      this.novasCategorias.push(categoriaEncontrados);

      produtosEncontrados.forEach((produto) => {
        produto.categoria = categoriaEncontrados;
      });

      this.todos.push(...produtosEncontrados);
    });

    this.produtosPorCategoria = _.groupBy(this.produtos, produto => produto.categoria ? produto.categoria?.nome?.trim() : 'Outros')
  }

  abraDetalhesProduto(produto: any, indiceProduto: any = null) {
    if(!produto.montar){
      SiteProdutoComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
        this.dialogService, this.isMobile, produto, indiceProduto);
    } else {
       SiteMontarpizzaComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
        this.dialogService, this.isMobile, produto, indiceProduto);
    }
  }

  fecheTela() {
    this.window.close();
  }

  proximo() {
    this.posicao ++;

    if( this.posicao >= this.novasCategorias.length ) {
      this.posicao = this.novasCategorias.length - 1;
    }
  }

  anterior() {
    this.posicao --;

    if( this.posicao < 0 ) {
      return this.posicao = 0;
    }
  }

  deveExibirBannerTema() {
    return false;
  }

  deveExibirMenu() {
    return false;
  }

  deveExibirTopo() {
    return false;
  }

  deveTerBordas() {
    return false;
  }

  ngAfterViewInit(): void {
    this.carrinho.verifiqueDeveExibir();
  }

  abrirCarrinho() {
    this.exibirCarrinho = true;

    setTimeout( () => {
      this.telaCarrinho.setModoPanel();
    }, 0);
  }

  voltar() {
    this.exibirCarrinho = false;

    setTimeout( () => {
    this.carrinho.verifiqueDeveExibir();
    }, 0);
  }
}
