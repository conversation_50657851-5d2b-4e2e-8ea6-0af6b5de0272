<ng-container *ngIf="exibirCarrinho">
  <div class="pl-2 pr-2">
    <app-header-tela [titulo]="'Se<PERSON> Carr<PERSON>ho'" (clicouVoltar)="voltar()"></app-header-tela>
    <app-loja-carrinho #telaCarrinho></app-loja-carrinho>
  </div>
</ng-container>

<ng-container *ngIf="!exibirCarrinho">
  <div class="pl-2">
    <app-header-tela [titulo]="'Produtos baseado na sua mensagem...'" [exibirFechar]="true" (fechou)="fecheTela()" ></app-header-tela>
  </div>

  <div style="overflow-y: scroll;width: 100%;" class="pb-3" [style.height]="alturaPopup">
  <h5 class="mt-3 text-muted pl-2">Selecionamos os produtos de acordo com sua mensagem</h5>

  <ng-container  *ngFor="let categoria of novasCategorias">
    <div class="mb-2 secaoCategoria" #secaoCategoria style="position: relative;" *ngIf="!categoria.ocultar && produtosPorCategoria[categoria.nome] && produtosPorCategoria[categoria.nome].length > 0" [ngClass]="{'categoria_ia': categoria.ia}"
         [class.ecommerce]="categoria.destaque">
      <h4 [id]="categoria.nome" class="categoria  font-weight-bold pl-2">
        <ng-container *ngIf="categoria.ia">
          <i class="fa fa-magic"></i>
        </ng-container>
        <span class="font-italic">
        {{categoria.nome}}
        </span>
      </h4>
    </div>

    <ng-container *ngFor="let produto of produtosPorCategoria[categoria.nome] | slice:0: 10">
      <app-produto-container [produto]="produto" [exibirPrecos]="true" [layoutHorizontal]="true"
                             (onAbrirDetalhes)="abraDetalhesProduto($event)" [botaoEscolher]="true"></app-produto-container>
    </ng-container>
  </ng-container>
  </div>

  <hr>

  <footer class="footer p-0 col" style="left: 0px;background: #333;">
    <div class="row">
      <div class="col p-2 d-flex justify-content-center">
        <button class="btn btn-large btn-blue" (click)="fecheTela()">Adicionar Mais Produtos</button>
      </div>
    </div>
    <div class="pl-2 pr-2">
    <app-carrinho #carrinho [exibindoMenu]="false" (clicouAbrirCarrinho)="this.abrirCarrinho()"></app-carrinho>
    </div>
  </footer>
</ng-container>
