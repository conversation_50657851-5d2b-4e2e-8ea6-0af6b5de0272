<div class="alert alert-danger mb-2" role="alert" *ngIf="erroInicializacao">
  <i class="fas fa-times"></i>
  {{erroInicializacao}}
</div>

<div class="alert alert-danger mb-2" role="alert" *ngIf="msgErro" #erroDiv>
  <i class="fas fa-exclamation-triangle"></i> {{msgErro}}
</div>


<form id="frm" [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"
      novalidate #frm="ngForm" *ngIf="!erroInicializacao">

  <div class="bloqueio" *ngIf="(carregandoScript || !this.definiuSessaoPagSeguro || carregando3ds)   "  >
    <div class="k-icon k-i-loading mt-5 centralizado fa-2x"  ></div>
  </div>

  <div >
    <div class="row">
      <div class="col-12 col-sm-9">
        <div class="form-group ">
          <label for="numero">Número  do Cartão </label>

          <input type="text" autocomplete="cc-number" class="form-control" [mask]="'0000-0000-0000-0000'" style="font-family: monospace;"
                 id="numero" name="numero" [(ngModel)]="cartao.numero" #numero="ngModel"
                 required (ngModelChange)="alterouNumeroCartao()" >


          <i class="fa fa-credit-card fa-2x" [ngClass]="{ 'text-muted': !cartao.bandeira,  ' text-success': cartao.bandeira && cartao.numeroValido}" > </i>
          <div class="invalid-feedback">
            <p *ngIf="numero.errors?.required">Obrigatório</p>
            <p *ngIf="numero.errors?.mask">Número do cartão de crédito inválido.</p>
          </div>
        </div>
      </div>

      <div class="col-12 col-sm-12">
        <div class="form-group ">
          <label for="nome">Nome impresso</label>
          <input type="text" class="form-control" required
                 id="nome" name="nome" autocomplete="cc-name" [(ngModel)]="cartao.nome" #nome="ngModel"
                 placeholder="Nome impresso" value="" >
          <div class="invalid-feedback">
            <p *ngIf="nome.errors?.required">Obrigatório</p>
          </div>
        </div>
      </div>


      <div class="col-6 col-sm-4">
        <div class="form-group ">
          <label for="cvv">CVV</label>
          <input type="text" class="form-control" autocomplete="cc-csc" [mask]="'000'"
                 id="cvv" name="cvv" [(ngModel)]="cartao.cvv" #cvv="ngModel"
                 required    >
          <div class="invalid-feedback">
            <p *ngIf="cvv.errors?.required">Obrigatório</p>
          </div>
        </div>
      </div>

      <div class="col-6 col-sm-4">
        <div class="form-group ">
          <label for="validade">Data validade</label>
          <kendo-dateinput name="validade" autocomplete="cc-exp" id="validade" format="MM/yy" class="form-control"
                           required validadeCartao #validade="ngModel"  [(ngModel)]="cartao.validade">

          </kendo-dateinput>
          <div class="invalid-feedback">
            <p *ngIf="validade.errors?.required">Obrigatório</p>
            <p *ngIf="validade.errors?.validadeExpirada">Data de validade não pode ser menor que o mês/ano atual</p>
          </div>
        </div>
      </div>

      <div class="col-6 ">
        <div class="form-group">
          <label for="nome">CPF</label>
          <input type="text" class="form-control" autocomplete="off" required
                 id="cpf" name="cpf" [(ngModel)]="cartao.cpf" #cpf="ngModel"
                 mask="000.000.000-00" cpfValido placeholder="___.___.___-__" value="">
          <div class="invalid-feedback">
            <p *ngIf="cpf.errors?.required">Obrigatório</p>
            <p *ngIf="cpf.errors?.cpfInvalido">CPF é invalido</p>
          </div>
        </div>
      </div>

      <div class="col-6 ">
        <div class="form-group">
          <label>Tipo do Cartão</label>

          <kendo-combobox   name="tipoDoCartao" [(ngModel)]="cartao.tipoDoCartao"
                            [data]="tiposCartao" required
                            [textField]="'nome'" [valueField]="'id'"  #tipoDoCartao="ngModel"   class="form-control" >
          </kendo-combobox>

          <div class="invalid-feedback">
            <p *ngIf="tipoDoCartao.errors?.required">Obrigatório</p>
          </div>

        </div>
      </div>
    </div>

    <div class="row">
      <div *ngIf="parcelamento && parcelamento.parcelas.length" class="col-12 mt-2 form-group">
        <label for="parcelamento">Número de parcelas</label>

        <kendo-combobox id="parcelamento"  name="parcelamento" [(ngModel)]="cartao.parcelamento" [data]="parcelamento.parcelas " required
                        textField = 'descricao'  #parcelamentoModel="ngModel"
                        placeholder="Selecione a quantidade de parcelas" class="form-control" >
        </kendo-combobox>

        <div class="invalid-feedback">
          <p *ngIf="parcelamentoModel.errors?.required">Obrigatório</p>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="form-group mb-2 col">
        <label for="nome">Email</label>
        <input kendoTextBox id="email" name="email" placeholder="Informe seu email"
               class="form-control"   #email="ngModel"
               [(ngModel)]="cartao.email" required/>
        <small id="emailHelp" class="form-text text-muted">Você receberá notificações e o comprovante de pagamento nesse email.</small>

        <div class="invalid-feedback">
          <p *ngIf="email.errors?.required">Email é obrigatório</p>
        </div>
      </div>
    </div>

    <div class="alert alert-success alert-dismissible fade show mt-2" role="alert" *ngIf="mensagemSucesso">
      <i class="mdi mdi-check-all mr-2"></i> {{mensagemSucesso}}
      <button type="button" class="close" data-dismiss="alert" aria-label="Fechar" (click)="fecheMensagemSucesso()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>

    <div class="alert alert-danger alert-dismissible fade show mt-2" role="alert" *ngIf="mensagemErro">
      <i class="mdi mdi-check-all mr-2"></i> {{mensagemErro}}
      <button type="button" class="close" data-dismiss="alert" aria-label="Fechar" (click)="fecheMensagemErro()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
  </div>
</form>
