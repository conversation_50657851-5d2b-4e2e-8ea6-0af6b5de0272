import {
  ApplicationRef,
  Component, ElementRef,
  EventEmitter,
  Inject, Input,
  NgZone, OnDestroy,
  OnInit,
  Output,
  Renderer2,
  ViewChild
} from '@angular/core';
import {DOCUMENT} from "@angular/common";
import {DadosCartao} from "../../objeto/DadosCartao";
import {ICartaoCreditoGateway} from "../../objeto/ICartaoCreditoGateway";
import {PagseguroService} from "../../services/pagseguro.service";
import {AutorizacaoLojaService} from "../../services/autorizacao-loja.service";
import {CadCartaoPadrao} from "../../objeto/CadCartaoPadrao";
import {NgForm} from "@angular/forms";
import {IFormCartaoRespostaValido} from "../../objeto/IFormCartaoRespostaValido";

declare let PagSeguro;

@Component({
  selector: 'app-cad-cartao-pagseguro-connect',
  templateUrl: './cad-cartao-pagseguro-connect.component.html',
  styleUrls: ['./cad-cartao-pagseguro-connect.component.scss']
})
export class CadCartaoPagseguroConnectComponent  implements OnInit, OnDestroy, ICartaoCreditoGateway {
  @ViewChild('frm', { static: false})  frm: NgForm;
  @ViewChild('frm', { read: ElementRef }) public frmElement: ElementRef;
  @Input() parcelamento: any;
  @Input() pedido: any;
  @Output() onCriou = new EventEmitter();
  empresa: any;
  processando: any;
  mensagemSucesso: any;
  mensagemErro: any;
  cartao: any = new DadosCartao();
  carregandoScript = true;
  bandeira: any = null;
  bancoEmissor: any = null;
  definiuSessaoPagSeguro = false;
  parcelas: any;
  msgErro: string;
  tentativas = 1;
  erroInicializacao: string;
  timeout: any;
  publicKey: string;
  senderHash: string;
  carregando3ds: boolean;
  tiposCartao = [{id: 'CREDIT_CARD', nome: 'Crédito'},
    { id: 'DEBIT_CARD', nome: 'Debito'}
  ]
  constructor(private _renderer2: Renderer2, private ngZone: NgZone,  private pagSeguroService: PagseguroService,
              private app: ApplicationRef, private autorizacaoService: AutorizacaoLojaService,
              private cadCartaoPadrao: CadCartaoPadrao,
              @Inject(DOCUMENT) private _document: Document, private _ngZone: NgZone) {

    this.cartao.numero = '';
    this.cartao.cvv = '';
    this.cartao.nome = '';
    this.cartao.cpf = '';
    this.cartao.validade = null;
    this.cartao.dataNascimento = null;
  }

  ngOnInit() {
    this.addScriptPagseguro();
  }

  ehValido(): IFormCartaoRespostaValido {
    return this.cadCartaoPadrao.ehValido(this.frm, this.frmElement);
  }

  fecheMensagemErro(){
    delete this.mensagemErro;
  }

  setSessaoAtiva = function(session, publicKey, sandbox){
    this.definiuSessaoPagSeguro = true;
    this.publicKey = publicKey;
    PagSeguro.setUp({
      session: session,
      env: sandbox ? PagSeguro.env.SANDBOX : PagSeguro.env.PROD
    });
  }

  private addScriptPagseguro() {
    let script = this._renderer2.createElement('script');
    script.type = 'text/javascript';
    script.src = 'https://assets.pagseguro.com.br/checkout-sdk-js/rc/dist/browser/pagseguro.min.js';
    this._renderer2.appendChild(this._document.body, script);

    setTimeout(() => {
      this.inicializePagSeguro();
    }, 1000)
  }


  inicializePagSeguro(){
    if(typeof PagSeguro === 'object'){
      this.carregandoScript = false;
      this.crieSessaoPagSeguro();
    } else {
      if(this.tentativas <= 10){
        this.tentativas++;
        this.timeout = setTimeout(() => {
          this.inicializePagSeguro();
        }, 1000)
      } else {
        this.erroInicializacao =  'Não foi possivel carreguar os scripts do PagSeguro'
      }
    }
  }

  canceleProcessando(){
    this._ngZone.run(() => {
      this.processando = false;
    });

  }

  fecheMensagemSucesso() {
    delete this.mensagemSucesso;
  }

  alterouNumeroCartao() {
    if( this.cartao.numero.length <= 6 || !this.definiuSessaoPagSeguro )
      return;

  }

  ativeAssinatura() {  }

  obtenhaBandeira() {
    if( !this.bandeira ) { return null; }

    if(typeof this.bandeira === 'string') return this.bandeira;

    return this.bandeira.brand.name;
  }

  crieTokenCartao(enderecoCobranca: any): Promise<DadosCartao> {
    return new Promise( async (resolve, reject) => {
      const mesValidade = (this.cartao.validade.getMonth() + 1).toString().padStart(2, '0');

      const card = PagSeguro.encryptCard({
        publicKey: this.publicKey,
        number: this.cartao.numero, // Número do cartão de crédito
        securityCode: this.cartao.cvv, // CVV do cartão
        expMonth: mesValidade, // Mês da expiração do cartão
        expYear: this.cartao.validade.getFullYear(),
        holder: this.cartao.nome,
      });

      const encrypted = card.encryptedCard;
      const hasErrors = card.hasErrors;
      const errors = card.errors;

      if(hasErrors) {
        let msgErro = 'Cartão inválido'

        if(typeof errors === 'string') msgErro   = String(`${msgErro}: ${errors}`)

        if(Array.isArray(errors)){
          console.log(errors[0])
          let erros = errors.map((item: any) => String(`${item.code}: ${item.message}`))
          msgErro   = String(`${msgErro}: ${erros}`)
        }

        return  reject(msgErro)
      }

      this.setTokenCartao(encrypted);
      let erro3ds: any = await this.inicie3Ds(enderecoCobranca);

      if(erro3ds)
        return reject(erro3ds)

      resolve(this.cartao);
    })
  }



  calculeParcelas() {
    return new Promise( (resolve, reject) => {
      PagSeguro.getInstallments({
        amount: this.pedido.total,
        maxInstallmentNoInterest: 1,
        brand: this.obtenhaBandeira(),
        success: (response) => {
          // Retorna as opções de parcelamento disponíveis
          resolve(response);
        },
        error: (response) => {
          // callback para chamadas que falharam.
          reject(response);
        },
        complete: (response) => {
          // Callback para todas chamadas.
        }
      });
    });
  }

  setTokenCartao(token: string) {
    this.cartao.token = token;
  }

  crieSessaoPagSeguro(){
    this.pagSeguroService.crieSessaoConnect().then( (resposta: any) => {
      this.setSessaoAtiva(resposta.session, resposta.publicKey, resposta.sandbox)
    }).catch( erro => {
      console.log(erro)
      this.erroInicializacao = 'Não foi possível criar sessão de pagamento.'
      this.app.tick();
    });


    if(this.parcelamento){
      this.parcelamento.parcelas = []
      for(let i = 1; i <= this.parcelamento.numeroParcelas; i++){
        let valor: any = this.parcelamento.total / i ;

        valor = valor.toFixed(2).replace('.', ',')

        this.parcelamento.parcelas.push( { numeroParcelas: i, descricao: String(`${i}x de R$ ${valor} sem juros`)});
      }
    }
  }

  exibaCartao(dadosCartao: DadosCartao) {
    this.cartao = new DadosCartao();

    this.cartao.tipoDoCartao = this.tiposCartao[0];

    Object.assign(this.cartao, dadosCartao);

    let usuario = this.autorizacaoService.getUsuario();

    if(usuario && usuario.cpf) this.cartao.cpf = usuario.cpf
    if(usuario && usuario.email) this.cartao.email = usuario.email
  }


  async inicie3Ds(enderecoCobranca: any){
    return new Promise((resolve) => {
      try {
        let contato = this.pedido.contato || (this.pedido as any).cliente,
          telefone = contato.telefone,
          tipoPagamento = this.cartao.tipoDoCartao.id //DEBIT_CARD //esse vai usuario vai informar na tela

        if(!enderecoCobranca) return resolve('Endereço de cobrança é obrigatorio')
        if(!enderecoCobranca.cep) return resolve('Endereço de cobrança sem CEP, verifique o cadastro')
        if(!enderecoCobranca.complemento) return resolve('Complemento do endereço cobrança do cartão não informado')
        if(!telefone) return resolve('Telefone de contato não informado')

        if(contato.nome.split(' ').length === 1)
          return resolve(`Nome usuário inválido: "${contato.nome}". Atualize seu cadastro você precisa informar seu nome completo`)

        const totalPedido = this.pedido.totalPagar != null ? this.pedido.totalPagar : this.pedido.obtenhaValorAhPagar() ;

        let valor = (totalPedido * 100).toFixed(2);
        let request: any = {
          beforeChallenge:  (data: any) => {
            console.log('chamou antes do desafio...')
            console.log(data)
            this.bandeira = data.brand;
            this.bancoEmissor = data.issuer;
            data.open();
          },
          data: {
            customer: {
              name: contato.nome.replace(/\d+/g, ""),
              email: this.cartao.email,
              phones: [
                {
                  country: '55',
                  area: telefone.substring(0, 2),
                  number: telefone.substring(2),
                  type: 'MOBILE'
                }
              ]
            },
            paymentMethod: {
              type: tipoPagamento,
              installments: 1,
              card: {
                number: this.cartao.numero,
                expMonth: this.cartao.validade.getMonth() + 1,
                expYear: this.cartao.validade.getFullYear(),
                holder: {
                  name: this.cartao.nome
                }
              }
            },
            amount: {
              // tslint:disable-next-line:radix
              value:  parseInt(valor),
              currency: 'BRL'
            },
            billingAddress: {
              street: enderecoCobranca.logradouro,
              number: enderecoCobranca.numero,
              complement: enderecoCobranca.complemento || "",
              regionCode: enderecoCobranca.cidade ? enderecoCobranca.cidade.estado.sigla : enderecoCobranca.uf,
              country: 'BRA',
              city: enderecoCobranca.cidade ? enderecoCobranca.cidade.nome : enderecoCobranca.nomeCidade,
              postalCode: enderecoCobranca.cep.replace(/\D/g, "").trim()
            },
            dataOnly: false
          }
        }

        if(this.pedido.entrega && this.pedido.entrega.endereco){
          const enderecoEntrega: any = this.pedido.entrega.endereco;

          request.data.shippingAddress = {
            street: enderecoEntrega.logradouro,
            number: enderecoEntrega.numero || 's/n',
            complement: enderecoEntrega.complento || "complemento",
            regionCode: enderecoEntrega.cidade ? enderecoEntrega.cidade.estado.sigla : enderecoEntrega.uf,
            country: 'BRA',
            city: enderecoEntrega.cidade ? enderecoEntrega.cidade.nome : enderecoEntrega.nomeCidade,
            postalCode: enderecoEntrega.cep
          }
        }

        this.carregando3ds = true;

        console.log(request.data);

        PagSeguro.authenticate3DS(request).then(  (result) =>  {
          let authenticationStatus = result.status;
          this.carregando3ds = false;
          let erroAutenticacao;

          switch (authenticationStatus) {
            case 'AUTH_FLOW_COMPLETED':
               //fechou autenticação
               if( result.authenticationStatus === 'NOT_AUTHENTICATED'){
                 erroAutenticacao = 'Autenticação não realizada, é preciso fazer autenticação do seu cartao '
                 if(this.bandeira) erroAutenticacao += this.bandeira.toUpperCase();
                 if(this.bancoEmissor) erroAutenticacao += ' com o banco emissor: ' + this.bancoEmissor
               } else {
                 this.cartao.autenticacao3ds = result.id;
                 this.cartao.valorPagamento = totalPedido;
               }
              // Seguir o fluxo para cobrança repassando o authenticationId para a API de Cobrança.
              break;
            case 'AUTH_NOT_SUPPORTED':
              let bandeirasObrigatorias = ["mastercard", "visa", "elo"];
              let cartaoDebito = this.cartao.tipoDoCartao.id === this.tiposCartao[1].id;

              if(cartaoDebito)
                erroAutenticacao = 'Autenticação não suportada: Cartão não elegível, tente outro cartão'
              else if(bandeirasObrigatorias.indexOf(this.bandeira) >= 0)
                erroAutenticacao = 'Autenticação do cartão crédito não realizada, verifique os dados do cartao'

              // Cartão não elegível ao 3DS. Para o meio de pagamento `DÉBITO` a transação deve ser finalizada após este retorno.

              break;
            case 'CHANGE_PAYMENT_METHOD':  // Solicite que o comprador troque o meio de pagamento, pois o mesmo não será aceito na cobrança.
              erroAutenticacao = 'Falha na Autenticação: Cartão não aceito! escolha outra forma de pagamento'
              break;
            // É um status intermediário. Elé é retornando em casos que o emissor do cartão solicita que o desafio seja realizado.
            // Indique que o desafio deve ser exibido ao usuário.
            case  'REQUIRE_CHALLENGE':
              break;
          }
          resolve(erroAutenticacao);
        }).catch(  (err) => {
          this.carregando3ds = false;
          let msgErro = 'Falha ao autenticar cartão: ';
          if(err instanceof PagSeguro.PagSeguroError ) {
            console.log(err);
            console.log(err.detail);
            if(err.detail.errorMessages){
              let erros = err.detail.errorMessages.map( (error) =>  error.code + ": " + error.description +
                (error.parameterName ? String(`(${error.parameterName})`) : ''));

              msgErro = msgErro + erros.join(', ');
            } else if(err.detail.message){
              msgErro = msgErro + err.detail.message
            }

            resolve(msgErro)

          } else {
            resolve(msgErro + (err.message || err))
          }
        })
      } catch (err) {
        console.error(err)
        resolve('Falha ao iniciar autenticação do Cartão: ' + (err.message || err))
      }
    })
  }

  ngOnDestroy(): void {
    if(this.timeout) clearTimeout(this.timeout)
  }
}
