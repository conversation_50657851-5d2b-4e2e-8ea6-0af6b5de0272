import {
  AfterViewInit, ApplicationRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild, ViewContainerRef
} from '@angular/core';
import {Endereco} from "../../objeto/Endereco";
import {<PERSON><PERSON>orm, NgModel} from "@angular/forms";
import {EnderecoService} from "../../services/endereco.service";
import {ConstantsService} from "../../services/ConstantsService";
import {AutoCompleteComponent, ComboBoxComponent} from "@progress/kendo-angular-dropdowns";
import {debounceTime, delay, switchMap, map, tap, distinctUntilChanged, startWith} from "rxjs/operators";
import {PedidoLoja} from "../../objeto/PedidoLoja";
import {from, Observable, Subject, Subscription} from "rxjs";
import {AutorizacaoLojaService} from "../../services/autorizacao-loja.service";
import {FormUtils} from "../../objeto/FormUtils";
import {ActivatedRoute, Router} from "@angular/router";
import {DominiosService} from "../../services/dominios.service";
import {CarrinhoService} from "../../services/carrinho.service";
import {FormaDeEntrega} from "../../objeto/FormaDeEntrega";

declare var tt;

@Component({
  selector: 'app-form-endereco',
  templateUrl: './form-endereco.component.html',
  styleUrls: ['./form-endereco.component.scss']
})
export class FormEnderecoComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('frm', { static: true})  frm: NgForm;
  @ViewChild('frm', { read: ElementRef }) public frmElement: ElementRef;

  @ViewChild('txtNumero', {static: true})   txtNumero: ElementRef;
  @ViewChild('cboZonaDeEntrega', {static: false})   cboZonaDeEntrega: ComboBoxComponent;
  @ViewChild('txtLogradouro', {static: true}) private txtLogradouro: any;
  @ViewChild('txtCEP', {static: true}) private txtCEP: any;
  @ViewChild('complementoInput', {static: true})   complementoInput: ElementRef;
  endereco: Endereco =  Endereco.novo();

  @Output() telaCarregou = new EventEmitter();
  @Output() submit = new EventEmitter();

  @Input() pedido: PedidoLoja
  @Input() buscouUmEndereco = false;
  @Input() exibirBotao = true;
  @Input() pontoReferencia = true;
  @Input() ignorarConfigEmpresa: boolean;
  @Input() cepObrigatorio = false;
  @Input() complementoObrigatorio = false;
  @Input() labelSalvarEndereco = 'Salvar Endereço';
  @Input() deveCalcularTaxaDeEntrega = true;

  estados: any = [];
  cidades: any = [];
  buscandoCEP = false;
  msgErroCEP = '';
  cep: string;
  msgErro: any;
  cepValidado: any;
  calculandoTaxa: boolean;
  bairroObrigatorio = true;
  map: any;
  enderecos: any;
  enderecosContato: any;
  buscouCep: boolean;
  @Input() exibirDepoisCep: boolean;
  escolherPosicaoExata = true;
  escolheuUmEnderecoServer = false;

  @ViewChild('txtBusca', { static: false })
  public txtBusca: ElementRef;

  @ViewChild('cboEstado', {static: false})
  public cboEstado: ComboBoxComponent;

  @ViewChild('cboCidade', {static: false})
  public cboCidade: ComboBoxComponent;
  public keyUp = new Subject<KeyboardEvent>();
  subscription: Subscription;

  public source: Array<{ text: string, value: number }> = [];
  public data: Array<any>;
  enderecoEscolhido: any = {};
  buscandoAutocomplete: boolean;
  buscouAutoComplete: boolean;
  respostaCep: any = {};

  entregaPorZona = false;
  zonasDeEntrega = [];
  zonasBackup = [];
  formaDoPedido;
  zonaDeEntrega: null;
  empresa: any;
  escolherExistente: any;
  carregou = false;
  public mask = '00.000-000';
  nomePagina = '';
  posicionarNoMapa = false;
  latitude = '';
  longitude = '';
  cidadePadrao: any
  exibirBuscaGoogle = false;
  constructor(private enderecoService: EnderecoService, private constantsService: ConstantsService,
     private autorizacao: AutorizacaoLojaService, private router: Router,             private app: ApplicationRef,
              private dominiosService: DominiosService, private carrinhoService: CarrinhoService,
              private activatedRoute: ActivatedRoute) {
    this.nomePagina = dominiosService.obtenhaRaizCardapio();
    this.data = [];
  }

  roundLatLng(num) {
    return Math.round(num * 1000000) / 1000000;
  }

  ngOnInit() {
    if( this.ignorarConfigEmpresa )
      return

    this.activatedRoute.queryParams.subscribe( (params) => {
      if( params.gps ) {
        this.endereco.gps = true;
        this.posicionarNoMapa = true;
        this.latitude = params.lat;
        this.longitude = params.lng;
      }
      this.constantsService.empresa$.subscribe((empresa) => {
        if (!empresa) return;

        this.empresa = empresa;

        this.exibirBuscaGoogle = empresa.modulos.find((item: any) => item.id === 10) != null;


        for (let i = 0; i < this.empresa.formasDeEntrega.length; i++) {
          const formaDeEntrega = this.empresa.formasDeEntrega[i];

          if (formaDeEntrega.nome === FormaDeEntrega.RECEBER_EM_CASA)
            this.formaDoPedido = formaDeEntrega;
        }

        if (this.formaDoPedido && this.formaDoPedido.tipoDeCobranca === 'zona') {
          this.entregaPorZona = true;
          this.zonasDeEntrega = this.formaDoPedido.zonasDeEntrega;
        }

        if (!this.formaDoPedido.exibirTelaBusca)
          this.digitarEnderecoManualmente();

        if (this.formaDoPedido && this.formaDoPedido.bairroOpcional)
          this.bairroObrigatorio = false;

        if (this.formaDoPedido && this.formaDoPedido.cepObrigatorio)
          this.cepObrigatorio = true;

        if (this.formaDoPedido && this.formaDoPedido.complementoObrigatorio)
          this.complementoObrigatorio = true;

      });
    });

    this.autorizacao.obtenhaEnderecos().then( (enderecos) => {
      this.setEnderecosContato(enderecos)
      this.carregou = true;
    }).catch( (erro) => {  this.carregou = true; });
  }

  escolheuEnderecoGoogle(endereco: any){
    this.selecionou(endereco);
    this.app.tick();
  }

  setCidadePadrao(cidade: any){
    this.cidadePadrao = cidade;
  }

  buscouEnderecosGoogle(result: any){
    this.buscouAutoComplete = true;
    this.buscandoAutocomplete = false;
    this.data = result;
  }

  ngAfterViewInit(): void {
    this.cboEstado.searchbar.input.nativeElement.setAttribute('autocomplete', 'new-password');
    this.cboCidade.searchbar.input.nativeElement.setAttribute('autocomplete', 'new-password');

    this.subscription = this.keyUp.pipe(
      debounceTime(700),
      map((event: any) => event.target.value),
      distinctUntilChanged(),
      switchMap((value: string) => {
        if( !value || value.length <= 3 ) {
          return from([]);
        }

        this.buscandoAutocomplete = true;
        return this.enderecoService.autocomplete(value)
      }
      )).subscribe( (dados) => {
      this.data = dados.resultados;
      this.buscouAutoComplete = true;
      this.buscandoAutocomplete = false;
    });

    this.enderecoService.obtenhaEstados().then( (estados) => {
      this.estados = estados;

      if(this.cidadePadrao && ! this.endereco.cidade)
          this.selecioneCidadePadrao()

      if(this.endereco.estado){
        this.endereco.estado = this.estados.find( estado => estado.sigla === this.endereco.estado.sigla)
        if(this.endereco.cidade)
          this.cidades = [this.endereco.cidade]
      }


      this.telaCarregou.emit(true);
    });
  }

  selecioneCidadePadrao(){
    this.endereco.estado = this.estados.find((estado: any) => estado.id === this.cidadePadrao.estado.id)
    this.endereco.cidade = this.cidadePadrao
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  exibaEndereco(endereco: Endereco) {
    if(endereco) {
      this.data = [];
      this.endereco = Endereco.novo();
      Object.assign(this.endereco, endereco)
      if(endereco.cidade){
        this.endereco.cidade = endereco.cidade;
        this.endereco.estado = endereco.cidade.estado;

        this.carregueEstadoECidade(this.endereco.cidade.nome, this.endereco.estado.nome);
      }

      if(this.formaDoPedido.bloquearBairroAposCEP && this.endereco.zonaDeEntrega){
        this.zonasBackup = this.zonasDeEntrega;
        this.zonasDeEntrega = [this.endereco.zonaDeEntrega]
      }

    }
  }

  mudouEstado(estado: any) {
    if( estado == null ) {
      return;
    }

    this.enderecoService.obtenhaCidades(estado).then( (cidades) => {
      if(cidades && cidades.length)
        this.cidades = cidades;
    })
  }

  selecionarBairroDaZona(){
    return this.formaDoPedido && this.formaDoPedido.tipoDeCobranca === 'zona' &&
      this.formaDoPedido.selecionarBairroDaZona;
  }

  alterou() {
    if( this.endereco.cep.length < 8 )
      return;

    this.buscandoCEP = true;
    this.msgErroCEP = '';

    if(this.zonasBackup.length) this.zonasDeEntrega = this.zonasBackup;

    this.enderecoService.busquePorCEP(this.unmask(this.endereco.cep)).then( async (resposta) => {
      this.buscouCep = true;
      this.buscandoCEP = false;
      this.respostaCep = resposta;
      this.cepValidado = resposta.localidade != null;
      this.endereco.logradouro = resposta.logradouro;
      if( this.selecionarBairroDaZona() ) {
        this.tenteSelecionarZonaPeloBairro(resposta.bairro);
      } else {
        this.endereco.bairro = resposta.bairro;
      }

      const localidade = resposta.localidade;
      const nomeCidade = localidade.split('/')[0];
      const sigla = localidade.split('/')[1];
      this.carregueEstadoECidade(nomeCidade, sigla);

      if(this.cepValidado) {
        this.endereco.localizacao = null;
        if( !this.endereco.logradouro ) {
          this.txtLogradouro.nativeElement.focus();
        } else {
          this.txtNumero.nativeElement.focus();
        }
      }
    }).catch( () => {
      this.msgErroCEP = 'CEP não encontrado! Verifique se está correto.';
      this.buscouCep = true;
      this.cepValidado = false;
      this.buscandoCEP = false;
    });
  }

  tenteSelecionarZonaPeloBairro(nomeBairro: string) {
    let zonaProcurada = null;
    for( let i = 0; i < this.zonasDeEntrega.length; i++ ) {
      const zona = this.zonasDeEntrega[i];


      const nomeZona = zona.nome.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g, "");
      nomeBairro = nomeBairro.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g, "");

      if( nomeZona === nomeBairro ) {
        zonaProcurada = zona;
      }
    }

    if( zonaProcurada ) {
      this.endereco.zonaDeEntrega = zonaProcurada;
      this.endereco.bairro = zonaProcurada.nome;
      if(this.formaDoPedido.bloquearBairroAposCEP){
        this.zonasBackup = this.zonasDeEntrega;
        this.zonasDeEntrega = [zonaProcurada]
      }
    } else {
      this.endereco.zonaDeEntrega = null;
    }
  }

  unmask(val) {
    return val ? val.replace(/\D+/g, '') : val;
  }

  private carregueEstadoECidade(nomeCidade: string, nome: string) {
    for( const estado of this.estados ) {
      if( nome === estado.nome || nome === estado.sigla) {
        this.enderecoService.obtenhaCidades(estado).then( (cidades) => {
          this.cidades = cidades;
          for( const cidade of this.cidades ) {
            if( nomeCidade.toUpperCase() === cidade.nome.toUpperCase() ) {
              this.endereco.estado = estado;
              this.endereco.cidade = cidade;
              break;
            }
          }
        });

        break;
      }
    }
  }

  scrollTo(el: Element): void {
    if(el) {
      el.scrollIntoView({ behavior: 'smooth' });
    }
  }

  onSubmit(event) {
    if( this.frm.valid ) {
      this.calculandoTaxa = true;

      if( this.deveCalcularTaxaDeEntrega ) {
        if( this.entregaPorZona ) {
          this.caculeTaxaEntrega(this.endereco);
        } else {
          this.caculeLocalizacao(this.endereco);
        }
      } else {
        this.submit.emit(this.endereco);
      }
    } else {
      let $primeiroInvalido = document.querySelector('#frmEndereco .ng-invalid');

      if( $primeiroInvalido ) {
        $primeiroInvalido = $primeiroInvalido.parentElement

        this.scrollTo($primeiroInvalido);
      }
    }

    event.stopPropagation();
    event.preventDefault();
  }

  devePosicionarNoMapa() {
    return this.formaDoPedido.permiteUsarGps && !this.endereco.gps;
  }

  caculeLocalizacao(endereco: any) {
    this.calculandoTaxa = true;
    if( !endereco.gps ) {
      endereco.localizacao = '';
    }

    this.enderecoService.calculeTaxaDeEntrega('Receber em casa',
      endereco, this.pedido.obtenhaSubTotal()).then( (dados) => {
      this.pedido.entrega.setTaxaEntrega(endereco, dados);
      this.pedido.novosEnderecos.push(endereco);

      this.carrinhoService.salvePedido(this.pedido);

      this.calculandoTaxa = false;

      const localizacao = dados.localizacao;
      this.latitude = localizacao.split(',')[0];
      this.longitude = localizacao.split(',')[1];
      this.posicionarNoMapa = this.devePosicionarNoMapa();

      if( !this.posicionarNoMapa ) {
        this.submit.emit(endereco);
      }
    }).catch( erro => {
      this.calculandoTaxa = false;
      this.msgErro = erro;
      setTimeout( () => {
        window.scrollTo(0, window.innerHeight);
      }, 0);
    });
  }

  caculeTaxaEntrega(endereco: any){
    this.calculandoTaxa = true;
    this.enderecoService.calculeTaxaDeEntrega('Receber em casa',
      endereco, this.pedido.obtenhaSubTotal()).then( (dados) => {
      Object.assign(endereco, dados)
      this.submit.emit(endereco);
      this.calculandoTaxa = false;
    }).catch( erro => {
      this.calculandoTaxa = false;
      this.msgErro = erro;
      window.scrollTo(0, window.innerHeight);
    });
  }

  volteEndereco(){
    this.buscouUmEndereco = false;
    this.endereco =  Endereco.novo();
    this.cepValidado = false;
  }

  selecionou(enderecoEscolhido: any) {
    this.buscouUmEndereco = true;
    this.escolheuUmEnderecoServer = enderecoEscolhido.id != null;
    this.endereco.logradouro = enderecoEscolhido.logradouro;
    this.endereco.bairro = enderecoEscolhido.bairro;

    this.endereco.numero = '';
    this.endereco.complemento = '';
    this.endereco.cep = enderecoEscolhido.cep;
    this.endereco.localizacao = enderecoEscolhido.localizacao;
    this.endereco.pontoDeReferencia = enderecoEscolhido.pontoDeReferencia;
    this.carregueEstadoECidade(enderecoEscolhido.cidade.nome, enderecoEscolhido.estado.nome);

    setTimeout( () => {
      this.cepValidado = true;

      if( this.entregaPorZona ) {
        this.cboZonaDeEntrega.focus();
      } else {
        this.txtNumero.nativeElement.focus();
      }
    }, 0);
  }

  digitarEnderecoManualmente() {
    this.buscouUmEndereco = true;
    this.endereco.logradouro = '';
    this.endereco.bairro = '';
    this.endereco.numero = '';
    this.endereco.estado = null;
    this.endereco.cidade = null;
    this.endereco.cep = '';
    this.endereco.complemento = '';
    this.endereco.pontoDeReferencia = '';
    this.endereco.localizacao = null;

    if(this.cidadePadrao)
      this.selecioneCidadePadrao()

    setTimeout( () => {
      this.txtCEP.nativeElement.focus();
    }, 0);
  }

  alterouCidade(){
    if(!this.endereco.cidade && this.cidades.length === 1){
      if(this.endereco.estado)
        this.mudouEstado(this.endereco.estado)
    }
  }

  setEnderecosContato(enderecos: any = []){
    this.enderecosContato = [];

    enderecos.forEach( (dadosEndereco: any) => {
      let endereco = Endereco.novo();

      Object.assign(endereco, dadosEndereco)

      if(dadosEndereco.cidade){
        endereco.cidade = dadosEndereco.cidade;
        endereco.estado = dadosEndereco.cidade.estado;
      }

      if( !endereco.descricaoCompleta )
        endereco.descricaoCompleta = endereco.obtenhaEnderecoCompleto();

      this.enderecosContato.push(endereco)
    });
  }

  selecionouZona() {
    setTimeout( () => {
      if(this.formaDoPedido && this.formaDoPedido.selecionarBairroDaZona){
        this.endereco.bairro = this.endereco.zonaDeEntrega.nome;
      }
    }, 0)
  }

  ehValido() {
    (this.frm as {submitted: boolean}).submitted = true;

    const ctrlInvalido = FormUtils.obtenhaControleInvalido(this.frm, this.frmElement);

    console.log(ctrlInvalido);

    if( ctrlInvalido ) {
      return {
        valido: false,
        controle: ctrlInvalido
      }
    }

    return {
      valido: this.frm.valid
    };
  }

  obtenhaEndereco() {
    let endereco = Object.assign({}, this.endereco)

    endereco.cep = this.unmask(endereco.cep);

    return endereco;
  }

  busqueCEP(cep: string) {
    this.endereco.cep = cep;

    this.alterou();
  }

  salvouALocalizacao(endereco: Endereco, preencheuEndereco: boolean) {
    this.posicionarNoMapa = false;
    this.endereco = endereco;

    if( preencheuEndereco ) {
      this.submit.emit(this.endereco);
    }
  }

  exibaMapa(posicao: { lat: number, lng: number }) {
    this.endereco.gps = true;
    this.posicionarNoMapa = true;
    this.latitude = posicao.lat + '';
    this.longitude = posicao.lng + '';
  }

  clicouVoltarLocalizacao() {
    this.posicionarNoMapa = false;
  }
}
