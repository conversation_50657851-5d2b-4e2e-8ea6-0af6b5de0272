.k-widget {
  font-size: 13px;
}

input[type="text"]:disabled {
  outline: none;
  cursor: default;
  opacity: 0.6;
  -webkit-filter: grayscale(0.1);
  filter: grayscale(0.1);
  pointer-events: none;
  box-shadow: none;
}

.text-muted {
  color: #f7f7f7;
}

::ng-deep .k-list .k-item.k-state-focused:not(.k-state-selected) {
  /*
  background: rgba(74, 129, 212, 0.1);
  box-shadow: none;
   */
}

::ng-deep .k-list .k-item:focus, .k-list .k-item.k-state-focused, .k-list-optionlabel:focus, .k-list-optionlabel.k-state-focused {
  box-shadow: none !important;
}

.endereco .media {
  border-bottom: solid 1px #ececec;
  cursor: pointer;
}

.footer {
  border-top: solid 1px #eeeeee;
  background: #fff;
  padding: 0px;
  position: fixed;
}

.produto {
  border-bottom: solid 1px #e8e8e8;
}

@media (min-width: 1025px) {
  .footer {
    position: initial !important;
  }
}

ul li{
  list-style: none;

  ::ng-deep .btn-outline-light {
    color: #607D8B;
    border-color: #f1f5f7;
  }
}

.alerta-personalizado {
  padding: 0px;
  font-size: 0.875rem;
  color: #BA0933;
  background: transparent;
  border: 0;
}


@media (max-width: 950px) {
  form{
    padding: 12px;
    .form-control{
      font-size: 16px;

      padding: 0;
      margin: 0;
      font-stretch: 100%;
      line-height: normal;
      font-family: Poppins, sans-serif;
      padding: 20px 10px;
    }
  }
}

