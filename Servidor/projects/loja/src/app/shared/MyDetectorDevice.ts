import { Injectable } from '@angular/core';
import { DeviceDetectorService } from 'ngx-device-detector';
import { ActivatedRoute } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class MyDetectorDevice {
  constructor(
    private deviceDetectorService: DeviceDetectorService,
    private route: ActivatedRoute
  ) {
  }

  isMobile(): boolean {

    const isMobileDevice = this.deviceDetectorService.isMobile();
    const hasMobileParam = this.route?.snapshot?.queryParams?.['mobile'] === 'true';

    return isMobileDevice || hasMobileParam;
  }

  isDesktop(): boolean {
    const isDesktopDevice = this.deviceDetectorService.isDesktop();

    return isDesktopDevice;
  }

  isTablet(): boolean {
    const isTabletDevice = this.deviceDetectorService.isTablet();

    return isTabletDevice;
  }
}
