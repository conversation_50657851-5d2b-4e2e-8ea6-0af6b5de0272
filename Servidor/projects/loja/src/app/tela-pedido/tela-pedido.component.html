<app-header-tela [titulo]="'Pedido'"></app-header-tela>

<div  class="fundo" >
  <div class="d-flex justify-content-center align-items-center" style="height: 400px" *ngIf="!pedido && carregou">
    <div class="align-self-center text-center" style="color: #F1556C;">
      <i class="fas fa-exclamation-triangle" style="font-size: 48px"></i>
      <p>
        Não encontrado!
      </p>
    </div>
  </div>

  <div *ngIf="pedido">
    <div class="mt-3 espaco_topo"></div>

    <div class="alert text-center" role="alert" [ngClass]="{'alert-success': pedido.status == 'Entregue'}"
         *ngIf="pedido.status == 'Entregue'">
      <i class="fas fa-check-circle"></i> Seu pedido foi entregue em {{pedido.horarioDescricao}}
    </div>

    <div class="alert alert-info text-center" role="alert" *ngIf="pedido.status != 'Entregue'"
       [ngClass]="{'alert-danger' : pedido.cancelado}">
      <i class="fas fa-info-circle"></i> Pedido {{pedido.status  ==='Novo' ? 'Confirmado' : pedido.status}}
    </div>

    <div>
      <h4 class="d-inline" style="position: relative;top: 5px;">
        Pedido #{{pedido.codigo}}
      </h4>
    </div>

    <div class="mt-3 mb-0"></div>
    <div class="linha"></div>

    <div class="desktop">
      <div *ngFor="let item of pedido.itens" class="produto pt-2 pb-2 item_produto">
        <div class="media">
          <div class="media-body">
            <h5 class="mt-0 mb-1"><span>{{item.qtde}} X</span> {{item.descricao}}</h5>
            {{item.produto.descricao}}

            <div *ngIf="item.adicionaisImprirmir?.length" >
                       <span  *ngFor="let last = last;let adicional of item.adicionaisImprirmir " class="d-block ml-2 font-weight-bold">
                         {{adicional.descricao}}{{last ? '' : ', '}}
                       </span>
            </div>

            <span *ngIf="item.observacao" class="font-11"><br>obs.:<i>"{{item.observacao}}"</i></span>
          </div>
          <h5 class="preco mt-0">{{item.total | currency: 'BRL'}}

          </h5>
        </div>
      </div>
    </div>

    <div class="produto pt-2 pb-1" [hidden]="!pedido.observacoes">
      <div class="media mt-1">
        <div class="media-body">
          <h5 class="mt-0 mb-1"><span>Observação</span></h5>
        </div>
        <h5 class="mt-0">
          <i>
          {{pedido.observacoes}}
          </i>
        </h5>
      </div>
    </div>

    <span *ngIf="!exibirSomenteItens">

        <div class="linha mt-0 mb-0" *ngIf="pedido.itens.length > 3"></div>

        <div class="produto pt-2 pb-1">
          <div class="media mt-1">
            <div class="media-body">
              <h5 class="mt-0 mb-1"><span>Forma de Entrega</span></h5>
            </div>
            <h5 class="mt-0"><strong>{{pedido.formaDeEntrega}}</strong></h5>
          </div>
          <div class="pt-0 pb-0" *ngIf="!pedido.retirar">
            <h5 class="text-muted"><i class="fas fa-map-marker-alt"></i> Endereço Escolhido</h5>
            <h5>
              {{pedido.enderecoCompleto}}
            </h5>
          </div>
        </div>

        <div class="produto pt-1 pb-0" *ngIf="pedido.cliente.telefone">
          <div class="media mt-2" *ngIf="pedido.cliente">
            <div class="media-body">
              <h5 class="mt-0 mb-1"><span>Meus dados</span></h5>
            </div>
            <h5 class="mt-0 text-right">
              <strong>{{pedido.cliente.nome}}</strong> <br>
              <label class="text-muted mt-1">{{pedido.cliente.telefone | telefone}}</label>
            </h5>
          </div>

          <div *ngIf="!pedido.cliente.telefone">
            <h5>Meus dados</h5>
            <div class="form-group mb-2">
              <button class="btn btn-xs btn-outline-blue" type="button">Informe nome e telefone</button>
            </div>
          </div>
        </div>

    </span>

    <div class="produto item_produto pt-2 pb-0">
      <div class="media">
        <div class="media-body">
          <h5 class="mt-0 mb-1 text-muted"><span>Subtotal</span></h5>
        </div>
        <h5 class="mt-0 text-muted">{{pedido.total | currency: 'BRL'}}</h5>
      </div>
      <div *ngFor="let pagamento of pedido.pagamentos">
        <div class="media" *ngIf="pagamento.formaDePagamento === 'dinheiro'">
          <div class="media-body">
            <h5 class="mt-0 mb-1"><span>Dinheiro</span></h5>
          </div>
          <h5 class="mt-0">{{pagamento.trocoPara | currency: 'BRL'}}</h5>
        </div>
      </div>
      <div class="media" *ngIf="pedido.entrega && pedido.entrega.taxaDeEntrega">
        <div class="media-body">
          <h5 class="mt-0 mb-1"><span>Taxa De Entrega</span></h5>
        </div>
        <h5 class="mt-0">{{pedido.taxaDeEntrega | currency: 'BRL'}}</h5>
      </div>
      <div class="media" *ngIf="pedido.pagamento && pedido.pagamento.formaDePagamento === 'Dinheiro'">
        <div class="media-body">
          <h5 class="mt-0 mb-1"><span>Troco</span></h5>
        </div>
        <h5 class="mt-0 preco">{{pedido.troco | currency: 'BRL'}}</h5>
      </div>
      <div class="media mt-0">
        <div class="media-body">
          <h5 class="mt-0 mb-1"><span>Total</span></h5>
        </div>
        <h5 class="mt-0 preco"><strong>{{pedido.total | currency: 'BRL'}}</strong></h5>
      </div>
    </div>

    <div style="height: 40px" class="mobile"></div>

    <footer class="footer"  >


      <button id="pedir-novamente" class="btn btn-blue float-lg-right mt-2 mb-2" [hidden]="!pedido.pedirNovamente"
              (click)="pedirNovamente()" [disabled]="validandoPedido">
        <i class="k-icon k-i-loading mr-1" *ngIf="validandoPedido"></i>
        Pedir novamente
      </button>

      <div class="clearfix"></div>

      <p class="alert alert-danger" role="alert" *ngIf="msgErro">
        <i class="fa fa-times mr-2"></i> {{msgErro}}
      </p>

      <p class="alert alert-success" role="alert" *ngIf="msg">
        <i class="mdi mdi-check-all mr-2"></i>  {{msg}}
      </p>
    </footer>
  </div>
</div>
