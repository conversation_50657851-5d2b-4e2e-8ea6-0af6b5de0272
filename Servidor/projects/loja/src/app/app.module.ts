import {enableProdMode, LOCALE_ID, NgModule} from '@angular/core';
import { AppComponent } from './app.component';
import {CommonModule, registerLocaleData} from "@angular/common";
import {MatTabsModule} from "@angular/material/tabs";
import {InlineSVGModule} from "ng-inline-svg";
import {FormsModule} from "@angular/forms";
// @ts-ignore
import {NgxMaskModule} from "ngx-mask";
import {InputsModule, NumericTextBoxModule, RadioButtonModule} from "@progress/kendo-angular-inputs";
import {SiteProdutosComponent} from "../produtos/site-produtos.component";
import {SiteProdutoComponent} from "../site-produto/site-produto.component";
import {TopoComponent} from "../topo/topo.component";
import {CarrinhoComponent} from "../carrinho/carrinho.component";
import {LojaCarrinhoComponent} from "../loja-carrinho/loja-carrinho.component";
import {HeaderTelaComponent} from "../header-tela/header-tela.component";
import {EscolherEnderecoComponent} from "../escolher-endereco/escolher-endereco.component";
import {AutofocusDirective} from "../directives/autofocus.directive";
import {AfterValueChangedDirective} from "../directives/after-value-changed.directive";
import {CPFPipe, TelefonePipe} from "../directives/loja.pipes";
import {LojaPagamentoComponent} from "../loja-pagamento/loja-pagamento.component";
import {EntregaComponent} from "../entrega/entrega.component";
import {LojaDadosClienteComponent} from "../loja-dados-cliente/loja-dados-cliente.component";
import {LojaFooterComponent} from "../loja-footer/loja-footer.component";
import {ConstantsService} from "../services/ConstantsService";
import {PaginaComponent} from "../pagina/pagina.component";
import localPT from "@angular/common/locales/pt";
import { ModalEscolherEnderecoComponent } from './modal-escolher-endereco/modal-escolher-endereco.component';
import { FormEnderecoComponent } from './form-endereco/form-endereco.component';
import {FinalizarPedidoComponent} from "./finalizar-pedido/finalizar-pedido.component";
import {DropDownsModule} from "@progress/kendo-angular-dropdowns";
import {BrowserModule} from "@angular/platform-browser";
import {AppRoutingModule} from "./app-routing.module";
import {HttpClientJsonpModule, HttpClientModule} from "@angular/common/http";
import {BrowserAnimationsModule} from "@angular/platform-browser/animations";
import {
  CPFValidator,
  NomeConmpletoValidator,
  NomeValidoValidator, LogradouroContatoValidator, ValorTrocoPedido, MaxlengthDirective, ValidadeCartaoValidator
} from "../directives/form-validadores.directive";
import {CurrencyMaskModule} from "ng2-currency-mask";
import {SelecionarNoFocoDirective} from "../directives/selecionar-no-foco.directive";
import { SiteCampoAdicionalComponent } from './site-campo-adicional/site-campo-adicional.component';
import { TelaBuscaProdutosComponent } from './tela-busca-produtos/tela-busca-produtos.component';
import {FiltroProdutosPipe} from "./tela-busca-produtos/FiltroProdutosPipe";
import { LojaMenuComponent } from './loja-menu/loja-menu.component';
import { LojaPerfilComponent } from './loja-perfil/loja-perfil.component';
import { CadContatoLojaComponent } from './cad-contato/cad-contato-loja.component';
import { PainelLoginComponent } from './painel-login/painel-login.component';
import { PedidosLojaComponent } from './loja-pedidos/pedidos-loja.component';
import { HeaderLojaComponent } from './topo-menu/header-loja.component';
import { TelaPedidoComponent } from './tela-pedido/tela-pedido.component';
import { MeusDadosComponent } from './meus-dados/meus-dados.component';
import { ValidarContatoComponent } from './cad-contato/validar-contato/validar-contato.component';
import { ConfirmarContatoComponent } from './cad-contato/confirmar-contato/confirmar-contato.component';
import { TopoDesktopComponent } from './topo-desktop/topo-desktop.component';
import { LojaRecuperarComponent } from './loja-recuperar/loja-recuperar.component';
import {DialogModule, WindowModule} from "@progress/kendo-angular-dialog";
import {IntlModule} from "@progress/kendo-angular-intl";
import '@progress/kendo-angular-intl/locales/pt/all';
import {
    DateInputsModule,
    DatePickerModule,
    DateTimePickerModule,
    TimePickerModule
} from "@progress/kendo-angular-dateinputs";
import {TelaAcompanharPedidoComponent} from "./tela-acompanhar-pedido/tela-acompanhar-pedido.component";
import { PedidoRibbonComponent } from './pedido-ribbon/pedido-ribbon.component';
import { AgendarEntregaComponent } from './agendar-entrega/agendar-entrega.component';
import {ButtonModule} from "@progress/kendo-angular-buttons";
import { ExibirWhatsappComponent } from './exibir-whatsapp/exibir-whatsapp.component';
import {CadCartaocreditoPagSeguroComponent} from "./cad-cartaocredito/cad-cartaocredito-pag-seguro.component";
import { TelaCartaoDeCreditoComponent } from './tela-cartao-de-credito/tela-cartao-de-credito.component';
import {ValidadorPagamentoPagSeguroDirective} from "../directives/ValidadorPagamentoPagSeguro.directive";
import { AdicionaisCustomizadosComponent } from './adicionais-customizados/adicionais-customizados.component';
import {SiteMontarpizzaComponent} from "../site-montarpizza/site-montarpizza.component";
import {SiteAdicionarProdutoComponent} from "../site-adicionar-produto/site-adicionar-produto.component";
import { NavigationModule } from '@progress/kendo-angular-navigation';
import { TelaAdicionalPedidoComponent } from './tela-adicional-pedido/tela-adicional-pedido.component';
import {NgImageSliderModule} from "ng-image-slider";
import { PopupEnderecoChinaInboxComponent } from './popup-endereco-china-inbox/popup-endereco-china-inbox.component';
import { CadCartaoMercadopagoComponent } from './cad-cartao-mercadopago/cad-cartao-mercadopago.component';
import {NgxSmartBannerModule} from "@netcreaties/ngx-smart-banner";
import {CookieService} from "ngx-cookie-service";
import {ScrollingModule} from "@angular/cdk/scrolling";
import { PaginaFranquiaComponent } from './franquia/pagina-franquia/pagina-franquia.component';
import { TelaEnderecoRedirecionarComponent } from './franquia/tela-endereco-redirecionar/tela-endereco-redirecionar.component';
import { DialogListarLojasComponent } from './franquia/dialog-listar-lojas/dialog-listar-lojas.component';
import { TelaListaLojasComponent } from './franquia/tela-lista-lojas/tela-lista-lojas.component';
import { LojaConfiguracoesComponent } from './loja-configuracoes/loja-configuracoes.component';
import { TelaPosicaoMapaComponent } from './tela-posicao-mapa/tela-posicao-mapa.component';
import {NotificationModule} from "@progress/kendo-angular-notification";
import { TelaBuscaEcommerceComponent } from './tela-busca-ecommerce/tela-busca-ecommerce.component';
import {InfiniteScrollModule} from "ngx-infinite-scroll";
import { CadCartaoCieloComponent } from './cad-cartao-cielo/cad-cartao-cielo.component';
import { CadCartaoPagarmeComponent } from './cad-cartao-pagarme/cad-cartao-pagarme.component';
import { TelaPreencherEnderecoComponent } from './rede/tela-preencher-endereco/tela-preencher-endereco.component';
import { TelaMultiLojaComponent } from './tela-multi-loja/tela-multi-loja.component';
import {ProdutoContainerComponent} from "../produtos/produto-container/produto-container.component";
import { PaginaMultiLojaComponent } from './pagina-multi-loja/pagina-multi-loja.component';
import { TelaAvaliarPedidoComponent } from './tela-avaliar-pedido/tela-avaliar-pedido.component';
import {LabelModule} from "@progress/kendo-angular-label";
import {SeletorCodigoPaisComponent} from "./seletor-codigo-pais/seletor-codigo-pais.component";
import { SiteWizardProdutosIaComponent } from './produtos/site-wizard-produtos-ia/site-wizard-produtos-ia.component';
import {BandeiraLogoLojaComponent} from "../loja-pagamento/formas-pagamentos/bandeira-logo-loja.component";
  import {
  FormasPagamentoLojaMobileComponent
} from "../loja-pagamento/formas-pagamentos/formas-pagamento-loja-mobile.component";
import {
  FormasPagamentoLojaMobileNovaComponent
} from "../loja-pagamento/formas-pagamentos/formas-pagamento-loja-mobile-nova.component";
import {
  FormasPagamentoLojaDesktopComponent
} from "../loja-pagamento/formas-pagamentos/formas-pagamento-loja-desktop.component";
import {FormasPagamentoLojaDesktopNovaComponent} from "../loja-pagamento/formas-pagamentos/formas-pagamento-loja-desktop-nova";
import {TrocoLojaComponent} from "../loja-pagamento/formas-pagamentos/troco-loja.component";
import { FinalizarPedidoTotemComponent } from './finalizar-pedido-totem/finalizar-pedido-totem.component';
import { PainelHorariosComponent } from './painel-horarios/painel-horarios.component';
import {SiteCategoriasComponent} from "../produtos/categorias/site-categorias.component";
import {NgbRatingModule} from "@ng-bootstrap/ng-bootstrap";
import { ValidarContatoZapComponent } from './cad-contato/validar-contato-zap/validar-contato-zap.component';
import { CadCartaoPagseguroConnectComponent } from './cad-cartao-pagseguro-connect/cad-cartao-pagseguro-connect.component';
import {
  AddressAutocompletePlacesComponent
} from "./googlemaps/address-autocomplete-places/address-autocomplete-places.component";
import { CadCartaoEredeComponent } from './cad-cartao-erede/cad-cartao-erede.component';
import {ThreeDSDialogComponent} from "./tela-acompanhar-pedido/three-ds-dialog";
import {ThreeDSRetornoComponent} from "./tela-acompanhar-pedido/three-ds-retorno";
import {TabStripModule} from "@progress/kendo-angular-layout";
import {ProdutoIconeAlimentarComponent} from "../produtos/produto-icone-alimentar";
import {TagsProdutoComponent} from "../produtos/tags-produto.component";
import {
    ListaEmpresasMultiMarcaComponent
} from "../../../../src/app/multimarca/lista-empresas-multi-marca/lista-empresas-multi-marca.component";
import {SiteBrindesComponent} from "../produtos/brindes/site-brindes.component";
import {TabletPedidosComponent} from "./tablet-pedidos/tablet-pedidos.component";
import { SiteProdutoTabletComponent } from '../site-produto-tablet/site-produto-tablet.component';
import {CadCartaoTunapayComponent} from "./cad-cartao-tunapay/cad-cartao-tunapay.component";
import {PixelTrackingService} from "../services/pixel-tracking.service";
import { TunapayCarteirasDigitaisComponent } from '../loja-pagamento/formas-pagamentos/tunapay-carteirasdigitais.componet';
import {MyDetectorDevice} from "./shared/MyDetectorDevice";
import {PagamentoPixQrcodeComponent} from "./tela-acompanhar-pedido/pagamento-pix/pagamento-pix-qrcode.component";
import {ModalVincularComandaComponent} from "../loja-modal-vincular-comanda/modal-vincular-comanda.component";
import {ZXingScannerModule} from "@zxing/ngx-scanner";

import {TabletAuthComponent} from "./tablet-pedidos/autenticar/tablet-auth.component";
import {TabletPedidosConfigurarComponent} from "./tablet-pedidos/associar-tablet/tablet-pedidos-configurar";

// @ts-ignore
export const options: Partial | (() => Partial) = null;
registerLocaleData(localPT, 'pt-BR');

@NgModule({
    declarations: [
        AppComponent,
        PaginaComponent,
        SiteProdutosComponent, ProdutoContainerComponent, SiteCategoriasComponent, SiteBrindesComponent,
        SiteProdutoComponent, FinalizarPedidoComponent, ProdutoIconeAlimentarComponent, TagsProdutoComponent,
        FormasPagamentoLojaDesktopComponent, FormasPagamentoLojaDesktopNovaComponent,
        FormasPagamentoLojaMobileComponent, FormasPagamentoLojaMobileNovaComponent, TrocoLojaComponent, TunapayCarteirasDigitaisComponent,
        ModalEscolherEnderecoComponent,
        TopoComponent,
        CarrinhoComponent,
        LojaCarrinhoComponent,
        HeaderTelaComponent,
        EscolherEnderecoComponent,
        AutofocusDirective,
        AfterValueChangedDirective, TelefonePipe, CPFPipe,
        LojaPagamentoComponent,
        EntregaComponent,
        LojaDadosClienteComponent,
        LojaFooterComponent,
        FormEnderecoComponent, ValorTrocoPedido, CPFValidator, NomeConmpletoValidator,
        NomeValidoValidator, LogradouroContatoValidator, ValidadeCartaoValidator,
        MaxlengthDirective,
        SelecionarNoFocoDirective,
        FiltroProdutosPipe,
        TelaBuscaProdutosComponent,
        LojaMenuComponent,
        LojaPerfilComponent,
        CadContatoLojaComponent,
        PainelLoginComponent,
        PedidosLojaComponent,
        HeaderLojaComponent,
        TelaPedidoComponent,
        TelaAcompanharPedidoComponent, ThreeDSDialogComponent, ThreeDSRetornoComponent, PagamentoPixQrcodeComponent,
        MeusDadosComponent,
        ValidarContatoComponent,
        ConfirmarContatoComponent,
        SiteCampoAdicionalComponent,
        TopoDesktopComponent,
        LojaRecuperarComponent,
        PedidoRibbonComponent,
        AgendarEntregaComponent,
        ExibirWhatsappComponent,
        CadCartaocreditoPagSeguroComponent,
        TelaCartaoDeCreditoComponent,
        ValidadorPagamentoPagSeguroDirective,
        AdicionaisCustomizadosComponent,
        SiteMontarpizzaComponent,
        SiteAdicionarProdutoComponent,
        TelaAdicionalPedidoComponent,
        PopupEnderecoChinaInboxComponent,
        CadCartaoMercadopagoComponent,
        PaginaFranquiaComponent,
        TelaEnderecoRedirecionarComponent,
        DialogListarLojasComponent,
        TelaListaLojasComponent,
        LojaConfiguracoesComponent,
        TelaPosicaoMapaComponent,
        TelaBuscaEcommerceComponent,
        CadCartaoCieloComponent,
        CadCartaoPagarmeComponent,
        TelaPreencherEnderecoComponent,
        TelaMultiLojaComponent,
        PaginaMultiLojaComponent,
        TelaAvaliarPedidoComponent,
        SeletorCodigoPaisComponent, BandeiraLogoLojaComponent,
        SeletorCodigoPaisComponent,
        SiteWizardProdutosIaComponent,
        FinalizarPedidoTotemComponent,
        PainelHorariosComponent,
        ValidarContatoZapComponent,
        CadCartaoPagseguroConnectComponent,
        AddressAutocompletePlacesComponent,
        CadCartaoEredeComponent, ListaEmpresasMultiMarcaComponent, CadCartaoTunapayComponent,
        TabletPedidosComponent, TabletPedidosConfigurarComponent, TabletAuthComponent,
        SiteProdutoTabletComponent, ModalVincularComandaComponent
    ],
    imports: [
        IntlModule,
        BrowserModule,
        BrowserAnimationsModule,
        AppRoutingModule,
        HttpClientModule,
        HttpClientJsonpModule,
        CommonModule,
        InlineSVGModule,
        FormsModule,
        DropDownsModule,
        RadioButtonModule, ButtonModule,
        NumericTextBoxModule,
        NgxMaskModule.forRoot(options),
        MatTabsModule, InfiniteScrollModule,
        CurrencyMaskModule,
        InputsModule,
        DialogModule,
        WindowModule,
        DatePickerModule,
        DateTimePickerModule,
        TimePickerModule, DateInputsModule, NavigationModule,
        NgImageSliderModule,
        NotificationModule,
        NgxSmartBannerModule, ScrollingModule, LabelModule, NgbRatingModule, TabStripModule,
        ZXingScannerModule
    ],
    providers: [
        { provide: LOCALE_ID, useValue: 'pt-BR' },
        ConstantsService,
        MyDetectorDevice,
        CookieService,
        PixelTrackingService
    ],
    exports: [
        AutofocusDirective,
        SelecionarNoFocoDirective,
        PaginaComponent,
        AfterValueChangedDirective
    ],
    bootstrap: [AppComponent]
})
export class AppModule { }
