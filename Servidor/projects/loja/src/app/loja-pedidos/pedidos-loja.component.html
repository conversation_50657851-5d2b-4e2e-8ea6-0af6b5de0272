<app-header-loja [titulo]="'Pedidos já realizados'"></app-header-loja>

<span *ngIf="carregou && !pedidos.length">
  <img src="/assets/loja/sem-pedidos.png" class="centralizado text-muted mt-3 mb-3">

  <p class="text-center mt-3">Você não tem nenhum pedido</p>

  <a class="btn btn-light centralizado" routerLink="/loja/index"> Ver produtos da loja</a>
</span>


<span *ngIf="carregou && pedidos.length" class="mt-4 mb-2">

  <div *ngFor="let pedido of pedidos" class="d-pedidos">
     <span class="text-dark"><b>#{{pedido.codigo}}</b></span>


     <span class="badge  ml-2 mt-1"
           [ngClass]="{'badge-danger':pedido.cancelado, 'badge-blue': !pedido.cancelado }">
       {{pedido.status  ==='Novo' ? 'Confirmado' : pedido.status}}</span>

     <span class="float-right preco"><b>{{pedido.total | currency: "BRL" }}</b></span>

     <p class="text-muted">{{pedido.horarioDescricao}}

       <span  class="badge badge-light ml-2"  >{{pedido.formaDeEntrega}}</span>
     </p>

     <a routerLink="/loja/exibir-pedido/{{pedido.guid}}" *ngIf="!pedido.aguardandoPagamentoOnline" class="text-blue">
       ver pedido</a>

     <a routerLink="/loja/pedido/acompanhar/{{pedido.guid}}" *ngIf="pedido.aguardandoPagamentoOnline" class="text-blue">
       acompanhar pedido</a>
  </div>

</span>

