import { Component, OnInit } from '@angular/core';
import {ITela} from "../../objeto/ITela";
import {PedidosService} from "../../services/pedidos.service";

@Component({
  selector: 'app-pedidos-loja',
  templateUrl: './pedidos-loja.component.html',
  styleUrls: ['./pedidos-loja.component.scss']
})
export class PedidosLojaComponent implements OnInit , ITela{
  pedidos = [];
  carregou = false;
  constructor( private pedidosService: PedidosService) { }

  ngOnInit(): void {

    this.pedidosService.listePedidos(0, 50).then(  pedidos => {
      this.carregou = true;
      this.pedidos = pedidos;
    })
  }

  deveExibirTopo() {
    return false;
  }

  deveExibirMenu(){
    return true;
  }

  deveTerBordas() {
    return true;
  }

  deveExibirBannerTema() {
    return false;
  }
}
