<div id="telaConfirmacao" class="tela-confirmacao" *ngIf="sucesso">
  <div style="width: 100%;overflow: hidden;">
    <img id="imagemPrincipal" name="imagemPrincipal" #imagemPrincipal src="/assets/loja/alsultan-tela-totem-horizontal.jpg" style="width: 100%;"/>
  </div>
  <div class="container">
    <div class="row justify-content-center align-items-center">
      <div class="col-8 d-flex align-items-center">
        <!-- Ícone à esquerda -->
        <i class="fas fa-check-circle text-success" style="font-size: 80px;"></i>
        <!-- Conteúdo do texto à direita -->
        <div class="ml-3">
          <h2>
            {{pedido.contato.nome}}, o seu pedido confirmado!
          </h2>
          <p>Seu pedido foi realizado com sucesso.</p>
          <span>Código do seu Pedido</span>
          <h1 style="font-size: 60px;">#{{pedido.codigo}}</h1>
        </div>
      </div>
    </div>

    <div class="text-center mt-5">
      <button class="btn btn-lg btn-primary" (click)="fecharTelaPedido()">Fechar Tela</button>
    </div>
  </div>
</div>

<div class="container mt-3">

  <div class="row mb-4">
    <div class="col-12">
      <h2>Finalizar pedido</h2>
    </div>
    <div class="col-md-12">
      <button onclick="history.back()" class="btn btn-secondary btn-lg">&larr; Voltar</button>
    </div>
  </div>
  <hr>

  <div class="row">
    <div class="col-md-12">
      <form style="min-height: calc(100vh - 350px);display: flex;flex-direction: column;" (ngSubmit)="onSubmit($event)" #frmPedidoTotem="ngForm"
            [ngClass]="{'needs-validation': !frmPedidoTotem.submitted, 'was-validated': frmPedidoTotem.submitted}"  novalidate>
        <!-- Campo Nome -->
        <div class="form-group">
          <label for="nome">Nome</label>
          <input type="text" class="form-control form-control-lg" id="nome" name="nome" #nomeInput
          #nome="ngModel" placeholder="Digite seu nome" [(ngModel)]="dadosPedidoTotem.nome"
          autofocus [autofocus]="true" required  [disabled]="enviandoPedido">

          <div *ngIf="nome.errors?.required" class="invalid-feedback mt-1 alert alert-danger erro-mensagem" role="alert">
            O seu nome é obrigatório.
          </div>
        </div>

        <!-- Campo Telefone -->
        <div class="form-group">
          <label for="telefone">Telefone</label>
          <kendo-maskedtextbox  id="telefone" name="telefone"  #telefone="ngModel" [mask]="phoneMask" (change)="informouTelefone()"
                                #kendoMaskedTextBox="kendoMaskedTextBox"
                                class="form-control form-control-lg my-custom-maskedtextbox"  [disabled]="enviandoPedido"
                                [(ngModel)]="dadosPedidoTotem.telefone"   required>
          </kendo-maskedtextbox>
          <!-- mensagem de erro required -->
          <div *ngIf="telefone.errors?.required" class="invalid-feedback mt-1 alert alert-danger erro-mensagem" role="alert">
              Informe seu telefone.
          </div>
        </div>

        <!-- Campo Código da Comanda -->
        <div class="form-group">
          <label for="comanda">Código da Comanda</label>
          <input type="text" id="comanda" name="comanda" #comanda="ngModel"
                 class="form-control form-control-lg" [disabled]="enviandoPedido"
                 [(ngModel)]="dadosPedidoTotem.comanda" required>

          <div *ngIf="comanda.errors?.required" class="invalid-feedback mt-1 alert alert-danger erro-mensagem" role="alert">
            Informe o código da comanda.
          </div>
        </div>

        <div class="row" *ngIf="msgErro">
          <div class="col-md-12">
            <div class="alert alert-danger erro-mensagem" role="alert">
              {{msgErro}}
            </div>
          </div>
        </div>

        <!-- Botão de Envio -->
        <footer class="footer">
        <div style="margin-top: auto;">
          <div class="d-flex justify-content-center" >
            <button type="submit" class="btn btn-primary btn-xl">Finalizar Pedido</button>
          </div>
        </div>
        </footer>
      </form>
    </div>
  </div>

  <div style="height: 100px;">&nbsp;</div>
</div>
