<div #appendTo class="append-container"></div>

<div id="mapid" [style.height.px]="altura">
</div>

<div class="form-salvar-localizacao">
  <div class="card card-body">
    <div class="alert alert-danger mb-2" role="alert" *ngIf="msgErro">
      <i class="fas fa-exclamation-triangle"></i> {{msgErro}}
    </div>
    <div class="alert alert-warning mb-2" role="alert" *ngIf="msgWarning">
      <i class="fas fa-exclamation-triangle"></i> <strong>{{msgWarning}}</strong>
    </div>

    <p>
      <strong><i class="fas fa-mouse-pointer font-18"></i> &nbsp;Arraste o mapa para escolher seu endereço.</strong>
    </p>

    <div class="alert alert-success mt-2" role="alert" *ngIf="msgAlterouLocalizacao">
      <i class="mdi mdi-check-all mr-2"></i> {{msgAlterouLocalizacao}}
    </div>

    <div>
      <button class="btn btn-danger" (click)="salvarLocalizacao()" [disabled]="salvando || calculandoTaxa">Salvar Localização</button>
      <button class="btn btn-primary ml-2" (click)="cancelarMudarLocalizacao()">Voltar</button>
    </div>
  </div>
</div>
