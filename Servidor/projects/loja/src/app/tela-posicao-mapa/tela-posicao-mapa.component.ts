import {AfterViewInit, Component, Input, OnInit, ViewChild, ViewContainerRef} from '@angular/core';
import {ITela} from "../../objeto/ITela";
import {ActivatedRoute, Router} from "@angular/router";
import {Location} from "@angular/common";
import {Endereco} from "../../objeto/Endereco";
import {CarrinhoService} from "../../services/carrinho.service";
import {GeoService} from "../../services/geo.service";
import {DominiosService} from "../../services/dominios.service";
import {FormaDeEntrega} from "../../objeto/FormaDeEntrega";
import {EnderecoService} from "../../services/endereco.service";
import {FormEnderecoComponent} from "../form-endereco/form-endereco.component";
import {MyDetectorDevice} from "../shared/MyDetectorDevice";
import { NotificationService } from "@progress/kendo-angular-notification";

declare var L;

@Component({
  selector: 'app-tela-posicao-mapa',
  templateUrl: './tela-posicao-mapa.component.html',
  styleUrls: ['./tela-posicao-mapa.component.scss']
})
export class TelaPosicaoMapaComponent implements OnInit, ITela, AfterViewInit {
  @Input() latitude;
  @Input() longitude;
  @Input() formEndereco: FormEnderecoComponent;
  @Input() preencherEndereco: boolean;

  @ViewChild("appendTo", { read: ViewContainerRef })
  public appendTo: ViewContainerRef;

  map: any;
  mudandoLocalizacao = false;
  marcaLoja: any;
  msgAlterouLocalizacao: any;
  salvando: any;
  pedido: any;
  nomePagina: string;
  calculandoTaxa = false;
  msgErro = '';
  msgWarning = '';
  isMobile = false;
  altura = 200;
  posicaoOriginal = null;
  circuloLimites = null;
  gps = false;

  constructor(private router: Router, private activatedRoute: ActivatedRoute, private _location: Location,
              private geoService: GeoService, private carrinhoService: CarrinhoService,
              private dominiosService: DominiosService, private enderecoService: EnderecoService,
              private detectorDevice: MyDetectorDevice,
              private notificationService: NotificationService) {
    this.nomePagina = dominiosService.obtenhaRaizCardapio();

    this.isMobile = this.detectorDevice.isMobile();
    this.altura = window.innerHeight - 160;

    if( this.isMobile ) {
      this.altura = window.innerHeight;
    }

    this.activatedRoute.queryParams.subscribe( (params) => {
      if (params.gps) {
        this.gps = true;
      }
    });
  }

  ngOnInit(): void {
  }


  deveExibirMenu() {
    return false;
  }

  deveExibirTopo() {
    return false;
  }

  ngAfterViewInit(): void {
    this.pedido = this.carrinhoService.obtenhaPedido();

    this.activatedRoute.queryParams.subscribe( (queryParams) => {
      const latitude = queryParams.lat;
      const longitude = queryParams.lng;
      this.preencherEndereco = true;

      if( queryParams.e === '0' ) {
        this.preencherEndereco = false;
      }

      delete L.Icon.Default.prototype._getIconUrl;

      L.Icon.Default.mergeOptions({
        iconRetinaUrl: '/assets/leaflet/images/marker-icon-2x.png',
        iconUrl: '/assets/leaflet/images/marker-icon.png',
        shadowUrl: '/assets/leaflet/images/marker-shadow.png'
      });

      this.map = L.map('mapid', {
        scrollWheelZoom: 'center',
        minZoom: 9
      }).setView([this.latitude, this.longitude], 16);

      this.posicaoOriginal = this.map.getCenter();

      this.map.on('move', (evento) => {
        this.marcaLoja.setLatLng(this.map.getCenter());
        this.marcaLoja.closePopup();
      });

      this.map.on('moveend', (evento) => {
        const posicao = this.map.getCenter();
        const distancia = posicao.distanceTo(this.posicaoOriginal);
        if( distancia > 500 ) {
          if( !this.circuloLimites ) {
            this.circuloLimites = L.circle(this.posicaoOriginal, {
              autoPan: true,
              draggable: false,
              radius: 500
            });

            this.circuloLimites.addTo(this.map);
          }

          setTimeout( () => {
            this.exibaMensagemErroLocalizacaoDistante();
            //this.map.setView(this.posicaoOriginal, 16);
            //this.marcaLoja.setLatLng(this.map.getCenter());
          }, 300);
        }
      });

      this.crieMarcaLoja(this.latitude, this.longitude);

      const popup = this.marcaLoja.addTo(this.map).bindPopup(`<p><strong>Você está aqui.</strong></p>`).openPopup();

      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
      }).addTo(this.map);
    });
  }

  private crieMarcaLoja(latitude: string, longitude: string) {
    if( this.marcaLoja ) {
      for( let assinante of this.marcaLoja.assinantes ) {
        this.map.off(assinante.evento, assinante.fn);
      }
      this.marcaLoja.removeFrom(this.map);
    }

    this.marcaLoja = L.marker([latitude, longitude], {
      autoPan: true,
      draggable: false
    });

    this.marcaLoja.addTo(this.map);
  }

  exibaMensagemErroLocalizacaoDistante() {
    this.msgWarning = "Você selecionou um local muito distante do endereço informado. " +
      "Sua entrega será realizada pela localização informada.";
  }

  salvarLocalizacao() {
    this.calculandoTaxa = true;

    let entrega = this.carrinhoService.obtenhaPedido().entrega;
    let endereco = entrega.endereco;

    if( this.preencherEndereco && this.gps ) { //usuario escolheu usar localização gps
      this.geoService.obtenhaEndereco(this.map.getCenter()).then((respEndereco) => {
        endereco = Endereco.novo();
        Object.assign(endereco, respEndereco);
        this.pedido.entrega.formaDeEntrega = FormaDeEntrega.RECEBER_EM_CASA;
        this.pedido.entrega.gps = true;
        this.pedido.entrega.endereco = endereco;

        endereco.gps = true;

        //this.carrinhoService.salvePedido(this.pedido);

        this.calculandoTaxa = false;
        this.formEndereco.salvouALocalizacao(endereco, false);
      });
    } else {
      endereco.localizacao = this.map.getCenter().lat + "," + this.map.getCenter().lng;

      this.enderecoService.calculeTaxaDeEntrega('Receber em casa',
        endereco, this.pedido.obtenhaSubTotal()).then( (dados) => {
        this.pedido.novosEnderecos.push(endereco);
        this.pedido.entrega.setTaxaEntrega(endereco, dados);
        this.carrinhoService.salvePedido(this.pedido);

        this.calculandoTaxa = false;
        this.formEndereco.salvouALocalizacao(endereco, true);
      }).catch( erro => {
        this.calculandoTaxa = false;
        this.msgErro = erro;
      });
    }
  }

  cancelarMudarLocalizacao() {
    this.formEndereco.clicouVoltarLocalizacao();
  }

  deveTerBordas() {
    return false;
  }

  deveExibirBannerTema() {
    return false;
  }
}
