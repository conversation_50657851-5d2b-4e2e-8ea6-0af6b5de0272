.busca {
  height: 32px;
  width: 30em;
}

.search-results {
  height: auto;
  overflow: hidden;
}


.box-imagem{
  height: 200px;
}
.img-fluid {
  max-height: 200px;
  max-width: 150px;
}

.nome-produto{
  height: 45px;
  margin: 0px 10px;
}

::ng-deep .k-breadcrumb .k-breadcrumb-link {
  color: #3d033d !important;
}

.filtros{

  .card-title{
    color: #000;
    font-size: 16px;
    line-height: 2;
    cursor: pointer;
    border-bottom: 1px #333 solid;
  }

  a {
     display: block;
     color:#333333;
     margin-bottom: 10px;
   }

    a.categoria-pai{
      color: #56167D !important;
      font-weight: 500;

      &.selecionado{
        font-weight: 800;
      }
    }

    a.selecionado{
      color: #3d033d !important;
      text-decoration: underline;
    }

}

.left-side-menu{
  left: 0px;
  bottom: 0px;
  top: auto;
  width: 80%;
  transition: all .2s ease-out;
  z-index: 10010 !important;
  padding-top: 0px;

  &.exibir{
    display: block;
  }

  .slimscroll-menu{
    overflow: scroll;
    max-height: 800px;
  }

  ul > li > a {
    color: #6e768e;
    display: block;
    padding: 2px 15px;
    position: relative;
    transition: all 0.4s;
    font-family: "Poppins", sans-serif;
    font-size: .875rem;
  }

  .nav-second-level{
    margin-left: 15px;
  }
}

.modal-backdrop  {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: #333;
  opacity: .5;
  z-index: 10001 !important;
}

.preco{
  color: #6db31b;
}

@media (max-width: 768px) {
  .busca{
    max-width: 100%;
    margin-top: 16px;
  }
  ::ng-deep .k-breadcrumb .k-breadcrumb-link,
  ::ng-deep.k-breadcrumb .k-breadcrumb-root-link{
    font-size: 12px;

    padding: 6px 0px;
  }

  .titulo{
    padding-top: 15px !important;
    background: #000000c9;
    padding-bottom: 15px !important;
    h4{
      color: #fff
    }
  }
  .row{
    margin-left: -6px !important;
    margin-right: -6px !important;
    .col-6, .col-12{
      padding-right: 6px;
      padding-left: 6px;
    }
  }
}


