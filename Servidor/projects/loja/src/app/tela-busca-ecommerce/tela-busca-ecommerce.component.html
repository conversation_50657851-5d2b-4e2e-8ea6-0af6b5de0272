<div class="row mt-2">
  <div class="col">
    <kendo-breadcrumb [items]="items" (itemClick)="onItemClick($event)" separatorIcon="line"></kendo-breadcrumb>
  </div>
  <div class="col">

    <kendo-textbox class="busca" name="filtro" placeholder="O que você procura?" (valueChange)="onChange($event)"
                   [(ngModel)]="filtro" [showSuccessIcon]="false"  appAutoFocus [autoFocus]="true">
      <ng-template kendoTextBoxSuffixTemplate>
        <kendo-textbox-separator></kendo-textbox-separator>
        <button kendoButton [look]="'clear'" [icon]="'loading'" [hidden]="!buscando"></button>
        <button kendoButton [look]="'clear'" [icon]="'search'" [hidden]="buscando" (click)="busqueProdutos(0, this.filtro)"></button>
      </ng-template>
    </kendo-textbox>
  </div>
</div>

<div class="row mt-2 filtros">
  <div class="d-none d-sm-block col-sm-3">
    <div class="card" >
       <div class="card-body">
         <h4>Filtrar Resultados</h4>

         <div class="card-title">
            {{categoriaTopo?.nome}}
         </div>

         <ng-container *ngFor="let subcategoria of categoriaTopo?.subcategorias">

           <span >
                 <a href="" (click)="navegueParaCategoria(subcategoria)" class="categoria-pai"
                    [ngClass]="{'selecionado' : subcategoria.id === categoria?.id}"
                 > {{subcategoria.nome}} </a>

                 <a href="" (click)="navegueParaCategoria(catfilha)" class=" ml-2" *ngFor="let catfilha of subcategoria.subcategorias; let i = index;"
                  [hidden]="i >= 8 && !subcategoria.mostrarMais" [ngClass]="{'selecionado' : catfilha.id === categoria?.id}">
                 {{catfilha.nome}}</a>

                 <p *ngIf="subcategoria.subcategorias.length > 8" (click)="subcategoria.mostrarMais = true" class="cpointer  text-blue"
                  [hidden]="subcategoria.mostrarMais">
                      <i class="k-icon k-i-plus"></i>       Mostar mais</p>
           </span>



         </ng-container>

         <ng-container *ngIf="!categoria" >
           <ng-container *ngFor="let categoria of categorias">
             <div class="card-title">
               {{categoria.nome}}
             </div>
             <ng-container *ngFor="let subcategoria of categoria.subcategorias">

               <a href="" (click)="navegueParaCategoria(subcategoria)" class="categoria-pai"
                  [ngClass]="{'selecionado' : subcategoria.id === categoria?.id}"
               > {{subcategoria.nome}} </a>

               <a href="" (click)="navegueParaCategoria(catfilha)" class=" ml-2" *ngFor="let catfilha of subcategoria.subcategorias; let i = index;"
                  [hidden]="i >= 8 && !subcategoria.mostrarMais" [ngClass]="{'selecionado' : catfilha.id === categoria?.id}">
                 {{catfilha.nome}}</a>

               <p *ngIf="subcategoria.subcategorias.length > 8" (click)="subcategoria.mostrarMais = true" class="cpointer  text-blue"
                  [hidden]="subcategoria.mostrarMais">
                 <i class="k-icon k-i-plus"></i>       Mostar mais</p>


             </ng-container>

           </ng-container>

         </ng-container>

       </div>
    </div>
  </div>
  <div class="col-12 col-sm-9">
    <h3>{{categoria?.nome}}</h3>

    <h6>Produtos encontrados: <i class="k-i-loading k-icon ml-2" *ngIf="buscando"></i></h6>


    <div class="card mt-2" *ngIf="!produtos.length && buscouTodos">
      <div class="card-body">
          <h5 style="line-height: 20px;">Não foi encontrado nenhum produto

            <span *ngIf="categoria">na categoria <b>"{{categoria.nome}}"</b></span>

            <span *ngIf="filtro"> que contenha  "<b>{{filtro}}</b>"</span>
          </h5>

        <a class="text-primary" href=""  (click)="verTodos($event)">voltar</a>

      </div>
    </div>


    <div class="row search-results"
         infiniteScroll
         [infiniteScrollDistance]="2"
         [infiniteScrollThrottle]="24"
         (scrolled)="onScroll()"
         [scrollWindow]="true">

      <div class="col-6 col-sm-4  produto pt-2 pb-2 cpointer"    (click)="abraDetalhesProduto(produto)"  *ngFor="let produto of produtos">

        <div style="border: solid 1px #efefef;">
          <div    class="justify-content-center align-items-center box-imagem d-flex " >
            <img *ngIf="produto.imagens && produto.imagens.length > 0;  " class="img img-fluid"
                 [src]="'/images/empresa/' + produto.imagens[0].linkImagem" alt="Imagem" >

          </div>
          <div class="media">
            <div class="media-body pt-2  text-center">
              <h5 *ngIf="produto.indisponivel" class="text-danger">Indisponível!</h5>

              <p class="mb-2 nome-produto">{{produto.nome}}</p><br>

              <h5 class="mt-0 mb-1 font-15 preco">
                <span class=" text-muted font-12"  *ngIf="produto.valorMinimo"><i>A partir de</i></span>

                {{(produto.valorMinimo ? produto.valorMinimo : produto.preco) | currency: 'BRL'}}</h5>

              <h5 class="preco font-14" *ngIf="!produto.indisponivel">

                <i *ngIf="produto.qtdMaxima"><label class="text-muted" style="font-size: 10px">*Até {{produto.qtdMaxima}} por pedido</label></i>
                <span *ngIf="exibirUnidade(produto)" class="text-muted font-11">
               / kg
            </span>
              </h5>



            </div>

          </div>
        </div>
      </div>



    </div>
    <div class="row mt-2 mb-2" [hidden]="!buscandoScroll">
      <div class="col text-center ">
         <i class="k-i-loading k-icon mr-1"></i> carregando...
      </div>
    </div>

    <div style="padding-bottom: 80px"></div>

  </div>
</div>


<span class=" d-block d-sm-none">
  <div class="left-side-menu enlarged filtros  "  [ngClass]="{'exibir': sharedDataService.exibirMenuMobile}">
    <div class="slimScrollDiv">
      <div class="enlarged slimscroll-menu">

        <div class="nav-user titulo">
          <h4>Filtrar Resultados</h4>
        </div>

        <div class="nav-user mt-2">
            <div class="card-title">  {{categoriaTopo?.nome}}    </div>
        </div>

        <div class="sidebar-menu"  >
            <ul class="metismenu">
              <li  *ngFor="let subcategoria of categoriaTopo?.subcategorias">
                <a href="" (click)="navegueParaCategoria(subcategoria)" class="categoria-pai"> {{subcategoria.nome}}</a>

                <ul class="nav-second-level">
                  <li *ngFor="let catfilha of subcategoria.subcategorias; let i = index;">
                    <a href="" (click)="navegueParaCategoria(catfilha)"> {{catfilha.nome}}</a>
                  </li>
                </ul>
            </li>

              <li *ngIf="false">
                <a  class="cpointer  text-muted" (click)="fecheMenuCategorias();">
                      <i class="fa fa-arrow-left ct-point"></i> fechar</a>
              </li>
          </ul>
        </div>
      </div>
    </div>
  </div>

   <div class="modal-backdrop" *ngIf="sharedDataService.exibirMenuMobile" (click)="sharedDataService.notifiqueExibirMenu()"></div>
</span>


