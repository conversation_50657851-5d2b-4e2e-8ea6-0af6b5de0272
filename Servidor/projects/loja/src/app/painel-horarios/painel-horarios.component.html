 <app-header-tela [titulo]="empresa.nome" [exibirFechar]="true" (fechou)="fecheTela()"  >  </app-header-tela>

<h5 class="mt-3  ">
  <i class="{{horarioDoServico.icone}}" kendoTooltip title="{{horarioDoServico.descricao}}" ></i>
  Hor<PERSON>rios <b>{{horarioDoServico.descricao}}</b></h5>


<div class="calendario">
  <ng-container *ngFor="let diaSemana of diasDaSemana; let dia  = index;">
    <div class="row" [class.hoje]="ehHoje(dia)">
      <div class="col-3">
        {{diaSemana}}
      </div>
      <div class="col-9">
      <span *ngFor="let horario of obtenhaHorariosDia(dia); let i = index; let ultimo = last"><b>
        {{obtenhaHorario(horario.horarioAbertura) }} ás
        {{obtenhaHorario(horario.horarioFechamento )}}

        <span   [hidden]="ultimo">| </span>
      </b></span>
      </div>
    </div>
  </ng-container>
</div>


