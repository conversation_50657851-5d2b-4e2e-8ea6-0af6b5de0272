import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AddressAutocompletePlacesComponent } from './address-autocomplete-places.component';

describe('AddressAutocompletePlacesComponent', () => {
  let component: AddressAutocompletePlacesComponent;
  let fixture: ComponentFixture<AddressAutocompletePlacesComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ AddressAutocompletePlacesComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(AddressAutocompletePlacesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
