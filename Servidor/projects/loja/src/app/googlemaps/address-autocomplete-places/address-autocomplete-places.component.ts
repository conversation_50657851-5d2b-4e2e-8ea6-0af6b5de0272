/// <reference types="@types/googlemaps" />
import {
  AfterViewInit, ApplicationRef,
  Component,
  ElementRef,
  EventEmitter, Inject,
  Input,
  OnDestroy,
  OnInit,
  Output, Renderer2,
  ViewChild, ViewContainerRef
} from '@angular/core';
import {GoogleMapsService} from "../../../services/google-maps.service";

@Component({
  selector: 'app-address-autocomplete-places',
  templateUrl: './address-autocomplete-places.component.html',
  styleUrls: ['./address-autocomplete-places.component.scss']
})
export class AddressAutocompletePlacesComponent   implements OnInit, AfterViewInit {
  @ViewChild('search', { static: true }) searchElementRef: ElementRef;
  @ViewChild('map', { static: false }) mapElementRef: ElementRef;
  @Output() onSelecionou = new EventEmitter();
  @Input() empresa: any = {}
  @Input() cepObrigatorio: boolean;
  public enderecoEscolhido: any = {
    enderecoCompleto: '',
    logradouro: '',
    bairro: '',
    cep: '',
    localizacao: '',
    cidade: { nome: '' },
    estado: { nome: '' }
  };
  public manualEntry  = false;
  public showMap  = false;
  private autocomplete: google.maps.places.Autocomplete;
  private geocoder: google.maps.Geocoder;
  private map: google.maps.Map;
  private marker: google.maps.Marker;
  buscandoAutocomplete: boolean;
  // Defina as coordenadas para o local de entrega (por exemplo, Brasília, Brasil)
  private deliveryLocation: any;
  constructor(private googleMapsService: GoogleMapsService,      private app: ApplicationRef) { }

  ngOnInit(): void {
    let defaultBounds: any, componentRestrictions: any;
    this.googleMapsService.load('places').then(  () => {

      this.cepObrigatorio = this.empresa.formasDeEntrega.find((item: any) => item.cepObrigatorio) != null;

      if(this.empresa.latitudeLongitude){
        let coordenadas: any = this.empresa.latitudeLongitude.split(',');

        this.deliveryLocation = { lat: Number(coordenadas[0]), lng: Number(coordenadas[1])};
      } else   if(this.empresa.enderecoCompleto && this.empresa.enderecoCompleto.localizacao){
        let coordenadas: any = this.empresa.enderecoCompleto.localizacao.split(',');

        this.deliveryLocation = { lat: Number(coordenadas[0]), lng: Number(coordenadas[1])};
      }

      if(this.deliveryLocation){
        defaultBounds = new google.maps.LatLngBounds(
          new google.maps.LatLng(this.deliveryLocation.lat - 0.05, this.deliveryLocation.lng - 0.05),
          new google.maps.LatLng(this.deliveryLocation.lat + 0.05, this.deliveryLocation.lng + 0.05)
        );
      }

      let options: any = {
        types: ['address'] // Inclui todos os tipos de endereço
      }

      if(defaultBounds) {
        options.bounds = defaultBounds;
        options.strictBounds =  false;
      }

      if(componentRestrictions) options.componentRestrictions = componentRestrictions;


      this.autocomplete = new google.maps.places.Autocomplete(this.searchElementRef.nativeElement, options);

      this.geocoder = new google.maps.Geocoder();

      this.autocomplete.addListener('place_changed', async () => {

        const place: google.maps.places.PlaceResult = this.autocomplete.getPlace();

        if (place.geometry === undefined || place.geometry === null)
          return;


        await this.fillAddress(place);
        this.app.tick();
      });
    }).catch(error => {
      console.error('Error loading Google Maps API:', error);
    });
  }

  ngAfterViewInit(): void {
    // Simula o foco no input para abrir o dropdown do autocomplete
    setTimeout(() => {
      this.searchElementRef.nativeElement.focus();
    }, 300); // Ajuste o tempo conforme necessário

  }


  async fillAddress(place: google.maps.places.PlaceResult) {
    this.enderecoEscolhido.enderecoCompleto = place.formatted_address;
    this.enderecoEscolhido.localizacao =    String(`${place.geometry.location.lat()},${ place.geometry.location.lng()}`);

    place.address_components.forEach(component => {
      const componentType = component.types[0];

      switch (componentType) {
        case 'street_number':
          this.enderecoEscolhido.logradouro = `${component.long_name} ${this.enderecoEscolhido.logradouro}`;
          break;
        case 'route':
          this.enderecoEscolhido.logradouro += component.long_name;
          break;
        case 'sublocality_level_1':
        case 'locality':
          this.enderecoEscolhido.bairro = component.long_name;
          break;
        case 'administrative_area_level_1':
          this.enderecoEscolhido.estado.nome = component.long_name;
          break;
        case 'administrative_area_level_2':
          this.enderecoEscolhido.cidade.nome = component.long_name;
          break;
        case 'postal_code':
          this.enderecoEscolhido.cep = component.long_name;
          break;
      }
    });

    if(!this.enderecoEscolhido.cep && this.cepObrigatorio )
       await this.geocodeLatLng(place.geometry.location);

    console.log('Endereco Escolhido:', this.enderecoEscolhido);

    this.onSelecionou.emit(this.enderecoEscolhido)
  }

  async geocodeLatLng(latlng: google.maps.LatLng): Promise<any> {
    return new Promise((resolve) => {
      this.geocoder.geocode({ location: latlng }, (results, status) => {
        if (status === 'OK' && results[0]) {
          const place = results[0];
          place.address_components.forEach(component => {
            if (component.types[0] === 'postal_code') {
              this.enderecoEscolhido.cep = component.long_name;
              console.log('CEP atualizado:', this.enderecoEscolhido.cep);
            }
          });
        }
        resolve(null)
      });
    });
  }

  toggleMap(): void {
    this.showMap = !this.showMap;
    if (this.showMap && !this.map) {
      this.initMap();
    }
  }

  initMap(): void {
    const mapOptions: google.maps.MapOptions = {
      center: { lat: -15.7942, lng: -47.8822 }, // Brasília, Brasil
      zoom: 12
    };

    this.map = new google.maps.Map(this.mapElementRef.nativeElement, mapOptions);

    this.marker = new google.maps.Marker({
      map: this.map,
      draggable: true
    });

    this.map.addListener('click', (event: google.maps.MapMouseEvent) => {
      this.placeMarker(event.latLng);
    });

    this.marker.addListener('dragend', (event: google.maps.MapMouseEvent) => {
      this.enderecoEscolhido.localizacao = event.latLng;

      console.log('Localização Arrastada:', this.enderecoEscolhido.localizacao);
      this.app.tick();
    });
  }

  placeMarker(location: google.maps.LatLng | google.maps.LatLngLiteral): void {
    this.marker.setPosition(location);
    this.enderecoEscolhido.localizacao = location;
    console.log('Localização Clicada:', this.enderecoEscolhido.localizacao);
    this.app.tick();
  }
}
