import {Component, ElementRef, Input, OnInit, ViewChild} from '@angular/core';
import {ICartaoCreditoGateway} from "../../objeto/ICartaoCreditoGateway";
import {DadosCartao} from "../../objeto/DadosCartao";
import {PedidoLoja} from "../../objeto/PedidoLoja";
import {HttpClient} from "@angular/common/http";
import {CadCartaoPadrao} from "../../objeto/CadCartaoPadrao";
import {AutorizacaoLojaService} from "../../services/autorizacao-loja.service";
import { IFormCartaoRespostaValido } from '../../objeto/IFormCartaoRespostaValido';
import {NgForm} from "@angular/forms";

@Component({
  selector: 'app-cad-cartao-erede',
  templateUrl: './cad-cartao-erede.component.html',
  styleUrls: ['./cad-cartao-erede.component.scss']
})
export class CadCartaoEredeComponent implements OnInit, ICartaoCreditoGateway {
  @ViewChild('frm', { static: false})  frm: NgForm;
  @ViewChild('frm', { read: ElementRef }) public frmElement: ElementRef;
  mensagemSucesso: any;
  mensagemErro: any;
  empresa: any;
  processando: any;
  cartao: any = new DadosCartao();
  bandeira: any = null;
  carregando: boolean;
  @Input() pedido: PedidoLoja;
  tiposCartao = [{id: 'credit', nome: 'Crédito'},
    {id: 'debit', nome: 'Debito'}
  ]

  constructor(protected http: HttpClient, protected autorizacaoService: AutorizacaoLojaService,
              private cadCartaoPadrao: CadCartaoPadrao) {
  }

  ehValido(): IFormCartaoRespostaValido {
    return this.cadCartaoPadrao.ehValido(this.frm, this.frmElement);
   }

  fecheMensagemErro(){
    delete this.mensagemErro;
  }

  ngOnInit(): void {

    let usuario = this.autorizacaoService.getUsuario();

    if(usuario && usuario.cpf) this.cartao.cpf = usuario.cpf
    if(usuario && usuario.email) this.cartao.email = usuario.email;

    this.cartao.tipoDoCartao = this.tiposCartao[0];
  }

  crieTokenCartao(enderecoConbraca: any): Promise<DadosCartao> {
    let dadosCartao: any = {
      "number": this.cartao.numero,
      "holder_name": this.cartao.nome,
      "exp_month":  (this.cartao.validade.getMonth() + 1).toString().padStart(2, '0'),
      "exp_year":  this.cartao.validade.getFullYear(),
      "email": this.cartao.email,
      "cvv": this.cartao.cvv,
      "kind": this.cartao.tipoDoCartao.id,
    }


    return new Promise( (resolve, reject) => {
       this.http.post('/erede/cartao/tokenize', dadosCartao).toPromise().then((token: any) => {
         this.cartao.token = token;
         this.cartao.deviceInfo =  this.getDeviceInfo();
         resolve(this.cartao)
       }).catch((resp: any) => {
         let erro = resp.error && resp.error.erro ? resp.error.erro : (resp.erro || 'Falha gerar token cartão');
         reject(erro)
       })
    })
  }

  getDeviceInfo() {
    const deviceInfo: any = {
      colorDepth: window.screen.colorDepth, // Profundidade de cor da tela
      deviceType3ds: 'BROWSER', // Definido como 'BROWSER' por padrão
      javaEnabled: navigator.javaEnabled(), // Verifica se o Java está habilitado
      // @ts-ignore
      language: navigator.language || navigator.userLanguage, // Idioma do navegador
      screenHeight: window.screen.height, // Altura da tela
      screenWidth: window.screen.width, // Largura da tela
      timeZoneOffset: new Date().getTimezoneOffset() / 60 // Fuso horário em horas
    };

    return deviceInfo;
  }


  exibaCartao(dadosCartao: DadosCartao) {
  }
}
