<app-header-tela [titulo]="'Pedido'" [exibirFechar]="true" (fechou)="fecheTela()" ></app-header-tela>


<div class="container p-2">
  <h3>Avalie seu Pedido</h3>

  <div>
    <span class="text-muted">{{pedido.horario | date: 'dd/MM/yyyy' }}</span>
  </div>

  <hr class="mt-0">

  <div class="desktop font-11">
    <div *ngFor="let item of pedido.itens" class="produto pt-2 pb-2 item_produto">
      <div class="media">
        <div class="media-body">
          <h5 class="mt-0 mb-1"><span>{{item.qtde}} X</span> {{item.descricao}}</h5>
          {{item.produto.descricao}}

          <div *ngIf="item.adicionaisImprirmir?.length" >
                       <span  *ngFor="let last = last;let adicional of item.adicionaisImprirmir " class="d-block ml-2 font-weight-bold">
                         {{adicional.descricao}}{{last ? '' : ', '}}
                       </span>
          </div>

          <span *ngIf="item.observacao" class="font-11"><br>obs.:<i>"{{item.observacao}}"</i></span>
        </div>
        <h5 class="preco mt-0">{{item.total | currency: 'BRL'}}

        </h5>
      </div>
    </div>
  </div>

  <div class="pt-1">
    <h4 class="mb-0">O que achou do pedido?</h4>
    <ngb-rating [(rate)]="avaliacao.nota" [max]="5" (rateChange)="this.msgErro = '';">
      <ng-template let-fill="fill" let-index="index">
        <span class="star"  [class.filled]="index < avaliacao.nota" [class.bad]="avaliacao.nota <= 3">&#9733;</span>
      </ng-template>
    </ngb-rating>

    <h4>Você gostou da entrega?</h4>

    <hr class="mt-1 mb-1">
    <input type="radio" id="simEntrega" name="gostouEntrega" #gostouEntrega [(ngModel)]="avaliacao.gostouDaEntrega" kendoRadioButton [value]="true" />
    <kendo-label
      class="k-radio-label"
      for="simEntrega"
      text="Sim, gostei"></kendo-label>
    <hr class="mt-1 mb-1">
    <input type="radio" id="naoEntrega" name="gostouEntrega" [value]="false" #gostouEntrega
           kendoRadioButton [(ngModel)]="avaliacao.gostouDaEntrega" />
    <kendo-label
      class="k-radio-label"
      for="naoEntrega"
      text="Não, poderia melhorar"></kendo-label>
  </div>

  <div class="pt-3"></div>

  <kendo-label><i class="fas fa-comment-alt"></i> Deixar comentário</kendo-label>

  <textarea #comentario [style.width]="'100%'" class="form-control mt-1" [(ngModel)]="avaliacao.comentario">
  </textarea>

  <div class="alert alert-danger mt-2" role="alert" *ngIf="msgErro">
    <i class="mdi mdi-block-helper mr-2"></i> {{msgErro}}
  </div>

  <div class="pt-3">
    <button class="btn btn-block btn-primary p-2" (click)="enviarAvaliacao()" [disabled]="avaliando">Avaliar</button>
  </div>
</div>
