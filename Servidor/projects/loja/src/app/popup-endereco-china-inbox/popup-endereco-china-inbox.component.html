<div>
    <form id="frmEndereco" [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"
        novalidate #frm="ngForm" (ngSubmit)="onSubmit($event)">

    <div class="form-group mb-2" *ngIf="false">
      <label  >Nome Local</label>
      <input kendoTextBox   name="descricao"
             class="form-control" appAutoFocus [autoFocus]="true" #descricao="ngModel"
             [(ngModel)]="endereco.descricao" required/>

      <small id="emailHelp" class="form-text text-muted">Exemplos: Casa, Trabalho</small>

      <div class="invalid-feedback">
        <p *ngIf="descricao.errors?.required">Nome do Local é obrigatório</p>
      </div>
    </div>

    <button class="btn btn-outline-blue btn-rounded"
            style="position: absolute;top: 10px; right: 10px;z-index: 9999;background: #fff;" type="button"
            *ngIf="pedido.entrega.formaDeEntrega"
            (click)="fecheTela()">
      <i _ngcontent-men-c218="" class="fas fa-times"></i>
    </button>
    <div style="text-align: center;" class="mb-2 mt-2">
      <img src="/images/empresa/{{empresa.logo}}"
           width="100px;" style="text-align: center;"/>
    </div>

    <div style="text-align: center;" class="mb-2" *ngIf="processando">
      <i class="k-i-loading k-icon" style="font-size: 50px"></i>
      <div>
        Carregando
      </div>
    </div>

    <ng-container *ngIf="escolhaLojaRetirar">
      <ng-container *ngIf="grupoDeLojas">
        <h4>
          <button class="btn btn-link" (click)="voltarOpcaoEntregar()" style="margin-right: 5px;" type="button">
            <i class="fas fa-chevron-left" style="font-size: 24px;"></i>
          </button>
          Escolha uma de nossas lojas
        </h4>
        <div style="margin-left: -24px;margin-right: -24px;">
          <app-tela-lista-lojas [grupoDeLojas]="grupoDeLojas" [popoup]="true"></app-tela-lista-lojas>
        </div>
      </ng-container>
    </ng-container>

    <div   [hidden]="processando || escolhaLojaRetirar">
      <div class="text-center mt-4 mb-3" *ngIf="opcaoEntrega === ''">
        <h4 class="mb-2">O que você deseja?</h4>
        <button class="btn btn-primary" (click)="receberEmCasa()" [class.btn-lg]="ehDesktop">
          Receber em Casa
        </button>
        <span>&nbsp;&nbsp;ou&nbsp;&nbsp;</span>
        <button type="button" class="btn btn-blue" (click)="retirarNaLoja()" [class.btn-lg]="ehDesktop">
          Retirar na Loja
        </button>
      </div>

      <ng-container *ngIf="opcaoEntrega === 'retirada'">
        <div class="text-center mb-3" *ngIf="lojaCerta === false">
          <ng-container *ngIf="!lojaEncontrada">
            <h3 class="mt-2 mb-3 font-18">
              Ainda não entregamos na sua região, porém você pode fazer seu pedido agora e retirar em uma loja próxima.
            </h3>

            <button type="button" class="btn btn-primary" (click)="retirarNaLoja()" [class.btn-lg]="ehDesktop">
              Retirar na Loja
            </button>
            <span>&nbsp;&nbsp;ou&nbsp;&nbsp;</span>
            <button type="button" class="btn btn-blue" (click)="escolherOutroCEP()" [class.btn-lg]="ehDesktop">
              Mudar Endereço
            </button>
          </ng-container>

          <ng-container *ngIf="lojaEncontrada">
            <p class="mt-0" style="position: relative;top: -20px;">
              <button class="btn btn-link" (click)="mudarOpcaoEntrega()" style="margin-right: 5px;" type="button">
                <i class="fas fa-chevron-left" style="font-size: 24px;"></i>
              </button>
              Não fazemos delivery na sua região, porém encontramos a loja {{lojaEncontrada.nome}} que faz. O que deseja?
            </p>

            <button type="button" class="btn btn-primary" (click)="mudarLoja()" style="font-size: 1.0rem;" [class.btn-lg]="ehDesktop">
              <img src="https://www.chinainbox.com.br/ccstore/v1/images/?source=/file/general/cib_mob_icon-store.png&height=20&width=20"/>
              Acessar Loja {{lojaEncontrada.nome}}
            </button>
            <span>&nbsp;&nbsp;ou&nbsp;&nbsp;</span>
            <button type="button" class="ml-2 btn btn-secondary" style="font-size: 1.0rem;" (click)="retirarNaLoja()" [class.btn-lg]="ehDesktop">
              Retirar Nesta Loja
            </button>
          </ng-container>
        </div>
        <ng-container *ngIf="lojaCerta === null">
          <div class="text-center">
            <h4 class="mt-0">Onde você está agora?</h4>
          </div>

          <div class="mb-3">
            Você escolher: <b>Receber em Casa</b> &nbsp;
            <button type="button" class="btn btn-primary btn-xs" (click)="mudarOpcaoEntrega()">Mudar</button>
          </div>

          <div class="form-group mb-3" *ngIf="!escolherUsarLocalizacao">
            <div class="input-group">
              <div class="input-group-prepend" *ngIf="escolheuPreencherCep" (click)="voltarEscolhaPreencherCEP()">
                <span class="input-group-text" id="span-icone" style="background: none;border: none;">
                  <i class="fas fa-chevron-left" style="font-size: 24px;"></i>
                </span>
              </div>
              <input kendoTextBox #txtCEP (change)="alterou($event)"
                     type="text" class="form-control" autocomplete="off" style="width: calc(100% - 120px);"
                     id="cep" name="cep" [(ngModel)]="endereco.cep" #cep="ngModel"
                     (focusin)="focoCampoCEP()" [mask]="'00.000-000'"
                     [readonly]="escolheuUmEnderecoServer" tabindex="0"
                     placeholder="Informe o CEP." value="" [required]="true"/>

              <span class="input-group-prepend">
                      <button class="btn btn-secondary" type="button" (click)="alterou(null)"
                              style="border-top-right-radius: 3px;border-bottom-right-radius: 0px; ">
                          <i class="fa fa-search" *ngIf="!buscandoCEP"></i>
                        <div class="k-i-loading ml-1 mr-1" style="font-size: 20px;" *ngIf="buscandoCEP" ></div>
                      </button>
                </span>
            </div>
            <small id="cepHelp" class="form-text text-muted">Informe o CEP para buscar os dados da rua, bairro, cidade.</small>

            <div class="invalid-feedback">
              <p *ngIf="cep.errors?.required">Cep é obrigatório</p>
            </div>
          </div>

          <div class="form-group mb-2" *ngIf="!escolheuPreencherCep">
            <div class="input-group">
              <button type="button" id="btnEscolherUsarLocalizacao" style="background: none;border: none;" (click)="voltarEscolhaUsarLocalizacao()"
                      *ngIf="escolherUsarLocalizacao">
                  <i class="fas fa-chevron-left" style="font-size: 24px;"></i>
              </button>

              <button class="btnUsarLocalizacao btn btn-outline-secondary" type="button" (click)="usarLocalizacao()" [class.usandoLocalizacao]="escolherUsarLocalizacao">
                <i class="fas fa-location-arrow"></i>
                Usar Minha Localização
              </button>
            </div>
            <small id="localizacaoHelp" class="form-text text-muted">Clique no botão acima para o sistema pegar automaticamente sua localização.</small>
          </div>

          <ng-container *ngIf="cepValidado">
            <div class="form-group mb-2">
              <label for="logradouro">Logradouro</label>
              <input type="text" class="form-control" autocomplete="off"
                     id="logradouro" name="logradouro" [(ngModel)]="endereco.logradouro" #logradouro="ngModel"
                     [readOnly]="cepValidado && respostaCep.logradouro !== ''" #txtLogradouro
                     placeholder="Logradouro: Rua, av. etc" value="" required>
              <div class="invalid-feedback">
                <p *ngIf="logradouro.errors?.required">Endereço é obrigatório</p>
              </div>
            </div>

            <div class="row">
              <div class="form-group mb-2 col-4">
                <label for="numero">Número</label>
                <input type="number" class="form-control" autocomplete="off" #txtNumero
                       id="numero" name="numero" [(ngModel)]="endereco.numero" #numero="ngModel"
                       placeholder="Número" required>
                <div class="invalid-feedback">
                  <p *ngIf="numero.errors?.required">Número é obrigatório</p>
                </div>
              </div>

              <div class="form-group mb-2 col" style="padding-left: 0px;">
                <label for="bairro">Bairro</label>
                <input type="text" class="form-control" autocomplete="off"
                       id="bairro" name="bairro" [(ngModel)]="endereco.bairro" #bairro="ngModel"
                       placeholder="Nome do bairro"   [required]="true" >
                <div class="invalid-feedback">
                  <p *ngIf="bairro.errors?.required">Bairro é obrigatório</p>
                </div>
              </div>
            </div>

            <div class="form-group mb-2">
              <label for="complemento">Complemento</label>
              <input type="text" class="form-control" autocomplete="off"
                     id="complemento" name="complemento" [(ngModel)]="endereco.complemento"   #complementoInput
                     placeholder="ponto de referência, lote, quadra, etc." value="">

            </div>
            <div class="form-group mb-2">
              <label for="estado">Estado:</label>
              <kendo-combobox id="estado" name="estado" [(ngModel)]="endereco.estado" [data]="estados"  [filterable]="false"
                              placeholder="Selecione um Estado" class="form-control"   [textField]="'nome'" [valueField]="'id'"
                              required   #estado="ngModel" [readonly]="cepValidado" #cboEstado
                              autocomplete="disabled"
                              (valueChange)="mudouEstado($event)">
              </kendo-combobox>
              <div class="invalid-feedback">
                <p *ngIf="estado.errors?.required">Estado é obrigatório</p>
                <p *ngIf="estado.errors?.nomeCompleto">Informe o Estado</p>
              </div>
            </div>
            <div class="form-group mb-2">
              <label for="cidade">Cidade: </label>
              <kendo-combobox id="cidade" name="cidade" [(ngModel)]="endereco.cidade" [data]="cidades" [filterable]="false"
                              placeholder="Selecione uma Cidade" class="form-control"  [textField]="'nome'" [valueField]="'id'"
                              #cboCidade
                              required #cidade="ngModel"  [readonly]="cepValidado ">
              </kendo-combobox>
              <div class="invalid-feedback">
                <p *ngIf="cidade.errors?.required">Cidade é obrigatória</p>
              </div>
            </div>

            <div class="alert alert-danger mt-2 mb-2" role="alert" *ngIf="msgErro">
              <i class="fas fa-exclamation-triangle"></i> {{msgErro}}
            </div>

            <div style="height: 60px;width: 100%;">
            </div>
          </ng-container>

          <footer class="footer" *ngIf="exibirBotao && cepValidado">
            <div>
              <div class="row" style="padding: 15px;">
                <div class="col">
                  <button class="btn btn-blue btn-block" type="submit" [disabled]="calculandoTaxa" >
                    <i class="k-icon k-i-loading" *ngIf="calculandoTaxa"></i>
                    Continuar</button>
                </div>
              </div>
            </div>
          </footer>
        </ng-container>
      </ng-container>
    </div>
  </form>
</div>
