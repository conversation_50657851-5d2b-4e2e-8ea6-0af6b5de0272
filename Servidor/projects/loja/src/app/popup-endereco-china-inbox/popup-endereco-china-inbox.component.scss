::ng-deep .desktop .k-dialog-wrapper {
    justify-content: start;
    top: 50px;
  }

::ng-deep .desktop .k-window-content {
    padding: 24px;
}

.footer {
  border-top: solid 1px #eeeeee;
  background: #fff;
  padding: 0px;
  position: fixed;
}

@media (min-width: 1025px) {
  .footer {
    position: initial !important;
  }
}

.btnUsarLocalizacao {
  width: 100%;

  &.usandoLocalizacao {
    margin-left: 10px;
    width: calc(100% - 50px);
  }
}

.footer {
  border-top: solid 1px #eeeeee;
  background: #fff;
  padding: 0px;
  position: fixed;
  z-index: 9999;
  position: absolute !important;
  bottom: 0px;
  width: 100%;
  left: 0% !important;
  background-color: #f7f8f8;
}

::ng-deep .dialog-produto {
  overflow-x: hidden;
  position: initial;
}

::ng-deep .mobile .dialog-produto {
  padding: 12px;
}

::ng-deep .k-dialog-wrapper {
  z-index: 100001;
}
