import {AfterViewInit, Component, HostListener, Input, OnInit} from '@angular/core';
import {ClienteService} from "../../../services/cliente.service";
import {ActivatedRoute} from "@angular/router";
import {AutorizacaoLojaService} from "../../../services/autorizacao-loja.service";

@Component({
  selector: 'app-tela-lista-lojas',
  templateUrl: './tela-lista-lojas.component.html',
  styleUrls: ['./tela-lista-lojas.component.scss']
})
export class TelaListaLojasComponent implements OnInit, AfterViewInit {
  lojas: Array<any> = [];
  @Input() grupoDeLojas: any;
  @Input() popoup = false;
  @Input() mesa: string;

  filtro: any = {}
  timerBusca;
  inicio = 0
  buscando = false;
  buscouTodas = false;
  estaFazendoScroll: boolean
  urlInteligente = '';
  usuario: any;

  constructor(private clienteService: ClienteService, private activatedRoute: ActivatedRoute,
              private autorizacao: AutorizacaoLojaService) {
  }

  ngOnInit(): void {
    this.autorizacao.estaLogado().then((estahLogado) => {
      this.usuario = this.autorizacao.getUsuario();

      this.activatedRoute.queryParams.subscribe((queryParams) => {
        if (this.usuario && queryParams.u) {
          this.urlInteligente = queryParams.u;
        }
      });
    });
  }

  @HostListener('window:scroll', ['$event']) // for window scroll events
  onScroll(event) {
    if( this.estaFazendoScroll || this.buscando || this.buscouTodas) {
      return;
    }

    this.carregueLojas(this.filtro.texto);

  }

  limpeBusca(){
    this.inicio = 0;
    this.filtro = {};
    this.carregueLojas();
  }

  onFiltreLojas(event: any) {
    if(this.timerBusca) clearTimeout(this.timerBusca);

    this.timerBusca = setTimeout( () => {
      this.inicio = 0;
      this.lojas = [];
      this.carregueLojas(event.target.value)
    }, 1000)
  }

  carregueLojas(texto: string = null) {
    this.buscando = true;
    this.inicio  = this.lojas.length;

    this.clienteService.obtenhaLojasFranquia(this.grupoDeLojas, texto, this.inicio).then((lojas) => {
      lojas.forEach( loja => this.lojas.push(loja));
      this.buscando = false;
      this.buscouTodas = lojas.length === 0
    });
  }

  ngAfterViewInit(): void {
    this.carregueLojas();
  }

  obtenhaLink(loja: any) {
    if( this.grupoDeLojas.paginaDirecionarPara === 'whatsapp' ) {
      return 'https://api.whatsapp.com/send/?phone=55' + loja.numeroWhatsapp.whatsapp + '&app_absent=0&text=' +
        encodeURIComponent(this.grupoDeLojas.msgConversaWhatsapp);
    }

    let baseUrl = '';
    if( loja.urlDaEmpresa ) {
      baseUrl = 'https://' + loja.urlDaEmpresa.hostname + this.urlInteligente;

    } else {
      baseUrl = 'https://' + loja.dominio + '.meucardapio.ai' + this.urlInteligente;
    }

    if (this.mesa) {
      baseUrl += '/mesa/n/' + this.mesa;
    }

    const urlAtual = window.location.href;
    const paramsDaURL = new URLSearchParams(new URL(urlAtual).search);

    if (baseUrl.includes('?')) {
      baseUrl += '&' + paramsDaURL.toString();
    } else {
      baseUrl += '?' + paramsDaURL.toString();
    }

    return baseUrl;
  }

  abraLink(loja: any) {
    let url = '';

    if( (<any>window).ga ) {
      (<any>window).ga('send', 'event', {
        eventCategory: 'ClicouNaLoja',
        eventAction: 'click',
        eventLabel: loja.nome,
        transport: 'beacon',
        'hitCallback': () => {
          url = this.obtenhaLink(loja);

          window.location.href = url;
        },
        'hitCallbackFail' : () => {
          url = this.obtenhaLink(loja);

          window.location.href = url;
        }
      });

      return;
    }

    url = this.obtenhaLink(loja);

    window.location.href = url;
  }
}
