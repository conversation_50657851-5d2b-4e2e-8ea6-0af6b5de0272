<div class="modal-content">
  <div>
    <div *ngIf="!enderecoEscolhido">
      <div class="form-group" >
        <label for="descricao">Selecione o endereço de entrega </label>
        <kendo-combobox id="descricao" name="descricao" [data]="enderecos" placeholder="selecionar"
                        class="form-control" appAutoFocus [autoFocus]="true"  [allowCustom]="false"
                        [(ngModel)]="endereco" [valueField]="'id'"   [textField]="'descricaoCompleta'">
        </kendo-combobox>
      </div>

      <div class="alert alert-danger alert-dismissible fade show mt-2 mb-2" role="alert" *ngIf="msgErro">
        {{msgErro}}
        <button type="button" class="close" data-dismiss="alert" aria-label="Fechar">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>

      <div  class="mt-3">
        <button   type="button" class="btn btn-blue btn-block"  [disabled]="!endereco || calculandoTaxa">
          <i class="k-icon k-i-loading  " *ngIf="calculandoTaxa"></i>
          Entregar nesse</button>
        <button   type="button" class="btn btn-primary btn-block" (click)="this.enderecoEscolhido = true">Cadastrar Novo</button>
      </div>
    </div>

    <div *ngIf="!escolherLojaRetirada">
      <app-form-endereco #telaEndereco (submit)="salveEndereco($event)"
                         (telaCarregou)="fnTelaCarregou($event)" [pedido]="pedido"
                         [buscouUmEndereco]="true"
                         [labelSalvarEndereco]="labelSalvarEndereco" [deveCalcularTaxaDeEntrega]="false"
                         [cepObrigatorio]="pedido.entrega.cepObrigatorio"></app-form-endereco>
    </div>

    <div *ngIf="escolherLojaRetirada">
      <div class="alert alert-success" role="alert">
        <strong>Não entregamos no seu endereço, mas você pode escolher uma loja para fazer retirada.</strong>
      </div>

      <div style="margin-left: -16px;margin-right: -16px;max-height: 600px;">
        <app-tela-lista-lojas #telaListaLojas [popoup]="true"
                              [grupoDeLojas]="objGrupoDeLojas"></app-tela-lista-lojas>
      </div>
    </div>
  </div>
</div>

