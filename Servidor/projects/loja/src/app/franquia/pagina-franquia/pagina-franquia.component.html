<div [ngClass]="{'vermelho': objGrupoDeLojas.id === 17}">
  <header>
    <div class="navbar-custom">
      <div class="container-fluid ">
        <div class="row">
          <div class="logo-topo  col mt-1" style="padding-right: 0px;">
            <img [src]="'/images/empresa/' + objGrupoDeLojas.logo" class="logomarca"/>
            <h3 class="d-inline-block titulo-pagina" style="max-width: 500px;" *ngIf="objGrupoDeLojas.id === 17">
              Escolha a loja mais próxima de você e faça seu pedido pelo whatsapp
            </h3>
          </div>
        </div>
      </div>

    </div>
  </header>

  <div class="content" *ngIf="!buscando">

    <div class="capa" [style.background-image]="'url(/images/empresa/' + objGrupoDeLojas.fundo + ')'" *ngIf="objGrupoDeLojas.fundo">
      <div class="container-fluid ">
        <div class="banner-promo" *ngIf="objGrupoDeLojas.capa">
          <img [src]="'/images/empresa/' + objGrupoDeLojas.capa">

        </div>
        <div class="box" [ngClass]="{semBanner: !objGrupoDeLojas.capa}">

          <div *ngIf="!objGrupoDeLojas.naoExibirBuscaDeLojasPorCep" class="card busca-loja mt-2">

            <div class="card-body">

              <h2 class="titulo">
                Informe seu CEP para escolher entre receber em sua casa
                ou retirar na unidade próxima a você:
              </h2>

              <div class="input-group mb-3">
                <div class="input-group-prepend">
                    <span class="input-group-text"  >
                      <i class=" fa fa-search fa-lg text-muted"></i>
                    </span>
                </div>

                <input type="text" class="form-control" placeholder="Seu CEP" inputmode="numeric"
                       [mask]="mask" appAutoFocus
                       aria-label="Seu cep" [(ngModel)]="cep" (keyup.enter)="abraModalEscolherEndereco()">
                <div class="input-group-append">
                  <button class="btn btn-success" type="button" (click)="abraModalEscolherEndereco()">
                    <i class="fas fa-search"></i>
                  </button>
                </div>
              </div>

              <div>
                <h3 class="titulo">Ou</h3>
              </div>

              <button class="btn btn-success btn-localizacao btn-block" type="button" [disabled]="buscandoLoja" (click)="usarMinhaLocalizacao()">
                <i class="fas fa-search-location"></i> Usar Minha Localização
              </button>
            </div>

          </div>

          <div class="card retirar" *ngIf="false">

            <i class="icone icone-store "></i>

            <h5 class="text-center">RETIRAR EM UMA LOJA</h5>

          </div>


        </div>
      </div>

    </div>

    <div style="background-color: #e8e8e8;" *ngIf="objGrupoDeLojas.id && !objGrupoDeLojas.naoListarLojas">
      <app-tela-lista-lojas [grupoDeLojas]="objGrupoDeLojas" *ngIf="objGrupoDeLojas.id !== 17" [mesa]="mesa"></app-tela-lista-lojas>
    </div>
  </div>

  <div kendoDialogContainer></div>


  <ng-template #conteudoDialogTemplate>
    <i class="fas fa-exclamation-circle text-danger mr-2" style="font-size: 50px;float:left;"></i> <h4
    class="mt-0">{{erro}}</h4>
  </ng-template>
</div>
