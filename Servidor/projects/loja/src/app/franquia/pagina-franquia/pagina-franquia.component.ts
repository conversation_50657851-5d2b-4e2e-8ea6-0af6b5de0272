import {Component, HostListener, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {ActivatedRoute, NavigationEnd} from "@angular/router";
import {ConstantsService} from "../../../services/ConstantsService";
import {ClienteService} from "../../../services/cliente.service";
import {DialogService} from "@progress/kendo-angular-dialog";
import {MyDetectorDevice} from "../../shared/MyDetectorDevice";
import {TelaEnderecoRedirecionarComponent} from "../tela-endereco-redirecionar/tela-endereco-redirecionar.component";
import {PopupUtils} from "../../../objeto/PopupUtils";
import {EnderecoService} from "../../../services/endereco.service";
import {AutorizacaoLojaService} from "../../../services/autorizacao-loja.service";

@Component({
  selector: 'app-pagina-franquia',
  templateUrl: './pagina-franquia.component.html',
  styleUrls: ['./pagina-franquia.component.scss']
})
export class PaginaFranquiaComponent implements OnInit {
  @ViewChild('conteudoDialogTemplate') public templateref: TemplateRef<any>;

  filtro: any = {}
  franquia: any = {};
  private timerBusca;
  buscando: boolean;
  inicio = 0
  buscouTodas = false;
  isMobile = false;
  mask = '00.000-000';
  grupoDeLojas = '';
  url = '';
  cep = '';
  objGrupoDeLojas = {
    id: null,
    logo: '',
    capa: '',
    fundo: '',
    naoListarLojas: false,
    naoExibirBuscaDeLojasPorCep: false
  };
  msgErro = '';
  erro = '';
  buscandoLoja: boolean;
  usuario: any;
  urlInteligente = '';
  mesa: any;

  constructor(private activatedRoute: ActivatedRoute, private clienteService: ClienteService,
              private constantsService: ConstantsService, private dialogService: DialogService,
              private detectorDevice: MyDetectorDevice,
              private enderecoService: EnderecoService, private autorizacao: AutorizacaoLojaService,
              private route: ActivatedRoute) {
    this.isMobile = this.detectorDevice.isMobile();

    this.route.queryParams.subscribe(params => {
      this.mesa = params['mesa'];
    });
  }

  ngOnInit(): void {
    this.url = location.hostname;

    if (location.port) {
      this.url += ':' + location.port
    }

    this.carregueFranquia();

    this.autorizacao.estaLogado().then((estahLogado) => {
      this.usuario = this.autorizacao.getUsuario();

      this.activatedRoute.queryParams.subscribe((queryParams) => {
        if (this.usuario && queryParams.u) {
          this.urlInteligente = queryParams.u;
        }
      });
    });

    if (this.detectorDevice.isMobile() || this.detectorDevice.isTablet()) {
      document.body.classList.add("mobile");
    } else {
      document.body.classList.add("desktop");
    }

    window.addEventListener('ativouGPS', evt => {
      this._usarMinhaLocalizacao();
    });
    window.addEventListener('negouGPS', evt => {
      this.exibaMensagemPopup();
    });
    window.addEventListener('negouGPSParaSempre', evt => {
      this.exibaMensagemPopup();
    });
  }

  exibaMensagemPopup() {
    this.buscandoLoja = false;
    this.erro = 'Você precisa ativar a geolocalização para poder utilizar essa funcionalidade.';

    this.dialogService.open({
      title: null,
      content: this.templateref,
      actions: [
        {
          text: 'Retirar'
        }
      ],
      width: 400,
      minWidth: 250
    });
  }

  onActivate(componente: any) {

  }


  // @HostListener('scroll', ['$event']) // for scroll events of the current element



  carregueFranquia(){
    this.buscando = true;
    this.clienteService.obtenhaGrupoDeLojas(this.url).then( (grupoDeLojas: any) => {
      this.objGrupoDeLojas = grupoDeLojas;
      this.grupoDeLojas = grupoDeLojas.nome;
      this.buscando = false;
    });

    /*
    this.clienteService.obtenhaFranquiaPorUrl(this.url).then((franquia) => {
      this.franquia = franquia;
      this.buscando = false;
    })

     */
  }

  carregueLojas(texto: string = null){

    this.buscando = true;
    this.inicio  = this.franquia.lojas.length;

    this.clienteService.obtenhaLojasFranquia(this.objGrupoDeLojas, texto, this.inicio).then((lojas) => {
      lojas.forEach( loja => this.franquia.lojas.push(loja));
      this.buscando = false;
      this.buscouTodas = (lojas.length === 0);
    });
  }



  limpeBusca(){
    this.inicio = 0;
    this.filtro = {};
    this.carregueLojas();
  }

  onFiltreLojas(event: any) {
    if(this.timerBusca) clearTimeout(this.timerBusca);

    this.timerBusca = setTimeout( () => {
       this.inicio = 0;
       this.carregueLojas(event.target.value)
    }, 1000)
  }

  fecharTelaTestar() {

  }

  abraModalEscolherEndereco() {
    if( (<any>window).ga ) {
      (<any>window).ga('send', 'event', {
        eventCategory: 'Busca Endereco',
        eventAction: 'click',
        eventLabel: 'busca endereco',
        transport: 'beacon'
      });
    }

    if( (<any>window).dataLayer ) {
      (<any>window).dataLayer.push({'event': 'Busca Endereco'});
    }

    let dimensao = PopupUtils.calculeAlturaLargura(this.isMobile)

    const windowRef = this.dialogService.open({
      title: 'Informe Seu Endereço',
      content: TelaEnderecoRedirecionarComponent,
      minWidth: 250,
      width: dimensao.largura
    });

    const telaRedirecionarEndereco: TelaEnderecoRedirecionarComponent = windowRef.content.instance;
    telaRedirecionarEndereco.labelSalvarEndereco = 'Encontrar Loja';
    telaRedirecionarEndereco.window = windowRef;
    telaRedirecionarEndereco.pedido = {
      entrega: {
        cepObrigatorio: true
      }
    };
    telaRedirecionarEndereco.urlInteligente = this.urlInteligente;
    telaRedirecionarEndereco.objGrupoDeLojas = this.objGrupoDeLojas;
    telaRedirecionarEndereco.grupoDeLojas = this.grupoDeLojas;

    telaRedirecionarEndereco.telaCarregou.subscribe( (carregou) => {
      telaRedirecionarEndereco.busqueCEP(this.cep);
    });

    document.body.style.overflow = "hidden";
    document.body.style.paddingRight = "17px";
    const $divDialog = windowRef.dialog.location.nativeElement;

    if( $divDialog ) {
      const $divDialogContent = $divDialog.getElementsByClassName('k-dialog-content');
      const $kDialog = $divDialog.getElementsByClassName('k-dialog');
      //$kDialog[0].style.height = (dimensao.altura + 50) + 'px';

      if( $divDialogContent.length > 0 ) {
        $divDialogContent[0].classList.add('dialog-endereco');
      }
    }

    const $elemento: HTMLElement = document.querySelector('.carrinho_desktop .is-sticky');

    if( $elemento ) $elemento.style.paddingRight = '17px';

    windowRef.result.subscribe(async (endereco: any) => {
      document.body.style.overflow = "";
      document.body.style.paddingRight = "";
      if( $elemento ) $elemento.style.paddingRight = '';
      if (endereco && endereco.cidade) {
      }
    });
  }

  usarMinhaLocalizacao() {
    if( (<any>window).flutter_inappwebview ) {
      this.buscandoLoja = true;

      (<any>window).flutter_inappwebview.callHandler('ativarGPS');
      return;
    }

    this._usarMinhaLocalizacao();
  }

  _usarMinhaLocalizacao() {
    if( (<any>window).ga ) {
      (<any>window).ga('send', 'event', {
        eventCategory: 'Localizacao',
        eventAction: 'click',
        eventLabel: 'usar localizacao',
        transport: 'beacon'
      });
    }
    if( (<any>window).dataLayer ) {
      (<any>window).dataLayer.push({'event': 'Localizacao'});
    }

    this.buscandoLoja = true;

    navigator.geolocation.getCurrentPosition((posicao) => {
      const latlng = {
        lat: posicao.coords.latitude,
        lng: posicao.coords.longitude,
      };

      this.enderecoService.encontreLojaGPS(this.grupoDeLojas, latlng).then( (respLojaEncontrada) => {
        this.buscandoLoja = false;

        if( !respLojaEncontrada.encontrou ) {
          this.erro = respLojaEncontrada.msg;
          this.exibaTelaRetirar();
          return;
        }

        const lojaEncontrada = respLojaEncontrada;

        let linkLoja = lojaEncontrada.link + this.urlInteligente;

        if( linkLoja.indexOf('api.whatsapp.com') !== -1 ) {
          linkLoja = lojaEncontrada.link;
        }

        window.location.href = linkLoja;
      }).catch( (erro) => {
        this.buscandoLoja = false;
        this.erro = erro;

        this.exibaTelaRetirar();
      });
    }, (erro) => {
      this.buscandoLoja = false;
      this.erro = 'Você precisa ativar a geolocalização para poder utilizar essa funcionalidade.';

      this.dialogService.open({
        title: null,
        content: this.templateref,
        actions: [
          {
            text: 'Retirar'
          }
        ],
        width: 400,
        minWidth: 250
      });
    }, {enableHighAccuracy: true});
  }

  private exibaTelaRetirar() {
    this.dialogService.open({
      title: null,
      content: this.templateref,
      actions: [
        {
          text: 'Retirar'
        }
      ],
      width: 400,
      minWidth: 250
    });
  }
}
