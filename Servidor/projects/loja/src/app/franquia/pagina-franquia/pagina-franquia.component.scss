header{
  display: table;
}
.form-control{
  border: none;
}

.logomarca {
  height: 70px;
  float: left;
  margin-right: 10px;
}

.btn-localizacao {
  padding: 10px 20px !important;
}

.box {
  &.semBanner {
    top: 100px;
    position: relative;
  }
}

.vermelho {
  .navbar-custom {
    background: #E82C2A;
    height: 100px;

    h3 {
      color: #fff;
    }
  }

  .capa {
    background-size: 1600px;
  }

  .titulo-pagina {
    top: 5px;
    position: relative;
  }

  .logomarca {
    height: 90px;
  }

  .btn-success {
    background: #E82C2A;
    border: solid 1px #e3211e;
  }

  .btn-localizacao {
    border-radius: 50px;
  }
}

@media only screen and (max-width: 600px) {
  .vermelho {
    .capa {
      background-size: 800px;
    }

    .box {
      &.semBanner {
        top: 80px;
        position: relative;
      }
    }
  }

  .btn-localizacao {
    padding: 8px 20px;
  }

  .navbar-custom {
    background: #E82C2A;
    height: 120px;
  }

  .d-inline-block {
    display: inline !important;
  }
}

.input-group-prepend{
  border:none;
  .input-group-text{
    background: #fff;
    border: none;
  }
}
input{
  height: 60px;
  line-height: 30px;
  font-size: 24px;
}
.container-fluid{
  max-width: 1100px;
}


.navbar-custom {
  position: absolute;
  background-color: #fff;
  height: 80px;
  .fe-menu{
    font-size: 40px;
  }
  .logo-topo{
    display: inline-block;

  }
  .button-menu-mobile {
    color: #000;
  }
}
.content{
  margin-top: 80px;
}

.capa {
  background-color: #333;
  background-position: center;
  padding-bottom: 0px;
  overflow: auto;
  position: relative;

  .banner-promo{
    padding-top: 15px;
    position: relative;
    height: 250px;
    padding-bottom: 17px;
    img{
      height: 100%;
      margin: 0 auto;
      display: block;
    }
  }
  &.sem-promo{
    height: 400px;
  }

  .box{
    max-width: 575px;
    margin: 0 auto;
  }
  .busca-loja{
    background: #000000a3;
    color: #fff;

    .titulo{
      color: #fff;
      font-size: 25px;
      line-height: 30px;
      text-align: center;
    }


    button{
      font-size: 20px;
      padding: 10px 15px;
    }


  }

  .retirar{
    background-color: #75716feb;
    color: #fff;
    h5{
      color: #fff;
      font-size: 1.5em;
      padding: 0.2em;
    }
    .icone-store{
      height: 25px;
      width: 30px;
      position: absolute;
      left: 20px;
      top: 12px;
      background-color: #fff;
      -webkit-mask: url('/images/franquia/cib/store_1.svg') no-repeat center;
      mask: url('/images/franquia/cib/store_1.svg') no-repeat center;

    }
  }
}

.lojas {
  background-color: #e8e8e8;
  min-height: 600px;
  padding-top: 30px;
  padding-bottom: 40px;

  .card-box{
    display: table;
    width: 100%;
  }
  .bolinha{
    margin: 5px;
    width: 8px;
    height: 8px;
    border-radius: 100px;
    float: left;
    &.aberto{
      background: #6db31b;
    }

    &.fechada{
      background: #f1556c;
    }
  }

  .endereco{
    margin-top: 0;
    margin-bottom: 0;
    height: 50px;
    overflow: hidden;
  }
}
.btn-secondary {
  background-color: #313131 !important;
}

@media (max-width: 900px) {
  h3, .h3 {
    font-size: 1.2rem;
  }
  .container-fluid {
    max-width: inherit;
    width: 100%;
  }
  input{
    height: 45px;
    line-height: 25px;
    font-size: 18px;
  }

  .capa {
    width: 100%;
    background-position: -87px 28px;
    background-size: 139%;
    background-repeat: no-repeat;
    background-color: #29292b;
    .box{
      max-width: 92%;
    }
    .banner-promo{
      height: auto;
      width: 100%;
      padding-bottom: 8px;
      img{
        height: auto;
        width: 100%;
      }
    }
    .busca-loja {
      .titulo {
        font-size: 18px;
      }

      button{
        font-size: 18px;
        padding: 0px 20px;
      }
    }
  }

  .lojas{
    padding-left: 10px;
    padding-right: 10px;
    .card-box{
      padding: 10px 5px;
      img{
        height: 70px;
        margin-top: 20px;
      }
      .row>div{
        padding-right: 0px;
        padding-left: 0px
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .logo-topo {
    display: flex !important;
  }

  .logo-topo .logomarca {
    display: block;
    margin-left: auto;
    margin-right: auto;
  }
}
