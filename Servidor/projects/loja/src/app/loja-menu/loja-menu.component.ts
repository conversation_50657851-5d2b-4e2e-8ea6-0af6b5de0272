import {Component, EventEmitter, Input, OnDestroy, OnInit, Output} from '@angular/core';
import {DominiosService} from "../../services/dominios.service";
import {ConstantsService} from "../../services/ConstantsService";
import {SharedDataService} from "../../services/shared.data.service";
import {AutorizacaoLojaService} from "../../services/autorizacao-loja.service";

import {NavigationEnd, Router} from "@angular/router";
import {Subscription} from "rxjs";


@Component({
  selector: 'app-loja-menu',
  templateUrl: './loja-menu.component.html',
  styleUrls: ['./loja-menu.component.scss']
})
  export class LojaMenuComponent implements OnInit, OnDestroy {
  @Input() exibirMenu = true;
  @Output() onBuscarProdutos = new EventEmitter();
  @Output() onExibirBrindes = new EventEmitter();
  url: any = 'index';
  estaNoHome  = true;
  estaNaBuscaPorCategoria = false;
  exibirMenuCategorias = false;
  urlCardapio = '';
  exibirPoweredBy = false;
  usuario: any = {};
  estaNoLocal: boolean;
  evtEmpresa: Subscription;
  empresa: any;

  constructor(private dominiosService: DominiosService, private constantsService: ConstantsService,
              private router: Router,
              public sharedDataService: SharedDataService, private autorizacao: AutorizacaoLojaService) { }

  ngOnInit(): void {
    this.usuario = this.autorizacao.getUsuario() || {}

    this.autorizacao.usuario$.subscribe( (usuario) => {
      if(!usuario) return;
      this.usuario = usuario;
    });

    this.evtEmpresa = this.constantsService.empresa$.subscribe((empresa) => {
      if (!empresa) return

      this.empresa = empresa;
    });

   let link = this.dominiosService.obtenhaUrlAtiva();

   if(link !== '') this.url = link;

   this.urlCardapio = location.hostname;

   if( this.urlCardapio.indexOf('meucardapio.ai') !== -1 || this.urlCardapio.indexOf('localhost') !== -1 ) {
     this.exibirPoweredBy = true;
   }

   this.constantsService.empresa$.subscribe( (empresa: any) => {
     if( !empresa) {
       return;
     }

     if( !this.exibirPoweredBy ) this.exibirPoweredBy = empresa.rede === 'chinainbox';
   })

    this.router.events.subscribe(event => {
      if (event instanceof NavigationEnd) {
        // A URL foi alterada, faça o que for necessário aqui

        const regex = /\/([^\/?]+)(?:\?.*)?$/;
        const match1 =  event.urlAfterRedirects.match(regex);
        console.log(match1)
        this.url = match1[1]
        this.veriqueMenuAtivo();
      }
    });

    this.veriqueMenuAtivo();
  }

  naveguePara(url: string) {
    this.url = url;
    this.veriqueMenuAtivo();
    this.dominiosService.navegueParaUrl(url);
  }

  veriqueMenuAtivo(){
    this.estaNoHome = this.url === 'index' || this.url === 'loja' || this.url === '/';
    this.estaNaBuscaPorCategoria = this.url.indexOf('busca') >= 0 &&  this.url.indexOf('ecommerce')
    this.estaNoLocal = this.url.indexOf('local') >= 0;
  }

  clicouExibirMenuCategorias(){
    this.sharedDataService.notifiqueExibirMenu();
  }

  busqueProdutos(){
    this.onBuscarProdutos.emit({})
  }

  exibirTelaBrindes(){
    this.onExibirBrindes.emit({})
  }

  ngOnDestroy(): void {
    if(this.evtEmpresa)
      this.evtEmpresa.unsubscribe();
  }
}
