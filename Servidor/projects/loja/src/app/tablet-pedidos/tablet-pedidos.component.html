<!-- Dialog de confirmação de pedido -->
<div class="confirmacao-pedido-dialog" *ngIf="pedidoConfirmado">
  <div class="confirmacao-pedido-overlay" (click)="fecharDialogConfirmacao()"></div>
  <div class="confirmacao-pedido-container">
    <div class="confirmacao-icon">
      <i class="fas fa-utensils"></i>
      <i class="fas fa-check"></i>
    </div>
    <div class="confirmacao-titulo">Pedido Confirmado!</div>
    <div class="confirmacao-mensagem">Seu pedido já está em preparação.</div>
    <button class="btn-confirmar" (click)="fecharDialogConfirmacao()">OK</button>
  </div>
</div>

<!-- Dialog de leitura de cartão do cliente -->
<div class="cartao-cliente-dialog" *ngIf="leituraCartaoAberta">
  <div class="cartao-cliente-overlay" (click)="fecharLeituraCartao()"></div>
  <div class="cartao-cliente-container">
    <!-- Cabeçalho (ocupando largura total) -->
    <div class="cartao-cliente-header">
      <button class="btn-voltar" (click)="fecharLeituraCartao()">
        <i class="fas fa-arrow-left"></i> VOLTAR
      </button>
      <div class="logo-header">
        <img src="https://promokit.com.br/assets/loja/logosmall.png" alt="Logo">
      </div>
      <div class="spacer"></div>
    </div>

    <!-- Layout de duas colunas -->
    <div class="cartao-cliente-duas-colunas">
      <!-- Coluna esquerda (Scanner) -->
      <div class="coluna-scanner">
        <div class="scanner-container">
          <div class="scanner-frame">
            <div class="scanner-line"></div>
            <div class="scanner-corners top-left"></div>
            <div class="scanner-corners top-right"></div>
            <div class="scanner-corners bottom-left"></div>
            <div class="scanner-corners bottom-right"></div>
          </div>
          <video #videoElement id="videoElement" autoplay playsinline class="scanner-video"></video>
        </div>
      </div>

      <!-- Coluna direita (Instruções) -->
      <div class="coluna-instrucoes">
        <div class="instrucoes-container">
          <div class="instrucao-texto">
            <h3><i class="fas fa-qrcode"></i> Leitura de Comanda</h3>
            <p><strong>Posicione sua COMANDA</strong> na frente da câmera para ler o código dela e concluir seu pedido.</p>
            <p><i class="fas fa-info-circle"></i> Mantenha a uma distância de aproximadamente 10 cm para melhor leitura.</p>
          </div>
          <div class="instrucao-imagem">
            <img src="/assets/images/qrcode_reader.png" alt="Posicione o QR Code">
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="cardapio-container">
  <!-- Alerta de Mesa não informada -->
  <div class="alerta-mesa mt-2" *ngIf="!mesaValida" [hidden]="carregando">
    <div class="alerta-mesa-conteudo">
      <i class="fas fa-exclamation-triangle"></i>
      <span>{{erroValidarMesa}}  Não será possível enviar o pedido</span>
    </div>
  </div>

  <!-- Cabeçalho -->
  <header class="cardapio-header">
    <!-- Logo e Nome -->
    <div class="logo-empresa" (click)="dominiosService.vaParaHome()">
      <img class="logo-img" *ngIf="empresa?.logo" [src]="'https://promokit.com.br/images/empresa/' + empresa?.logo" [alt]="empresa?.nome"/>
      <h1 class="nome-empresa">{{empresa?.nome}}</h1>
    </div>

    <!-- Busca -->
    <div class="search-bar">
      <i class="fas fa-search"></i>
      <input
        type="text"
        [(ngModel)]="termoBusca"
        placeholder="Buscar no cardápio..."
        (keyup.enter)="buscarProdutos()"
      >
    </div>

    <!-- Ações -->
    <div class="actions">
      <button class="btn-carrinho" (click)="abrirCarrinho()">
        <div class="carrinho-info">
          <i class="fas fa-shopping-cart"></i>
          <div class="carrinho-texto" *ngIf="carrinhoItens > 0">
            <span class="carrinho-total">{{pedido?.total | currency:'BRL'}}</span>
            <span class="carrinho-itens">{{carrinhoItens}} {{carrinhoItens === 1 ? 'item' : 'itens'}}</span>
          </div>
          <div class="carrinho-texto" *ngIf="carrinhoItens === 0">
            <span class="carrinho-itens">Carrinho vazio</span>
          </div>
        </div>
        <span *ngIf="carrinhoItens > 0" class="badge">{{carrinhoItens}}</span>
      </button>
    </div>
  </header>

  <div class="cardapio-content">
    <!-- Coluna de navegação principal (primeira coluna) -->
    <div class="navegacao-principal">
      <div class="nav-item" [class.ativo]="secaoAtiva === 'destaques'" (click)="navegarParaSecao('destaques')">
        <i class="fas fa-star"></i>
        <span>Destaques</span>
      </div>
      <div class="nav-item" [class.ativo]="secaoAtiva === 'cardapio'" (click)="navegarParaSecao('cardapio')">
        <i class="fas fa-utensils"></i>
        <span>Cardápio</span>
      </div>
      <div class="nav-item" [class.ativo]="secaoAtiva === 'conta'" (click)="abrirMeusPedidos()">
        <i class="fas fa-shopping-cart"></i>
        <span>Minha Conta</span>
        <span *ngIf="carrinhoItens > 0" class="badge-mini">{{carrinhoItens}}</span>
      </div>
      <div class="nav-item" [class.ativo]="secaoAtiva === 'avaliar'" (click)="navegarParaSecao('avaliar')">
        <i class="fas fa-star-half-alt"></i>
        <span>Avaliar o Local</span>
      </div>
      <div class="nav-item" [class.ativo]="secaoAtiva === 'mural'" (click)="navegarParaSecao('mural')">
        <i class="fas fa-clipboard-list"></i>
        <span>Mural da Casa</span>
      </div>
      <div class="nav-item" [class.ativo]="secaoAtiva === 'sobre'" (click)="navegarParaSecao('sobre')">
        <i class="fas fa-info-circle"></i>
        <span>Sobre a Casa</span>
      </div>
    </div>

    <!-- Menu lateral de categorias (segunda coluna) - visível apenas quando cardápio está selecionado -->
    <div class="menu-categorias-cards" *ngIf="secaoAtiva === 'cardapio'" [class.aberto]="menuAberto">
      <div class="categorias-scroll-container">
        <div class="categoria-card" *ngFor="let categoria of categoriasParaExibicao"
             (click)="navegarParaCategoria(categoria)"
             [class.ativo]="verificaCategoriaSelecionada(categoria)">
          <div class="categoria-imagem" [style.background-image]="'url(' + obtenhaImagemCategoria(categoria) + ')'">
            <div class="categoria-overlay">
              <i class="fas" [ngClass]="categoria.icone"></i>
              <span>{{categoria.nome}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Conteúdo principal com categorias e produtos -->
    <div class="conteudo-principal" *ngIf="secaoAtiva === 'cardapio'">
      <!-- Categorias em cards (visíveis apenas na versão mobile) -->
      <div class="categorias-cards">
        <h2>Categorias</h2>
        <div class="categorias-scroll-container-mobile">
          <div class="categoria-card" *ngFor="let categoria of categorias" (click)="navegarParaCategoria(categoria)">
            <div class="categoria-imagem" [style.background-image]="'url(' + obtenhaImagemCategoria(categoria) + ')'">
              <div class="categoria-overlay">
                <i class="fas" [ngClass]="categoria.icone"></i>
                <span>{{categoria.nome}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Conteúdo principal - Produtos -->
      <div class="produtos-container">
        <!-- Banner da categoria atual -->
        <div class="categoria-banner" *ngIf="categoriaAtiva">
          <div class="banner-imagem" [style.background-image]="'url(' + obtenhaImagemCategoria(categoriaAtiva) + ')'">
            <h2>{{categoriaAtiva.nome.toUpperCase()}}</h2>
          </div>
        </div>

        <!-- Lista de produtos -->
        <div class="produtos-lista">
          <div *ngIf="produtosFiltrados.length === 0 && !carregando" class="sem-produtos">
            <p>Nenhum produto encontrado nesta categoria.</p>
          </div>

          <div class="produto-card" *ngFor="let produto of produtosFiltrados">
            <div class="produto-imagem">
              <img [src]="obtenhaImagemProduto(produto)" alt="{{produto.nome}}">
            </div>
            <div class="produto-info">
              <h3>{{produto.nome.toUpperCase()}}</h3>
              <p *ngIf="produto.descricao" class="produto-descricao">{{produto.descricao}}</p>
              <p *ngIf="produto.ingredientes" class="produto-ingredientes">{{produto.ingredientes}}</p>

              <!-- Preço do produto seguindo o padrão do produto-container -->
              <div class="produto-preco" *ngIf="exibirPrecos && !produto.indisponivel && produto.exibirPrecoNoCardapio !== false">
                <ng-container *ngIf="!produto.template?.exibirPrecosTamanhos">
                  <span class="preco-label" *ngIf="produto.valorMinimo"><i>A partir de</i></span>
                  <span class="preco-valor">
                    {{obtenhaPrecoExibicao(produto) | currency: 'BRL'}}
                    <span class="unidade" *ngIf="exibirUnidade(produto)"> / Kg</span>
                  </span>

                  <span class="preco-antigo" *ngIf="temPrecoAntigo(produto)">
                    {{produto.precoAntigo | currency: 'BRL'}}
                  </span>

                  <span class="cashback-badge" *ngIf="produto.cashbackValor">
                    <i class="fe-star-on"></i>
                    {{produto.cashbackValor | currency: 'BRL'}}
                  </span>
                </ng-container>

                <!-- Preços por tamanho -->
                <div class="tamanhos-precos" *ngIf="produto.template?.exibirPrecosTamanhos">
                  <div *ngFor="let tamanho of produto.tamanhos" class="tamanho-item">
                    <span class="tamanho-descricao">{{tamanho.descricao}}</span>
                    <span class="preco-label" *ngIf="tamanho.valorMinimo"><i>A partir de</i></span>
                    <span class="preco-valor">
                      {{(tamanho.novoPreco ? tamanho.novoPreco : tamanho.preco) | currency: 'BRL'}}
                    </span>
                    <span class="preco-antigo" *ngIf="tamanho.precoAntigo">
                      {{tamanho.precoAntigo | currency: 'BRL'}}
                    </span>
                  </div>
                </div>

                <!-- Limite por pedido -->
                <small class="limite-pedido" *ngIf="produto.qtdMaxima">
                  *Até {{produto.qtdMaxima}} por pedido
                </small>
              </div>

              <!-- Produto indisponível -->
              <div class="produto-indisponivel" *ngIf="produto.indisponivel">
                <span class="texto-indisponivel">Indisponível!</span>
              </div>

              <!-- Informações adicionais -->
              <div class="produto-info-adicional" *ngIf="produto.tamanho || produto.fatias">
                <span *ngIf="produto.tamanho">{{produto.tamanho}}</span>
                <span *ngIf="produto.fatias">{{produto.fatias}} fatias</span>
              </div>

              <!-- Exibir botão de adicionar apenas se o produto tiver preço válido ou se permitir preços zerados -->
              <button class="btn-adicionar" *ngIf="temPrecoValido(produto) || exibirProdutosValorZerado"
                      (click)="adicionarAoCarrinho(produto)">ADICIONAR</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Conteúdo para outras seções -->
    <div class="conteudo-outras-secoes" *ngIf="secaoAtiva !== 'cardapio'">
      <!-- Seção de Destaques -->
      <div class="secao-conteudo" *ngIf="secaoAtiva === 'destaques'">
        <h2>Destaques</h2>
        <div class="produtos-lista">
          <div *ngIf="produtosDestaque.length === 0 && !carregando" class="sem-produtos">
            <p>Nenhum produto em destaque no momento.</p>
          </div>

          <div class="produto-card" *ngFor="let produto of produtosDestaque">
            <div class="produto-imagem">
              <img [src]="obtenhaImagemProduto(produto)" alt="{{produto.nome}}">
            </div>
            <div class="produto-info">
              <h3>{{produto.nome.toUpperCase()}}</h3>
              <p *ngIf="produto.descricao">{{produto.descricao}}</p>
              <p *ngIf="produto.tamanho">{{produto.tamanho}} + {{produto.fatias}} fatias. A partir de R$ {{formatarPreco(produto.valor)}}</p>
              <p *ngIf="!produto.tamanho && produto.ingredientes">{{produto.ingredientes}}</p>
              <button class="btn-adicionar" (click)="adicionarAoCarrinho(produto)">ADICIONAR</button>
            </div>
          </div>
        </div>
      </div>

      <!-- Seção de Meus Pedidos (unificada com o carrinho) -->
      <div class="secao-conteudo meus-pedidos-tablet" *ngIf="secaoAtiva === 'conta'">
        <h2 class="pedidos-titulo">Meus Pedidos</h2>

        <!-- Abas para navegar entre carrinho atual e histórico -->
        <div class="pedidos-abas">
          <div class="aba" [class.ativa]="abaAtiva === 'carrinho'" (click)="selecionarAba('carrinho')">
            <i class="fas fa-shopping-cart mr-2"></i>
            Carrinho Atual
            <span *ngIf="carrinhoItens > 0" class="badge-mini ml-2">{{carrinhoItens}}</span>
          </div>
          <div class="aba" [class.ativa]="abaAtiva === 'historico'" (click)="selecionarAba('historico')">
            <i class="fas fa-history mr-2"></i>
            Minha Conta
          </div>
        </div>

        <!-- Conteúdo do Carrinho -->
        <div class="aba-conteudo" *ngIf="abaAtiva === 'carrinho'">
          <!-- Exibir mensagem quando o carrinho está vazio -->
          <div class="carrinho-vazio" *ngIf="pedido && pedido.itens.length == 0">
            <div class="carrinho-vazio-conteudo">
              <img src="https://promokit.com.br/assets/loja/sem-pedidos.png" width="96px" class="mb-3"/>
              <h3 class="text-muted">Seu carrinho está vazio</h3>
              <h5 class="text-muted">Adicione itens do cardápio</h5>
              <button class="btn-continuar-comprando mt-3" (click)="navegarParaSecao('cardapio')">
                Ver Cardápio
              </button>
            </div>
          </div>

          <!-- Conteúdo do carrinho quando há itens -->
          <div class="carrinho-conteudo" *ngIf="pedido && pedido.itens.length > 0">
            <div class="carrinho-itens">
              <!-- Lista de itens no carrinho -->
              <div *ngFor="let item of pedido.itens; let i = index" class="carrinho-item">
                <div class="item-container">
                  <div class="item-imagem" *ngIf="item.produto.imagens && item.produto.imagens.length > 0">
                    <img [src]="'https://promokit.com.br/images/empresa/' + item.produto.imagens[0].linkImagem" alt="{{item.produto.nome}}">
                  </div>
                  <div class="item-info">
                    <div class="item-descricao">
                      <h4>{{item.qtde}}{{item.obtenhaUnidade()}} {{item.obtenhaDescricao()}}</h4>
                      <p *ngIf="item.observacao" class="item-observacao">
                        Observação: "{{item.observacao}}"
                      </p>
                    </div>
                    <div class="item-acoes">
                      <button class="btn-editar" (click)="editarItem(i, item)">
                        <i class="fas fa-edit"></i> Editar
                      </button>
                      <button class="btn-remover" (click)="removaItem(item)">
                        <i class="fas fa-trash"></i> Remover
                      </button>
                    </div>
                  </div>
                  <div class="item-valor">
                    <h4 *ngIf="!item.brinde">{{item.total | currency: 'BRL'}}</h4>
                    <h4 *ngIf="item.brinde" class="resgate-preco">-{{item.valorResgatado}} {{item.produto.acumulo}}</h4>
                  </div>
                </div>

                <!-- Adicionais do item -->
                <div class="item-adicionais" *ngIf="item.valoresAdicionais && item.valoresAdicionais.length > 0">
                  <p class="adicionais-lista">
                    <ng-container *ngFor="let last=last; let elemento of item.valoresAdicionais">
                      {{elemento.qtde}}x {{elemento.nome}}{{last ? '' : ', '}}
                    </ng-container>
                  </p>
                </div>
              </div>
            </div>

            <!-- Resumo do pedido -->
            <div class="carrinho-resumo">
              <div class="resumo-linha">
                <span>Subtotal</span>
                <span [ngClass]="{'com-desconto': pedido.desconto > 0}">{{pedido.obtenhaSubTotal() | currency: 'BRL'}}</span>
              </div>

              <!-- Total -->
              <div class="resumo-linha total">
                <span>Total</span>
                <span class="total-valor">{{pedido.total | currency: 'BRL'}}</span>
              </div>

              <!-- Botões de ação -->
              <div class="carrinho-acoes">
                <button class="btn-voltar" (click)="navegarParaSecao('cardapio')">
                  <i class="fas fa-arrow-left"></i> Continuar Comprando
                </button>
                <button class="btn-finalizar" (click)="abrirLeituraCartao()"
                        [disabled]="eviandoPedido || calculandoTaxa">
                  <i *ngIf="eviandoPedido" class="fas fa-spinner fa-spin mr-1"></i>
                  Enviar Pedido <i *ngIf="!eviandoPedido" class="fas fa-arrow-right"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Conteúdo do Histórico de Pedidos -->
        <div class="aba-conteudo" *ngIf="abaAtiva === 'historico'">
          <!-- Se não tiver pedidos no histórico -->
          <div class="historico-vazio" *ngIf="!historicoCarregado || pedidosHistorico.length === 0">
            <div class="historico-vazio-conteudo" *ngIf="!carregandoHistorico">
              <img src="https://promokit.com.br/assets/loja/sem-pedidos.png" width="96px" class="mb-3"/>
              <h3 class="text-muted">Você ainda não fez nenhum pedido</h3>
              <h5 class="text-muted">Explore nosso cardápio</h5>
              <button class="btn-continuar-comprando mt-3" (click)="navegarParaSecao('cardapio')">
                Ver Cardápio
              </button>
            </div>
            <div class="historico-carregando" *ngIf="carregandoHistorico">
              <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
              <h3 class="text-muted">Carregando seus pedidos...</h3>
            </div>
          </div>

          <!-- Listagem dos pedidos no histórico -->
          <div class="historico-pedidos" *ngIf="historicoCarregado && pedidosHistorico.length > 0">
            <div class="pedido-card" *ngFor="let pedidoHistorico of pedidosHistorico">
              <div class="pedido-header">
                <div class="pedido-info">
                  <span class="pedido-numero">Pedido #{{pedidoHistorico.codigo}}</span>
                  <span class="pedido-data">{{pedidoHistorico.data | date:'dd/MM/yyyy HH:mm'}}</span>
                </div>
                <div class="pedido-status" [ngClass]="'status-' + pedidoHistorico.status.toLowerCase()">
                  {{pedidoHistorico.status}}
                </div>
              </div>

              <div class="pedido-itens">
                <div class="pedido-item" *ngFor="let item of pedidoHistorico.itens">
                  <div class="item-quantidade">{{item.qtde}}x</div>
                  <div class="item-descricao">{{item.descricao}}</div>
                  <div class="item-preco">{{item.total | currency: 'BRL'}}</div>
                </div>
              </div>

              <div class="pedido-footer">
                <div class="pedido-total">
                  <span>Total:</span>
                  <span class="total-valor">{{pedidoHistorico.total | currency: 'BRL'}}</span>
                </div>
                <button class="btn-repetir-pedido" *ngIf="pedidoHistorico.status === 'Entregue'" (click)="repetirPedido(pedidoHistorico)">
                  <i class="fas fa-redo mr-1"></i> Repetir pedido
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Outras seções -->
      <div class="secao-conteudo" *ngIf="secaoAtiva === 'avaliar'">
        <h2>Avaliar o Local</h2>
        <p>Avalie sua experiência conosco!</p>
        <!-- Conteúdo da avaliação aqui -->
      </div>

      <div class="secao-conteudo" *ngIf="secaoAtiva === 'mural'">
        <h2>Mural da Casa</h2>
        <p>Confira as novidades e eventos especiais.</p>
        <!-- Conteúdo do mural aqui -->
      </div>

      <div class="secao-conteudo" *ngIf="secaoAtiva === 'sobre'">
        <h2>Sobre a Casa</h2>
        <p>Conheça nossa história e valores.</p>
        <!-- Conteúdo sobre a casa aqui -->
      </div>
    </div>
  </div>

  <!-- Notificação -->
  <div class="notificacao" *ngIf="notificacao" [ngClass]="notificacaoTipo">
    <i class="fas" [ngClass]="{'fa-check-circle': notificacaoTipo === 'success', 'fa-exclamation-circle': notificacaoTipo === 'error'}"></i>
    <span>{{notificacaoMensagem}}</span>
  </div>

  <!-- Painel lateral para produtos -->
  <div class="painel-lateral-overlay" [class.ativo]="painelLateralAberto" (click)="fecharPainelLateral()"></div>

  <div class="painel-lateral" [class.aberto]="painelLateralAberto" #painelLateral>
    <div class="painel-lateral-content">
      <ng-container #painelLateralContainer></ng-container>
    </div>
  </div>

  <!-- Painel lateral para o carrinho -->
  <div class="carrinho-lateral-overlay" [class.ativo]="carrinhoLateralAberto" (click)="fecharCarrinhoLateral()"></div>

  <div class="carrinho-lateral" [class.aberto]="carrinhoLateralAberto">
    <div class="carrinho-lateral-header">
      <div class="carrinho-titulo">
        <h3><i class="fas fa-shopping-cart"></i> Seu Pedido</h3>
        <span class="carrinho-itens-count" *ngIf="pedido?.mesa"><b>Mesa {{pedido.mesa.nome}}</b></span>
        <span class="carrinho-itens-count" *ngIf="!pedido?.mesa">{{pedido?.itens?.length || 0}} {{pedido?.itens?.length === 1 ? 'item' : 'itens'}} no carrinho</span>
      </div>
      <button class="btn-fechar" (click)="fecharCarrinhoLateral()">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <div class="carrinho-lateral-content">
      <!-- Exibir mensagem quando o carrinho está vazio -->
      <div class="d-flex justify-content-center align-items-center flex-column" style="height: 400px;" *ngIf="pedido && pedido.itens.length == 0">
        <div class="p-2">
          <img src="https://promokit.com.br/assets/loja/sem-pedidos.png" width="96px"/>
        </div>
        <div class="p-0">
          <h3 class="text-muted">Sua sacola está vazia</h3>
        </div>
        <div class="p-0">
          <h5 class="text-muted">Adicione Itens</h5>
        </div>
        <div class="p-2 mt-3">
          <button class="btn btn-primary" (click)="fecharCarrinhoLateral(); secaoAtiva = 'cardapio';">Ver Cardápio</button>
        </div>
      </div>

      <!-- Lista de itens no carrinho -->
      <div *ngIf="pedido && pedido.itens.length > 0">
        <div class="linha nao_mobile"></div>

        <div *ngFor="let item of pedido.itens; let i = index" class="produto item_produto">
          <div class="d-flex align-items-start">
            <div class="mr-3">
              <img class="img-produto"
                   [src]="obtenhaImagemProduto(item.produto)"
                   [alt]="item.produto.nome">
            </div>

            <div class="flex-grow-1">
              <div class="d-flex justify-content-between">
                <div class="produto-info">
                  <h5 class="nome-produto">{{item.obtenhaDescricao()}}</h5>
                  <div class="quantidade-info">
                    <span class="quantidade">{{item.qtde}}{{item.obtenhaUnidade()}}</span>
                    <span class="preco-unitario">{{formatarMoeda(item.produto.preco)}}</span>
                  </div>
                  <div *ngIf="item.produtoTamanho" class="item-detalhe">{{item.produtoTamanho.nome}}</div>
                </div>
                <div class="preco-total">{{formatarMoeda(item.total)}}</div>
              </div>

              <div *ngIf="item.valoresAdicionais && item.valoresAdicionais.length > 0" class="item-detalhe adicionais">
                <span class="adicional-label">Adicionais:</span>
                <ng-container *ngFor="let last=last; let elemento of item.valoresAdicionais">
                  {{elemento.qtde}}x {{elemento.nome}}{{last ? '' : ', '}}
                </ng-container>
              </div>

              <div *ngIf="item.adicionais && item.adicionais.length > 0" class="item-detalhe adicionais">
                <span class="adicional-label">Adicionais:</span>
                <ng-container *ngFor="let adicional of item.adicionais; let last = last">
                  {{adicional.nome}} ({{formatarMoeda(adicional.preco)}}){{last ? '' : ', '}}
                </ng-container>
              </div>

              <div *ngIf="item.observacao" class="item-detalhe observacao">
                <span class="observacao-label">Observação:</span>
                "{{item.observacao}}"
              </div>

              <div class="acoes-item">
                <button class="btn-acao editar" (click)="editarItem(i, item)">
                  <i class="fas fa-edit"></i> Editar
                </button>
                <button class="btn-acao remover" (click)="removaItem(item)">
                  <i class="fas fa-trash"></i> Remover
                </button>
              </div>
            </div>
          </div>
          <div class="linha-divisoria"></div>
        </div>
      </div>
    </div>

    <div class="carrinho-lateral-footer" *ngIf="pedido && pedido.itens.length > 0">
      <!-- Mensagem de erro de pedido -->
      <div class="erro-pedido" *ngIf="erroPedido">
        <div class="erro-pedido-conteudo">
          <i class="fas fa-exclamation-circle"></i>
          <span>
            <strong>Falha ao enviar pedido</strong>
            <div class="erro-mensagem">{{mensagemErroPedido}}</div>
          </span>
          <button class="btn-fechar-erro" (click)="limparErroPedido()" title="Fechar">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="erro-pedido-acoes">
          <button class="btn-tentar-novamente" (click)="enviarPedido()">
            <i class="fas fa-sync-alt"></i> Tentar Novamente
          </button>
        </div>
      </div>

      <div class="resumo-pedido">
        <div class="d-flex justify-content-between resumo-item">
          <span class="text-muted">Subtotal</span>
          <span class="text-white" [ngClass]="{'com-desconto': pedido.desconto > 0}">{{formatarMoeda(pedido.obtenhaSubTotal())}}</span>
        </div>

        <div class="d-flex justify-content-between resumo-item" *ngIf="pedido.desconto > 0">
          <span class="text-muted">Desconto</span>
          <span class="text-danger">-{{formatarMoeda(pedido.desconto)}}</span>
        </div>

        <div class="d-flex justify-content-between resumo-item" *ngIf="pedido.taxaEntrega > 0">
          <span class="text-muted">Taxa de entrega</span>
          <span class="text-white">{{formatarMoeda(pedido.taxaEntrega)}}</span>
        </div>

        <div class="d-flex justify-content-between resumo-item total">
          <span class="text-white">Total</span>
          <span class="preco"><strong>{{formatarMoeda(pedido.total)}}</strong></span>
        </div>
      </div>

      <div class="acoes-carrinho">
        <button class="btn-continuar" (click)="fecharCarrinhoLateral()">
          <i class="fas fa-arrow-left"></i>
          Continuar Comprando
        </button>
        <button class="btn-enviar-pedido" [disabled]="!mesaValida" (click)="abrirLeituraCartao()">
          Enviar Pedido
          <i class="fas fa-arrow-right ml-1"></i>
        </button>
      </div>
    </div>
  </div>
</div>
