// Variáveis
$cor-primaria: #ff4500;
$cor-secundaria: #333;
$cor-fundo: #1a1a1a;
$cor-texto: #fff;
$cor-borda: #444;
$cor-sucesso: #4caf50;
$cor-erro: #f44336;

// Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@mixin button-base {
  cursor: pointer;
  border: none;
  transition: all 0.2s;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
}

@mixin overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

@mixin notification-base {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 15px 25px;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  animation: fadeIn 0.3s ease-out;
  min-width: 300px;
  text-align: center;
}

// Animações (removendo duplicadas)
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// Classes utilitárias
.flex {
  display: flex;
  &-center { @include flex-center; }
  &-between { @include flex-between; }
  &-column { flex-direction: column; }
}

.text {
  &-primary { color: $cor-primaria; }
  &-white { color: $cor-texto; }
  &-muted { color: rgba($cor-texto, 0.6); }
}

.bg {
  &-primary { background-color: $cor-primaria; }
  &-secondary { background-color: $cor-secundaria; }
  &-dark { background-color: $cor-fundo; }
}

// Botões reutilizáveis
.btn {
  @include button-base;

  &-primary {
    background-color: $cor-primaria;
    color: $cor-texto;
    &:hover { background-color: darken($cor-primaria, 10%); }
  }

  &-secondary {
    background-color: rgba($cor-texto, 0.1);
    color: $cor-texto;
    border: 1px solid rgba($cor-texto, 0.2);
    &:hover { background-color: rgba($cor-texto, 0.15); }
  }
}

// Estilos base
.cardapio-container {
  // height: 100vh; // Comentado - Pode causar problemas em WebViews
  height: 100%;
  @extend .flex-column;
  overflow: hidden;
  @extend .bg-dark;
  @extend .text-white;
  font-family: 'Roboto', sans-serif;
}

// Estilos gerais
.cardapio-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: $cor-fundo;
  color: $cor-texto;
  font-family: 'Roboto', sans-serif;

  .cardapio-header {
    position: sticky;
    top: 0;
    z-index: 100;
    background-color: $cor-secundaria;
    border-bottom: 1px solid $cor-borda;
    padding: 8px 16px;
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 16px;
    align-items: center;
    height: 64px;

    // Logo e nome da empresa
    .logo-empresa {
      @extend .flex-center;
      gap: 12px;
      cursor: pointer;
      padding: 4px;
      border-radius: 8px;
      transition: background-color 0.2s;

      &:hover {
        background-color: rgba($cor-texto, 0.05);
      }

      .logo-img {
        height: 40px;
        width: 40px;
        object-fit: cover;
        border-radius: 6px;
      }

      .nome-empresa {
        font-size: 1.2rem;
        font-weight: 500;
        @extend .text-white;
        margin: 0;
        max-width: 200px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    // Barra de pesquisa
    .search-bar {
      position: relative;
      max-width: 400px;
      margin: 0 auto;
      width: 100%;

      input {
        width: 100%;
        padding: 10px 16px 10px 40px;
        background-color: rgba($cor-texto, 0.08);
        border: 1px solid rgba($cor-texto, 0.1);
        border-radius: 8px;
        @extend .text-white;
        font-size: 0.95rem;
        transition: all 0.2s;

        &:focus {
          background-color: rgba($cor-texto, 0.12);
          border-color: rgba($cor-texto, 0.2);
          outline: none;
        }

        &::placeholder {
          color: rgba($cor-texto, 0.5);
        }
      }

      i {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: rgba($cor-texto, 0.5);
        font-size: 1.1rem;
      }
    }

    // Ações (Carrinho e Perfil)
    .actions {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 100%;

      .btn-carrinho {
        @include button-base;
        background-color: #2B1810;
        padding: 8px 14px;
        border-radius: 8px;
        position: relative;
        min-width: 160px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.05);
        transition: all 0.2s ease;

        .carrinho-info {
          display: flex;
          align-items: center;
          gap: 10px;

          i {
            color: #FF4500;
            font-size: 1.2rem;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
          }

          .carrinho-texto {
            text-align: left;
            line-height: 1.2;

            .carrinho-total {
              display: block;
              color: #FF4500;
              font-weight: 600;
              font-size: 1rem;
              text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
            }

            .carrinho-itens {
              display: block;
              color: #fff;
              font-size: 0.8rem;
              opacity: 0.9;
            }
          }
        }

        .badge {
          position: absolute;
          top: -8px;
          right: -8px;
          background-color: #FF4500;
          color: white;
          border-radius: 50%;
          width: 22px;
          height: 22px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 0.8rem;
          font-weight: 600;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.25);
          border: 1px solid rgba(255, 255, 255, 0.2);
        }

        &:hover {
          background-color: #3a2218;
          transform: translateY(-1px);
          box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
        }

        &:active {
          transform: translateY(1px);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }
      }
    }
  }

  .cardapio-content {
    flex: 1;
    overflow-y: auto;
  }
}

// Responsividade
@media (max-width: 768px) {
  .cardapio-header {
    padding: 8px 12px;
    gap: 12px;

    .logo-empresa .nome-empresa {
      display: none;
    }

    .search-bar {
      max-width: none;
    }

    .actions {
      margin-right: 8px;
    }
  }

  .carrinho-lateral {
    width: 100%;
    max-width: none;
  }

  .navegacao-principal {
    display: none;
  }

  .cardapio-content {
    flex-direction: column;
  }

  .secao-conteudo {
    padding: 15px;
  }

  .pedidos-abas {
    padding: 0 15px;
    overflow-x: auto;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .acoes-carrinho {
    grid-template-columns: 1fr !important;
  }
}

// Menu lateral fixo (como na imagem)
.menu-lateral-fixo {
  width: 200px;
  background-color: $cor-secundaria;
  padding: 20px 0;

  @media (max-width: 768px) {
    position: fixed;
    top: 60px;
    left: -200px;
    height: calc(100vh - 60px);
    z-index: 100;
    transition: left 0.3s ease;

    &.aberto {
      left: 0;
    }
  }

  .categoria-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }

    &.ativo {
      background-color: $cor-primaria;
    }

    i {
      margin-right: 10px;
      width: 20px;
      text-align: center;
    }

    &.destaque {
      color: $cor-primaria;

      &.ativo {
        color: $cor-texto;
      }
    }
  }
}

// Conteúdo principal
.conteudo-principal {
  flex: 1;
  display: flex;
  flex-direction: column;
}

// Categorias em cards
.categorias-cards {
  display: none;
  padding: 20px;

  @media (max-width: 768px) {
    display: block;
  }

  h2 {
    margin-top: 0;
    margin-bottom: 15px;
  }

  .categoria-card {
    margin-bottom: 15px;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;

    .categoria-imagem {
      height: 100px;
      background-size: cover;
      background-position: center;
      position: relative;

      .categoria-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        i {
          font-size: 1.5rem;
          margin-bottom: 5px;
        }

        span {
          font-weight: bold;
        }
      }
    }
  }

  .categoria-card {
    min-height: 120px; /* Altura mínima para cada card */
    margin-bottom: 10px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
    position: relative;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
    }

    &.ativo {
      border: 2px solid $cor-primaria;

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        right: -10px;
        transform: translateY(-50%);
        border-width: 10px 0 10px 10px;
        border-style: solid;
        border-color: transparent transparent transparent $cor-primaria;
      }
    }

    .categoria-imagem {
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      position: relative;

      .categoria-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.8));
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 10px;
        text-align: center;

        i {
          font-size: 1.8rem;
          margin-bottom: 8px;
          color: white;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        span {
          font-weight: bold;
          font-size: 0.9rem;
          color: white;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}

// Produtos
.produtos-container {
  flex: 1;
  padding: 0;
  overflow-y: auto;

  .categoria-banner {
    width: 100%;
    height: 150px;
    margin-bottom: 20px;

    .banner-imagem {
      height: 100%;
      background-size: cover;
      background-position: center;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
      }

      h2 {
        position: relative;
        color: $cor-texto;
        font-size: 2rem;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        margin: 0;
      }
    }
  }

  .produtos-lista {
    padding: 0px 0px 20px;
    margin-left: 0;

    .produto-card {
      display: flex;
      align-items: flex-start;
      margin-bottom: 25px;
      padding-bottom: 25px;
      border-bottom: 1px solid $cor-borda;

      &:last-child {
        border-bottom: none;
      }

      .produto-info {
        flex: 1;
        padding-right: 0;

        h3 {
          margin-top: 0;
          margin-bottom: 10px;
          font-size: 1.2rem;
          color: $cor-texto;
        }

        .produto-descricao {
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 10px;
          font-size: 0.9rem;
        }

        .produto-ingredientes {
          color: rgba(255, 255, 255, 0.6);
          margin-bottom: 15px;
          font-size: 0.85rem;
          font-style: italic;
        }

        .produto-preco {
          margin-bottom: 15px;

          .preco-label {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.6);
            font-style: italic;
            margin-right: 5px;
          }

          .preco-valor {
            font-size: 1.1rem;
            font-weight: bold;
            color: $cor-primaria;

            .unidade {
              font-size: 0.8rem;
              font-weight: normal;
              color: rgba(255, 255, 255, 0.6);
            }
          }

          .preco-antigo {
            font-size: 0.9rem;
            text-decoration: line-through;
            color: rgba(255, 255, 255, 0.5);
            margin-left: 8px;
          }

          .cashback-badge {
            display: inline-block;
            background-color: #4caf50;
            color: white;
            font-size: 0.8rem;
            padding: 2px 6px;
            border-radius: 4px;
            margin-left: 8px;

            i {
              margin-right: 3px;
            }
          }

          .tamanhos-precos {
            margin-top: 10px;

            .tamanho-item {
              margin-bottom: 8px;

              .tamanho-descricao {
                display: block;
                font-size: 0.9rem;
                color: rgba(255, 255, 255, 0.8);
              }
            }
          }

          .limite-pedido {
            display: block;
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.5);
            margin-top: 5px;
          }
        }

        .produto-info-adicional {
          display: flex;
          margin-bottom: 15px;

          span {
            font-size: 0.85rem;
            color: rgba(255, 255, 255, 0.6);
            margin-right: 15px;

            &:last-child {
              margin-right: 0;
            }
          }
        }

        .btn-adicionar {
          background-color: $cor-primaria;
          color: white;
          border: none;
          padding: 8px 15px;
          border-radius: 4px;
          cursor: pointer;
          font-weight: bold;
          transition: background-color 0.2s;

          &:hover {
            background-color: darken($cor-primaria, 10%);
          }
        }
      }

      .produto-imagem {
        margin-right: 20px;
        margin-left: 0;
        flex-shrink: 0;
        width: 200px;
        height: 200px;
        border-radius: 8px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }
}

// Notificação
.notificacao {
  @include notification-base;

  i {
    font-size: 20px;
  }

  &.success {
    background-color: $cor-sucesso;
  }

  &.error {
    background-color: $cor-erro;
    font-size: 16px;
    padding: 18px 25px;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, 20px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
  from { opacity: 1; transform: translateY(0); }
  to { opacity: 0; transform: translateY(20px); }
}

.sem-produtos {
  padding: 20px;
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
}

// Navegação principal (primeira coluna)
.navegacao-principal {
  width: 120px;
  background-color: $cor-secundaria;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
  border-right: 1px solid rgba(255, 255, 255, 0.1);

  @media (max-width: 768px) {
    display: none;
  }

  .nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 15px 10px;
    cursor: pointer;
    transition: background-color 0.2s;
    margin-bottom: 5px;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }

    &.ativo {
      background-color: $cor-primaria;
    }

    i {
      font-size: 1.5rem;
      margin-bottom: 8px;
    }

    span {
      font-size: 0.85rem;
      text-align: center;
      max-width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

// Conteúdo para outras seções
.conteudo-outras-secoes {
  flex: 1;
  padding: 20px;

  .secao-conteudo {
    h2 {
      margin-top: 0;
      margin-bottom: 20px;
      color: $cor-texto;
      font-size: 1.8rem;
    }

    p {
      color: rgba(255, 255, 255, 0.7);
      margin-bottom: 20px;
    }

    // Estilo para seção de Meus Pedidos
    &.meus-pedidos-tablet {
      max-width: 1200px;
      margin: 0 auto;

      // Aplicar estilos de carrinho
      .carrinho-vazio {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 300px;
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 8px;
        padding: 30px;

        .carrinho-vazio-conteudo {
          text-align: center;

          img {
            margin-bottom: 20px;
            opacity: 0.8;
          }

          h3 {
            color: $cor-texto;
            margin-bottom: 10px;
          }

          h5 {
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 20px;
          }

          .btn-continuar-comprando {
            background-color: $cor-primaria;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.2s;

            &:hover {
              background-color: darken($cor-primaria, 10%);
            }
          }
        }
      }

      // Conteúdo do carrinho
      .carrinho-conteudo {
        display: grid;
        grid-template-columns: 3fr 2fr;
        gap: 30px;

        @media (max-width: 992px) {
          grid-template-columns: 1fr;
        }

        // Lista de itens no carrinho
        .carrinho-itens {
          background-color: rgba(0, 0, 0, 0.2);
          border-radius: 8px;
          padding: 20px;

          .carrinho-item {
            padding: 15px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);

            &:last-child {
              border-bottom: none;
            }

            .item-container {
              display: flex;
              gap: 15px;

              .item-imagem {
                width: 80px;
                height: 80px;
                flex-shrink: 0;
                border-radius: 8px;
                overflow: hidden;

                img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                }
              }

              .item-info {
                flex: 1;

                .item-descricao {
                  h4 {
                    margin: 0 0 10px;
                    color: $cor-texto;
                    font-size: 1.1rem;
                  }

                  .item-observacao {
                    color: rgba(255, 255, 255, 0.6);
                    font-size: 0.9rem;
                    font-style: italic;
                    margin-bottom: 10px;
                  }
                }

                .item-acoes {
                  display: flex;
                  gap: 10px;

                  button {
                    background: transparent;
                    border: none;
                    color: rgba(255, 255, 255, 0.7);
                    font-size: 0.9rem;
                    padding: 5px 10px;
                    border-radius: 4px;
                    cursor: pointer;
                    transition: all 0.2s;
                    display: flex;
                    align-items: center;
                    gap: 5px;

                    i {
                      font-size: 0.8rem;
                    }

                    &.btn-editar {
                      &:hover {
                        background-color: rgba(33, 150, 243, 0.1);
                        color: #2196f3;
                      }
                    }

                    &.btn-remover {
                      &:hover {
                        background-color: rgba(244, 67, 54, 0.1);
                        color: $cor-erro;
                      }
                    }
                  }
                }
              }

              .item-valor {
                text-align: right;
                min-width: 80px;

                h4 {
                  color: $cor-primaria;
                  font-weight: bold;
                  margin: 0;

                  &.resgate-preco {
                    color: #4caf50;
                  }
                }
              }
            }

            .item-adicionais {
              margin-top: 10px;
              padding-left: 95px;

              .adicionais-lista {
                color: rgba(255, 255, 255, 0.6);
                font-size: 0.85rem;
                margin: 0;
              }
            }
          }
        }

        // Resumo do pedido
        .carrinho-resumo {
          background-color: rgba(0, 0, 0, 0.2);
          border-radius: 8px;
          padding: 20px;
          position: sticky;
          top: 20px;

          .resumo-linha {
            display: flex;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);

            &:last-of-type {
              border-bottom: none;
            }

            span {
              color: $cor-texto;

              &.com-desconto {
                text-decoration: line-through;
                color: rgba(255, 255, 255, 0.5);
              }

              &.desconto-valor {
                color: $cor-erro;
              }

              &.resgate-valor {
                color: #4caf50;
              }
            }

            &.promocao, &.cupom {
              span {
                display: flex;
                align-items: center;
                gap: 5px;
                font-size: 0.9rem;

                .btn-remover-cupom {
                  background: none;
                  border: none;
                  color: $cor-erro;
                  font-size: 0.8rem;
                  padding: 0;
                  margin-left: 5px;
                  cursor: pointer;
                  text-decoration: underline;
                }
              }
            }

            &.total {
              font-weight: bold;
              font-size: 1.1rem;
              padding-top: 15px;
              margin-top: 5px;
              border-top: 2px solid rgba(255, 255, 255, 0.1);

              .total-valor {
                color: $cor-primaria;
                font-size: 1.2rem;
              }
            }
          }

          // Aplicar cupom
          .aplicar-cupom {
            margin-top: 20px;

            .cupom-toggle {
              display: flex;
              align-items: center;
              gap: 10px;
              padding: 12px;
              background-color: rgba(255, 255, 255, 0.05);
              border-radius: 6px;
              cursor: pointer;
              transition: background-color 0.2s;

              &:hover {
                background-color: rgba(255, 255, 255, 0.1);
              }

              i {
                color: $cor-primaria;
                font-size: 1.1rem;
              }

              span {
                flex: 1;
                color: $cor-texto;
              }
            }

            .cupom-form {
              margin-top: 15px;

              .cupom-input-group {
                display: flex;
                gap: 10px;

                .cupom-input {
                  flex: 1;
                  padding: 10px 12px;
                  background-color: rgba(255, 255, 255, 0.05);
                  border: 1px solid rgba(255, 255, 255, 0.1);
                  color: $cor-texto;
                  border-radius: 6px;

                  &:focus {
                    outline: none;
                    border-color: $cor-primaria;
                  }

                  &::placeholder {
                    color: rgba(255, 255, 255, 0.5);
                  }
                }

                .btn-aplicar-cupom {
                  background-color: $cor-primaria;
                  color: white;
                  border: none;
                  padding: 10px 15px;
                  border-radius: 6px;
                  cursor: pointer;
                  transition: background-color 0.2s;
                  min-width: 80px;
                  font-weight: bold;

                  &:hover {
                    background-color: darken($cor-primaria, 10%);
                  }

                  &:disabled {
                    background-color: rgba(255, 69, 0, 0.5);
                    cursor: not-allowed;
                  }
                }
              }

              .erro-cupom {
                color: $cor-erro;
                font-size: 0.85rem;
                margin-top: 10px;
              }
            }
          }

          // Botões de ação
          .carrinho-acoes {
            margin-top: 30px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;

            @media (max-width: 768px) {
              grid-template-columns: 1fr;
            }

            button {
              padding: 12px 15px;
              border-radius: 6px;
              font-weight: bold;
              cursor: pointer;
              transition: all 0.2s;
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 8px;

              &.btn-voltar {
                background-color: rgba(255, 255, 255, 0.1);
                color: $cor-texto;
                border: 1px solid rgba(255, 255, 255, 0.2);

                &:hover {
                  background-color: rgba(255, 255, 255, 0.15);
                }
              }

              &.btn-finalizar {
                background-color: $cor-primaria;
                color: white;
                border: none;

                &:hover {
                  background-color: darken($cor-primaria, 10%);
                }

                &:disabled {
                  background-color: rgba(255, 69, 0, 0.5);
                  cursor: not-allowed;
                }
              }
            }
          }
        }
      }

      .pedidos-titulo {
        margin-bottom: 20px;
        color: $cor-texto;
        font-size: 1.8rem;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: -8px;
          left: 0;
          width: 80px;
          height: 3px;
          background-color: $cor-primaria;
        }
      }

      // Abas para navegação entre carrinho e histórico
      .pedidos-abas {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
        border-bottom: 1px solid $cor-borda;
        padding-bottom: 10px;

        .aba {
          padding: 10px 20px;
          cursor: pointer;
          border-radius: 20px;
          transition: all 0.2s;
          display: flex;
          align-items: center;
          gap: 8px;

          &:hover {
            background-color: rgba(255, 255, 255, 0.1);
          }

          &.ativa {
            background-color: $cor-primaria;
            color: white;
          }

          .badge-mini {
            background-color: rgba(255, 255, 255, 0.2);
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 0.8rem;
          }

          i {
            font-size: 1.1rem;
          }
        }
      }

      // Conteúdo do histórico de pedidos
      .historico-vazio {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 60vh;

        .historico-vazio-conteudo, .historico-carregando {
          text-align: center;

          .btn-continuar-comprando {
            padding: 10px 20px;
            background-color: $cor-primaria;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;

            &:hover {
              background-color: darken($cor-primaria, 10%);
            }
          }
        }
      }

      .historico-pedidos {
        .pedido-card {
          background-color: rgba(255, 255, 255, 0.05);
          border-radius: 12px;
          margin-bottom: 20px;
          overflow: hidden;

          .pedido-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background-color: rgba(0, 0, 0, 0.2);

            .pedido-info {
              .pedido-numero {
                font-size: 1.1rem;
                font-weight: 500;
                color: $cor-primaria;
                margin-right: 12px;
              }

              .pedido-data {
                font-size: 0.9rem;
                color: rgba(255, 255, 255, 0.6);
              }
            }

            .pedido-status {
              padding: 5px 10px;
              border-radius: 4px;
              font-size: 14px;
              font-weight: bold;

              &.status-novo {
                background-color: #3f51b5;
                color: white;
              }

              &.status-em-preparação {
                background-color: #ff9800;
                color: black;
              }

              &.status-entregue {
                background-color: rgba($cor-sucesso, 0.2);
                color: $cor-sucesso;
              }

              &.status-preparacao {
                background-color: rgba(#ffc107, 0.2);
                color: #ffc107;
              }

              &.status-novo {
                background-color: rgba(#2196f3, 0.2);
                color: #2196f3;
              }
            }
          }

          .pedido-itens {
            padding: 15px 20px;

            .pedido-item {
              display: flex;
              align-items: center;
              margin-bottom: 12px;
              padding-bottom: 12px;
              border-bottom: 1px solid rgba(255, 255, 255, 0.1);

              &:last-child {
                margin-bottom: 0;
                padding-bottom: 0;
                border-bottom: none;
              }

              .item-quantidade {
                font-weight: 600;
                color: $cor-primaria;
                margin-right: 12px;
                min-width: 40px;
              }

              .item-descricao {
                flex: 1;
                font-size: 0.95rem;
              }

              .item-preco {
                font-weight: 500;
                color: rgba(255, 255, 255, 0.8);
              }
            }
          }

          .pedido-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background-color: rgba(0, 0, 0, 0.2);
            border-top: 1px solid rgba(255, 255, 255, 0.1);

            .pedido-total {
              display: flex;
              align-items: baseline;
              gap: 8px;

              span:first-child {
                font-size: 1rem;
                color: rgba(255, 255, 255, 0.7);
              }

              .total-valor {
                font-size: 1.5rem;
                font-weight: 600;
                color: $cor-primaria;
                letter-spacing: -0.5px;
              }
            }

            .btn-repetir-pedido {
              background-color: rgba($cor-primaria, 0.15);
              color: $cor-primaria;
              border: none;
              padding: 8px 16px;
              border-radius: 8px;
              cursor: pointer;
              display: flex;
              align-items: center;

              &:hover {
                background-color: rgba($cor-primaria, 0.25);
              }

              i {
                margin-right: 6px;
              }
            }
          }
        }
      }
    }
  }

  // Estilos para o carrinho de compras no tablet
  .carrinho-tablet {
    max-width: 1200px;
    margin: 0 auto;

    .carrinho-titulo {
      margin-bottom: 30px;
      color: $cor-texto;
      font-size: 2rem;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 0;
        width: 80px;
        height: 3px;
        background-color: $cor-primaria;
      }
    }

    // Carinho vazio
    .carrinho-vazio {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 300px;
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 8px;
      padding: 30px;

      .carrinho-vazio-conteudo {
        text-align: center;

        img {
          margin-bottom: 20px;
          opacity: 0.8;
        }

        h3 {
          color: $cor-texto;
          margin-bottom: 10px;
        }

        h5 {
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 20px;
        }

        .btn-continuar-comprando {
          background-color: $cor-primaria;
          color: white;
          border: none;
          padding: 12px 20px;
          border-radius: 6px;
          font-weight: bold;
          cursor: pointer;
          transition: background-color 0.2s;

          &:hover {
            background-color: darken($cor-primaria, 10%);
          }
        }
      }
    }

    // Conteúdo do carrinho
    .carrinho-conteudo {
      display: grid;
      grid-template-columns: 3fr 2fr;
      gap: 30px;

      @media (max-width: 992px) {
        grid-template-columns: 1fr;
      }

      // Lista de itens no carrinho
      .carrinho-itens {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 8px;
        padding: 20px;

        .carrinho-item {
          padding: 15px 0;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);

          &:last-child {
            border-bottom: none;
          }

          .item-container {
            display: flex;
            gap: 15px;

            .item-imagem {
              width: 80px;
              height: 80px;
              flex-shrink: 0;
              border-radius: 8px;
              overflow: hidden;

              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }

            .item-info {
              flex: 1;

              .item-descricao {
                h4 {
                  margin: 0 0 10px;
                  color: $cor-texto;
                  font-size: 1.1rem;
                }

                .item-observacao {
                  color: rgba(255, 255, 255, 0.6);
                  font-size: 0.9rem;
                  font-style: italic;
                  margin-bottom: 10px;
                }
              }

              .item-acoes {
                display: flex;
                gap: 10px;

                button {
                  background: transparent;
                  border: none;
                  color: rgba(255, 255, 255, 0.7);
                  font-size: 0.9rem;
                  padding: 5px 10px;
                  border-radius: 4px;
                  cursor: pointer;
                  transition: all 0.2s;
                  display: flex;
                  align-items: center;
                  gap: 5px;

                  i {
                    font-size: 0.8rem;
                  }

                  &.btn-editar {
                    &:hover {
                      background-color: rgba(33, 150, 243, 0.1);
                      color: #2196f3;
                    }
                  }

                  &.btn-remover {
                    &:hover {
                      background-color: rgba(244, 67, 54, 0.1);
                      color: $cor-erro;
                    }
                  }
                }
              }
            }

            .item-valor {
              text-align: right;
              min-width: 80px;

              h4 {
                color: $cor-primaria;
                font-weight: bold;
                margin: 0;

                &.resgate-preco {
                  color: #4caf50;
                }
              }
            }
          }

          .item-adicionais {
            margin-top: 10px;
            padding-left: 95px;

            .adicionais-lista {
              color: rgba(255, 255, 255, 0.6);
              font-size: 0.85rem;
              margin: 0;
            }
          }
        }
      }

      // Resumo do pedido
      .carrinho-resumo {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 8px;
        padding: 20px;
        position: sticky;
        top: 20px;

        .resumo-linha {
          display: flex;
          justify-content: space-between;
          padding: 12px 0;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);

          &:last-of-type {
            border-bottom: none;
          }

          span {
            color: $cor-texto;

            &.com-desconto {
              text-decoration: line-through;
              color: rgba(255, 255, 255, 0.5);
            }

            &.desconto-valor {
              color: $cor-erro;
            }

            &.resgate-valor {
              color: #4caf50;
            }
          }

          &.promocao, &.cupom {
            span {
              display: flex;
              align-items: center;
              gap: 5px;
              font-size: 0.9rem;

              .btn-remover-cupom {
                background: none;
                border: none;
                color: $cor-erro;
                font-size: 0.8rem;
                padding: 0;
                margin-left: 5px;
                cursor: pointer;
                text-decoration: underline;
              }
            }
          }

          &.total {
            font-weight: bold;
            font-size: 1.1rem;
            padding-top: 15px;
            margin-top: 5px;
            border-top: 2px solid rgba(255, 255, 255, 0.1);

            .total-valor {
              color: $cor-primaria;
              font-size: 1.2rem;
            }
          }
        }

        // Aplicar cupom
        .aplicar-cupom {
          margin-top: 20px;

          .cupom-toggle {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s;

            &:hover {
              background-color: rgba(255, 255, 255, 0.1);
            }

            i {
              color: $cor-primaria;
              font-size: 1.1rem;
            }

            span {
              flex: 1;
              color: $cor-texto;
            }
          }

          .cupom-form {
            margin-top: 15px;

            .cupom-input-group {
              display: flex;
              gap: 10px;

              .cupom-input {
                flex: 1;
                padding: 10px 12px;
                background-color: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                color: $cor-texto;
                border-radius: 6px;

                &:focus {
                  outline: none;
                  border-color: $cor-primaria;
                }

                &::placeholder {
                  color: rgba(255, 255, 255, 0.5);
                }
              }

              .btn-aplicar-cupom {
                background-color: $cor-primaria;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 6px;
                cursor: pointer;
                transition: background-color 0.2s;
                min-width: 80px;
                font-weight: bold;

                &:hover {
                  background-color: darken($cor-primaria, 10%);
                }

                &:disabled {
                  background-color: rgba(255, 69, 0, 0.5);
                  cursor: not-allowed;
                }
              }
            }

            .erro-cupom {
              color: $cor-erro;
              font-size: 0.85rem;
              margin-top: 10px;
            }
          }
        }

        // Botões de ação
        .carrinho-acoes {
          margin-top: 30px;
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 15px;

          @media (max-width: 768px) {
            grid-template-columns: 1fr;
          }

          button {
            padding: 12px 15px;
            border-radius: 6px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;

            &.btn-voltar {
              background-color: rgba(255, 255, 255, 0.1);
              color: $cor-texto;
              border: 1px solid rgba(255, 255, 255, 0.2);

              &:hover {
                background-color: rgba(255, 255, 255, 0.15);
              }
            }

            &.btn-finalizar {
              background-color: $cor-primaria;
              color: white;
              border: none;

              &:hover {
                background-color: darken($cor-primaria, 10%);
              }

              &:disabled {
                background-color: rgba(255, 69, 0, 0.5);
                cursor: not-allowed;
              }
            }
          }
        }
      }
    }
  }
}

// Menu de categorias com cards (segunda coluna)
.menu-categorias-cards {
  width: 180px;
  background-color: rgba(0, 0, 0, 0.2);
  overflow-y: auto;
  padding: 15px 10px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  border-right: 1px solid rgba(255, 255, 255, 0.1);

  // Container para o scroll, mantém padding e estilos de scrollbar
  .categorias-scroll-container {
    height: 100%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    padding-top: 10px; /* Adicionando espaço no topo do container */

    /* Estilização para scrollbar */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.3);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: rgba(0, 0, 0, 0.5);
    }

    // Removido :first-child margin-top, padding-top no container é suficiente
  }

  // Estilos dos cards de categoria (removida a duplicata)
  .categoria-card {
    min-height: 120px;
    margin-bottom: 10px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
    position: relative;
    // height: 100px; // Removido height fixo, min-height é melhor

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
    }

    &.ativo {
      border: 2px solid $cor-primaria;

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        right: -10px;
        transform: translateY(-50%);
        border-width: 10px 0 10px 10px;
        border-style: solid;
        border-color: transparent transparent transparent $cor-primaria;
      }
    }

    .categoria-imagem {
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      position: relative;

      .categoria-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.8));
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 10px;
        text-align: center;

        i {
          font-size: 1.8rem;
          margin-bottom: 8px;
          color: white;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        span {
          font-weight: bold;
          font-size: 0.9rem;
          color: white;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}

// Scrollbars personalizadas
@mixin custom-scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba($cor-texto, 0.05);
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba($cor-texto, 0.2);
    border-radius: 10px;
    transition: background 0.3s;

    &:hover {
      background: rgba($cor-texto, 0.3);
    }
  }
}

// Aplicar scrollbar personalizada
.menu-categorias-cards,
.produtos-lista,
.conteudo-principal,
.conteudo-outras-secoes,
.carrinho-lateral-content {
  @include custom-scrollbar;

  // Esconder scrollbar em dispositivos touch
  @media (pointer: coarse) {
    -webkit-overflow-scrolling: touch;
    &::-webkit-scrollbar {
      display: none;
    }
  }
}

// Firefox scrollbar
* {
  scrollbar-width: thin;

  // Mostrar scrollbar personalizada em dispositivos não-touch
  @media (pointer: fine) {
    &:hover::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

// Adicionar efeito de fade para indicar scroll
.menu-categorias-cards {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: linear-gradient(to top, $cor-secundaria, transparent);
    pointer-events: none;
    opacity: 0.8;
  }
}

.produtos-lista {
  position: relative;
  margin-left: 0;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: linear-gradient(to top, $cor-fundo, transparent);
    pointer-events: none;
    opacity: 0.8;
  }
}

.produto-preco {
  margin-bottom: 15px;

  .preco-label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
    font-style: italic;
    margin-right: 5px;
  }

  .preco-valor {
    font-size: 1.1rem;
    font-weight: bold;
    color: $cor-primaria;

    .unidade {
      font-size: 0.8rem;
      font-weight: normal;
      color: rgba(255, 255, 255, 0.6);
    }
  }

  .preco-antigo {
    font-size: 0.9rem;
    text-decoration: line-through;
    color: rgba(255, 255, 255, 0.5);
    margin-left: 8px;
  }

  .cashback-badge {
    display: inline-block;
    background-color: #4caf50;
    color: white;
    font-size: 0.8rem;
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: 8px;

    i {
      margin-right: 3px;
    }
  }

  .tamanhos-precos {
    margin-top: 10px;

    .tamanho-item {
      margin-bottom: 8px;

      .tamanho-descricao {
        display: block;
        font-size: 0.9rem;
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }

  .limite-pedido {
    display: block;
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.5);
    margin-top: 5px;
  }
}

.produto-indisponivel {
  margin-bottom: 15px;

  .texto-indisponivel {
    color: $cor-erro;
    font-weight: bold;
    font-size: 0.9rem;
  }
}

// Ajustar o layout do conteúdo principal para trabalhar bem com a segunda coluna
.cardapio-content {
  display: flex;
  flex: 1;

  .navegacao-principal {
    width: 120px;
    background-color: $cor-secundaria;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
    border-right: 1px solid rgba(255, 255, 255, 0.1);

    .nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      padding: 15px 10px;
      cursor: pointer;
      transition: background-color 0.2s;
      margin-bottom: 5px;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }

      &.ativo {
        background-color: $cor-primaria;
      }

      i {
        font-size: 1.5rem;
        margin-bottom: 8px;
      }

      span {
        font-size: 0.85rem;
        text-align: center;
        max-width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .menu-categorias-cards {
    width: 180px;
    background-color: rgba(0, 0, 0, 0.2);
    overflow-y: auto;
    padding: 15px 10px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
  }

  .conteudo-principal {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
  }
}

// Melhorar a responsividade
@media (max-width: 768px) {
  .cardapio-content {
    flex-direction: column;
  }

  .secao-conteudo {
    padding: 15px;
  }

  .pedidos-abas {
    padding: 0 15px;
    overflow-x: auto;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;

    &::-webkit-scrollbar {
      display: none;
    }
  }
}

// Carrinho Lateral
.carrinho-lateral-overlay {
  @include overlay;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;

  &.ativo {
    opacity: 1;
    visibility: visible;
  }
}

.carrinho-lateral {
  position: fixed;
  top: 0;
  right: -600px; // Começa fora da tela
  width: 90%;
  max-width: 600px;
  height: 100%;
  background-color: $cor-fundo;
  z-index: 1100;
  display: flex;
  flex-direction: column;
  box-shadow: -5px 0 25px rgba(0, 0, 0, 0.3);
  transition: right 0.3s ease-out;
  overflow: hidden;

  &.aberto {
    right: 0;
  }

  .carrinho-lateral-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .carrinho-titulo {
      display: flex;
      flex-direction: column;

      h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: white;

        i {
          margin-right: 10px;
          color: $cor-primaria;
        }
      }

      .carrinho-itens-count {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        margin-top: 4px;
      }
    }

    .btn-fechar {
      background: none;
      border: none;
      color: rgba(255, 255, 255, 0.7);
      font-size: 20px;
      cursor: pointer;
      transition: all 0.2s;
      width: 36px;
      height: 36px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: white;
      }
    }
  }

  .carrinho-lateral-content {
    flex: 1;
    overflow-y: auto;
    padding: 15px 20px;

    .produto {
      padding: 15px 0;
      position: relative;

      .img-produto {
        width: 70px;
        height: 70px;
        object-fit: cover;
        border-radius: 8px;
      }

      .produto-info {
        .nome-produto {
          font-size: 16px;
          font-weight: 600;
          color: white;
          margin: 0 0 5px;
        }

        .quantidade-info {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-bottom: 5px;

          .quantidade {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
          }

          .preco-unitario {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.6);
          }
        }
      }

      .preco-total {
        font-size: 16px;
        font-weight: 600;
        color: white;
      }

      .item-detalhe {
        font-size: 13px;
        color: rgba(255, 255, 255, 0.7);
        margin-top: 5px;

        &.adicionais {
          margin-top: 8px;

          .adicional-label {
            font-weight: 500;
            margin-right: 5px;
            color: rgba(255, 255, 255, 0.9);
          }
        }

        &.observacao {
          margin-top: 8px;
          font-style: italic;

          .observacao-label {
            font-weight: 500;
            margin-right: 5px;
            color: rgba(255, 255, 255, 0.9);
            font-style: normal;
          }
        }
      }

      .acoes-item {
        display: flex;
        gap: 10px;
        margin-top: 12px;

        .btn-acao {
          padding: 6px 12px;
          border: none;
          border-radius: 4px;
          font-size: 13px;
          font-weight: 500;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 5px;
          transition: all 0.2s;

          &.editar {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;

            &:hover {
              background-color: rgba(255, 255, 255, 0.2);
            }
          }

          &.remover {
            background-color: rgba(244, 67, 54, 0.1);
            color: #ff6b6b;

            &:hover {
              background-color: rgba(244, 67, 54, 0.2);
            }
          }

          i {
            font-size: 12px;
          }
        }
      }

      .linha-divisoria {
        height: 1px;
        background-color: rgba(255, 255, 255, 0.1);
        margin-top: 15px;
      }
    }
  }

  .carrinho-lateral-footer {
    padding: 15px 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background-color: rgba(0, 0, 0, 0.2);

    .resumo-pedido {
      .resumo-item {
        margin-bottom: 8px;
        font-size: 14px;

        &.total {
          margin-top: 15px;
          font-size: 16px;
          padding-top: 10px;
          border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .preco {
          font-size: 18px;
          font-weight: 700;
          color: $cor-primaria;
        }

        .com-desconto {
          text-decoration: line-through;
          opacity: 0.7;
        }
      }
    }

    .acoes-carrinho {
      display: flex;
      gap: 15px;
      margin-top: 20px;

      .btn-continuar {
        flex: 1;
        padding: 12px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 6px;
        background: none;
        color: white;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }

        i {
          font-size: 12px;
        }
      }

      .btn-enviar-pedido {
        flex: 2;
        padding: 12px;
        border: none;
        border-radius: 6px;
        background-color: $cor-primaria;
        color: white;
        font-size: 15px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;

        &:hover {
          background-color: darken($cor-primaria, 5%);
          transform: translateY(-2px);
        }

        &:disabled {
          background-color: rgba($cor-primaria, 0.5);
          cursor: not-allowed;
          transform: none;
        }

        i {
          font-size: 12px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .carrinho-lateral {
    width: 100%;
  }
}

.alerta-mesa {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px;
  margin-bottom: 15px;
  border-radius: 4px;
  border: 1px solid #f5c6cb;

  .alerta-mesa-conteudo {
    display: flex;
    align-items: center;

    i {
      margin-right: 10px;
      font-size: 18px;
    }

    span {
      font-weight: 500;
    }
  }
}

// Estilo para o erro de pedido
.erro-pedido {
  background: linear-gradient(135deg, darken($cor-erro, 20%) 0%, $cor-erro 100%);
  color: white;
  padding: 20px;
  margin-bottom: 22px;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
  animation: fadeInDown 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
  position: relative;
  overflow: hidden;
}

/* Estilos para dialog de leitura de cartão do cliente */
.cartao-cliente-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cartao-cliente-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
}

.cartao-cliente-container {
  position: relative;
  width: 100%;
  height: 100%;
  max-width: 100%;
  background-color: #1a1a1a;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.3s ease-out;
}

.cartao-cliente-header {
  background-color: #000;
  color: #fff;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border-bottom: 2px solid #ff4500;

  .btn-voltar {
    background: transparent;
    border: none;
    color: #ff4500;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    padding: 8px 15px;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;

    i {
      font-size: 14px;
    }

    &:hover {
      background-color: rgba(255, 69, 0, 0.1);
      transform: translateX(-2px);
    }
  }

  .logo-header {
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      height: 36px;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5));
    }
  }

  .spacer {
    width: 80px; /* Para equilibrar o botão voltar */
  }
}

/* Layout de duas colunas */
.cartao-cliente-duas-colunas {
  display: flex;
  flex: 1;
  overflow: hidden;
  background-color: #1a1a1a;

  /* Coluna esquerda - Scanner */
  .coluna-scanner {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .scanner-container {
      position: relative;
      width: 100%;
      height: 100%;
      background-color: #000;
      overflow: hidden;
      border-radius: 0;

      .scanner-frame {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 70%;
        height: 60%;
        border: 2px solid rgba(255, 69, 0, 0.5);
        border-radius: 8px;
        z-index: 2;

        .scanner-line {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 2px;
          background-color: #ff4500;
          box-shadow: 0 0 15px #ff4500;
          animation: scanLine 2s linear infinite;
        }

        .scanner-corners {
          position: absolute;
          width: 20px;
          height: 20px;
          border-color: #ff4500;
          border-style: solid;
          border-width: 0;

          &.top-left {
            top: -2px;
            left: -2px;
            border-top-width: 3px;
            border-left-width: 3px;
            border-top-left-radius: 4px;
          }

          &.top-right {
            top: -2px;
            right: -2px;
            border-top-width: 3px;
            border-right-width: 3px;
            border-top-right-radius: 4px;
          }

          &.bottom-left {
            bottom: -2px;
            left: -2px;
            border-bottom-width: 3px;
            border-left-width: 3px;
            border-bottom-left-radius: 4px;
          }

          &.bottom-right {
            bottom: -2px;
            right: -2px;
            border-bottom-width: 3px;
            border-right-width: 3px;
            border-bottom-right-radius: 4px;
          }
        }
      }

      .scanner-video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  /* Coluna direita - Instruções */
  .coluna-instrucoes {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 30px;
    overflow-y: auto;
    background-color: #222;

    .instrucoes-container {
      width: 100%;
      margin-bottom: 40px;
      background-color: #333;
      padding: 25px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      border-left: 4px solid #ff4500;

      .instrucao-texto {
        margin-bottom: 20px;

        h3 {
          font-size: 24px;
          font-weight: 600;
          color: #ff4500;
          margin-bottom: 15px;
          display: flex;
          align-items: center;
          gap: 10px;

          i {
            font-size: 22px;
          }
        }

        p {
          font-size: 16px;
          color: #ddd;
          margin-bottom: 10px;
          line-height: 1.5;

          i {
            color: #ff4500;
            margin-right: 5px;
          }

          strong {
            color: #fff;
          }
        }
      }

      .instrucao-imagem {
        display: flex;
        justify-content: center;
        margin-top: 20px;

        img {
          max-width: 400px;
          width: 100%;
          height: auto;
          filter: drop-shadow(0 4px 8px rgba(255, 69, 0, 0.3));
          transition: transform 0.3s ease;

          &:hover {
            transform: scale(1.05);
          }
        }
      }
    }

    .codigo-manual {
      width: 100%;

      h4 {
        font-size: 18px;
        font-weight: 500;
        color: #ddd;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 8px;

        i {
          color: #ff4500;
          font-size: 16px;
        }
      }

      .input-group {
        display: flex;

        input {
          flex: 1;
          padding: 15px;
          border: 1px solid #444;
          background-color: #2a2a2a;
          color: #fff;
          border-radius: 4px 0 0 4px;
          font-size: 16px;

          &:focus {
            outline: none;
            border-color: #ff4500;
            box-shadow: 0 0 0 2px rgba(255, 69, 0, 0.2);
          }

          &::placeholder {
            color: #777;
          }
        }

        .btn-confirmar-codigo {
          background-color: #ff4500;
          color: white;
          border: none;
          padding: 0 25px;
          border-radius: 0 4px 4px 0;
          cursor: pointer;
          font-weight: 600;
          font-size: 16px;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          gap: 8px;

          i {
            font-size: 14px;
          }

          &:hover {
            background-color: darken(#ff4500, 10%);
            transform: translateX(2px);
          }

          &:active {
            transform: translateY(1px);
          }
        }
      }

      .dica-codigo {
        margin-top: 15px;
        padding: 12px;
        background-color: rgba(255, 69, 0, 0.1);
        border-radius: 4px;
        border-left: 3px solid #ff4500;

        p {
          color: #bbb;
          font-size: 14px;
          margin: 0;
          font-style: italic;
        }
      }
    }
  }
}

/* Responsividade para telas menores */
@media (max-width: 768px) {
  .cartao-cliente-duas-colunas {
    flex-direction: column;

    .coluna-scanner {
      height: 40vh;
    }

    .coluna-instrucoes {
      padding: 20px;

      .instrucoes-container {
        padding: 15px;
        margin-bottom: 20px;

        .instrucao-texto {
          h3 {
            font-size: 20px;
          }
        }
      }
    }
  }
}

@keyframes scanLine {
  0% {
    top: 0;
    opacity: 0.8;
  }
  25% {
    opacity: 1;
  }
  50% {
    top: calc(100% - 2px);
    opacity: 0.8;
  }
  100% {
    top: 0;
    opacity: 0.8;
  }
}

// Painel lateral para produtos
.painel-lateral-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 998;
  transition: opacity 0.3s ease;
  opacity: 0;

  &.ativo {
    display: block;
    opacity: 1;
  }
}

.painel-lateral {
  position: fixed;
  top: 0;
  right: -600px; // Inicia fora da tela à direita
  width: 100%;
  max-width: 600px;
  height: 100%;
  background-color: $cor-fundo;
  z-index: 999;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease-in-out;
  overflow-y: auto;
  @include custom-scrollbar;

  &.aberto {
    transform: translateX(-600px); // Move para esquerda
  }
}
