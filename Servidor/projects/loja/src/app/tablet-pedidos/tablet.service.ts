import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import {ServerService} from "../../services/ServerService";

@Injectable({
  providedIn: 'root'
})
export class TabletService extends ServerService {
  private token: any = '';
  private keytablet = 'mcitabletnumero'

  constructor(protected http: HttpClient) {
    super(http);
  }

  // Armazenar token após autenticação
  setToken(token: string) {
    this.token = token;
  }

  // Obter headers com token
  private getAuthHeaders() {
    if (this.token) {
      return {
        headers: new HttpHeaders({
          'Authorization': `Bearer ${this.token}`
        })
      };
    }
    return {};
  }

  associarMesa(numeroTablet: string, idMesa: number) {
    return this.http.post('/tablets/associar-mesa', {
      numeroTablet: numeroTablet,
      idMesa: idMesa,
      token: this.token
    }).toPromise().then(this.retorno).catch(this.handleError);
  }

  obterInfoTablet(numero: string) {
    return this.obtenha(`/tablets/info/${numero}`, {});
  }

  listarMesas() {
    return this.http.get('/tablets/mesas', this.getAuthHeaders())
      .toPromise().then(this.retorno).catch(this.handleError);
  }

  autenticarGarcom(login: string, senha: string): Promise<any>{
    return new Promise(resolve => {
      this.http.post('/auth/tablet/login', {user: {email: login, password: senha}}).toPromise().then((resposta: any) => {
        if(resposta.sucesso) {
          // Armazenar token automaticamente após login bem-sucedido
          if(resposta.data.token)
            this.setToken(resposta.data.token);

          return resolve(resposta.data)
        }

        let retorno: any = { erro: resposta.erro || 'Falha ao autenticar usuario'}
        resolve(retorno);
      }).catch((err: any) => {
        resolve({ erro: err.message || err.messageText || err});
      })
    });
  }

  logout() {
    return this.http.post('/auth/tablet/logout', { token: this.token })
      .toPromise().then(this.retorno).catch(this.handleError);
  }

  getNumeroTablet() {
    // Em produção, isso seria obtido do hardware do tablet ou configuração
    // Por enquanto, usamos um identificador baseado no localStorage ou aleatório
    let numero = localStorage.getItem(this.keytablet);
    if (!numero) {
      numero = 'TAB-1GCDH3'
      localStorage.setItem(this.keytablet, numero);
    }
    return numero;
  }


}
