.tablet-configuracao-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  min-height: 100vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.configuracao-header {
  text-align: center;
  margin-bottom: 40px;
  color: white;

  .lock-icon {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.9;
  }

  h2 {
    margin: 0;
    font-size: 32px;
    font-weight: 300;
    letter-spacing: 1px;
    color: #fff;
  }
}

.configuracao-content {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  min-width: 400px;
  max-width: 650px;
  width: 100%;
  max-height: 85vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;

  &.mesa-selection {
    max-width: 900px;
    min-width: 600px;
    max-height: 90vh;
  }
}

// Estilo para login de garçom
.login-section {
  text-align: center;

  h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 24px;
    font-weight: 400;
  }

  .login-subtitle {
    color: #666;
    margin-bottom: 30px;
    font-size: 16px;
    line-height: 1.4;
  }

  .form-group {
    margin-bottom: 25px;
    text-align: left;

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #555;
      font-size: 14px;
    }

    .form-control {
      width: 100%;
      border: 2px solid #e9ecef;
      border-radius: 10px;
      font-size: 16px;
      transition: border-color 0.3s ease, box-shadow 0.3s ease;

      &:focus {
        border-color: #667eea;
        outline: none;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      &:disabled {
        background-color: #f8f9fa;
        opacity: 0.7;
      }
    }

    .invalid-feedback {
      display: block;
      margin-top: 5px;
      font-size: 12px;
      color: #dc3545;

      p {
        margin: 0;
      }
    }
  }

  .action-buttons {
    margin-top: 30px;

    .btn-login {
      width: 100%;
      padding: 15px;
      font-size: 18px;
      font-weight: 500;
      background: #667eea;
      color: white;
      border: none;
      border-radius: 10px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;

      &:hover:not(:disabled) {
        background: #5a67d8;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
      }

      &:disabled {
        background: #6c757d;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }
    }
  }
}

// Estilo para configuração da mesa
.mesa-config-section {
  text-align: center;

  .settings-icon {
    font-size: 48px;
    color: #667eea;
    margin-bottom: 20px;
  }

  h3 {
    color: #333;
    margin-bottom: 30px;
    font-size: 24px;
    font-weight: 400;
  }

  .garcom-info {
    background: #f0f8ff;
    border: 1px solid #b3d9ff;
    border-radius: 10px;
    padding: 12px;
    margin-bottom: 20px;

    p {
      margin: 0;
      color: #0066cc;
      font-size: 14px;
    }
  }

  .loading-mesas {
    text-align: center;
    padding: 40px 20px;
    color: #666;

    i {
      font-size: 32px;
      margin-bottom: 15px;
      color: #667eea;
    }

    p {
      margin: 0;
      font-size: 16px;
    }
  }

  .mesas-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
    max-height: 60vh;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 10px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    background: #f8f9fa;

    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: #667eea #f1f1f1;

    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #667eea;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #5a67d8;
    }

    .mesa-card {
      background: white;
      border: 2px solid #e9ecef;
      border-radius: 12px;
      padding: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      min-height: 160px;

      &.disponivel {
        border-color: #28a745;
        background: #f8fff9;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 25px rgba(40, 167, 69, 0.2);
          border-color: #20c997;
        }

        .mesa-icon i {
          color: #28a745;
        }
      }

      &.ocupada {
        border-color: #dc3545;
        background: #fff5f5;
        cursor: not-allowed;
        opacity: 0.8;

        .mesa-icon i {
          color: #dc3545;
        }

        &:hover {
          transform: none;
          box-shadow: none;
        }
      }

      .mesa-icon {
        font-size: 32px;
        margin-bottom: 15px;

        i {
          transition: color 0.3s ease;
        }
      }

      .mesa-info {
        text-align: center;
        margin-bottom: 15px;
        flex: 1;

        h4 {
          margin: 0 0 5px 0;
          font-size: 18px;
          font-weight: 600;
          color: #333;
        }

        .mesa-codigo {
          margin: 0;
          font-size: 12px;
          color: #666;
        }
      }

      .mesa-status {
        width: 100%;

        .status-indicator {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          font-size: 14px;
          font-weight: 500;

          &.text-success {
            color: #28a745;
          }

          &.text-danger {
            color: #dc3545;
          }

          .status-text {
            color: inherit;
          }
        }
      }

      .selecionar-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 30px;
        height: 30px;
        background: #28a745;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 14px;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &.disponivel:hover .selecionar-btn {
        opacity: 1;
      }

      // Estilo para mesa atual - aplicado quando é a mesa configurada no tablet
      &.mesa-atual {
        border-color: #2196f3 !important;
        background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%) !important;

        .mesa-icon i {
          color: #2196f3 !important;
        }

        .mesa-status .status-indicator {
          color: #2196f3 !important;

          &.text-primary {
            color: #2196f3 !important;
          }
        }

        &:hover {
          transform: none;
          box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2);
          cursor: default;
        }

        .selecionar-btn {
          background: #2196f3;
          opacity: 1;
        }
      }

      // Classe de debug para verificar se está sendo aplicada
      &.debug-mesa-atual {
        position: relative;

        &::before {
          content: 'MESA ATUAL';
          position: absolute;
          top: -10px;
          left: 50%;
          transform: translateX(-50%);
          background: red;
          color: white;
          padding: 2px 8px;
          font-size: 10px;
          font-weight: bold;
          border-radius: 3px;
          z-index: 1000;
        }
      }
    }
  }

  .no-mesas {
    text-align: center;
    padding: 40px 20px;
    color: #999;

    i {
      font-size: 48px;
      margin-bottom: 15px;
    }

    p {
      margin: 0;
      font-size: 16px;
    }
  }

  .legenda {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 30px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;

    .legenda-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 500;
      color: #555;

      .cor-indicador {
        width: 16px;
        height: 16px;
        border-radius: 50%;

        &.verde {
          background: #28a745;
        }

        &.vermelho {
          background: #dc3545;
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;

    .btn {
      padding: 12px 30px;
      border: none;
      border-radius: 10px;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;

      &.btn-primary {
        background: #667eea;
        color: white;

        &:hover:not(:disabled) {
          background: #5a67d8;
          transform: translateY(-2px);
        }

        &:disabled {
          background: #6c757d;
          cursor: not-allowed;
          transform: none;
        }
      }

      &.btn-secondary {
        background: #6c757d;
        color: white;

        &:hover {
          background: #5a6268;
          transform: translateY(-2px);
        }
      }
    }
  }
}

// Estilo para confirmação da mesa
.confirmacao-section {
  text-align: center;

  .confirmacao-icon {
    font-size: 64px;
    color: #667eea;
    margin-bottom: 20px;
  }

  h3 {
    color: #333;
    margin-bottom: 30px;
    font-size: 24px;
    font-weight: 400;
  }

  .mesa-confirmacao-info {
    margin-bottom: 40px;

    .mesa-card-preview {
      background: #f8fff9;
      border: 2px solid #28a745;
      border-radius: 15px;
      padding: 25px;
      margin-bottom: 25px;
      display: flex;
      align-items: center;
      gap: 20px;

      .mesa-icon {
        font-size: 40px;
        color: #28a745;
        flex-shrink: 0;
      }

      .mesa-details {
        flex: 1;
        text-align: left;

        h4 {
          margin: 0 0 8px 0;
          font-size: 20px;
          font-weight: 600;
          color: #333;
        }

        p {
          margin: 0 0 12px 0;
          font-size: 14px;
          color: #666;
        }

        .status-badge {
          display: inline-flex;
          align-items: center;
          gap: 6px;
          padding: 6px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 500;

          &.disponivel {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
          }
        }
      }
    }

    .confirmacao-texto {
      font-size: 18px;
      color: #555;
      margin-bottom: 20px;
      line-height: 1.5;

      strong {
        color: #333;
        font-weight: 600;
      }
    }

    .info-preview {
      display: flex;
      gap: 15px;
      justify-content: center;
      flex-wrap: wrap;

      .tablet-info-preview,
      .garcom-info-preview {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 12px;
        display: inline-block;

        small {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 14px;
          color: #666;

          i {
            color: #667eea;
          }
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;

    .btn {
      padding: 15px 25px;
      border: none;
      border-radius: 10px;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 10px;
      min-width: 140px;
      justify-content: center;

      &.btn-primary {
        background: #28a745;
        color: white;

        &.btn-confirmar {
          background: #667eea;

          &:hover:not(:disabled) {
            background: #5a67d8;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
          }
        }

        &:disabled {
          background: #6c757d;
          cursor: not-allowed;
          transform: none;
        }
      }

      &.btn-secondary {
        background: #6c757d;
        color: white;

        &:hover:not(:disabled) {
          background: #5a6268;
          transform: translateY(-2px);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }
      }
    }
  }
}

// Estilo para tela de sucesso
.success-section {
  text-align: center;

  .success-icon {
    font-size: 64px;
    color: #28a745;
    margin-bottom: 20px;
  }

  h3 {
    color: #28a745;
    margin-bottom: 15px;
    font-size: 24px;
    font-weight: 500;
  }

  p {
    color: #666;
    margin-bottom: 30px;
    font-size: 16px;
    line-height: 1.5;
  }

  .action-buttons {
    .btn-primary {
      background: #28a745;
      color: white;
      padding: 15px 40px;
      border: none;
      border-radius: 10px;
      font-size: 18px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: #218838;
        transform: translateY(-2px);
      }
    }
  }
}

// Alertas
.alert {
  margin: 20px 0;
  padding: 15px 20px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  font-weight: 500;

  &.alert-danger {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
  }

  &.alert-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
  }

  i {
    font-size: 16px;
  }
}

// Informações do tablet
.tablet-info {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px 15px;
  border-radius: 20px;
  backdrop-filter: blur(10px);

  small {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    color: #666;

    i {
      font-size: 14px;
    }
  }
}

// Validação de formulário
.was-validated .form-control:invalid {
  border-color: #dc3545;
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #dc3545;
}

// Responsividade
@media (max-width: 768px) {
  .tablet-configuracao-container {
    padding: 15px;
    justify-content: flex-start;
  }

  .configuracao-content {
    min-width: 300px;
    padding: 30px 20px;
    max-height: 95vh;

    &.mesa-selection {
      min-width: 320px;
      max-width: 100%;
      max-height: 95vh;
    }
  }

  .configuracao-header h2 {
    font-size: 28px;
  }

  .login-section h3,
  .mesa-config-section h3 {
    font-size: 20px;
  }

  .mesas-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
    max-height: 70vh;
    padding: 8px;

    .mesa-card {
      min-height: 140px;
      padding: 15px;

      .mesa-icon {
        font-size: 28px;
        margin-bottom: 10px;
      }

      .mesa-info h4 {
        font-size: 16px;
      }
    }
  }

  .legenda {
    flex-direction: column;
    gap: 15px;
  }

  .confirmacao-section {
    .mesa-card-preview {
      flex-direction: column;
      text-align: center;
      gap: 15px;

      .mesa-details {
        text-align: center;
      }
    }

    .action-buttons {
      flex-direction: column;
      gap: 15px;

      .btn {
        width: 100%;
      }
    }

    .info-preview {
      flex-direction: column;
      align-items: center;
    }
  }
}

// Adicionando breakpoint específico para tablets
@media (min-width: 769px) and (max-width: 1024px) {
  .configuracao-content.mesa-selection {
    max-width: 95vw;
    max-height: 85vh;
  }

  .mesas-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    max-height: 55vh;
    gap: 12px;
  }
}

// Para tablets em orientação landscape
@media (orientation: landscape) and (max-height: 768px) {
  .tablet-configuracao-container {
    justify-content: flex-start;
    padding: 10px;
  }

  .configuracao-content.mesa-selection {
    max-height: 95vh;
    padding: 20px;
  }

  .mesas-grid {
    max-height: 65vh;
  }

  .configuracao-header {
    margin-bottom: 20px;

    .lock-icon {
      font-size: 36px;
      margin-bottom: 10px;
    }

    h2 {
      font-size: 24px;
    }
  }
}

// Mesa atual configurada
.mesa-atual-info {
  margin-bottom: 30px;

  .alert {
    border: none;
    border-radius: 12px;
    padding: 20px;
    background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
    border-left: 4px solid #2196f3;
  }

  .mesa-atual-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    i {
      font-size: 24px;
      color: #2196f3;
      margin-right: 12px;
    }

    h4 {
      margin: 0;
      color: #1976d2;
      font-weight: 600;
    }
  }

  .mesa-atual-detalhes {
    margin-bottom: 20px;

    p {
      margin: 8px 0;
      font-size: 16px;

      &:first-child {
        font-size: 20px;
        font-weight: 600;
        color: #333;
      }
    }

    .mesa-codigo {
      color: #666;
      font-size: 14px;
    }

    .texto-info {
      color: #555;
      font-style: italic;
      font-size: 14px;
    }
  }

  .mesa-atual-acoes {
    .btn-trocar-mesa {
      background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
      border: none;
      border-radius: 8px;
      padding: 12px 24px;
      color: white;
      font-weight: 600;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
      }

      i {
        margin-right: 8px;
      }
    }
  }
}

// Tablet sem mesa configurada
.sem-mesa-info {
  margin-bottom: 30px;

  .alert {
    border: none;
    border-radius: 12px;
    padding: 20px;
    background: linear-gradient(135deg, #fff3e0 0%, #f8f9fa 100%);
    border-left: 4px solid #ff9800;
  }

  .sem-mesa-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    i {
      font-size: 24px;
      color: #ff9800;
      margin-right: 12px;
    }

    h4 {
      margin: 0;
      color: #f57c00;
      font-weight: 600;
    }
  }

  p {
    margin: 0;
    color: #666;
    font-size: 16px;
  }
}

// Container das mesas
.mesas-container {
  h4 {
    color: #333;
    font-weight: 600;
    margin-bottom: 20px;
    text-align: center;
  }
}

// Atualização dos cards de mesa
.mesas-grid {
  .mesa-card {
    // Removendo a definição duplicada que estava causando conflito
    // &.mesa-atual {
    //   border-color: #2196f3;
    //   background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
    //
    //   .mesa-icon i {
    //     color: #2196f3;
    //   }
    //
    //   &:hover {
    //     transform: none;
    //     box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2);
    //     cursor: default;
    //   }
    // }
  }
}

// Ações de troca
.acoes-troca {
  text-align: center;
  margin-top: 20px;

  .btn {
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 600;
  }
}

// Legenda atualizada
.legenda {
  .cor-indicador {
    &.azul {
      background-color: #2196f3;
    }
  }
}

// Seção de confirmação atualizada
.confirmacao-section {
  .confirmacao-config-section {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
  }

  .confirm-icon {
    font-size: 48px;
    color: #ff9800;
    margin-bottom: 20px;
  }

  h3 {
    color: #333;
    margin-bottom: 30px;
    font-weight: 600;
  }

  .confirmacao-detalhes {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .tablet-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 25px;

    p {
      margin: 5px 0;
      color: #555;
      font-weight: 500;
    }
  }

  // Visual de troca de mesa
  .troca-visual {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;

    .mesa-origem,
    .mesa-destino {
      text-align: center;

      h4 {
        font-size: 14px;
        color: #666;
        margin-bottom: 10px;
        text-transform: uppercase;
        font-weight: 600;
      }
    }

    .seta-troca {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #ff9800;
      font-weight: 600;

      i {
        font-size: 24px;
        margin-bottom: 5px;
      }

      span {
        font-size: 12px;
        font-weight: 700;
      }
    }
  }

  // Visual de associação (primeira vez)
  .associacao-visual {
    text-align: center;

    h4 {
      font-size: 16px;
      color: #333;
      margin-bottom: 15px;
      font-weight: 600;
    }
  }

  // Cards pequenos de mesa
  .mesa-card-small {
    display: inline-flex;
    align-items: center;
    background: white;
    border: 2px solid;
    border-radius: 10px;
    padding: 15px 20px;
    min-width: 160px;
    font-weight: 600;

    &.atual {
      border-color: #dc3545;
      background: #fff5f5;
      color: #dc3545;
    }

    &.nova {
      border-color: #28a745;
      background: #f8fff9;
      color: #28a745;
    }

    i {
      font-size: 20px;
      margin-right: 10px;
    }

    span {
      font-size: 16px;
    }

    small {
      display: block;
      font-size: 12px;
      opacity: 0.8;
      margin-top: 2px;
    }
  }

  // Botões de confirmação
  .confirmacao-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;

    .btn {
      min-width: 140px;
      border-radius: 8px;
      padding: 12px 24px;
      font-weight: 600;
      transition: all 0.3s ease;

      &.btn-voltar {
        background: #6c757d;
        border-color: #6c757d;

        &:hover {
          background: #5a6268;
          transform: translateY(-1px);
        }
      }

      &.btn-confirmar {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;

        &:hover:not(:disabled) {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }
      }
    }
  }
}

// Responsividade para telas menores
@media (max-width: 768px) {
  .troca-visual {
    flex-direction: column;
    gap: 15px;

    .seta-troca {
      transform: rotate(90deg);
    }
  }

  .confirmacao-buttons {
    flex-direction: column;

    .btn {
      width: 100%;
    }
  }

  .mesa-card-small {
    min-width: 140px;
    padding: 12px 16px;
  }
}
