<form [ngClass]="{'needs-validation': !frmLogin.submitted, 'was-validated': frmLogin.submitted}"
      novalidate #frmLogin="ngForm" (ngSubmit)="autenticarGarcom()">

  <div class="login-section">
    <h3>Acesso restrito</h3>
    <p class="login-subtitle">Digite suas credenciais para configurar a mesa</p>

    <div class="form-group">
      <label for="login">Login:</label>
      <kendo-textbox
        id="login"
        name="login"
        [(ngModel)]="login"
        placeholder="Digite seu login"
        required
        #loginInput="ngModel"
        [disabled]="carregando"
        class="form-control">
      </kendo-textbox>
      <div class="invalid-feedback" *ngIf="frmLogin.submitted && loginInput.errors?.required">
        <p>Login é obrigatório</p>
      </div>
    </div>

    <div class="form-group">
      <label for="senha">Senha:</label>
      <input
        kendoTextBox
        id="senha"
        name="senha"
        type="password"
        [(ngModel)]="senha"
        placeholder="Digite sua senha"
        required
        #senhaInput="ngModel"
        [disabled]="carregando"
        class="form-control" />

      <div class="invalid-feedback" *ngIf="frmLogin.submitted && senhaInput.errors?.required">
        <p>Senha é obrigatória</p>
      </div>
    </div>


    <!-- Mensagens de erro e sucesso -->
    <div class="alert alert-danger" *ngIf="mensagemErro">
      <i class="fas fa-exclamation-triangle"></i>
      {{mensagemErro}}
    </div>

    <div class="action-buttons">
      <button type="submit"
              class="btn btn-primary btn-login"
              [disabled]="!frmLogin.valid || carregando">
        <i class="fas fa-sign-in-alt" *ngIf="!carregando"></i>
        <i class="fas fa-spinner fa-spin" *ngIf="carregando"></i>
        Entrar
      </button>
    </div>
  </div>
</form>
