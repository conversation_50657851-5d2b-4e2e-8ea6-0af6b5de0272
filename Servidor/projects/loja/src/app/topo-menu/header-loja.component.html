<div class="remova_padding topo {{tema}}">
  <nav class="navbar navbar-expand-lg navbar-light bg-light shadow" [ngClass]="{'fixed-top': isMobile}">
    <div class="row" style="flex-grow: 1;">
      <div class="pl-2 span3 text-left" style="margin-top: 0px;    margin-left: 5px;">
        <button class="btn btn-outline-blue btn-rounded" (click)="voltar()" style="margin-right: 5px;">
          <i class="fa fa-arrow-left ct-point" ></i>
        </button>
      </div>
      <div class="col pl-1" style="position: relative">
        <h4  *ngIf="titulo" >  {{titulo}} </h4>

        <ng-container *ngIf="busca">
          <input kendoTextBox type="text" name="query"  [(ngModel)]="query" class="form-control"
                 (keyup)="onValueChange($event)" placeholder="Busque produto" appAutoFocus [autoFocus]="focarBusca"/>

          <div style="position: absolute;right: 20px;top: 2px;padding: 8px 12px;" (click)="limpe()" *ngIf="query" >
            <i class="fas fa-times font-18 text-muted"   ></i>
          </div>

        </ng-container>

      </div>
    </div>
  </nav>
</div>


<div style="height: 54px;width: 100%" *ngIf="isMobile"></div>
