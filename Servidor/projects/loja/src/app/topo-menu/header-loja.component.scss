.remova_padding {
  margin-left: -12px;
  margin-right: -12px;
}

.bg-light {
  background-color: #f7f8f8;
}

@media (min-width: 1025px) {

  .remova_padding {
    margin-left: -20px;
    margin-right: -20px;
  }

}

.topo-acoes-desktop{
  position: relative;
  height: 45px;
  width: 100%;
  top: 10px;
}

.chinainbox{
  .bg-light{
    background-color: #e52f26 !important;
  }

  h4{
    color: #fff !important;
  }

  .btn-outline-blue, .fa-times{
    color: #fff  !important;
    border-color: #fff  !important;
  }

  .btn-outline-blue:hover {
    background-color: #fff !important;
    i{
      color: red !important;
    }
  }

  .text-blue{
      color: #e3ae6a !important;
  }
}

.carnaval{
  .bg-light{
    background-color:  #D825A9  !important;
  }

  .btn-outline-blue, .fa-times{
    color: #fff000  !important;
    border-color: #fff000  !important;
  }

  h4{
    color: #fff000 !important;
  }

  .btn-outline-blue:hover {
    background-color: #fff000 !important;
    i{
      color:  #D825A9 !important;
    }
  }

  .text-blue{
    color: #fff000 !important;
  }
}
