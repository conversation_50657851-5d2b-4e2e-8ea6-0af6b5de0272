<app-header-loja #header [titulo]="'Identifique-se'"  [retorno]="urlDestino"   *ngIf="!this.abriuComoModal"  ></app-header-loja>
<app-header-tela [titulo]="'Identifique-se'" [exibirFechar]="true" (fechou)="fecheTela()" *ngIf="this.abriuComoModal"></app-header-tela>


<div class="card caixa_login  mt-3 {{empresa.tema}}" >
  <div class="">
    <div class="text-center w-75 mx-auto" *ngIf="empresa.logo">
      <img class="imagem_empresa" src="/images/empresa/{{empresa.logo}}"/>
    </div>

    <form [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"
          novalidate #frm="ngForm" (ngSubmit)="onLogin()"  >

      <div class="form-group mb-2 col"  >
        <label >Telefone com DDD</label>
        <input kendoTextBox    name="telefone"  #telefone="ngModel" mask="(00)0-0000-0000"
               appAutoFocus autocomplete="off" [required]=" true"
               type="text" class="form-control" autocomplete="off"
               [(ngModel)]="login.telefone"  placeholder="Informe seu telefone com DDD"   />

        <div class="invalid-feedback">
          <p *ngIf="telefone.errors?.required" >Informe um telefone com DDD</p>
        </div>

      </div>

      <div class="form-group mb-2 col"  >
        <label for="senha">Senha</label>
        <label   class="col-form-label float-right cpointer text-blue" (click)="exibirSenhaTela()">
          <i class="fa fa-eye fa-lg   " *ngIf="!exibirSenha"></i>
          <i class="fa fa-eye-slash fa-lg   " *ngIf="exibirSenha"></i>
        </label>
        <span *ngIf="!exibirSenha">
            <input class="form-control" type="password"  id="senha" name="senha"  #senha="ngModel" #senhaInput
                   [(ngModel)]="login.senha" placeholder="Informe sua senha"   required   >
            <div  class="invalid-feedback">
              <p  *ngIf="senha.errors?.required">Informe uma senha</p>
            </div>
          </span>

        <span *ngIf="exibirSenha">
            <input class="form-control" type="text"  id="senhaTexto" name="senhaTexto"  #senhaTexto="ngModel" #senhaInput
                   [(ngModel)]="login.senha" placeholder="Informe sua senha" required>
            <div  class="invalid-feedback">
              <p  *ngIf="senhaTexto.errors?.required">Senha é obrigatório</p>
            </div>
         </span>

      </div>

      <div class="form-group mb-0 text-center col">
        <button class="btn btn-blue btn-block" type="submit" [disabled]="logando" > Acessar </button>
        <div class="text-danger mt-2" role="alert" *ngIf="erro"  >
         <b> {{erro}}</b>
        </div>

      </div>

      <div class="text-center">
        <div class="col-12 text-center mt-3">
          <!--<p> <a href="pages-recoverpw.html" class="text-white-50 ml-1">Forgot your password?</a></p>-->
          <a href="#" class="text-dark mb-1" (click)="vaParaRecuperarSenha()" style="cursor: pointer;">Esqueceu  sua <span class=" ml-1 text-blue">
            <b>senha?</b></span>  </a>
          <div style="height: 10px;"></div>
          <a href="#" class="text-dark mb-1" (click)="vaParaCadastro()">É novo? Faça seu <span   class=" ml-1 text-blue"  >
            <b>cadastro</b></span></a>
        </div> <!-- end col -->
      </div>

    </form>
  </div>
</div>
