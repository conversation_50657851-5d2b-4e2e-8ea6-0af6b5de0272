import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {ActivatedRoute, Router} from "@angular/router";
import {NgForm} from "@angular/forms";
import {ConstantsService} from "../../services/ConstantsService";
import {ITela} from "../../objeto/ITela";
import {AutorizacaoLojaService} from "../../services/autorizacao-loja.service";
import {DominiosService} from "../../services/dominios.service";
import {HeaderLojaComponent} from "../topo-menu/header-loja.component";
import {CarrinhoService} from "../../services/carrinho.service";
import {MyDetectorDevice} from "../shared/MyDetectorDevice";
import {PopupUtils} from "../../objeto/PopupUtils";
import {DialogRef} from "@progress/kendo-angular-dialog";
import {DialogService} from "@progress/kendo-angular-dialog";
import {LojaRecuperarComponent} from "../loja-recuperar/loja-recuperar.component";
import {Location} from "@angular/common";
import {CadContatoLojaComponent} from "../cad-contato/cad-contato-loja.component";

@Component({
  selector: 'app-painel-login',
  templateUrl: './painel-login.component.html',
  styleUrls: ['./painel-login.component.scss']
})
export class PainelLoginComponent implements OnInit, ITela {
  constructor(private autorizacao: AutorizacaoLojaService, private router: Router, private detectorDevice: MyDetectorDevice,
              private dominiosService: DominiosService,  private carrinhoService: CarrinhoService,
              private activatedRoute: ActivatedRoute,      private _location: Location,
              private dialogService: DialogService,
              private constantsService: ConstantsService, private route: ActivatedRoute) {
  }
  @ViewChild('frm')  frm: NgForm;
  @ViewChild('header', {static: false })  header: HeaderLojaComponent;
  @ViewChild('senhaInput', {static: false})  senhaInput: ElementRef;
  public login: any = {};
  empresa: any = {};
  logando: any;
  erro: any;
  exibirSenha: boolean;
  urlDestino: string;
  window: any
  abriuComoModal = false;
  static abraComoPopup(router,  location, activatedRoute, dialogService: DialogService,
                       telefone: string = null, onClose: any = null){
    let dimensao = PopupUtils.calculeAlturaLargura(false)

    const windowRef: DialogRef = dialogService.open({
      title: null,
      content: PainelLoginComponent,
      minWidth: 200,
      width: dimensao.largura,
      height: 500
    });

    let telaLogin: PainelLoginComponent = windowRef.content.instance;

    telaLogin.setModal(windowRef, telefone);

    PopupUtils.abraJanela(router,  location, activatedRoute, windowRef, {}, onClose);

  }

  ngOnInit(): void {
    let state = window.history.state;
    if(state && state.email)
      this.login.email = state.email;

    if( this.route.snapshot.queryParams['tel'] || state.telefone)
      this.login.telefone =  this.route.snapshot.queryParams['tel'] || state.telefone

    if(state && state.erro)
      this.erro = state.erro;

    this.constantsService.empresa$.subscribe( empresa => this.empresa = empresa || {});
    this.urlDestino =    this.dominiosService.obtenhaRaizCardapio();

    if( this.route.snapshot.queryParams['t']){
      this.urlDestino = this.route.snapshot.queryParams['t']
    }    else  if (this.carrinhoService.temPedidoNoCarrinho())
      this.urlDestino =   this.dominiosService.obtenhaUrlCompleta( this.detectorDevice.isMobile()  ? 'carrinho' : 'pedido');

    setTimeout( () => {
      if(this.login.telefone)
        this.senhaInput.nativeElement.focus();
    }, 100)

    this.autorizacao.clearCookies();
  }

  onLogin() {
    delete this.erro;

    if(!this.frm.valid) return;
    this.logando = true;
    this.autorizacao.login(this.login.telefone, this.login.senha).then((erro: any) => {
      this.logando = false;
      if(!erro) {
       if(!this.abriuComoModal){
         this.router.navigate([this.urlDestino], { state: { fezLogin: 1}})
       } else {
         this.window.close({login: true});
       }
      } else {
        this.erro = erro;
      }
    });
  }

  exibirSenhaTela() {
    this.exibirSenha = ! this.exibirSenha ;
  }

  deveExibirTopo() {
    return false;
  }

  deveExibirMenu(){
    return true;
  }

  vaParaRecuperarSenha(){

    if(!this.abriuComoModal){
      let link = this.dominiosService.obtenhaUrlCompleta('recuperar');

      link = link + '?t=' + this.urlDestino;

      this.router.navigateByUrl(link).then( () => {});
    } else {

      LojaRecuperarComponent.abraComoPopup(this.router, this._location, this.activatedRoute, this.dialogService);

    }

    return false;
  }

  vaParaCadastro() {
    if(!this.abriuComoModal){
      let link = this.dominiosService.obtenhaUrlCompleta('cadastro');

      link = link + '?t=' + this.urlDestino;

      this.router.navigateByUrl(link).then( () => {});
    } else {
      this.fecheTela()
      CadContatoLojaComponent.abraComoPopup(this.router, this._location, this.activatedRoute, this.dialogService);
    }

    return false;
  }

  deveTerBordas() {
    return true;
  }

  deveExibirBannerTema() {
    return false;
  }

  setModal(window, telefone: string = null){
    this.window = window;
    this.abriuComoModal = true;
    if(telefone) this.login.telefone = telefone;
  }

  fecheTela() {
    this.window.close();
  }
}

