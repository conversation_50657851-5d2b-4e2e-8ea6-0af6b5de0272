.remova_padding {
  margin-left: -12px;
  margin-right: -12px;
}
.bg-light{
  background-color: #f7f8f8;
}

@media (min-width: 1025px) {

  .remova_padding {
    margin-left: -20px;
    margin-right: -20px;
  }
}

::ng-deep .tema-personalizado {
    &.topo {
      background-color: var(--cor-fundo, #f7f8f8) !important;

      .bg-light {
        background-color: var(--cor-fundo, #f7f8f8) !important;
      }

      h4.titulo {
        color: var(--cor-texto-primaria, inherit);
      }

      .btn-outline-blue {
        border-color: var(--cor-botao, #007bff);
        color: var(--cor-texto-botao, #007bff);

        &:hover {
          background-color: var(--cor-botao, #007bff);
          color: var(--cor-texto-botao, #fff);
        }
      }
    }
  }


::ng-deep .black_friday_2022 {
  .topo {
    .bg-light {
      background: #0e0e0e !important;
      border-bottom: solid 1px #676767;
      box-shadow: none !important;
      color: #cfcfcf;
    }

    h4.titulo {
      color: #cfcfcf;
    }

    .btn-outline-blue {
      border-color: #cfcfcf;
      color: #cfcfcf;
    }
  }
}

.chinainbox{
  .bg-light{
    background-color: #e52f26 !important;
  }
  h4{
    color: #fff !important;
  }

  .btn-outline-blue , .fa-times{
    color: #fff  !important;
    border-color: #fff  !important;
  }
  .btn-outline-blue:hover {

    background-color: #fff !important;;
    i{
      color: red !important;
    }
  }
}

.carnaval{
  .bg-light{
    background-color:  #5b1da6  !important;
  }

  .btn-outline-blue, .fa-times{
    color: #fff  !important;
    border-color: #fff  !important;
  }


  h4{
    color: #fff !important;
  }

  .btn-outline-blue:hover {
    background-color: #fff !important;
    i{
      color:  #5b1da6 !important;
    }
  }

  .text-blue{
    color: #fce101 !important;
  }
}

