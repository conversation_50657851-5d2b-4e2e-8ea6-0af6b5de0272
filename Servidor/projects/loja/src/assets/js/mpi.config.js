let  Environment = localStorage.getItem('EnvironmentMpi');
function bpmpi_config() {
  function notifiqueTela(idcampo, dados){
    try{
      console.log('notificar campo: ' + idcampo)
      var $elem = $(String(`#${idcampo}`));
      $elem.val(true);
      $elem[0].dispatchEvent(new CustomEvent('change',{'detail': dados}));
    } catch (err){
      console.log('Falha ao notificar tela: ' + idcampo)
      console.error(err)
    }
  }
  return {
    onReady:  ()=>{
     notifiqueTela('carregou3ds', {});
    },
    onSuccess: (e)=>{
      // Cartão elegível para autenticação, e portador autenticou com sucesso.
      console.log('onSuccess')
      console.log(e)
      notifiqueTela('autenticou3ds', e)
    },
    onFailure: function (e) {
      // Cartão elegível para autenticação, porém o portador finalizou com falha.
      console.log('onFailure')
      console.log(e)
      notifiqueTela("erro3ds", { erro: 'Autenticação do cartão nao concluída'});
    },
      onUnenrolled: function (e) {
      console.log('onUnenrolled') ;
      console.log(e);
      notifiqueTela("erro3ds", { erro:'Cartão não elegível para autenticação (não autenticável)'})

    },
    onDisabled: function () {
      // Loja não requer autenticação do portador (classe "bpmpi_auth" false -> autenticação desabilitada).
      console.log('onDisabled')
      console.log()
      notifiqueTela('autenticou3ds', e)
    },
    onError: function (e) {
      // Erro no processo de autenticação.
      console.log('onError')
      console.log(e)
      notifiqueTela("erro3ds", { erro:'Falha  no processo de autenticação, tente novamente.'});
    },
    onUnsupportedBrand: function (e) {
      // Bandeira não suportada para autenticação.
      let returnCode = e.ReturnCode;
      let returnMessage = e.ReturnMessage;
      console.log('onUnsupportedBrand')
      notifiqueTela("erro3ds", { erro:'Falha ao autenticar cartão, tente novamente.'});
    },
    Environment:   Environment,
    Debug: Environment === 'SDB'
  };
}
