{"version": 3, "sources": ["custom/fonts/_fonts.scss", "custom/structure/_general.scss", "custom/structure/_left-menu.scss", "app-rtl.css", "../../node_modules/bootstrap/scss/mixins/_breakpoints.scss", "custom/structure/_topbar.scss", "custom/structure/_page-head.scss", "custom/structure/_footer.scss", "custom/structure/_right-sidebar.scss", "custom/structure/_layouts.scss", "custom/components/_helper.scss", "custom/components/_social.scss", "custom/components/_widgets.scss", "custom/components/_custom-checkbox.scss", "custom/components/_custom-radio.scss", "custom/components/_ribbons.scss", "custom/components/_print.scss", "custom/components/_preloader.scss", "custom/pages/_authentication.scss", "custom/pages/_components-demo.scss", "custom/pages/_error.scss", "custom/pages/_logout.scss", "custom/pages/_faq.scss", "custom/pages/_maintenance.scss", "custom/pages/_timeline.scss", "custom/pages/_email.scss", "custom/pages/_sitemap.scss", "custom/pages/_search-results.scss", "custom/pages/_pricing.scss", "custom/pages/_gallery.scss", "custom/pages/_coming-soon.scss", "custom/pages/_profile.scss", "custom/pages/_taskboard.scss", "custom/pages/_ecommerce.scss", "vendor/_metisMenu.scss", "custom/plugins/_waves.scss", "custom/plugins/_ion-rangeslider.scss", "custom/plugins/_calendar.scss", "custom/plugins/_sparkline-chart.scss", "custom/plugins/_datatable.scss", "custom/plugins/_daterange.scss", "custom/plugins/_datepicker.scss", "custom/plugins/_form-wizard.scss", "custom/plugins/_select2.scss", "custom/plugins/_slimscroll.scss", "custom/plugins/_toaster.scss", "custom/plugins/_sweetalert.scss", "custom/plugins/_custom-select.scss", "custom/plugins/_tippy.scss", "custom/plugins/_nestable-list.scss", "custom/plugins/_hopscotch.scss", "custom/plugins/_flot.scss", "custom/plugins/_morris.scss", "custom/plugins/_chartjs.scss", "custom/plugins/_chartist.scss", "custom/plugins/_c3.scss", "custom/plugins/_ricksaw.scss", "custom/plugins/_responsive-table.scss", "custom/plugins/_footables.scss", "custom/plugins/_bootstrap-tables.scss", "custom/plugins/_tablesaw.scss", "custom/plugins/_jsgrid.scss", "custom/plugins/_multiple-select.scss", "custom/plugins/_autocomplete.scss", "custom/plugins/_bootstrap-select.scss", "custom/plugins/_bootstrap-touchspin.scss", "custom/plugins/_parsley.scss", "custom/plugins/_flatpickr.scss", "custom/plugins/_clockpicker.scss", "custom/plugins/_summernote.scss", "custom/plugins/_quilljs-editor.scss", "custom/plugins/_dropzone.scss", "custom/plugins/_dropify.scss", "custom/plugins/_x-editable.scss", "custom/plugins/_cropper.scss", "custom/plugins/_google-maps.scss", "custom/plugins/_vector-maps.scss", "custom/plugins/_mapeal-maps.scss", "custom/rtl/_general-rtl.scss", "custom/rtl/_bootstrap-rtl.scss", "custom/rtl/_spacing-rtl.scss", "custom/rtl/_float-rtl.scss", "custom/rtl/_text-rtl.scss", "../../node_modules/bootstrap/scss/mixins/_text-truncate.scss", "../../node_modules/bootstrap/scss/mixins/_text-emphasis.scss", "../../node_modules/bootstrap/scss/mixins/_hover.scss", "../../node_modules/bootstrap/scss/mixins/_text-hide.scss", "custom/rtl/_structure-rtl.scss", "custom/rtl/_plugins-rtl.scss", "custom/rtl/_components-rtl.scss", "custom/rtl/_pages-rtl.scss"], "names": [], "mappings": "AAIA,iFAOA,WACI,YAAA,0BACA,IAAA,oCACA,IAAA,2BAAA,CAAA,qCAAA,eACA,YAAA,IAGJ,WACI,YAAA,0BACA,IAAA,sCACA,IAAA,6BAAA,CAAA,uCAAA,eACA,YAAA,IAEJ,WACI,YAAA,0BACA,IAAA,qCACA,IAAA,4BAAA,CAAA,sCAAA,eACA,YAAA,IAEJ,WACI,YAAA,0BACA,IAAA,uCACA,IAAA,8BAAA,CAAA,wCAAA,eACA,YAAA,IAEJ,WACI,YAAA,0BACA,IAAA,mCACA,IAAA,0BAAA,CAAA,oCAAA,eACA,YAAA,ICpCJ,KACE,SAAA,SACA,WAAA,KAGF,KACE,eAAA,KACA,WAAA,OCLF,WACI,QAAA,EADJ,cAIQ,WAAA,KAJR,cAOQ,QAAA,EAPR,iBASY,MAAA,KAKZ,uBCqCA,sBDjCY,QAAA,IAAA,KACA,MAAA,QACA,QAAA,MACA,SAAA,SACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IACA,UAAA,QATZ,6BAAA,6BC8CE,4BACA,4BDnCc,MAAA,QAMhB,8BCiCA,6BD7BY,MAAA,QAMZ,SACI,OAAA,KACA,SAAA,OACA,MAAA,KAIJ,cACI,YAAA,MACA,SAAA,OACA,QAAA,EAAA,KAAA,IAAA,KACA,WAAA,KACA,WAAA,KAIJ,gBACI,MAAA,MACA,WAAA,KACA,OAAA,EACA,QAAA,KAAA,EACA,SAAA,MACA,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SACA,IAAA,KACA,mBAAA,EAAA,IAAA,IAAA,KAAA,eAAA,CAAA,EAAA,IAAA,IAAA,EAAA,gBAAA,WAAA,EAAA,IAAA,IAAA,KAAA,eAAA,CAAA,EAAA,IAAA,IAAA,EAAA,gBAIJ,sBAIgB,MAAA,QACA,QAAA,MACA,QAAA,KAAA,KACA,SAAA,SACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IACA,YAAA,OAAA,CAAA,WACA,UAAA,QAVhB,6BAAA,4BAAA,4BAeoB,MAAA,QACA,gBAAA,KAhBpB,2BAmBoB,eAAA,OAnBpB,wBAuBoB,QAAA,aACA,YAAA,UACA,OAAA,EAAA,KAAA,EAAA,IACA,WAAA,OACA,eAAA,OACA,MAAA,KA5BpB,kCA+BoB,MAAA,MA/BpB,oCAiCwB,aAAA,EAjCxB,6BAsCgB,MAAA,QAtChB,uBA0CgB,aAAA,KA1ChB,0BA6CoB,aAAA,KA7CpB,0BAmDQ,mBAAA,kBAAA,KAAA,WAAA,kBAAA,KAAA,WAAA,UAAA,KAAA,WAAA,UAAA,IAAA,CAAA,kBAAA,KACA,SAAA,SACA,MAAA,KACA,QAAA,aACA,YAAA,wBACA,eAAA,KACA,YAAA,OACA,UAAA,OACA,kBAAA,eAAA,UAAA,eA3DR,iCA6DY,QAAA,QA7DZ,qBAiEQ,WAAA,IAjER,0CAuEgB,kBAAA,cAAA,UAAA,cAvEhB,0BA6EQ,QAAA,KAAA,KACA,eAAA,MACA,eAAA,KACA,OAAA,QACA,UAAA,SACA,eAAA,UACA,MAAA,QACA,YAAA,IAKR,oBAGQ,MAAA,eAHR,6BAQY,QAAA,KARZ,6BAWY,QAAA,MAXZ,0BAgBQ,SAAA,SACA,YAAA,EACA,MAAA,eACA,QAAA,EAnBR,yCCgBE,2CDOU,SAAA,kBACA,OAAA,eAxBZ,yCA2BY,WAAA,OCHV,+CACA,qDAFA,+CADA,oDDtBF,oDAqCgB,QAAA,eArChB,sDAwCgB,OAAA,kBAxChB,8CA6CoB,SAAA,SACA,YAAA,OA9CpB,gDAiDwB,QAAA,KAAA,KACA,WAAA,KAlDxB,uDAAA,sDAAA,sDAuD4B,MAAA,QAvD5B,kDA0D4B,UAAA,SACA,aAAA,KA3D5B,qDA+D4B,QAAA,KACA,aAAA,KAhE5B,sDAsE4B,SAAA,SACA,MAAA,mBACA,MAAA,QACA,iBAAA,KACA,mBAAA,KAAA,WAAA,KA1E5B,2DA6EgC,QAAA,OA7EhC,oEAAA,kEAmFgC,QAAA,KAnFhC,uDAwF4B,QAAA,MACA,KAAA,KACA,SAAA,SACA,MAAA,MACA,OAAA,eACA,mBAAA,IAAA,IAAA,KAAA,EAAA,qBAAA,WAAA,IAAA,IAAA,KAAA,EAAA,qBA7F5B,0DAgGgC,mBAAA,IAAA,IAAA,KAAA,EAAA,qBAAA,WAAA,IAAA,IAAA,KAAA,EAAA,qBAhGhC,yDAmGgC,mBAAA,KAAA,WAAA,KACA,QAAA,IAAA,KACA,SAAA,SACA,MAAA,MACA,QAAA,EAvGhC,+DAyGoC,MAAA,QAzGpC,8CAiHoB,QAAA,IAAA,EACA,QAAA,KACA,iBAAA,KAnHpB,0DAwHgC,QAAA,MACA,KAAA,MACA,WAAA,MACA,SAAA,SACA,MAAA,MA5HhC,mEAkIgC,SAAA,SACA,MAAA,KACA,IAAA,KACA,kBAAA,eAAA,UAAA,eArIhC,0DA2I4B,MAAA,QA3I5B,wBAqJQ,YAAA,eArJR,kBA0JQ,KAAA,eA1JR,oBA+JQ,QAAA,KAKR,cACI,WAAA,OEjQA,4BFqQA,KACI,WAAA,OACA,eAAA,KAEJ,gBACI,QAAA,KACA,QAAA,aAEJ,gCAEQ,QAAA,MAGR,cAAA,wBACI,YAAA,YACA,QAAA,EAAA,KAEJ,eACI,QAAA,KAEJ,UACI,QAAA,MAQR,6BAEQ,MAAA,MAFR,mCAKQ,MAAA,MACA,WAAA,OANR,2DAa4B,QAAA,MACA,UAAA,KACA,YAAA,KACA,MAAA,KACA,OAAA,EAjB5B,uDAsBoB,aAAA,EAtBpB,yDAwBwB,QAAA,KAAA,KChFtB,0CDwDF,+CA+BY,QAAA,eA/BZ,iDAkCY,YAAA,MAlCZ,yDAqCY,KAAA,MArCZ,+CAyCY,iBAAA,QAKZ,qDAGY,WAAA,KAHZ,+DAS4B,QAAA,aACA,UAAA,KACA,YAAA,KACA,YAAA,IACA,aAAA,KACA,eAAA,OACA,MAAA,KAW5B,oCAGQ,iBAAA,QAHR,qCAOQ,iBAAA,QACA,mBAAA,KAAA,WAAA,KACA,aAAA,IAAA,MAAA,QATR,2DAewB,MAAA,QAfxB,kEAAA,iEAAA,iEAoB4B,MAAA,QApB5B,kEAwBwB,MAAA,KACA,iBAAA,QACA,mBAAA,KA1BxB,+DAgCgB,MAAA,QAhChB,4DCzFE,2DDiIkB,MAAA,QAxCpB,kEAAA,kECtFI,iEACA,iEDgIoB,iBAAA,YACA,MAAA,QA5CxB,mECjFE,kEDuIkB,MAAA,KAOpB,mFAMwB,iBAAA,QAUxB,mBACI,iBAAA,QACA,mBAAA,KAAA,WAAA,KACA,QAAA,KAAA,IACA,MAAA,IACA,YAAA,GACA,WAAA,KANJ,kCASQ,cAAA,IATR,wCAYY,iBAAA,QACA,MAAA,KGxgBZ,MACI,QAAA,MACA,YAAA,KAFJ,mBAIQ,QAAA,MAJR,mBAOQ,QAAA,KAPR,yBAUQ,MAAA,QACA,YAAA,IACA,UAAA,KACA,eAAA,UAbR,0BAgBQ,MAAA,KACA,YAAA,IACA,UAAA,KACA,eAAA,UAIR,UACI,OAAA,KACA,MAAA,MACA,MAAA,KAGJ,eACI,iBAAA,QACA,mBAAA,EAAA,IAAA,IAAA,KAAA,eAAA,CAAA,EAAA,IAAA,IAAA,EAAA,gBAAA,WAAA,EAAA,IAAA,IAAA,KAAA,eAAA,CAAA,EAAA,IAAA,IAAA,EAAA,gBACA,QAAA,EAAA,KAAA,EAAA,EACA,SAAA,MACA,KAAA,EACA,MAAA,EACA,OAAA,KACA,QAAA,IARJ,+BAYY,MAAA,KAZZ,sCAeY,QAAA,EAAA,KACA,MAAA,qBACA,UAAA,KACA,QAAA,MACA,YAAA,KACA,WAAA,OACA,WAAA,KArBZ,wCA0BY,iBAAA,sBA1BZ,2BAgCQ,SAAA,OACA,OAAA,KACA,QAAA,MACA,UAAA,MACA,aAAA,KApCR,2CAuCY,QAAA,WACA,eAAA,OAxCZ,4EA2CgB,UAAA,SACA,MAAA,qBA5ChB,yCAgDY,OAAA,KACA,OAAA,KACA,aAAA,KACA,cAAA,EACA,MAAA,KACA,iBAAA,sBACA,mBAAA,KAAA,WAAA,KACA,cAAA,KAAA,EAAA,EAAA,KAvDZ,+CA0DY,YAAA,EACA,QAAA,EA3DZ,gCA+DY,iBAAA,sBACA,aAAA,YACA,MAAA,qBACA,cAAA,EAAA,KAAA,KAAA,EACA,mBAAA,eAAA,WAAA,eAnEZ,mCAwEQ,OAAA,KACA,MAAA,KACA,QAAA,aACA,OAAA,KACA,YAAA,KACA,MAAA,KACA,iBAAA,YACA,UAAA,KACA,OAAA,QAhFR,+CAoFQ,QAAA,KAMR,aACI,WAAA,MAGJ,mBACI,YAAA,EADJ,+BAIQ,iBAAA,KACA,QAAA,KAAA,KALR,8BASQ,UAAA,KACA,eAAA,OAVR,oCAcQ,QAAA,aACA,SAAA,SACA,IAAA,KACA,MAAA,KAjBR,gCAqBQ,QAAA,KAAA,KArBR,6CAwBY,MAAA,KACA,OAAA,KACA,MAAA,KACA,UAAA,KACA,YAAA,KACA,WAAA,OACA,aAAA,KACA,cAAA,IACA,MAAA,KAhCZ,gDAoCY,cAAA,IACA,SAAA,OACA,YAAA,KACA,cAAA,SACA,YAAA,OACA,MAAA,QAzCZ,kDA4CgB,YAAA,IA5ChB,sDA+CgB,QAAA,MA/ChB,qDAkDgB,QAAA,MACA,SAAA,OACA,cAAA,SACA,YAAA,OACA,UAAA,KAtDhB,0CA2DY,YAAA,KACA,YAAA,OACA,YAAA,KA7DZ,kDAkEY,QAAA,IAAA,KAKZ,kBACI,MAAA,MADJ,oBAGQ,eAAA,OACA,aAAA,IAIR,UACI,QAAA,EAAA,eADJ,cAGQ,OAAA,KACA,MAAA,KAKR,qBACI,iBAAA,eACA,mBAAA,EAAA,IAAA,IAAA,EAAA,eAAA,WAAA,EAAA,IAAA,IAAA,EAAA,eAFJ,4CAMY,MAAA,QANZ,8CAWY,iBAAA,mBAXZ,yCAgBQ,MAAA,QAhBR,kEAsBY,MAAA,kBAtBZ,+CAyBY,MAAA,QACA,iBAAA,QACA,aAAA,QA3BZ,sCA8BY,iBAAA,QACA,MAAA,QCnPZ,4BAEQ,UAAA,QACA,OAAA,EACA,YAAA,KACA,MAAA,QALR,kCAQQ,MAAA,MACA,WAAA,KATR,4BAaQ,YAAA,IFwDJ,4BElDA,4BAEQ,QAAA,MACA,YAAA,OACA,cAAA,SACA,SAAA,OACA,YAAA,KANR,4BASQ,QAAA,MAKZ,yBACI,kCAEQ,QAAA,MAKZ,yBACI,4BACI,QAAA,MC3CR,QACI,OAAA,EACA,QAAA,KAAA,KAAA,KACA,SAAA,SACA,MAAA,EACA,MAAA,QACA,KAAA,MACA,iBAAA,QAPJ,wBAWY,MAAA,QACA,YAAA,OACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAbZ,8BAegB,MAAA,QAfhB,sCAkBgB,YAAA,EAMhB,YACI,KAAA,YACA,WAAA,OACA,iBAAA,YH0CA,4BGtCA,QACI,KAAA,YACA,WAAA,QCjCR,WACI,iBAAA,KACA,mBAAA,EAAA,EAAA,KAAA,EAAA,eAAA,CAAA,EAAA,IAAA,EAAA,EAAA,gBAAA,WAAA,EAAA,EAAA,KAAA,EAAA,eAAA,CAAA,EAAA,IAAA,EAAA,EAAA,gBACA,QAAA,MACA,SAAA,MACA,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SACA,MAAA,MACA,QAAA,KACA,MAAA,gBACA,MAAA,OACA,IAAA,EACA,OAAA,EAXJ,2BAcQ,iBAAA,QACA,QAAA,KAAA,KACA,MAAA,KAhBR,6BAmBQ,iBAAA,QACA,OAAA,KACA,MAAA,KACA,YAAA,KACA,MAAA,KACA,WAAA,OACA,cAAA,IACA,WAAA,KA1BR,mCA6BY,iBAAA,QA7BZ,qBAiCQ,QAAA,KACA,WAAA,OAlCR,+BAoCY,SAAA,SACA,OAAA,KACA,MAAA,KACA,OAAA,EAAA,KAAA,KAAA,KAvCZ,0CAyCgB,SAAA,SACA,MAAA,KACA,OAAA,EACA,OAAA,KACA,MAAA,KACA,iBAAA,KACA,YAAA,KACA,cAAA,IACA,mBAAA,EAAA,IAAA,IAAA,EAAA,eAAA,CAAA,EAAA,IAAA,KAAA,EAAA,gBAAA,WAAA,EAAA,IAAA,IAAA,EAAA,eAAA,CAAA,EAAA,IAAA,KAAA,EAAA,gBAjDhB,wBAqDY,cAAA,IArDZ,0BAuDgB,MAAA,QAOhB,kBACI,iBAAA,mBACA,SAAA,SACA,KAAA,EACA,MAAA,EACA,IAAA,EACA,OAAA,EACA,QAAA,KACA,QAAA,KACA,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SAGJ,8BAEQ,MAAA,EAFR,qCAKQ,QAAA,MJVJ,4BIeA,WACI,SAAA,KADJ,4BAGQ,OAAA,gBCvFZ,2BAGY,UAAA,OACA,OAAA,EAAA,KACA,mBAAA,EAAA,IAAA,IAAA,EAAA,eAAA,CAAA,EAAA,IAAA,KAAA,EAAA,gBAAA,WAAA,EAAA,IAAA,IAAA,EAAA,eAAA,CAAA,EAAA,IAAA,KAAA,EAAA,gBALZ,iCASY,UAAA,OACA,OAAA,EAAA,KAVZ,0BAcY,OAAA,EAAA,KACA,UAAA,qBAfZ,mCAoBgB,UAAA,oBClBhB,UACE,UAAA,KAGF,UACE,UAAA,MAGF,UACE,UAAA,MAGF,UACE,UAAA,MAGF,UACE,UAAA,MAKF,uBACE,YAAA,OAAA,CAAA,WAIF,WACE,OAAA,OACA,MAAA,OAGF,WACE,OAAA,QACA,MAAA,QAGF,WACE,OAAA,OACA,MAAA,OAGF,WACE,OAAA,OACA,MAAA,OAGF,WACE,OAAA,KACA,MAAA,KAGF,YACE,OAAA,OACA,MAAA,OAGF,cACE,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,MAAA,KACA,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,OAAA,KACA,iBAAA,OAAA,cAAA,OAAA,gBAAA,OACA,MAAA,KAGF,cACE,aAAA,KADF,iCAGI,OAAA,EAAA,EAAA,KAAA,MACA,QAAA,aACA,OAAA,IAAA,MAAA,KACA,cAAA,IAMJ,WP6qBA,WACA,WACA,WO3qBE,SAAA,OACA,cAAA,SACA,QAAA,YACA,mBAAA,SAGF,WACE,mBAAA,EAGF,WACE,mBAAA,EAIF,WACE,mBAAA,EAGF,WACE,mBAAA,ECvGF,kBACI,OAAA,KACA,MAAA,KACA,YAAA,iBACA,QAAA,MACA,OAAA,IAAA,MAAA,QACA,cAAA,IACA,MAAA,QCPJ,aACI,SAAA,SACA,SAAA,OAFJ,2BAIQ,UAAA,KAKR,0BAEQ,cAAA,IAAA,MAAA,KACA,SAAA,OACA,QAAA,QAAA,EACA,SAAA,SALR,uCAOY,cAAA,KAPZ,0CAUY,QAAA,MACA,MAAA,KACA,aAAA,KACA,MAAA,KAbZ,8CAegB,MAAA,KAfhB,6CAmBY,MAAA,QACA,QAAA,MACA,cAAA,IArBZ,2CAwBY,MAAA,QACA,QAAA,MACA,UAAA,SACA,OAAA,EACA,SAAA,OA5BZ,2CA+BY,MAAA,QACA,UAAA,SACA,SAAA,SACA,MAAA,IACA,IAAA,KAOZ,mBACI,WAAA,KACA,OAAA,MACA,QAAA,EAAA,KAHJ,sBAKQ,cAAA,KALR,gCAQQ,MAAA,KACA,WAAA,OACA,MAAA,KAVR,oCAYY,cAAA,KACA,MAAA,KAbZ,kCAgBY,UAAA,KACA,WAAA,OAjBZ,+BAqBQ,WAAA,QACA,cAAA,IACA,QAAA,aACA,QAAA,KACA,SAAA,SAzBR,iCA2BY,QAAA,MACA,UAAA,KACA,WAAA,OACA,YAAA,IACA,SAAA,SA/BZ,iCAkCY,OAAA,EACA,YAAA,IAnCZ,qCAsCY,MAAA,IACA,IAAA,EACA,OAAA,MAAA,YACA,QAAA,IACA,OAAA,EACA,MAAA,EACA,SAAA,SACA,eAAA,KACA,iBAAA,QACA,aAAA,IACA,YAAA,KACA,mBAAA,QAjDZ,sCAqDQ,MAAA,KACA,UAAA,KACA,YAAA,KACA,MAAA,IAxDR,qCA4DY,MAAA,gBA5DZ,2CA+DY,MAAA,gBACA,aAAA,KACA,WAAA,MACA,MAAA,cAlEZ,oCAqEY,iBAAA,QArEZ,0CAuEgB,aAAA,YACA,kBAAA,QACA,iBAAA,QACA,KAAA,cC9HhB,gBAEQ,QAAA,aACA,aAAA,IACA,SAAA,SACA,YAAA,IACA,cAAA,EANR,wBAQY,iBAAA,KACA,cAAA,IACA,OAAA,IAAA,MAAA,QACA,QAAA,GACA,QAAA,aACA,OAAA,KACA,KAAA,EACA,YAAA,MACA,SAAA,SACA,mBAAA,IAAA,YAAA,WAAA,IAAA,YACA,MAAA,KACA,QAAA,YACA,IAAA,IApBZ,uBAuBY,MAAA,QACA,QAAA,aACA,UAAA,KACA,OAAA,KACA,KAAA,EACA,YAAA,MACA,aAAA,IACA,YAAA,IACA,SAAA,SACA,IAAA,EACA,MAAA,KAjCZ,+BAqCQ,OAAA,QACA,QAAA,EACA,QAAA,EACA,QAAA,YAxCR,8CA0CY,QAAA,IA1CZ,mDA+CY,eAAA,KACA,QAAA,EAhDZ,oDAqDY,QAAA,GACA,SAAA,SACA,IAAA,IACA,KAAA,IACA,QAAA,MACA,MAAA,IACA,OAAA,IACA,OAAA,IAAA,MAAA,QACA,iBAAA,EACA,kBAAA,EACA,kBAAA,cAGA,UAAA,cAlEZ,sDAuEY,iBAAA,QACA,OAAA,YAKZ,wCAGY,cAAA,IAKZ,0BACI,WAAA,EAGJ,gCAEQ,OAAA,KACA,MAAA,KACA,SAAA,SAJR,gCAOQ,OAAA,KACA,MAAA,KARR,uCAUY,YAAA,EAVZ,sCAaY,YAAA,EAOR,6DAGY,iBAAA,QACA,aAAA,QAJZ,4DAOY,aAAA,KAPZ,+DAGY,iBAAA,QACA,aAAA,QAJZ,8DAOY,aAAA,KAPZ,6DAGY,iBAAA,QACA,aAAA,QAJZ,4DAOY,aAAA,KAPZ,0DAGY,iBAAA,QACA,aAAA,QAJZ,yDAOY,aAAA,KAPZ,6DAGY,iBAAA,QACA,aAAA,QAJZ,4DAOY,aAAA,KAPZ,4DAGY,iBAAA,QACA,aAAA,QAJZ,2DAOY,aAAA,KAPZ,2DAGY,iBAAA,QACA,aAAA,QAJZ,0DAOY,aAAA,KAPZ,0DAGY,iBAAA,QACA,aAAA,QAJZ,yDAOY,aAAA,KAPZ,0DAGY,iBAAA,QACA,aAAA,QAJZ,yDAOY,aAAA,KAPZ,0DAGY,iBAAA,QACA,aAAA,QAJZ,yDAOY,aAAA,KCpHhB,aAEQ,QAAA,aACA,aAAA,IACA,SAAA,SACA,YAAA,IACA,cAAA,EANR,qBAQY,cAAA,OAAA,IAAA,YACA,mBAAA,OAAA,IAAA,YACA,iBAAA,KACA,cAAA,IACA,OAAA,IAAA,MAAA,QACA,QAAA,GACA,QAAA,aACA,OAAA,KACA,KAAA,EACA,YAAA,MACA,SAAA,SACA,WAAA,OAAA,IAAA,YACA,MAAA,KACA,QAAA,YArBZ,oBAwBY,gBAAA,eAAA,IAAA,8BACA,cAAA,WACA,aAAA,WACA,cAAA,aAAA,IAAA,8BACA,kBAAA,WACA,mBAAA,kBAAA,IAAA,8BACA,iBAAA,QACA,cAAA,IACA,QAAA,IACA,QAAA,aACA,OAAA,KACA,KAAA,IACA,YAAA,MACA,SAAA,SACA,IAAA,IACA,UAAA,WACA,WAAA,kBAAA,IAAA,8BAAA,WAAA,UAAA,IAAA,8BAAA,WAAA,UAAA,IAAA,6BAAA,CAAA,kBAAA,IAAA,8BACA,MAAA,KAzCZ,yBA6CQ,OAAA,QACA,QAAA,EACA,QAAA,EACA,QAAA,YAhDR,wCAkDY,QAAA,IAlDZ,6CAuDY,eAAA,KACA,QAAA,IAAA,KAAA,yBACA,QAAA,KAAA,OAzDZ,8CAgEY,kBAAA,WACA,UAAA,WAjEZ,gDAsEY,OAAA,YAKZ,oBACI,WAAA,EAGJ,0BAEQ,OAAA,KAMJ,8CAGY,iBAAA,QAHZ,uDAQY,aAAA,QARZ,sDAWY,iBAAA,QAXZ,gDAGY,iBAAA,QAHZ,yDAQY,aAAA,QARZ,wDAWY,iBAAA,QAXZ,8CAGY,iBAAA,QAHZ,uDAQY,aAAA,QARZ,sDAWY,iBAAA,QAXZ,2CAGY,iBAAA,QAHZ,oDAQY,aAAA,QARZ,mDAWY,iBAAA,QAXZ,8CAGY,iBAAA,QAHZ,uDAQY,aAAA,QARZ,sDAWY,iBAAA,QAXZ,6CAGY,iBAAA,QAHZ,sDAQY,aAAA,QARZ,qDAWY,iBAAA,QAXZ,4CAGY,iBAAA,QAHZ,qDAQY,aAAA,QARZ,oDAWY,iBAAA,QAXZ,2CAGY,iBAAA,QAHZ,oDAQY,aAAA,QARZ,mDAWY,iBAAA,QAXZ,2CAGY,iBAAA,QAHZ,oDAQY,aAAA,QARZ,mDAWY,iBAAA,QAXZ,2CAGY,iBAAA,QAHZ,oDAQY,aAAA,QARZ,mDAWY,iBAAA,QClGhB,YACE,SAAA,SADF,oBAII,SAAA,SACA,MAAA,KACA,QAAA,IAAA,KACA,cAAA,KACA,mBAAA,IAAA,IAAA,KAAA,mBAAA,WAAA,IAAA,IAAA,KAAA,mBACA,MAAA,KACA,UAAA,KACA,YAAA,IAXJ,2BAcM,QAAA,IACA,aAAA,MACA,aAAA,KACA,QAAA,MACA,SAAA,SACA,OAAA,MACA,KAAA,EACA,cAAA,MACA,QAAA,GAtBN,+BAyBM,YAAA,MACA,cAAA,EAAA,IAAA,IAAA,EA1BN,gCA6BM,aAAA,MACA,cAAA,IAAA,EAAA,EAAA,IA9BN,uCAiCQ,MAAA,EAjCR,sCAuCQ,OAAA,EAAA,KAAA,KAAA,KAvCR,4BA6CI,MAAA,KA7CJ,4BAkDQ,WAAA,QAlDR,mCAqDU,aAAA,QAAA,YAAA,YArDV,8BAkDQ,WAAA,QAlDR,qCAqDU,aAAA,QAAA,YAAA,YArDV,4BAkDQ,WAAA,QAlDR,mCAqDU,aAAA,QAAA,YAAA,YArDV,yBAkDQ,WAAA,QAlDR,gCAqDU,aAAA,QAAA,YAAA,YArDV,4BAkDQ,WAAA,QAlDR,mCAqDU,aAAA,QAAA,YAAA,YArDV,2BAkDQ,WAAA,QAlDR,kCAqDU,aAAA,QAAA,YAAA,YArDV,0BAkDQ,WAAA,QAlDR,iCAqDU,aAAA,QAAA,YAAA,YArDV,yBAkDQ,WAAA,QAlDR,gCAqDU,aAAA,QAAA,YAAA,YArDV,yBAkDQ,WAAA,QAlDR,gCAqDU,aAAA,QAAA,YAAA,YArDV,yBAkDQ,WAAA,QAlDR,gCAqDU,aAAA,QAAA,YAAA,YArDV,wBA6DI,SAAA,SACA,KAAA,KACA,IAAA,KACA,QAAA,EACA,SAAA,OACA,MAAA,KACA,OAAA,KACA,WAAA,MApEJ,6BAuEM,UAAA,KACA,MAAA,KACA,WAAA,OACA,YAAA,KACA,UAAA,eACA,kBAAA,eACA,MAAA,MACA,QAAA,MACA,mBAAA,EAAA,EAAA,IAAA,EAAA,eAAA,CAAA,EAAA,IAAA,EAAA,EAAA,gBAAA,WAAA,EAAA,EAAA,IAAA,EAAA,eAAA,CAAA,EAAA,IAAA,EAAA,EAAA,gBACA,SAAA,SACA,IAAA,KACA,KAAA,MACA,YAAA,IAnFN,oCAsFQ,QAAA,GACA,SAAA,SACA,KAAA,EACA,IAAA,KACA,QAAA,GACA,aAAA,IAAA,MAAA,YACA,cAAA,IAAA,MAAA,YA5FR,mCAgGQ,QAAA,GACA,SAAA,SACA,MAAA,EACA,IAAA,KACA,QAAA,GACA,YAAA,IAAA,MAAA,YACA,cAAA,IAAA,MAAA,YAtGR,qCA+GQ,WAAA,QA/GR,4CAkHU,YAAA,IAAA,MAAA,QACA,WAAA,IAAA,MAAA,QAnHV,2CAuHU,aAAA,IAAA,MAAA,QACA,WAAA,IAAA,MAAA,QAxHV,uCA+GQ,WAAA,QA/GR,8CAkHU,YAAA,IAAA,MAAA,QACA,WAAA,IAAA,MAAA,QAnHV,6CAuHU,aAAA,IAAA,MAAA,QACA,WAAA,IAAA,MAAA,QAxHV,qCA+GQ,WAAA,QA/GR,4CAkHU,YAAA,IAAA,MAAA,QACA,WAAA,IAAA,MAAA,QAnHV,2CAuHU,aAAA,IAAA,MAAA,QACA,WAAA,IAAA,MAAA,QAxHV,kCA+GQ,WAAA,QA/GR,yCAkHU,YAAA,IAAA,MAAA,QACA,WAAA,IAAA,MAAA,QAnHV,wCAuHU,aAAA,IAAA,MAAA,QACA,WAAA,IAAA,MAAA,QAxHV,qCA+GQ,WAAA,QA/GR,4CAkHU,YAAA,IAAA,MAAA,QACA,WAAA,IAAA,MAAA,QAnHV,2CAuHU,aAAA,IAAA,MAAA,QACA,WAAA,IAAA,MAAA,QAxHV,oCA+GQ,WAAA,QA/GR,2CAkHU,YAAA,IAAA,MAAA,QACA,WAAA,IAAA,MAAA,QAnHV,0CAuHU,aAAA,IAAA,MAAA,QACA,WAAA,IAAA,MAAA,QAxHV,mCA+GQ,WAAA,QA/GR,0CAkHU,YAAA,IAAA,MAAA,QACA,WAAA,IAAA,MAAA,QAnHV,yCAuHU,aAAA,IAAA,MAAA,QACA,WAAA,IAAA,MAAA,QAxHV,kCA+GQ,WAAA,QA/GR,yCAkHU,YAAA,IAAA,MAAA,QACA,WAAA,IAAA,MAAA,QAnHV,wCAuHU,aAAA,IAAA,MAAA,QACA,WAAA,IAAA,MAAA,QAxHV,kCA+GQ,WAAA,QA/GR,yCAkHU,YAAA,IAAA,MAAA,QACA,WAAA,IAAA,MAAA,QAnHV,wCAuHU,aAAA,IAAA,MAAA,QACA,WAAA,IAAA,MAAA,QAxHV,kCA+GQ,WAAA,QA/GR,yCAkHU,YAAA,IAAA,MAAA,QACA,WAAA,IAAA,MAAA,QAnHV,wCAuHU,aAAA,IAAA,MAAA,QACA,WAAA,IAAA,MAAA,QCvHV,abg4CE,Qa/3CE,gBb83CF,eADA,gBADA,Wav3CM,QAAA,KAEJ,Wb63CF,SAFA,cACA,WAEA,Kaz3CM,QAAA,EACA,OAAA,GCfR,WACI,SAAA,MACA,IAAA,EACA,KAAA,EACA,MAAA,EACA,OAAA,EACA,iBAAA,KACA,QAAA,KAGJ,QACI,MAAA,KACA,OAAA,KACA,SAAA,SACA,KAAA,IACA,IAAA,IACA,OAAA,MAAA,EAAA,EAAA,MAGJ,SACI,OAAA,EAAA,KACA,UAAA,KACA,SAAA,SACA,YAAA,QACA,WAAA,IAAA,MAAA,QACA,aAAA,IAAA,MAAA,QACA,cAAA,IAAA,MAAA,QACA,YAAA,IAAA,MAAA,QACA,kBAAA,cAAA,UAAA,cACA,kBAAA,iBAAA,KAAA,SAAA,OAAA,UAAA,iBAAA,KAAA,SAAA,OAGJ,Sd24CA,ecz4CI,cAAA,IACA,MAAA,KACA,OAAA,KAGJ,oCACI,GACI,kBAAA,UACA,UAAA,UAEJ,KACI,kBAAA,eACA,UAAA,gBAIR,4BACI,GACI,kBAAA,UACA,UAAA,UAEJ,KACI,kBAAA,eACA,UAAA,gBCzDR,4BACI,WAAA,MAGJ,YACI,iBAAA,gCACA,gBAAA,MAIJ,uBACI,iBAAA,QACA,gBAAA,MACA,oBAAA,OAGJ,+BACI,iBAAA,8BACA,iBAAA,QAIJ,aACI,MAAA,MCtBJ,aACI,YAAA,KACA,cAAA,MAFJ,kBAKQ,cAAA,KACA,YAAA,IAMR,qBAEQ,OAAA,QACA,YAAA,KACA,YAAA,OACA,cAAA,SACA,QAAA,MACA,SAAA,OAPR,uBASY,cAAA,EACA,YAAA,QAVZ,mBAcQ,WAAA,OACA,eAAA,OACA,UAAA,KACA,MAAA,KACA,OAAA,KACA,YAAA,KACA,aAAA,KACA,MAAA,kBACA,cAAA,IACA,QAAA,aACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAxBR,2BA4BQ,cAAA,IACA,mBAAA,IACA,gBAAA,YACA,cAAA,KA/BR,iChB4+CE,mCgB18CU,MAAA,QAQZ,mBACI,SAAA,SACA,OAAA,MACA,WAAA,MACA,SAAA,KC1DJ,YACI,MAAA,QACA,YAAA,oBAAA,IAAA,GAAA,CAAA,oBAAA,KAAA,IACA,UAAA,KACA,YAAA,KACA,YAAA,OAAA,CAAA,WAIJ,gBACI,UAAA,MACA,YAAA,OAAA,CAAA,WACA,WAAA,MAHJ,sBAMQ,KAAA,KACA,aAAA,EACA,gBAAA,MACA,iBAAA,GAAA,IACA,kBAAA,EACA,kBAAA,OAAA,GAAA,SAAA,OAAA,UAAA,OAAA,GAAA,SAAA,OAXR,sCAeQ,OAAA,QACA,wBAAA,MAAA,gBAAA,MAhBR,sCAoBQ,OAAA,QACA,wBAAA,MAAA,gBAAA,MArBR,sCAyBQ,OAAA,QACA,wBAAA,MAAA,gBAAA,MA1BR,sCA8BQ,OAAA,QACA,wBAAA,MAAA,gBAAA,MA/BR,sCAmCQ,OAAA,QACA,wBAAA,IAAA,gBAAA,IAIR,0BACI,KACI,kBAAA,MAIR,kBACI,KACI,kBAAA,MAIR,6CAAA,yBACI,sBAEQ,KAAA,QACA,OAAA,QACA,aAAA,EACA,iBAAA,EAAA,EACA,kBAAA,EACA,kBAAA,KAAA,UAAA,MCtEZ,kBACI,MAAA,MACA,OAAA,EAAA,KACA,QAAA,KAAA,EAHJ,wBAMQ,iBAAA,KACA,kBAAA,EACA,kBAAA,KAAA,GAAA,YAAA,UAAA,KAAA,GAAA,YARR,wBAYQ,kBAAA,KAAA,GAAA,UAAA,KAAA,GACA,yBAAA,IAAA,IAAA,iBAAA,IAAA,IAIR,wBACI,GACI,kBAAA,KAEJ,KACI,kBAAA,GAIR,gBACI,GACI,kBAAA,KAEJ,KACI,kBAAA,GAIR,wBACI,GACI,kBAAA,UAEJ,KACI,kBAAA,gBAIR,gBACI,GACI,kBAAA,UAEJ,KACI,kBAAA,gBAIR,wBACI,GACI,QAAA,EAEJ,KACI,QAAA,GAIR,gBACI,GACI,QAAA,EAEJ,KACI,QAAA,GCnER,oBACI,OAAA,KACA,MAAA,KACA,MAAA,QACA,WAAA,OACA,cAAA,IACA,MAAA,KACA,YAAA,IACA,YAAA,KACA,iBAAA,qBAGJ,cACI,WAAA,EACA,YAAA,KACA,YAAA,IACA,UAAA,KAGJ,YACI,YAAA,KACA,MAAA,QCrBJ,cACI,iBAAA,KACA,kBAAA,MACA,kBAAA,QAAA,GAAA,SAAA,UAAA,QAAA,GAAA,SACA,4BAAA,OAAA,oBAAA,OACA,OAAA,MAGJ,2BACI,KACI,kBAAA,MAEJ,GACI,kBAAA,GALR,mBACI,KACI,kBAAA,MAEJ,GACI,kBAAA,GAIR,6CAAA,yBACI,cACI,iBAAA,EACA,kBAAA,EACA,kBAAA,KAAA,UAAA,KACA,4BAAA,OAAA,oBAAA,QCtBR,UACI,cAAA,KACA,SAAA,SAFJ,iBAIQ,iBAAA,QACA,OAAA,EACA,QAAA,GACA,KAAA,IACA,SAAA,SACA,IAAA,KACA,MAAA,IACA,QAAA,EAXR,qBAcQ,cAAA,KACA,WAAA,KACA,SAAA,SAhBR,wBAmBQ,WAAA,KACA,QAAA,MACA,OAAA,KAAA,EACA,SAAA,SACA,QAAA,KAvBR,0BA0BQ,WAAA,KA1BR,4BA4BY,QAAA,aACA,aAAA,IA7BZ,8BAgCY,OAAA,KACA,MAAA,KACA,cAAA,IpBsBR,yBoBhBA,qBAEQ,aAAA,MACA,WAAA,MAHR,wBAMQ,YAAA,KANR,yBASQ,WAAA,QACA,cAAA,IACA,QAAA,MACA,OAAA,KACA,KAAA,MACA,WAAA,MACA,SAAA,SACA,WAAA,OACA,IAAA,IACA,MAAA,KAlBR,2BAoBY,MAAA,QACA,UAAA,KACA,SAAA,SACA,KAAA,IACA,WAAA,IAxBZ,yBA4BQ,QAAA,WACA,eAAA,IACA,MAAA,IAGR,eACI,QAAA,UADJ,sBAGQ,QAAA,GACA,QAAA,MACA,MAAA,IALR,qCASY,cAAA,KAAA,MAAA,YACA,aAAA,KAAA,MAAA,eACA,WAAA,KAAA,MAAA,YACA,QAAA,MACA,OAAA,EACA,KAAA,MACA,WAAA,MACA,SAAA,SACA,IAAA,IACA,MAAA,EAlBZ,wCAuBY,QAAA,GACA,QAAA,MACA,MAAA,IAzBZ,4DA6BgB,cAAA,KAAA,MAAA,YACA,YAAA,KAAA,MAAA,eACA,WAAA,KAAA,MAAA,YACA,QAAA,MACA,OAAA,EACA,KAAA,KACA,WAAA,MACA,SAAA,SACA,MAAA,MACA,IAAA,IACA,MAAA,EAvChB,wDA0CgB,MAAA,MACA,WAAA,KA3ChB,0DA6CoB,MAAA,MACA,YAAA,IA9CpB,iDAmDY,KAAA,KACA,MAAA,MApDZ,yCAuDY,QAAA,KAvDZ,gDA0DY,aAAA,KACA,YAAA,EACA,WAAA,OpBhEZ,4BoBuEA,qBAEQ,WAAA,OACA,SAAA,SAHR,yBAMQ,QAAA,MAMZ,aACI,aAAA,MADJ,+BAGQ,SAAA,SACA,eAAA,KACA,aAAA,KACA,YAAA,IAAA,MAAA,QANR,qCAQY,QAAA,GACA,QAAA,MACA,SAAA,SACA,IAAA,IACA,KAAA,KACA,MAAA,KACA,OAAA,KACA,cAAA,IACA,WAAA,KACA,OAAA,IAAA,MAAA,QAjBZ,iDAoBY,SAAA,SACA,KAAA,OAKZ,yBACI,aACI,aAAA,EADJ,+BAGQ,SAAA,mBACA,QAAA,MACA,KAAA,YACA,cAAA,MCzLZ,eACI,MAAA,MACA,MAAA,KACA,QAAA,EAAA,KAAA,KAAA,KAGJ,gBACI,OAAA,QAAA,EAAA,QAAA,MACA,YAAA,IAAA,MAAA,QACA,QAAA,OAAA,EAAA,OAAA,KAGJ,cACI,QAAA,MACA,aAAA,EAFJ,iBAIQ,SAAA,SACA,QAAA,MACA,OAAA,KACA,YAAA,KACA,OAAA,QACA,4BAAA,IAAA,oBAAA,IATR,mBAWY,MAAA,QAXZ,uBAcY,WAAA,QACA,4BAAA,KAAA,oBAAA,KAfZ,2BAkBY,MAAA,KACA,SAAA,SAnBZ,6BAsBY,MAAA,MtBmzDN,oDACA,kCsB10DN,0CA0BgB,QAAA,MACA,MAAA,KA3BhB,kCA8BgB,OAAA,IAAA,MAAA,YACA,cAAA,MACA,OAAA,KAAA,KAAA,EACA,OAAA,EACA,MAAA,EACA,YAAA,EACA,UAAA,EApChB,oDAuCgB,OAAA,KAAA,KAAA,EAAA,KAvChB,0CA0CgB,WAAA,KACA,MAAA,QACA,YAAA,KA5ChB,oCA+CgB,SAAA,SACA,IAAA,EACA,KAAA,MACA,MAAA,EACA,cAAA,SACA,SAAA,OACA,YAAA,OACA,cAAA,EACA,YAAA,KAvDhB,6BA2DY,SAAA,SACA,IAAA,EACA,KAAA,MACA,MAAA,EACA,OAAA,EtB6yDN,mCsB52DN,sCAkEgB,SAAA,SACA,IAAA,EAnEhB,sCAsEgB,KAAA,EACA,MAAA,MACA,cAAA,SACA,SAAA,OACA,YAAA,OA1EhB,mCA6EgB,MAAA,EACA,MAAA,MACA,aAAA,KA/EhB,wBtB03DE,+BsBryDM,WAAA,QACA,4BAAA,KAAA,oBAAA,KAtFR,wBtB+3DE,8BsBryDM,mBAAA,MAAA,IAAA,EAAA,EAAA,QAAA,WAAA,MAAA,IAAA,EAAA,EAAA,QA1FR,0BA6FQ,YAAA,IACA,MAAA,QA9FR,qCAkGQ,OAAA,QACA,OAAA,KACA,MAAA,KACA,SAAA,SACA,QAAA,aACA,mBAAA,MAAA,EAAA,EAAA,EAAA,IAAA,QAAA,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,QACA,cAAA,IAxGR,2CA0GY,QAAA,EACA,OAAA,QA3GZ,yDA8GY,QAAA,EA9GZ,2CAiHY,SAAA,SACA,IAAA,IACA,KAAA,IACA,MAAA,IACA,OAAA,IACA,OAAA,QACA,WAAA,QACA,QAAA,EACA,cAAA,YACA,4BAAA,KAAA,oBAAA,KA1HZ,kDA6HY,WAAA,QAKZ,aAEQ,MAAA,QACA,QAAA,IAAA,KACA,QAAA,MAIR,WACI,OAAA,IAAA,MAAA,QAGJ,yBACI,eACI,MAAA,KACA,MAAA,KAEJ,gBACI,YAAA,EACA,OAAA,EACA,aAAA,EAEJ,oDAGY,YAAA,GAMhB,yBACI,2BAEQ,cAAA,KAGR,6BAEQ,MAAA,MAFR,oCAIY,KAAA,KAJZ,6BAQQ,KAAA,MARR,mCAUY,WAAA,MACA,cAAA,KACA,aAAA,MC/LhB,SACI,WAAA,KACA,aAAA,EAFJ,eAMY,WAAA,KACA,aAAA,EAPZ,YAYQ,YAAA,KACA,eAAA,IACA,WAAA,KACA,SAAA,SAfR,cAkBY,gBAAA,KACA,MAAA,QACA,QAAA,MACA,YAAA,OACA,SAAA,OACA,cAAA,SAvBZ,oBA0BgB,MAAA,QA1BhB,YA+BQ,YAAA,KACA,cAAA,KACA,YAAA,KAjCR,eAoCY,SAAA,SApCZ,iBAuCgB,YAAA,QAvChB,sBA2CgB,QAAA,GACA,QAAA,aACA,MAAA,KACA,OAAA,KACA,cAAA,IAAA,KAAA,MACA,YAAA,IAAA,MAAA,qBACA,SAAA,SACA,IAAA,MClDhB,gCAEQ,QAAA,KAAA,KAAA,KAAA,KACA,mBAAA,KACA,WAAA,KACA,gBAAA,KALR,gCAQQ,eAAA,KACA,cAAA,IAAA,MAAA,QACA,cAAA,KAVR,2CAeY,QAAA,KAAA,cACA,aAAA,KChBZ,cACI,SAAA,SADJ,sCAIQ,eAAA,KAJR,iCAQQ,UAAA,KACA,iBAAA,oBACA,OAAA,KACA,QAAA,aACA,MAAA,KACA,YAAA,KACA,cAAA,IAdR,kCAkBQ,QAAA,KAAA,EAAA,EAlBR,uCAqBY,UAAA,IACA,MAAA,QACA,eAAA,IACA,eAAA,UAxBZ,qCA4BQ,MAAA,QACA,WAAA,KACA,OAAA,EACA,QAAA,KAAA,EAAA,EAAA,EA/BR,wCAkCY,QAAA,KAKZ,0BACI,iBAAA,QACA,MAAA,KAFJ,6CAKQ,iBAAA,qBALR,iDAAA,mDAQQ,MAAA,QC/CR,aACI,cAAA,KADJ,eAIQ,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SACA,MAAA,QACA,cAAA,IACA,QAAA,IAAA,KACA,QAAA,aACA,cAAA,IACA,YAAA,IACA,YAAA,OAAA,CAAA,WAXR,qBAaY,iBAAA,QACA,MAAA,KAdZ,sBAkBQ,iBAAA,QACA,MAAA,KAKR,SACI,iBAAA,KACA,cAAA,IACA,mBAAA,EAAA,IAAA,IAAA,KAAA,eAAA,CAAA,EAAA,IAAA,IAAA,EAAA,gBAAA,WAAA,EAAA,IAAA,IAAA,KAAA,eAAA,CAAA,EAAA,IAAA,IAAA,EAAA,gBACA,cAAA,KAJJ,sBAOQ,QAAA,KACA,QAAA,MARR,0BAWY,OAAA,gBAAA,OAAA,QAXZ,oBAgBQ,QAAA,KACA,WAAA,IAAA,MAAA,QACA,SAAA,SAlBR,uBAqBY,QAAA,MACA,SAAA,OACA,YAAA,OACA,cAAA,SAxBZ,kCA4BY,SAAA,SACA,MAAA,KACA,UAAA,KACA,IAAA,KCvDZ,gBACI,UAAA,KACA,YAAA,IAFJ,qBAIQ,UAAA,KACA,YAAA,IACA,QAAA,MAIR,YACI,MAAA,KACA,MAAA,IAGJ,YACI,OAAA,KAGJ,uB3BklEA,sBAEA,gCADA,e2B/kEI,KAAA,QCvBJ,uBACI,iBAAA,QACA,OAAA,EAAA,QACA,QAAA,KACA,WAAA,KCJJ,UACI,WAAA,KACA,cAAA,EAGF,aACE,iBAAA,KACA,OAAA,IAAA,MAAA,QACA,QAAA,KACA,cAAA,KACA,cAAA,IAGF,0BACE,cAAA,EAGF,qBACE,QAAA,IAAA,IACA,UAAA,KAGF,oBACE,YAAA,KACA,WAAA,IAGF,kBACE,OAAA,IAAA,OAAA,kBACA,iBAAA,kBACA,QAAA,KC9BJ,aACI,SAAA,SACA,SAAA,OAFJ,6BAKQ,SAAA,SACA,MAAA,EACA,IAAA,EACA,QAAA,OAAA,OAAA,EAAA,OACA,QAAA,EACA,QAAA,EACA,WAAA,OACA,kBAAA,iBAAA,UAAA,iBACA,mBAAA,IAAA,IAAA,KAAA,GAAA,WAAA,IAAA,IAAA,KAAA,GAbR,mCAkBY,QAAA,EACA,WAAA,QACA,kBAAA,cAAA,UAAA,cApBZ,2BAyBQ,YAAA,OAzBR,gCA6BQ,OAAA,KACA,YAAA,KACA,YAAA,IACA,UAAA,KACA,iBAAA,QACA,WAAA,OACA,QAAA,EAAA,KACA,cAAA,IAKR,eACI,QAAA,IACA,WAAA,IAFJ,sBAKQ,iBAAA,kBClDR;;;;;;;AAQA,kBACE,MAAA,MACA,YAAA,QAEF,4BACE,MAAA,KAQF,mCACE,QAAA,QAEF,6CACE,QAAA,QAQF,4BACE,QAAA,QAEF,sCACE,QAAA,QAQF,6BACE,QAAA,QAEF,uCACE,QAAA,QAEF,uBACE,MAAA,MAEF,iCACE,MAAA,KAEF,iCACE,QAAA,QAEF,oCACE,kBAAA,cACA,UAAA,cAEF,uBACE,MAAA,MAEF,iCACE,MAAA,KAEF,iCACE,QAAA,QAEF,2CACE,QAAA,QAEF,qBACE,QAAA,KAEF,wBACE,QAAA,MAEF,uBACE,SAAA,SACA,OAAA,EACA,SAAA,OACA,mCAAA,KAAA,2BAAA,KACA,4BAAA,KAAA,oBAAA,KACA,4BAAA,MAAA,CAAA,WAAA,oBAAA,MAAA,CAAA,WAGF,sBACE,SAAA,SAGF,6BACE,SAAA,SACA,QAAA,GACA,MAAA,KACA,OAAA,KACA,aAAA,IAAA,EAAA,EAAA,IACA,aAAA,MACA,aAAA,QACA,MAAA,IACA,kBAAA,eAAA,kBACA,UAAA,eAAA,kBACA,yBAAA,IACA,iBAAA,IACA,IAAA,IACA,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SAGF,uCACE,MAAA,KACA,KAAA,IACA,kBAAA,eAAA,kBACA,UAAA,eAAA,kBAGF,qC/BgrEA,iD+B9qEE,kBAAA,gBAAA,kBACA,UAAA,gBAAA,kBAGF,+C/B+qEA,2D+B7qEE,kBAAA,eAAA,kBACA,UAAA,eAAA,kBChIF;;;;;;AAOC,cACC,SAAA,SACA,OAAA,QACA,QAAA,aACA,SAAA,OACA,oBAAA,KACA,iBAAA,KACA,gBAAA,KACA,YAAA,KACA,4BAAA,YAEF,4BACE,SAAA,SACA,cAAA,IACA,MAAA,MACA,OAAA,MACA,WAAA,MACA,YAAA,MACA,QAAA,EACA,WAAA,eAIA,WAAA,mHACA,mBAAA,IAAA,IAAA,SAGA,WAAA,IAAA,IAAA,SACA,4BAAA,iBAAA,CAAA,QAGA,4BAAA,OAAA,CAAA,kBAAA,oBAAA,OAAA,CAAA,kBAAA,oBAAA,SAAA,CAAA,QAAA,oBAAA,SAAA,CAAA,OAAA,CAAA,kBACA,kBAAA,SAAA,eAIA,UAAA,SAAA,eACA,eAAA,KAEF,wCACE,WAAA,qBAIA,WAAA,2IAEF,0CACE,WAAA,eAEF,sDACE,WAAA,qBAEF,oBACE,mBAAA,eAGA,WAAA,eAEF,chCsyEA,cgCpyEE,kBAAA,cAIA,UAAA,cACA,mBAAA,oDAEF,chCqyEA,oBAFA,oBACA,sBgChyEE,YAAA,OACA,eAAA,OACA,OAAA,QACA,OAAA,KACA,QAAA,EACA,MAAA,QACA,iBAAA,cACA,UAAA,IACA,YAAA,IACA,WAAA,OACA,gBAAA,KACA,QAAA,EAEF,cACE,QAAA,MAAA,MACA,cAAA,KAEF,oBACE,OAAA,EACA,QAAA,MAAA,MAEF,qBACE,cAAA,KACA,eAAA,OAEF,kCACE,QAAA,EAEF,yCACE,SAAA,SACA,IAAA,EACA,KAAA,EACA,QAAA,EAEF,cACE,WAAA,OACA,MAAA,MACA,OAAA,MACA,YAAA,MACA,cAAA,IAEF,aACE,mBAAA,KACA,mBAAA,EAAA,IAAA,MAAA,IAAA,gBACA,WAAA,EAAA,IAAA,MAAA,IAAA,gBACA,mBAAA,IAAA,IAGA,WAAA,IAAA,IAEF,oBACE,mBAAA,EAAA,IAAA,KAAA,IAAA,eACA,WAAA,EAAA,IAAA,KAAA,IAAA,eAEF,aACE,QAAA,MClIF,oBjCq6EA,qBAEA,uBADA,mBAEA,sBACA,uBAEA,yBADA,qBiCp6EQ,iBAAA,QANR,iDjC+6EA,2CACA,qCACA,mDACA,6CACA,uCiCv6EY,iBAAA,QAZZ,4BjCw7EA,8BADA,0BAEA,8BAEA,gCADA,4BiCv6EY,iBAAA,QAKZ,uBAEQ,OAAA,KAFR,sBAKQ,WAAA,QACA,WAAA,wEAAA,WAAA,kDjC86ER,qBiC16EA,sBjCy6EA,wBADA,qBADA,qBADA,wBADA,oBiC75EQ,iBAAA,QARR,sBAWQ,iBAAA,QAXR,6BjCm7EA,sCADA,+BADA,2BiCh6EQ,iBAAA,QAjBR,oCjCu7EA,8BiCj6EQ,iBAAA,QAtBR,kDjC27EA,4CiCj6EQ,iBAAA,QjCu6ER,qBiCn6EA,sBjCk6EA,wBADA,oBiC55EQ,iBAAA,QjCm6EN,4BiCx6EF,6BjCu6EE,+BADA,2BiC/5EU,iBAAA,QAPZ,wBAWQ,OAAA,IAAA,MAAA,QACA,mBAAA,EAAA,IAAA,IAAA,oBAAA,WAAA,EAAA,IAAA,IAAA,oBjCq6ER,qBiCj7EA,qBAgBQ,MAAA,QACA,iBAAA,QjCu6ER,sBiCn6EA,uBjCk6EA,yBADA,qBiC55EQ,iBAAA,QALR,yBAQQ,OAAA,IAAA,MAAA,QC5FR,UACI,MAAA,KACA,cAAA,EAGJ,SACI,WAAA,KAGJ,2BAEQ,WAAA,KAIR,YACI,OAAA,KAAA,EAAA,IAAA,EADJ,eAGQ,UAAA,QACA,YAAA,SACA,eAAA,UAIR,4BAEQ,YAAA,IAIR,QACI,WAAA,KAGJ,6BlCm/EA,6BAGA,4BAFA,yBACA,yBkC/+EQ,QAAA,EAIR,wBAEQ,WAAA,QACA,UAAA,KACA,YAAA,KACA,QAAA,KAAA,EACA,eAAA,UlCo/ER,yBAEA,yBADA,qBAFA,mBAFA,gBkC7+EA,gBlC8+EA,mBkCt+EQ,aAAA,QAIR,kDAIgB,MAAA,MACA,OAAA,KACA,MAAA,KACA,WAAA,OACA,YAAA,KACA,iBAAA,QACA,cAAA,IACA,OAAA,IACA,YAAA,OAAA,CAAA,WACA,UAAA,KAMhB,WACI,WAAA,QACA,OAAA,KACA,MAAA,QACA,eAAA,WACA,mBAAA,KAAA,WAAA,KACA,cAAA,IACA,OAAA,EAAA,IACA,QAAA,IAAA,KACA,OAAA,KAGJ,eACI,YAAA,QACA,UAAA,KAGJ,gBACI,WAAA,QAGJ,oBACI,WAAA,QlCm+EJ,iBACA,mBkCj+EA,eAGI,iBAAA,QACA,MAAA,KACA,YAAA,KAGJ,iBACI,WAAA,QAGJ,uBAEQ,WAAA,KAIR,UACI,cAAA,IACA,OAAA,KACA,OAAA,KACA,UAAA,SACA,OAAA,IAAA,IACA,QAAA,IAAA,IACA,WAAA,OAGJ,gBACI,OAAA,KACA,OAAA,KAAA,EACA,QAAA,IAAA,KACA,MAAA,KAGJ,sCAGY,cAAA,IAHZ,gCAOQ,cAAA,IAPR,2BAUQ,MAAA,KAIR,gCAEQ,MAAA,KjC5FJ,4BiCiGA,uBAAA,qBAAA,sBAEQ,MAAA,KACA,QAAA,MACA,MAAA,KACA,OAAA,KAAA,EAGR,oBAIgB,MAAA,KAKhB,iBACI,QAAA,MCxLR,YACE,mBAAA,YAAA,WAAA,YACA,MAAA,eACA,OAAA,eACA,iBAAA,eACA,mBAAA,EAAA,IAAA,IAAA,EAAA,eAAA,CAAA,EAAA,IAAA,KAAA,EAAA,gBAAA,WAAA,EAAA,IAAA,IAAA,EAAA,eAAA,CAAA,EAAA,IAAA,KAAA,EAAA,gBACA,QAAA,IAAA,eACA,cAAA,IACA,aAAA,eAGF,UACE,MAAA,kBACA,UAAA,eACA,YAAA,eACA,YAAA,OAAA,CAAA,qBACA,YAAA,cChBF,oCACI,QAAA,EAGJ,gBACI,gBAAA,mBACA,cAAA,eAFJ,kCAAA,mCAQY,iBAAA,QARZ,qCAAA,sCAWgB,aAAA,QAXhB,+BAgBgB,QAAA,YAhBhB,+BAAA,+BAqBY,QAAA,IAAA,MAAA,kBACA,eAAA,KACA,MAAA,QACA,iBAAA,qBAKZ,iBACI,YAAA,IAKJ,8EAAA,8EAMwB,mBAAA,EAAA,IAAA,IAAA,EAAA,eAAA,CAAA,EAAA,IAAA,KAAA,EAAA,gBAAA,WAAA,EAAA,IAAA,IAAA,EAAA,eAAA,CAAA,EAAA,IAAA,KAAA,EAAA,gBACA,iBAAA,QACA,IAAA,OARxB,2EAAA,2EAiBwB,iBAAA,QACA,IAAA,OASxB,mBACI,iBAAA,QACA,OAAA,KACA,MAAA,KACA,mBAAA,KAAA,WAAA,KACA,cAAA,IACA,WAAA,OACA,QAAA,GAPJ,sBAUQ,cAAA,KACA,iBAAA,qBACA,MAAA,KnCTJ,4BmCcA,wBAAA,4BACI,QAAA,aACA,UAAA,OAGJ,mBACI,QAAA,KAEJ,wBAEQ,WAAA,OACA,QAAA,MACA,OAAA,OAAA,EAAA,YAGR,eACI,QAAA,aACA,cAAA,QAIR,4BAEQ,iBAAA,QC1GR,iBACI,YAAA,OAAA,CAAA,WADJ,mCAAA,2BAAA,iCAIQ,iBAAA,QCLR,YACI,QAAA,eADJ,etC0tFE,esCttFM,MAAA,KACA,OAAA,KACA,cAAA,ItCytFN,yDACA,mDACA,uCACA,sCACA,6CACA,+CACA,6CACA,4CACA,gDACA,0CACA,2CACA,6CACA,oDACA,sDACA,oDACA,mDACA,0DACA,4DACA,0DACA,yDACA,6DACA,uDACA,2CACA,0CACA,iDACA,mDACA,iDACA,gDACA,oDACA,8CsC5vFF,sCAAA,wCAAA,+CAAA,iDAAA,+CAAA,8CAAA,qDAAA,uDAAA,qDAAA,oDtC4vFiD,8BAA+B,uCAAwC,6CAA8C,oCsCpsFlJ,iBAAA,kBACA,iBAAA,eACA,MAAA,KtCusFlB,qCACA,mCsClwFF,oCAAA,kCAiEoB,WAAA,QtCosFlB,iCACA,iCsCtwFF,4BAAA,4BAwEoB,MAAA,QACA,QAAA,GAzEpB,qCtC0wFE,wBACA,wBACA,8BsCzrFM,WAAA,QAnFR,qCAuFY,WAAA,IAKZ,2BAGQ,cAAA,IAAA,MAAA,KAHR,4BAMQ,oBAAA,QANR,kDAUY,WAAA,IAAA,MAAA,QAVZ,iDAaY,WAAA,IAAA,MAAA,KCxGZ,oBACI,YAAA,QACA,aAAA,QCFJ,8CAEQ,OAAA,IAAA,MAAA,QACA,OAAA,KACA,QAAA,EAJR,2EAMY,YAAA,KACA,aAAA,KAPZ,wEAUY,OAAA,KACA,MAAA,KACA,MAAA,IAZZ,0EAcgB,aAAA,QAAA,YAAA,YAAA,YACA,aAAA,IAAA,IAAA,EAAA,IAMhB,gFAIgB,aAAA,YAAA,YAAA,QAAA,sBACA,aAAA,EAAA,IAAA,IAAA,cAMhB,yBACI,QAAA,IAAA,KAGJ,kBACI,OAAA,IAAA,MAAA,QACA,mBAAA,EAAA,IAAA,IAAA,KAAA,eAAA,CAAA,EAAA,IAAA,IAAA,EAAA,gBAAA,WAAA,EAAA,IAAA,IAAA,KAAA,eAAA,CAAA,EAAA,IAAA,IAAA,EAAA,gBAGJ,sDAEQ,QAAA,KACA,iBAAA,KAHR,6EAKY,OAAA,IAAA,MAAA,QACA,QAAA,EANZ,iFAUQ,iBAAA,QAVR,yEAaQ,iBAAA,QACA,MAAA,QAdR,+EAgBY,iBAAA,QACA,MAAA,KAKZ,gDAEQ,WAAA,KACA,OAAA,IAAA,MAAA,kBAHR,6EAKY,QAAA,IAAA,KALZ,uEAQY,OAAA,EARZ,2EAWY,iBAAA,QACA,OAAA,KACA,MAAA,KACA,cAAA,IACA,QAAA,EAAA,IACA,WAAA,IAhBZ,mFAmBY,MAAA,KACA,aAAA,IApBZ,yFAsBgB,MAAA,KCrFhB,eACI,OAAA,eCDJ,iBACI,QAAA,KACA,YAAA,OAAA,CAAA,WACA,iBAAA,QACA,UAAA,KACA,YAAA,KALJ,oBAOQ,YAAA,OAAA,CAAA,WAPR,mBAUQ,UAAA,SAVR,yBAYY,MAAA,KAKZ,aACI,QAAA,KAAA,KAAA,KAAA,KAGJ,uBACI,SAAA,SACA,IAAA,MACA,MAAA,MACA,UAAA,KACA,OAAA,QACA,OAAA,KACA,MAAA,KACA,iBAAA,QACA,cAAA,IACA,WAAA,OACA,YAAA,KAGJ,iBACI,OAAA,IACA,IAAA,EACA,cAAA,EAIA,iBACI,iBAAA,QACA,MAAA,KACA,aAAA,QAHJ,mBACI,iBAAA,QACA,MAAA,KACA,aAAA,QAHJ,iBACI,iBAAA,QACA,MAAA,KACA,aAAA,QAHJ,cACI,iBAAA,QACA,MAAA,KACA,aAAA,QAHJ,iBACI,iBAAA,QACA,MAAA,KACA,aAAA,QAHJ,gBACI,iBAAA,QACA,MAAA,KACA,aAAA,QAHJ,eACI,iBAAA,QACA,MAAA,KACA,aAAA,QAHJ,cACI,iBAAA,QACA,MAAA,KACA,aAAA,QAHJ,cACI,iBAAA,QACA,MAAA,KACA,aAAA,QAHJ,cACI,iBAAA,QACA,MAAA,KACA,aAAA,QAKR,eACI,iBAAA,QACA,MAAA,KACA,aAAA,QCpDJ,aACE,YAAA,OAAA,CAAA,WACA,mBAAA,EAAA,KAAA,KAAA,eAAA,WAAA,EAAA,KAAA,KAAA,eAFF,0BAKI,UAAA,KALJ,4BAQI,UAAA,KARJ,2BAWI,OAAA,KAAA,EAXJ,yBAAA,0BAAA,6BAcI,OAAA,IAAA,MAAA,QACA,UAAA,KACA,mBAAA,KAAA,WAAA,KAhBJ,wCAmBI,iBAAA,kBACA,UAAA,SApBJ,sCAwBI,iBAAA,kBACA,UAAA,SAzBJ,iCA6BI,mBAAA,eAAA,WAAA,eAIJ,2BACE,MAAA,QACA,aAAA,QAGF,0BACE,aAAA,QADF,gC3Cq7FE,sD2Cr7FF,mEAKI,iBAAA,QALJ,uCAAA,8CASI,aAAA,QAKJ,0BACE,MAAA,QACA,aAAA,QAGF,wBACE,aAAA,QADF,8BAGI,iBAAA,QAGJ,+BAAA,gCAAA,mCACE,QAAA,EACA,OAAA,IAAA,MAAA,QAGF,6BACE,iBAAA,kB3C02FF,2B4C/6FI,QAAA,KAGJ,aACI,YAAA,KACA,OAAA,KACA,aAAA,QACA,cAAA,MAGJ,kBAAA,oBAAA,mBACI,aAAA,QAGJ,mBACI,OAAA,KACA,YAAA,KCjBJ,eACI,UAAA,QADJ,8CAGQ,iBAAA,YAIR,aACI,MAAA,QACA,mBAAA,EAAA,IAAA,IAAA,EAAA,eAAA,CAAA,EAAA,IAAA,KAAA,EAAA,gBAAA,WAAA,EAAA,IAAA,IAAA,EAAA,eAAA,CAAA,EAAA,IAAA,KAAA,EAAA,gBACA,iBAAA,KAHJ,6BAMQ,iBAAA,KANR,+BAUQ,KAAA,KAIR,gCAEQ,WAAA,QACA,WAAA,sEAAA,WAAA,yCAIR,wEAKoB,WAAA,IAAA,MAAA,KACA,aAAA,IAAA,MAAA,YACA,YAAA,IAAA,MAAA,YAPpB,2EAgBoB,cAAA,IAAA,MAAA,KACA,aAAA,IAAA,MAAA,YACA,YAAA,IAAA,MAAA,YAlBpB,yEA2BoB,YAAA,IAAA,MAAA,KACA,WAAA,IAAA,MAAA,YACA,cAAA,IAAA,MAAA,YA7BpB,0EAsCoB,aAAA,IAAA,MAAA,KACA,WAAA,IAAA,MAAA,YACA,cAAA,IAAA,MAAA,YCpEpB,6BAGY,WAAA,QACA,OAAA,KACA,QAAA,IAAA,KACA,OAAA,KACA,YAAA,IACA,cAAA,IARZ,mCAUgB,MAAA,QAVhB,yBAcY,OAAA,KACA,UAAA,KACA,OAAA,EACA,MAAA,QACA,MAAA,KAlBZ,mBAsBQ,OAAA,IAAA,EAtBR,mCAyBY,MAAA,KACA,OAAA,KA1BZ,qBA8BQ,OAAA,EACA,OAAA,eACA,MAAA,KAhCR,sBAmCQ,OAAA,KACA,OAAA,KACA,QAAA,IAAA,KAAA,IAAA,KACA,WAAA,QACA,YAAA,IAvCR,4BAyCY,MAAA,QAzCZ,4BA6CQ,QAAA,QACA,YAAA,wBACA,MAAA,QAIR,U9CmiGA,gB8CjiGI,WAAA,qBAGJ,sBAEQ,mBAAA,EAAA,IAAA,IAAA,KAAA,eAAA,CAAA,EAAA,IAAA,IAAA,EAAA,gBAAA,WAAA,EAAA,IAAA,IAAA,KAAA,eAAA,CAAA,EAAA,IAAA,IAAA,EAAA,gBC1DR,qBACI,OAAA,IAAA,MAAA,QACA,cAAA,IAFJ,qC/CimGE,qC+C5lGM,iBAAA,kBACA,iBAAA,eACA,aAAA,kBACA,YAAA,eACA,OAAA,EAAA,EAAA,EAAA,cACA,YAAA,OAAA,CAAA,WACA,MAAA,eAXR,8CAeQ,WAAA,QACA,QAAA,EACA,cAAA,IAjBR,2FAuBgB,aAAA,KAAA,MAAA,QAvBhB,oFA2BgB,OAAA,KA3BhB,qFAiCgB,YAAA,KAAA,MAAA,QACA,KAAA,KAlChB,4FAsCgB,YAAA,EAAA,MAAA,QAtChB,kFA2CgB,cAAA,KAAA,MAAA,QACA,IAAA,EA5ChB,yFAgDgB,cAAA,EAAA,MAAA,eAhDhB,oFAqDgB,WAAA,KAAA,MAAA,QACA,IAAA,KAtDhB,2FAyDgB,WAAA,EAAA,MAAA,eAzDhB,wBA+DQ,YAAA,OAAA,CAAA,WACA,cAAA,KAhER,wCAmEQ,YAAA,OAAA,CAAA,WCnER,SACI,QAAA,IAAA,KACA,iBAAA,QACA,QAAA,IACA,MAAA,KACA,QAAA,EACA,cAAA,IAGJ,WAEQ,OAAA,KACA,YAAA,OAAA,CAAA,WAIR,aACI,aAAA,cACA,YAAA,KACA,cAAA,KACA,UAAA,KACA,YAAA,IACA,MAAA,QACA,eAAA,UAGJ,wBAGY,cAAA,I/CwCR,4B+CjCA,aACI,QAAA,MCrCR,mBAEQ,YAAA,OAAA,CAAA,qBAGR,cACI,SAAA,SACA,QAAA,GAFJ,mCAKQ,UAAA,KACA,WAAA,OACA,cAAA,IACA,QAAA,KAAA,KACA,WAAA,QACA,MAAA,KACA,YAAA,OAAA,CAAA,WAXR,2DAcY,YAAA,IACA,OAAA,MAAA,EACA,YAAA,OAAA,CAAA,WAhBZ,uDAoBY,YAAA,OACA,OAAA,KAAA,EACA,MAAA,KC3BZ,eACI,OAAA,KACA,SAAA,SACA,MAAA,KCHJ,0BACI,MAAA,KAGJ,UACI,WAAA,MADJ,oBAGQ,KAAA,QACA,MAAA,QACA,UAAA,KACA,YAAA,EAIR,8CAEQ,MAAA,KACA,KAAA,KACA,UAAA,KAIR,yCnDyrGA,0CACA,2CACA,iDmDprGgB,OAAA,QAPhB,yCnD+rGA,0CACA,2CACA,iDmDlrGgB,OAAA,QAfhB,yCnDqsGA,0CACA,2CACA,iDmDhrGgB,OAAA,QAvBhB,yCnD2sGA,0CACA,2CACA,iDmD9qGgB,OAAA,QA/BhB,yCnDitGA,0CACA,2CACA,iDmD5qGgB,OAAA,QAvChB,yCnDutGA,0CACA,2CACA,iDmD1qGgB,OAAA,QA/ChB,yCnD6tGA,0CACA,2CACA,iDmDxqGgB,OAAA,QAOhB,sBnDqqGA,2BmDlqGQ,KAAA,QAIR,sBnDkqGA,2BmD/pGQ,KAAA,QAIR,sBnD+pGA,2BmD5pGQ,KAAA,QAIR,sBnD4pGA,2BmDzpGQ,KAAA,QAIR,SACI,aAAA,IAGJ,kBACI,SAAA,SACA,QAAA,aACA,QAAA,EACA,UAAA,KACA,QAAA,IAAA,KACA,cAAA,IACA,WAAA,QACA,MAAA,KACA,WAAA,OACA,eAAA,KACA,QAAA,EACA,mBAAA,QAAA,IAAA,OAAA,WAAA,QAAA,IAAA,OAZJ,+BAcQ,QAAA,EClIR,YACI,mBAAA,EAAA,IAAA,IAAA,EAAA,eAAA,CAAA,EAAA,IAAA,KAAA,EAAA,gBAAA,WAAA,EAAA,IAAA,IAAA,EAAA,eAAA,CAAA,EAAA,IAAA,KAAA,EAAA,gBACA,QAAA,EAFJ,eAIQ,YAAA,KACA,YAAA,OAAA,CAAA,WALR,oBAOY,WAAA,QAPZ,eAWQ,OAAA,eAXR,eAcQ,iBAAA,QAIR,qBACI,UAAA,KACA,YAAA,IAGJ,SAEQ,YAAA,OAAA,CAAA,WACA,MAAA,QAGR,gBACI,YAAA,OAAA,CAAA,WACA,UAAA,KAEJ,SAAA,SACI,OAAA,QAEJ,+BACI,OAAA,KCrCJ,QACI,WAAA,KACA,SAAA,SACA,IAAA,EACA,MAAA,KAGJ,cACI,MAAA,QAGJ,oBAEQ,UAAA,KAIR,wBAEQ,YAAA,QACA,eAAA,MACA,YAAA,IAIR,8BrDqzGA,iCACA,+BqDlzGQ,YAAA,OAAA,CAAA,WAIR,kBAEQ,YAAA,OAAA,CAAA,qBCnCR,wDAEQ,QAAA,IAAA,KAFR,2CAKQ,OAAA,KACA,cAAA,EANR,sCASQ,QAAA,MATR,kCAaY,UAAA,KACA,YAAA,IAdZ,uCAkBQ,aAAA,KAlBR,6CAoBY,QAAA,aACA,aAAA,IACA,SAAA,SACA,cAAA,EAvBZ,qDAyBgB,iBAAA,KACA,cAAA,IACA,OAAA,IAAA,MAAA,QACA,QAAA,GACA,QAAA,aACA,OAAA,KACA,KAAA,EACA,YAAA,MACA,SAAA,SACA,mBAAA,IAAA,YAAA,WAAA,IAAA,YACA,MAAA,KACA,QAAA,EApChB,oDAuCgB,MAAA,QACA,QAAA,aACA,UAAA,KACA,OAAA,KACA,KAAA,EACA,YAAA,MACA,aAAA,IACA,YAAA,IACA,SAAA,SACA,IAAA,KACA,MAAA,KAjDhB,4DAqDY,OAAA,QACA,QAAA,EACA,QAAA,EACA,QAAA,EAxDZ,2EA0DgB,QAAA,IA1DhB,gFA+DgB,eAAA,KACA,QAAA,EAhEhB,iFAqEgB,QAAA,QACA,YAAA,sBACA,YAAA,IAvEhB,mFA4EgB,iBAAA,QACA,OAAA,YA7EhB,kFAkFgB,iBAAA,KACA,aAAA,QAnFhB,iFAsFgB,MAAA,QtDu0GhB,8CADA,4DsD55GA,4DA6FQ,WAAA,QACA,MAAA,KACA,aAAA,QtDo0GN,oDADA,kEsDl6GF,kEAkGY,MAAA,KAlGZ,8DAuGY,IAAA,eAvGZ,sCA2GQ,iBAAA,KACA,MAAA,QACA,OAAA,IAAA,MAAA,kBA7GR,kDA+GY,iBAAA,QACA,aAAA,QACA,MAAA,KACA,mBAAA,EAAA,EAAA,EAAA,IAAA,oBAAA,WAAA,EAAA,EAAA,EAAA,IAAA,oBAlHZ,+CAuHY,MAAA,MAvHZ,8DAyHgB,KAAA,KACA,MAAA,EC1HhB,WACI,YAAA,SACA,IAAA,2BACA,IAAA,kCAAA,2BAAA,CAAA,4BAAA,cAAA,CAAA,2BAAA,kBAAA,CAAA,oCAAA,cACA,YAAA,IACA,WAAA,OAGJ,qDACI,WACI,YAAA,SACA,IAAA,oCAAA,cACA,YAAA,IACA,WAAA,QAIR,cACI,iBAAA,KAGJ,sBACI,iBAAA,QAGJ,qBACI,iBAAA,QAGJ,wBAEQ,YAAA,IACA,MAAA,KAHR,0BAKY,SAAA,SACA,QAAA,MACA,QAAA,MAAA,OACA,YAAA,KACA,YAAA,KACA,MAAA,QACA,iBAAA,KACA,OAAA,IAAA,MAAA,QACA,cAAA,IAbZ,iCAkBY,MAAA,KACA,iBAAA,QACA,aAAA,QAKZ,mDACI,MAAA,MCvDJ,mDxDw+GA,mDACA,mDACA,mDACA,mDwDp+GY,QAAA,OAPZ,wBAWQ,cAAA,KAXR,oCAcQ,cAAA,IAAA,MAAA,YAIR,yBACI,QAAA,KAGJ,2CxDk+GA,uCwD/9GQ,WAAA,KACA,cAAA,EAJR,+CAQY,cAAA,eACA,OAAA,EAAA,IACA,OAAA,KAKZ,uBACI,OAAA,KADJ,gCAIY,YAAA,KAJZ,0CAUgB,QAAA,OAMhB,yBAEQ,YAAA,sBACA,YAAA,IAHR,4CAMQ,QAAA,QANR,0CASQ,QAAA,QATR,wCAYQ,QAAA,QACA,YAAA,IAbR,wCAgBQ,QAAA,QACA,YAAA,ICtER,gBAEQ,WAAA,QACA,iBAAA,KACA,OAAA,KAJR,mBAMY,YAAA,KANZ,kCASY,OAAA,KACA,YAAA,IACA,YAAA,OAAA,CAAA,WAXZ,aAeQ,WAAA,IAAA,MAAA,kBzD0hHR,mByDthHA,aAEI,UAAA,QACA,YAAA,QACA,QAAA,ezDwhHJ,mByDrhHA,yBAEI,cAAA,KzDuhHJ,0CyDphHA,0CAEI,UAAA,IACA,cAAA,KAGJ,uCACI,mBAAA,KAAA,WAAA,KACA,aAAA,QAGJ,sCACI,YAAA,KACA,iBAAA,KACA,eAAA,KACA,OAAA,IAAA,MAAA,QACA,QAAA,IAAA,KACA,MAAA,QANJ,4CASQ,QAAA,KAIR,uDAEQ,WAAA,KzDkhHR,6CADA,4CyD7gHA,4CAGI,MAAA,kBACA,iBAAA,QACA,QAAA,YACA,mBAAA,eAAA,WAAA,eACA,iBAAA,KAGJ,wCAEQ,QAAA,MAIR,4CACI,aAAA,IAAA,MAAA,QAGJ,uBACI,OAAA,QClFJ,aACI,QAAA,O1DmmHJ,8BAFA,gCAFA,kB0D5lHA,oB1D6lHA,uCAEA,gC0DzlHI,OAAA,KAGJ,6BAEQ,WAAA,KAIR,kCAEQ,WAAA,QACA,aAAA,QAIR,uCAEQ,WAAA,QACA,WAAA,iBAIR,gCAEQ,WAAA,QAIR,8B1D+kHA,gC0D7kHI,WAAA,QAGJ,c1D8kHA,eACA,iB0D5kHI,OAAA,IAAA,MAAA,QACA,QAAA,KAAA,KACA,QAAA,YAGJ,wBACI,WAAA,KAGJ,mBACI,QAAA,EACA,OAAA,EAAA,IAFJ,6CAIQ,iBAAA,QACA,MAAA,K1D8kHR,2B0D1kHA,qBAEI,iBAAA,QACA,cAAA,IACA,OAAA,KACA,MAAA,KACA,QAAA,aACA,WAAA,OACA,YAAA,KACA,MAAA,QAGJ,2BAEQ,MAAA,QACA,YAAA,IAHR,iCAKY,MAAA,QAKZ,uBAEQ,MAAA,KACA,OAAA,KACA,cAAA,IACA,iBAAA,0BACA,iBAAA,QACA,QAAA,YAPR,6BAUY,QAAA,GACA,iBAAA,QAKZ,2BACI,oBAAA,EAAA,OAGJ,sBACI,oBAAA,EAAA,OAGJ,2BAEQ,SAAA,SCjHR,cACI,WAAA,YAAA,kCAAA,UAAA,IAAA,IACA,MAAA,KACA,UAAA,MAHJ,uBAMQ,mBAAA,KAAA,WAAA,KACA,OAAA,IAAA,MAAA,QAPR,gCAUY,mBAAA,KAAA,WAAA,KACA,OAAA,IAAA,MAAA,QAXZ,mDAiBgB,OAAA,KACA,QAAA,IAAA,KAlBhB,yCAqBgB,iBAAA,QArBhB,iDA2BY,OAAA,KACA,QAAA,IAAA,KA5BZ,wCA+BY,iBAAA,QAKZ,eACI,mBAAA,KAAA,WAAA,KACA,QAAA,YAGJ,mBACI,YAAA,IACA,YAAA,OAAA,CAAA,WACA,MAAA,kBACA,UAAA,KC7CJ,0BACI,OAAA,IAAA,MAAA,QACA,WAAA,KACA,OAAA,QACA,SAAA,KACA,WAAA,gBACA,mBAAA,EAAA,IAAA,IAAA,gBAAA,WAAA,EAAA,IAAA,IAAA,gBAGF,yBACE,QAAA,IAAA,KACA,YAAA,OACA,SAAA,OAGF,4BACE,QAAA,IAGF,uBACE,WAAA,QACA,OAAA,QAGF,iCACE,YAAA,IACA,MAAA,QAGF,oBACE,QAAA,IACA,YAAA,IACA,YAAA,OAAA,CAAA,WAGF,2BACE,YAAA,IACA,UAAA,KACA,MAAA,QACA,QAAA,MCvCJ,sFACI,MAAA,eAGJ,0CAGY,QAAA,QACA,QAAA,aACA,YAAA,wBALZ,yCAQY,QAAA,YACA,eAAA,EATZ,oBAaQ,QAAA,YAbR,yBAgBQ,WAAA,kBCpBR,4CAGY,QAAA,EACA,OAAA,KACA,iBAAA,YACA,MAAA,QCNZ,qBACE,OAAA,EACA,QAAA,EAFF,wBAKI,WAAA,KACA,MAAA,QACA,WAAA,IACA,aAAA,KACA,SAAA,SATJ,+BAYM,QAAA,QACA,YAAA,wBACA,SAAA,SACA,KAAA,IACA,IAAA,KAKN,eACE,aAAA,QAGF,iBACE,aAAA,QC1BF,wBAAA,gCAAA,qCAAA,qCAAA,8BAAA,8BAAA,wBAAA,gCAAA,qCAAA,qCAAA,8BAAA,8BAAA,0BAAA,kCAAA,uCAAA,uCAAA,gCAAA,gCAmBQ,WAAA,QACA,aAAA,QhEqxHR,mEgEzyHA,mEhEwyHA,qEgE/wHQ,mBAAA,MAAA,EAAA,EAAA,QAAA,WAAA,MAAA,EAAA,EAAA,QhEuxHR,uCAFA,uCACA,4BgElxHA,4BAKQ,WAAA,QAIR,mCAEQ,OAAA,KCxCR,kCAEQ,iBAAA,QACA,MAAA,KCHR,WACE,YAAA,WACA,WAAA,OACA,YAAA,IACA,IAAA,6BACA,IAAA,oCAAA,2BAAA,CAAA,+BAAA,cAAA,CAAA,8BAAA,mBAGF,wBAEI,OAAA,IAAA,MAAA,QACA,mBAAA,KAAA,WAAA,KACA,OAAA,EAJJ,wCAOM,iBAAA,QACA,WAAA,IAAA,MAAA,QARN,uCAYQ,OAAA,KAKR,oBACE,QAAA,KAGF,eACE,OAAA,IAAA,MAAA,QACA,cAAA,MACA,QAAA,OAAA,MAHF,8BAOM,cAAA,ElEmzHN,qDkE9yHA,0DAEI,UAAA,MAGJ,cACG,QAAA,EC/CH,cACI,YAAA,OAAA,CAAA,WAGJ,WACI,OAAA,IAAA,MAAA,QACA,cAAA,MAGJ,YACI,YAAA,OAAA,CAAA,qBADJ,iBAGQ,QAAA,YCZR,UACE,OAAA,IAAA,OAAA,kBACA,WAAA,KACA,cAAA,ICHF,WACI,YAAA,QACA,IAAA,0BACA,IAAA,gCAAA,2BAAA,CAAA,2BAAA,cAAA,CAAA,0BAAA,kBAAA,CAAA,kCAAA,cACA,YAAA,IACA,WAAA,OAGJ,iBACI,OAAA,IAAA,OAAA,kBACA,WAAA,KACA,cAAA,IACA,YAAA,OAAA,CAAA,WCZJ,kBACI,WAAA,yBAAA,OAAA,OAAA,UAGJ,sBACI,WAAA,2BAAA,OAAA,OAAA,UAGJ,0BACI,QAAA,MCTJ,iCAEQ,MAAA,KACA,cAAA,MACA,aAAA,MACA,SAAA,OACA,iBAAA,QACA,WAAA,OACA,MAAA,KARR,qCAWY,UAAA,KAXZ,gCAgBQ,OAAA,KACA,MAAA,MAjBR,gCAoBQ,OAAA,OACA,MAAA,KArBR,gCAwBQ,OAAA,QACA,MAAA,KAzBR,gCA4BQ,OAAA,SACA,aAAA,EACA,MAAA,KAIR,4BvE+3HA,kCuE53HQ,cAAA,IACA,aAAA,IvEg4HR,iCuE53HA,8BAIY,UAAA,KAKZ,mBACI,WAAA,MACA,WAAA,KCrDJ,OAAA,gBACE,OAAA,MACA,WAAA,QACA,cAAA,IAGF,eACE,QAAA,MACA,WAAA,OACA,MAAA,KACA,UAAA,KACA,YAAA,KACA,WAAA,QACA,cAAA,IACA,QAAA,KAAA,KAGF,qBACE,KAAA,IACA,YAAA,MACA,MAAA,EACA,OAAA,EACA,SAAA,SALF,2BAQI,OAAA,MACA,YAAA,KAAA,MAAA,YACA,aAAA,KAAA,MAAA,YACA,WAAA,KAAA,MAAA,QAXJ,2BAeI,IAAA,MACA,YAAA,KAAA,MAAA,YACA,aAAA,KAAA,MAAA,YACA,cAAA,KAAA,MAAA,QCnCJ,kBACI,OAAA,KACA,WAAA,QACA,MAAA,KACA,YAAA,OAAA,CAAA,WACA,UAAA,SACA,QAAA,IAAA,ICNJ,aAEQ,SAAA,SAFR,qBAKY,IAAA,KALZ,sBASY,IAAA,KATZ,oBAaQ,SAAA,SACA,iBAAA,QACA,QAAA,IACA,cAAA,IACA,QAAA,IAAA,KACA,QAAA,KACA,UAAA,MACA,QAAA,KACA,MAAA,KACA,YAAA,OAAA,CAAA,WAtBR,gB1Ey+HA,iBACA,mB0E/8HQ,QAAA,aACA,WAAA,OACA,eAAA,OACA,cAAA,IACA,YAAA,IACA,OAAA,QACA,iBAAA,QACA,gBAAA,KACA,MAAA,KACA,UAAA,KACA,SAAA,SACA,IAAA,EACA,KAAA,KACA,MAAA,KACA,OAAA,KACA,YAAA,KA1CR,yBA+CY,YAAA,OAAA,CAAA,qBC/CZ,KACI,UAAA,IAGJ,KACI,WAAA,MCFJ,cACI,MAAA,KAMJ,eACI,WAAA,MACA,KAAA,eACA,MAAA,EACA,OAAA,KAJJ,iCAOQ,iBAAA,KACA,KAAA,eACA,MAAA,eAIR,qBACI,MAAA,eACA,KAAA,YAMJ,GACI,cAAA,EAMJ,WACI,OAAA,QAAA,OAAA,QAAA,MAGJ,iBACI,OAAA,QAAA,MAAA,QAAA,OAGJ,W5Ek/HA,oB4Eh/HI,UAAA,IAMJ,qBAEQ,OAAA,MAAA,KAAA,MAAA,MAIR,iCAEQ,aAAA,OACA,YAAA,EAHR,gCAOQ,YAAA,OACA,aAAA,EAOR,mBACI,aAAA,OACA,cAAA,QAFJ,0BAKQ,KAAA,EACA,MAAA,KAOR,0CAGY,aAAA,MACA,QAAA,QAOZ,gBACI,cAAA,OACA,aAAA,EAGJ,8BAEQ,KAAA,KACA,MAAA,QAHR,6BAQQ,KAAA,KACA,MAAA,QAIR,eACI,cAAA,QACA,aAAA,EAFJ,6CAMY,MAAA,SACA,KAAA,KAPZ,4CAWY,MAAA,qBACA,KAAA,KAZZ,0EAkBY,kBAAA,oBAAA,UAAA,oBAKZ,0BAEQ,MAAA,KACA,KAAA,EACA,aAAA,QAQR,qBACI,YAAA,KACA,aAAA,EAGJ,oBACI,aAAA,KACA,YAAA,E5Es8HJ,6CACA,4CAHA,wFACA,+EAHA,uDACA,oE4Eh8HA,uC5E87HA,oD4Et7HI,wBAAA,MACA,2BAAA,MACA,uBAAA,EACA,0BAAA,E5Eq8HJ,8CACA,6C4En8HA,sC5E67HA,mDAGA,qEACA,kFAHA,yDACA,sE4Ev7HI,uBAAA,MACA,0BAAA,MACA,wBAAA,EACA,2BAAA,ECrLI,KAAgC,OAAA,YAChC,M7EsnIR,M6EpnIU,WAAA,YAEF,M7EsnIR,M6EpnIU,YAAA,YACA,aAAA,YAEF,M7EsnIR,M6EpnIU,cAAA,YAEF,M7EsnIR,M6EpnIU,aAAA,YACA,YAAA,YAjBF,KAAgC,OAAA,kBAChC,M7E2oIR,M6EzoIU,WAAA,kBAEF,M7E2oIR,M6EzoIU,YAAA,kBACA,aAAA,YAEF,M7E2oIR,M6EzoIU,cAAA,kBAEF,M7E2oIR,M6EzoIU,aAAA,kBACA,YAAA,YAjBF,KAAgC,OAAA,iBAChC,M7EgqIR,M6E9pIU,WAAA,iBAEF,M7EgqIR,M6E9pIU,YAAA,iBACA,aAAA,YAEF,M7EgqIR,M6E9pIU,cAAA,iBAEF,M7EgqIR,M6E9pIU,aAAA,iBACA,YAAA,YAjBF,KAAgC,OAAA,iBAChC,M7EqrIR,M6EnrIU,WAAA,iBAEF,M7EqrIR,M6EnrIU,YAAA,iBACA,aAAA,YAEF,M7EqrIR,M6EnrIU,cAAA,iBAEF,M7EqrIR,M6EnrIU,aAAA,iBACA,YAAA,YAjBF,KAAgC,OAAA,kBAChC,M7E0sIR,M6ExsIU,WAAA,kBAEF,M7E0sIR,M6ExsIU,YAAA,kBACA,aAAA,YAEF,M7E0sIR,M6ExsIU,cAAA,kBAEF,M7E0sIR,M6ExsIU,aAAA,kBACA,YAAA,YAjBF,KAAgC,OAAA,iBAChC,M7E+tIR,M6E7tIU,WAAA,iBAEF,M7E+tIR,M6E7tIU,YAAA,iBACA,aAAA,YAEF,M7E+tIR,M6E7tIU,cAAA,iBAEF,M7E+tIR,M6E7tIU,aAAA,iBACA,YAAA,YAjBF,KAAgC,QAAA,YAChC,M7EovIR,M6ElvIU,YAAA,YAEF,M7EovIR,M6ElvIU,aAAA,YACA,cAAA,YAEF,M7EovIR,M6ElvIU,eAAA,YAEF,M7EovIR,M6ElvIU,cAAA,YACA,aAAA,YAjBF,KAAgC,QAAA,kBAChC,M7EywIR,M6EvwIU,YAAA,kBAEF,M7EywIR,M6EvwIU,aAAA,kBACA,cAAA,YAEF,M7EywIR,M6EvwIU,eAAA,kBAEF,M7EywIR,M6EvwIU,cAAA,kBACA,aAAA,YAjBF,KAAgC,QAAA,iBAChC,M7E8xIR,M6E5xIU,YAAA,iBAEF,M7E8xIR,M6E5xIU,aAAA,iBACA,cAAA,YAEF,M7E8xIR,M6E5xIU,eAAA,iBAEF,M7E8xIR,M6E5xIU,cAAA,iBACA,aAAA,YAjBF,KAAgC,QAAA,iBAChC,M7EmzIR,M6EjzIU,YAAA,iBAEF,M7EmzIR,M6EjzIU,aAAA,iBACA,cAAA,YAEF,M7EmzIR,M6EjzIU,eAAA,iBAEF,M7EmzIR,M6EjzIU,cAAA,iBACA,aAAA,YAjBF,KAAgC,QAAA,kBAChC,M7Ew0IR,M6Et0IU,YAAA,kBAEF,M7Ew0IR,M6Et0IU,aAAA,kBACA,cAAA,YAEF,M7Ew0IR,M6Et0IU,eAAA,kBAEF,M7Ew0IR,M6Et0IU,cAAA,kBACA,aAAA,YAjBF,KAAgC,QAAA,iBAChC,M7E61IR,M6E31IU,YAAA,iBAEF,M7E61IR,M6E31IU,aAAA,iBACA,cAAA,YAEF,M7E61IR,M6E31IU,eAAA,iBAEF,M7E61IR,M6E31IU,cAAA,iBACA,aAAA,YAQF,MAAwB,OAAA,mBACxB,O7Ey1IR,O6Ev1IU,WAAA,mBAEF,O7Ey1IR,O6Ev1IU,aAAA,mBAEF,O7Ey1IR,O6Ev1IU,cAAA,mBAEF,O7Ey1IR,O6Ev1IU,YAAA,mBAfF,MAAwB,OAAA,kBACxB,O7E42IR,O6E12IU,WAAA,kBAEF,O7E42IR,O6E12IU,aAAA,kBAEF,O7E42IR,O6E12IU,cAAA,kBAEF,O7E42IR,O6E12IU,YAAA,kBAfF,MAAwB,OAAA,kBACxB,O7E+3IR,O6E73IU,WAAA,kBAEF,O7E+3IR,O6E73IU,aAAA,kBAEF,O7E+3IR,O6E73IU,cAAA,kBAEF,O7E+3IR,O6E73IU,YAAA,kBAfF,MAAwB,OAAA,mBACxB,O7Ek5IR,O6Eh5IU,WAAA,mBAEF,O7Ek5IR,O6Eh5IU,aAAA,mBAEF,O7Ek5IR,O6Eh5IU,cAAA,mBAEF,O7Ek5IR,O6Eh5IU,YAAA,mBAfF,MAAwB,OAAA,kBACxB,O7Eq6IR,O6En6IU,WAAA,kBAEF,O7Eq6IR,O6En6IU,aAAA,kBAEF,O7Eq6IR,O6En6IU,cAAA,kBAEF,O7Eq6IR,O6En6IU,YAAA,kBAMN,QAAmB,OAAA,eACnB,S7Em6IJ,S6Ej6IM,WAAA,eAEF,S7Em6IJ,S6Ej6IM,YAAA,eACA,aAAA,kBAEF,S7Em6IJ,S6Ej6IM,cAAA,eAEF,S7Em6IJ,S6Ej6IM,aAAA,eACA,YAAA,e5EbF,yB4ElDI,QAAgC,OAAA,YAChC,S7Es+IN,S6Ep+IQ,WAAA,YAEF,S7Eq+IN,S6En+IQ,YAAA,YACA,aAAA,YAEF,S7Eo+IN,S6El+IQ,cAAA,YAEF,S7Em+IN,S6Ej+IQ,aAAA,YACA,YAAA,YAjBF,QAAgC,OAAA,kBAChC,S7Es/IN,S6Ep/IQ,WAAA,kBAEF,S7Eq/IN,S6En/IQ,YAAA,kBACA,aAAA,YAEF,S7Eo/IN,S6El/IQ,cAAA,kBAEF,S7Em/IN,S6Ej/IQ,aAAA,kBACA,YAAA,YAjBF,QAAgC,OAAA,iBAChC,S7EsgJN,S6EpgJQ,WAAA,iBAEF,S7EqgJN,S6EngJQ,YAAA,iBACA,aAAA,YAEF,S7EogJN,S6ElgJQ,cAAA,iBAEF,S7EmgJN,S6EjgJQ,aAAA,iBACA,YAAA,YAjBF,QAAgC,OAAA,iBAChC,S7EshJN,S6EphJQ,WAAA,iBAEF,S7EqhJN,S6EnhJQ,YAAA,iBACA,aAAA,YAEF,S7EohJN,S6ElhJQ,cAAA,iBAEF,S7EmhJN,S6EjhJQ,aAAA,iBACA,YAAA,YAjBF,QAAgC,OAAA,kBAChC,S7EsiJN,S6EpiJQ,WAAA,kBAEF,S7EqiJN,S6EniJQ,YAAA,kBACA,aAAA,YAEF,S7EoiJN,S6EliJQ,cAAA,kBAEF,S7EmiJN,S6EjiJQ,aAAA,kBACA,YAAA,YAjBF,QAAgC,OAAA,iBAChC,S7EsjJN,S6EpjJQ,WAAA,iBAEF,S7EqjJN,S6EnjJQ,YAAA,iBACA,aAAA,YAEF,S7EojJN,S6EljJQ,cAAA,iBAEF,S7EmjJN,S6EjjJQ,aAAA,iBACA,YAAA,YAjBF,QAAgC,QAAA,YAChC,S7EskJN,S6EpkJQ,YAAA,YAEF,S7EqkJN,S6EnkJQ,aAAA,YACA,cAAA,YAEF,S7EokJN,S6ElkJQ,eAAA,YAEF,S7EmkJN,S6EjkJQ,cAAA,YACA,aAAA,YAjBF,QAAgC,QAAA,kBAChC,S7EslJN,S6EplJQ,YAAA,kBAEF,S7EqlJN,S6EnlJQ,aAAA,kBACA,cAAA,YAEF,S7EolJN,S6EllJQ,eAAA,kBAEF,S7EmlJN,S6EjlJQ,cAAA,kBACA,aAAA,YAjBF,QAAgC,QAAA,iBAChC,S7EsmJN,S6EpmJQ,YAAA,iBAEF,S7EqmJN,S6EnmJQ,aAAA,iBACA,cAAA,YAEF,S7EomJN,S6ElmJQ,eAAA,iBAEF,S7EmmJN,S6EjmJQ,cAAA,iBACA,aAAA,YAjBF,QAAgC,QAAA,iBAChC,S7EsnJN,S6EpnJQ,YAAA,iBAEF,S7EqnJN,S6EnnJQ,aAAA,iBACA,cAAA,YAEF,S7EonJN,S6ElnJQ,eAAA,iBAEF,S7EmnJN,S6EjnJQ,cAAA,iBACA,aAAA,YAjBF,QAAgC,QAAA,kBAChC,S7EsoJN,S6EpoJQ,YAAA,kBAEF,S7EqoJN,S6EnoJQ,aAAA,kBACA,cAAA,YAEF,S7EooJN,S6EloJQ,eAAA,kBAEF,S7EmoJN,S6EjoJQ,cAAA,kBACA,aAAA,YAjBF,QAAgC,QAAA,iBAChC,S7EspJN,S6EppJQ,YAAA,iBAEF,S7EqpJN,S6EnpJQ,aAAA,iBACA,cAAA,YAEF,S7EopJN,S6ElpJQ,eAAA,iBAEF,S7EmpJN,S6EjpJQ,cAAA,iBACA,aAAA,YAQF,SAAwB,OAAA,mBACxB,U7E6oJN,U6E3oJQ,WAAA,mBAEF,U7E4oJN,U6E1oJQ,aAAA,mBAEF,U7E2oJN,U6EzoJQ,cAAA,mBAEF,U7E0oJN,U6ExoJQ,YAAA,mBAfF,SAAwB,OAAA,kBACxB,U7E2pJN,U6EzpJQ,WAAA,kBAEF,U7E0pJN,U6ExpJQ,aAAA,kBAEF,U7EypJN,U6EvpJQ,cAAA,kBAEF,U7EwpJN,U6EtpJQ,YAAA,kBAfF,SAAwB,OAAA,kBACxB,U7EyqJN,U6EvqJQ,WAAA,kBAEF,U7EwqJN,U6EtqJQ,aAAA,kBAEF,U7EuqJN,U6ErqJQ,cAAA,kBAEF,U7EsqJN,U6EpqJQ,YAAA,kBAfF,SAAwB,OAAA,mBACxB,U7EurJN,U6ErrJQ,WAAA,mBAEF,U7EsrJN,U6EprJQ,aAAA,mBAEF,U7EqrJN,U6EnrJQ,cAAA,mBAEF,U7EorJN,U6ElrJQ,YAAA,mBAfF,SAAwB,OAAA,kBACxB,U7EqsJN,U6EnsJQ,WAAA,kBAEF,U7EosJN,U6ElsJQ,aAAA,kBAEF,U7EmsJN,U6EjsJQ,cAAA,kBAEF,U7EksJN,U6EhsJQ,YAAA,kBAMN,WAAmB,OAAA,eACnB,Y7E8rJF,Y6E5rJI,WAAA,eAEF,Y7E6rJF,Y6E3rJI,YAAA,eACA,aAAA,kBAEF,Y7E4rJF,Y6E1rJI,cAAA,eAEF,Y7E2rJF,Y6EzrJI,aAAA,eACA,YAAA,gB5EbF,yB4ElDI,QAAgC,OAAA,YAChC,S7E8vJN,S6E5vJQ,WAAA,YAEF,S7E6vJN,S6E3vJQ,YAAA,YACA,aAAA,YAEF,S7E4vJN,S6E1vJQ,cAAA,YAEF,S7E2vJN,S6EzvJQ,aAAA,YACA,YAAA,YAjBF,QAAgC,OAAA,kBAChC,S7E8wJN,S6E5wJQ,WAAA,kBAEF,S7E6wJN,S6E3wJQ,YAAA,kBACA,aAAA,YAEF,S7E4wJN,S6E1wJQ,cAAA,kBAEF,S7E2wJN,S6EzwJQ,aAAA,kBACA,YAAA,YAjBF,QAAgC,OAAA,iBAChC,S7E8xJN,S6E5xJQ,WAAA,iBAEF,S7E6xJN,S6E3xJQ,YAAA,iBACA,aAAA,YAEF,S7E4xJN,S6E1xJQ,cAAA,iBAEF,S7E2xJN,S6EzxJQ,aAAA,iBACA,YAAA,YAjBF,QAAgC,OAAA,iBAChC,S7E8yJN,S6E5yJQ,WAAA,iBAEF,S7E6yJN,S6E3yJQ,YAAA,iBACA,aAAA,YAEF,S7E4yJN,S6E1yJQ,cAAA,iBAEF,S7E2yJN,S6EzyJQ,aAAA,iBACA,YAAA,YAjBF,QAAgC,OAAA,kBAChC,S7E8zJN,S6E5zJQ,WAAA,kBAEF,S7E6zJN,S6E3zJQ,YAAA,kBACA,aAAA,YAEF,S7E4zJN,S6E1zJQ,cAAA,kBAEF,S7E2zJN,S6EzzJQ,aAAA,kBACA,YAAA,YAjBF,QAAgC,OAAA,iBAChC,S7E80JN,S6E50JQ,WAAA,iBAEF,S7E60JN,S6E30JQ,YAAA,iBACA,aAAA,YAEF,S7E40JN,S6E10JQ,cAAA,iBAEF,S7E20JN,S6Ez0JQ,aAAA,iBACA,YAAA,YAjBF,QAAgC,QAAA,YAChC,S7E81JN,S6E51JQ,YAAA,YAEF,S7E61JN,S6E31JQ,aAAA,YACA,cAAA,YAEF,S7E41JN,S6E11JQ,eAAA,YAEF,S7E21JN,S6Ez1JQ,cAAA,YACA,aAAA,YAjBF,QAAgC,QAAA,kBAChC,S7E82JN,S6E52JQ,YAAA,kBAEF,S7E62JN,S6E32JQ,aAAA,kBACA,cAAA,YAEF,S7E42JN,S6E12JQ,eAAA,kBAEF,S7E22JN,S6Ez2JQ,cAAA,kBACA,aAAA,YAjBF,QAAgC,QAAA,iBAChC,S7E83JN,S6E53JQ,YAAA,iBAEF,S7E63JN,S6E33JQ,aAAA,iBACA,cAAA,YAEF,S7E43JN,S6E13JQ,eAAA,iBAEF,S7E23JN,S6Ez3JQ,cAAA,iBACA,aAAA,YAjBF,QAAgC,QAAA,iBAChC,S7E84JN,S6E54JQ,YAAA,iBAEF,S7E64JN,S6E34JQ,aAAA,iBACA,cAAA,YAEF,S7E44JN,S6E14JQ,eAAA,iBAEF,S7E24JN,S6Ez4JQ,cAAA,iBACA,aAAA,YAjBF,QAAgC,QAAA,kBAChC,S7E85JN,S6E55JQ,YAAA,kBAEF,S7E65JN,S6E35JQ,aAAA,kBACA,cAAA,YAEF,S7E45JN,S6E15JQ,eAAA,kBAEF,S7E25JN,S6Ez5JQ,cAAA,kBACA,aAAA,YAjBF,QAAgC,QAAA,iBAChC,S7E86JN,S6E56JQ,YAAA,iBAEF,S7E66JN,S6E36JQ,aAAA,iBACA,cAAA,YAEF,S7E46JN,S6E16JQ,eAAA,iBAEF,S7E26JN,S6Ez6JQ,cAAA,iBACA,aAAA,YAQF,SAAwB,OAAA,mBACxB,U7Eq6JN,U6En6JQ,WAAA,mBAEF,U7Eo6JN,U6El6JQ,aAAA,mBAEF,U7Em6JN,U6Ej6JQ,cAAA,mBAEF,U7Ek6JN,U6Eh6JQ,YAAA,mBAfF,SAAwB,OAAA,kBACxB,U7Em7JN,U6Ej7JQ,WAAA,kBAEF,U7Ek7JN,U6Eh7JQ,aAAA,kBAEF,U7Ei7JN,U6E/6JQ,cAAA,kBAEF,U7Eg7JN,U6E96JQ,YAAA,kBAfF,SAAwB,OAAA,kBACxB,U7Ei8JN,U6E/7JQ,WAAA,kBAEF,U7Eg8JN,U6E97JQ,aAAA,kBAEF,U7E+7JN,U6E77JQ,cAAA,kBAEF,U7E87JN,U6E57JQ,YAAA,kBAfF,SAAwB,OAAA,mBACxB,U7E+8JN,U6E78JQ,WAAA,mBAEF,U7E88JN,U6E58JQ,aAAA,mBAEF,U7E68JN,U6E38JQ,cAAA,mBAEF,U7E48JN,U6E18JQ,YAAA,mBAfF,SAAwB,OAAA,kBACxB,U7E69JN,U6E39JQ,WAAA,kBAEF,U7E49JN,U6E19JQ,aAAA,kBAEF,U7E29JN,U6Ez9JQ,cAAA,kBAEF,U7E09JN,U6Ex9JQ,YAAA,kBAMN,WAAmB,OAAA,eACnB,Y7Es9JF,Y6Ep9JI,WAAA,eAEF,Y7Eq9JF,Y6En9JI,YAAA,eACA,aAAA,kBAEF,Y7Eo9JF,Y6El9JI,cAAA,eAEF,Y7Em9JF,Y6Ej9JI,aAAA,eACA,YAAA,gB5EbF,yB4ElDI,QAAgC,OAAA,YAChC,S7EshKN,S6EphKQ,WAAA,YAEF,S7EqhKN,S6EnhKQ,YAAA,YACA,aAAA,YAEF,S7EohKN,S6ElhKQ,cAAA,YAEF,S7EmhKN,S6EjhKQ,aAAA,YACA,YAAA,YAjBF,QAAgC,OAAA,kBAChC,S7EsiKN,S6EpiKQ,WAAA,kBAEF,S7EqiKN,S6EniKQ,YAAA,kBACA,aAAA,YAEF,S7EoiKN,S6EliKQ,cAAA,kBAEF,S7EmiKN,S6EjiKQ,aAAA,kBACA,YAAA,YAjBF,QAAgC,OAAA,iBAChC,S7EsjKN,S6EpjKQ,WAAA,iBAEF,S7EqjKN,S6EnjKQ,YAAA,iBACA,aAAA,YAEF,S7EojKN,S6EljKQ,cAAA,iBAEF,S7EmjKN,S6EjjKQ,aAAA,iBACA,YAAA,YAjBF,QAAgC,OAAA,iBAChC,S7EskKN,S6EpkKQ,WAAA,iBAEF,S7EqkKN,S6EnkKQ,YAAA,iBACA,aAAA,YAEF,S7EokKN,S6ElkKQ,cAAA,iBAEF,S7EmkKN,S6EjkKQ,aAAA,iBACA,YAAA,YAjBF,QAAgC,OAAA,kBAChC,S7EslKN,S6EplKQ,WAAA,kBAEF,S7EqlKN,S6EnlKQ,YAAA,kBACA,aAAA,YAEF,S7EolKN,S6EllKQ,cAAA,kBAEF,S7EmlKN,S6EjlKQ,aAAA,kBACA,YAAA,YAjBF,QAAgC,OAAA,iBAChC,S7EsmKN,S6EpmKQ,WAAA,iBAEF,S7EqmKN,S6EnmKQ,YAAA,iBACA,aAAA,YAEF,S7EomKN,S6ElmKQ,cAAA,iBAEF,S7EmmKN,S6EjmKQ,aAAA,iBACA,YAAA,YAjBF,QAAgC,QAAA,YAChC,S7EsnKN,S6EpnKQ,YAAA,YAEF,S7EqnKN,S6EnnKQ,aAAA,YACA,cAAA,YAEF,S7EonKN,S6ElnKQ,eAAA,YAEF,S7EmnKN,S6EjnKQ,cAAA,YACA,aAAA,YAjBF,QAAgC,QAAA,kBAChC,S7EsoKN,S6EpoKQ,YAAA,kBAEF,S7EqoKN,S6EnoKQ,aAAA,kBACA,cAAA,YAEF,S7EooKN,S6EloKQ,eAAA,kBAEF,S7EmoKN,S6EjoKQ,cAAA,kBACA,aAAA,YAjBF,QAAgC,QAAA,iBAChC,S7EspKN,S6EppKQ,YAAA,iBAEF,S7EqpKN,S6EnpKQ,aAAA,iBACA,cAAA,YAEF,S7EopKN,S6ElpKQ,eAAA,iBAEF,S7EmpKN,S6EjpKQ,cAAA,iBACA,aAAA,YAjBF,QAAgC,QAAA,iBAChC,S7EsqKN,S6EpqKQ,YAAA,iBAEF,S7EqqKN,S6EnqKQ,aAAA,iBACA,cAAA,YAEF,S7EoqKN,S6ElqKQ,eAAA,iBAEF,S7EmqKN,S6EjqKQ,cAAA,iBACA,aAAA,YAjBF,QAAgC,QAAA,kBAChC,S7EsrKN,S6EprKQ,YAAA,kBAEF,S7EqrKN,S6EnrKQ,aAAA,kBACA,cAAA,YAEF,S7EorKN,S6ElrKQ,eAAA,kBAEF,S7EmrKN,S6EjrKQ,cAAA,kBACA,aAAA,YAjBF,QAAgC,QAAA,iBAChC,S7EssKN,S6EpsKQ,YAAA,iBAEF,S7EqsKN,S6EnsKQ,aAAA,iBACA,cAAA,YAEF,S7EosKN,S6ElsKQ,eAAA,iBAEF,S7EmsKN,S6EjsKQ,cAAA,iBACA,aAAA,YAQF,SAAwB,OAAA,mBACxB,U7E6rKN,U6E3rKQ,WAAA,mBAEF,U7E4rKN,U6E1rKQ,aAAA,mBAEF,U7E2rKN,U6EzrKQ,cAAA,mBAEF,U7E0rKN,U6ExrKQ,YAAA,mBAfF,SAAwB,OAAA,kBACxB,U7E2sKN,U6EzsKQ,WAAA,kBAEF,U7E0sKN,U6ExsKQ,aAAA,kBAEF,U7EysKN,U6EvsKQ,cAAA,kBAEF,U7EwsKN,U6EtsKQ,YAAA,kBAfF,SAAwB,OAAA,kBACxB,U7EytKN,U6EvtKQ,WAAA,kBAEF,U7EwtKN,U6EttKQ,aAAA,kBAEF,U7EutKN,U6ErtKQ,cAAA,kBAEF,U7EstKN,U6EptKQ,YAAA,kBAfF,SAAwB,OAAA,mBACxB,U7EuuKN,U6EruKQ,WAAA,mBAEF,U7EsuKN,U6EpuKQ,aAAA,mBAEF,U7EquKN,U6EnuKQ,cAAA,mBAEF,U7EouKN,U6EluKQ,YAAA,mBAfF,SAAwB,OAAA,kBACxB,U7EqvKN,U6EnvKQ,WAAA,kBAEF,U7EovKN,U6ElvKQ,aAAA,kBAEF,U7EmvKN,U6EjvKQ,cAAA,kBAEF,U7EkvKN,U6EhvKQ,YAAA,kBAMN,WAAmB,OAAA,eACnB,Y7E8uKF,Y6E5uKI,WAAA,eAEF,Y7E6uKF,Y6E3uKI,YAAA,eACA,aAAA,kBAEF,Y7E4uKF,Y6E1uKI,cAAA,eAEF,Y7E2uKF,Y6EzuKI,aAAA,eACA,YAAA,gB5EbF,0B4ElDI,QAAgC,OAAA,YAChC,S7E8yKN,S6E5yKQ,WAAA,YAEF,S7E6yKN,S6E3yKQ,YAAA,YACA,aAAA,YAEF,S7E4yKN,S6E1yKQ,cAAA,YAEF,S7E2yKN,S6EzyKQ,aAAA,YACA,YAAA,YAjBF,QAAgC,OAAA,kBAChC,S7E8zKN,S6E5zKQ,WAAA,kBAEF,S7E6zKN,S6E3zKQ,YAAA,kBACA,aAAA,YAEF,S7E4zKN,S6E1zKQ,cAAA,kBAEF,S7E2zKN,S6EzzKQ,aAAA,kBACA,YAAA,YAjBF,QAAgC,OAAA,iBAChC,S7E80KN,S6E50KQ,WAAA,iBAEF,S7E60KN,S6E30KQ,YAAA,iBACA,aAAA,YAEF,S7E40KN,S6E10KQ,cAAA,iBAEF,S7E20KN,S6Ez0KQ,aAAA,iBACA,YAAA,YAjBF,QAAgC,OAAA,iBAChC,S7E81KN,S6E51KQ,WAAA,iBAEF,S7E61KN,S6E31KQ,YAAA,iBACA,aAAA,YAEF,S7E41KN,S6E11KQ,cAAA,iBAEF,S7E21KN,S6Ez1KQ,aAAA,iBACA,YAAA,YAjBF,QAAgC,OAAA,kBAChC,S7E82KN,S6E52KQ,WAAA,kBAEF,S7E62KN,S6E32KQ,YAAA,kBACA,aAAA,YAEF,S7E42KN,S6E12KQ,cAAA,kBAEF,S7E22KN,S6Ez2KQ,aAAA,kBACA,YAAA,YAjBF,QAAgC,OAAA,iBAChC,S7E83KN,S6E53KQ,WAAA,iBAEF,S7E63KN,S6E33KQ,YAAA,iBACA,aAAA,YAEF,S7E43KN,S6E13KQ,cAAA,iBAEF,S7E23KN,S6Ez3KQ,aAAA,iBACA,YAAA,YAjBF,QAAgC,QAAA,YAChC,S7E84KN,S6E54KQ,YAAA,YAEF,S7E64KN,S6E34KQ,aAAA,YACA,cAAA,YAEF,S7E44KN,S6E14KQ,eAAA,YAEF,S7E24KN,S6Ez4KQ,cAAA,YACA,aAAA,YAjBF,QAAgC,QAAA,kBAChC,S7E85KN,S6E55KQ,YAAA,kBAEF,S7E65KN,S6E35KQ,aAAA,kBACA,cAAA,YAEF,S7E45KN,S6E15KQ,eAAA,kBAEF,S7E25KN,S6Ez5KQ,cAAA,kBACA,aAAA,YAjBF,QAAgC,QAAA,iBAChC,S7E86KN,S6E56KQ,YAAA,iBAEF,S7E66KN,S6E36KQ,aAAA,iBACA,cAAA,YAEF,S7E46KN,S6E16KQ,eAAA,iBAEF,S7E26KN,S6Ez6KQ,cAAA,iBACA,aAAA,YAjBF,QAAgC,QAAA,iBAChC,S7E87KN,S6E57KQ,YAAA,iBAEF,S7E67KN,S6E37KQ,aAAA,iBACA,cAAA,YAEF,S7E47KN,S6E17KQ,eAAA,iBAEF,S7E27KN,S6Ez7KQ,cAAA,iBACA,aAAA,YAjBF,QAAgC,QAAA,kBAChC,S7E88KN,S6E58KQ,YAAA,kBAEF,S7E68KN,S6E38KQ,aAAA,kBACA,cAAA,YAEF,S7E48KN,S6E18KQ,eAAA,kBAEF,S7E28KN,S6Ez8KQ,cAAA,kBACA,aAAA,YAjBF,QAAgC,QAAA,iBAChC,S7E89KN,S6E59KQ,YAAA,iBAEF,S7E69KN,S6E39KQ,aAAA,iBACA,cAAA,YAEF,S7E49KN,S6E19KQ,eAAA,iBAEF,S7E29KN,S6Ez9KQ,cAAA,iBACA,aAAA,YAQF,SAAwB,OAAA,mBACxB,U7Eq9KN,U6En9KQ,WAAA,mBAEF,U7Eo9KN,U6El9KQ,aAAA,mBAEF,U7Em9KN,U6Ej9KQ,cAAA,mBAEF,U7Ek9KN,U6Eh9KQ,YAAA,mBAfF,SAAwB,OAAA,kBACxB,U7Em+KN,U6Ej+KQ,WAAA,kBAEF,U7Ek+KN,U6Eh+KQ,aAAA,kBAEF,U7Ei+KN,U6E/9KQ,cAAA,kBAEF,U7Eg+KN,U6E99KQ,YAAA,kBAfF,SAAwB,OAAA,kBACxB,U7Ei/KN,U6E/+KQ,WAAA,kBAEF,U7Eg/KN,U6E9+KQ,aAAA,kBAEF,U7E++KN,U6E7+KQ,cAAA,kBAEF,U7E8+KN,U6E5+KQ,YAAA,kBAfF,SAAwB,OAAA,mBACxB,U7E+/KN,U6E7/KQ,WAAA,mBAEF,U7E8/KN,U6E5/KQ,aAAA,mBAEF,U7E6/KN,U6E3/KQ,cAAA,mBAEF,U7E4/KN,U6E1/KQ,YAAA,mBAfF,SAAwB,OAAA,kBACxB,U7E6gLN,U6E3gLQ,WAAA,kBAEF,U7E4gLN,U6E1gLQ,aAAA,kBAEF,U7E2gLN,U6EzgLQ,cAAA,kBAEF,U7E0gLN,U6ExgLQ,YAAA,kBAMN,WAAmB,OAAA,eACnB,Y7EsgLF,Y6EpgLI,WAAA,eAEF,Y7EqgLF,Y6EngLI,YAAA,eACA,aAAA,kBAEF,Y7EogLF,Y6ElgLI,cAAA,eAEF,Y7EmgLF,Y6EjgLI,aAAA,eACA,YAAA,gBCnEF,YAAwB,MAAA,gBACxB,aAAwB,MAAA,eACxB,YAAwB,MAAA,e7EoDxB,yB6EtDA,eAAwB,MAAA,gBACxB,gBAAwB,MAAA,eACxB,eAAwB,MAAA,gB7EoDxB,yB6EtDA,eAAwB,MAAA,gBACxB,gBAAwB,MAAA,eACxB,eAAwB,MAAA,gB7EoDxB,yB6EtDA,eAAwB,MAAA,gBACxB,gBAAwB,MAAA,eACxB,eAAwB,MAAA,gB7EoDxB,0B6EtDA,eAAwB,MAAA,gBACxB,gBAAwB,MAAA,eACxB,eAAwB,MAAA,gBCF5B,gBAAkB,YAAA,cAAA,CAAA,KAAA,CAAA,MAAA,CAAA,QAAA,CAAA,iBAAA,CAAA,aAAA,CAAA,oBAIlB,cAAiB,WAAA,kBACjB,WAAiB,YAAA,iBACjB,aAAiB,YAAA,iBACjB,eCTE,SAAA,OACA,cAAA,SACA,YAAA,ODeE,WAAwB,WAAA,gBACxB,YAAwB,WAAA,eACxB,aAAwB,WAAA,iB9EqCxB,yB8EvCA,cAAwB,WAAA,gBACxB,eAAwB,WAAA,eACxB,gBAAwB,WAAA,kB9EqCxB,yB8EvCA,cAAwB,WAAA,gBACxB,eAAwB,WAAA,eACxB,gBAAwB,WAAA,kB9EqCxB,yB8EvCA,cAAwB,WAAA,gBACxB,eAAwB,WAAA,eACxB,gBAAwB,WAAA,kB9EqCxB,0B8EvCA,cAAwB,WAAA,gBACxB,eAAwB,WAAA,eACxB,gBAAwB,WAAA,kBAM5B,gBAAmB,eAAA,oBACnB,gBAAmB,eAAA,oBACnB,iBAAmB,eAAA,qBAInB,mBAAuB,YAAA,cACvB,qBAAuB,YAAA,kBACvB,oBAAuB,YAAA,cACvB,kBAAuB,YAAA,cACvB,oBAAuB,YAAA,iBACvB,aAAuB,WAAA,iBAIvB,YAAc,MAAA,eEvCZ,cACE,MAAA,kBCUF,qBAAA,qBDLM,MAAA,kBANN,gBACE,MAAA,kBCUF,uBAAA,uBDLM,MAAA,kBANN,cACE,MAAA,kBCUF,qBAAA,qBDLM,MAAA,kBANN,WACE,MAAA,kBCUF,kBAAA,kBDLM,MAAA,kBANN,cACE,MAAA,kBCUF,qBAAA,qBDLM,MAAA,kBANN,aACE,MAAA,kBCUF,oBAAA,oBDLM,MAAA,kBANN,YACE,MAAA,kBCUF,mBAAA,mBDLM,MAAA,kBANN,WACE,MAAA,kBCUF,kBAAA,kBDLM,MAAA,kBANN,WACE,MAAA,kBCUF,kBAAA,kBDLM,MAAA,kBANN,WACE,MAAA,kBCUF,kBAAA,kBDLM,MAAA,kBFuCR,WAAa,MAAA,kBACb,YAAc,MAAA,kBAEd,eAAiB,MAAA,yBACjB,eAAiB,MAAA,+BAIjB,WIvDE,KAAA,CAAA,CAAA,EAAA,EACA,MAAA,YACA,YAAA,KACA,iBAAA,YACA,OAAA,EJuDF,sBAAwB,gBAAA,eAExB,YACE,WAAA,qBACA,cAAA,qBAKF,YAAc,MAAA,kBKjEd,UACI,MAAA,MAGJ,eACI,QAAA,EAAA,EAAA,EAAA,KADJ,+BAKY,MAAA,MALZ,sCASY,UAAA,IATZ,2BAeQ,YAAA,KAfR,yCAkBY,cAAA,KACA,aAAA,EACA,cAAA,EAAA,KAAA,KAAA,YApBZ,+CAwBY,aAAA,EAxBZ,gCA4BY,cAAA,KAAA,EAAA,EAAA,eAOZ,oCAGQ,KAAA,KACA,MAAA,KAJR,gCAQQ,QAAA,KAAA,KARR,6CAWY,MAAA,MACA,YAAA,KACA,aAAA,EAbZ,gDpF2xLE,0CoFzwLU,YAAA,EACA,aAAA,KAnBZ,kDAyBY,QAAA,IAAA,KAKZ,oBAEQ,eAAA,OACA,OAAA,IAAA,EAAA,EAAA,KACA,MAAA,MAMR,kCAEQ,MAAA,KAOR,cACI,aAAA,MACA,YAAA,EAIJ,wBAMoB,OAAA,EAAA,IAAA,EAAA,KANpB,kCAUoB,MAAA,KAVpB,oCAawB,YAAA,EAbxB,uBAmBgB,cAAA,KACA,aAAA,EApBhB,0BAuBoB,cAAA,KACA,aAAA,EAxBpB,0BA+BQ,KAAA,KACA,MAAA,KAhCR,iCAmCY,QAAA,QAnCZ,0CA0CgB,kBAAA,eAAA,UAAA,eAOhB,kDAa4B,YAAA,KACA,aAAA,IAd5B,qDAkB4B,cAAA,KACA,aAAA,EAnB5B,uDA0B4B,MAAA,KACA,KAAA,KA3B5B,0DAqCgC,MAAA,MACA,WAAA,MAtChC,mEA4CgC,KAAA,KACA,MAAA,EA7ChC,wBAwDQ,aAAA,eACA,YAAA,YAzDR,kBA8DQ,KAAA,YACA,MAAA,enF3IJ,4BmFiJA,cpFwrLF,wBoFtrLM,aAAA,aAQR,uDAMoB,cAAA,EANpB,iDAYY,aAAA,MACA,YAAA,EAbZ,yDAiBY,KAAA,KACA,MAAA,MAKZ,qDAGY,WAAA,MAHZ,+DAS4B,aAAA,IACA,YAAA,KAU5B,mBACI,YAAA,EACA,aAAA,GAMJ,QACI,KAAA,EACA,MAAA,MAGJ,YACI,MAAA,YnFpNA,4BmFwNA,QACI,MAAA,aAOR,WACI,MAAA,eACA,KAAA,OACA,MAAA,KAHJ,0CAQgB,MAAA,EACA,KAAA,KAMhB,8BAEQ,KAAA,EACA,MAAA,KCpTR,aACI,MAAA,MAMJ,gBAEQ,MAAA,MAFR,kBAMQ,cAAA,KACA,aAAA,EAIR,qBAEQ,MAAA,MAOR,2EAGY,cAAA,KAHZ,wEAOY,KAAA,IACA,MAAA,KARZ,2EAcY,MAAA,MACA,YAAA,IACA,aAAA,EAhBZ,2CAqBQ,MAAA,MAOR,iCAEQ,QAAA,IAAA,IAAA,EAAA,EAOR,0CAGY,MAAA,KAHZ,kDAOY,WAAA,MAPZ,wDAWY,cAAA,EACA,aAAA,QAQZ,wBAEQ,aAAA,EACA,cAAA,KAHR,+BAMY,KAAA,KACA,MAAA,IAQZ,mEACI,KAAA,EACA,MAAA,KAGJ,WACI,UAAA,IACA,WAAA,MAMJ,kBACI,YAAA,EACA,aAAA,IAFJ,mCAKQ,YAAA,EACA,aAAA,IAOR,sDACI,aAAA,IACA,cAAA,EC/HJ,2BAGM,MAAA,EACA,KAAA,KAJN,+BAQM,aAAA,MACA,YAAA,KACA,cAAA,IAAA,EAAA,EAAA,IAVN,gCAcM,YAAA,MACA,aAAA,EACA,cAAA,EAAA,IAAA,IAAA,EAhBN,uCAmBQ,KAAA,EACA,MAAA,KApBR,wBA2BI,MAAA,KACA,KAAA,KACA,WAAA,KA7BJ,6BAgCM,kBAAA,cAAA,UAAA,cACA,MAAA,MACA,KAAA,KAQN,0CAGM,MAAA,MACA,YAAA,KACA,aAAA,EALN,2CASM,MAAA,KACA,KAAA,IAQN,gCAEI,MAAA,MAFJ,qCAOM,KAAA,IACA,MAAA,KACA,YAAA,EACA,aAAA,KACA,mBAAA,YACA,kBAAA,QAZN,sCAiBI,MAAA,MACA,aAAA,KACA,YAAA,EAnBJ,qCAwBM,MAAA,eAxBN,2CA4BM,MAAA,eACA,aAAA,EACA,YAAA,KACA,WAAA,KA/BN,0CAoCQ,aAAA,YACA,kBAAA,YACA,mBAAA,QACA,iBAAA,QACA,KAAA,eACA,MAAA,cASR,gBAEI,cAAA,IACA,aAAA,EAHJ,wBAMM,KAAA,KACA,MAAA,EACA,YAAA,EACA,aAAA,MATN,uBAaM,KAAA,KACA,MAAA,EACA,aAAA,MACA,YAAA,EACA,aAAA,EACA,cAAA,IAlBN,oDAwBM,KAAA,KACA,MAAA,IACA,kBAAA,cAAA,UAAA,cAKN,uCAGM,aAAA,EAHN,sCAOM,aAAA,EAQN,aAEI,aAAA,EACA,cAAA,IAHJ,qBAMM,KAAA,KACA,MAAA,EACA,YAAA,EACA,aAAA,MATN,oBAaM,KAAA,EACA,MAAA,IACA,YAAA,EACA,aAAA,MC7KN,oBACI,aAAA,KACA,YAAA,EACA,WAAA,IAMJ,eACI,MAAA,MACA,QAAA,EAAA,KAAA,KAAA,KAGJ,gBACI,OAAA,QAAA,MAAA,QAAA,EACA,aAAA,IAAA,MAAA,QACA,YAAA,EACA,QAAA,OAAA,KAAA,OAAA,EAGJ,cACI,QAAA,MACA,cAAA,EAFJ,2BAKY,MAAA,MALZ,6BAQY,MAAA,MvFymMR,oDACA,kCuFlnMJ,0CAYgB,MAAA,MAZhB,oDAegB,OAAA,KAAA,KAAA,EAAA,KAfhB,0CAkBgB,aAAA,KACA,YAAA,EAnBhB,oCAsBgB,MAAA,MACA,KAAA,EAvBhB,6BA2BY,MAAA,MACA,KAAA,EA5BZ,sCA8BgB,MAAA,EACA,KAAA,MA/BhB,mCAkCgB,KAAA,EACA,MAAA,KACA,cAAA,KACA,aAAA,EAMhB,yBACI,gBACI,aAAA,EACA,OAAA,EACA,cAAA,EAEJ,oDAGY,aAAA,GAMhB,yBAEI,oCAGY,MAAA,KAHZ,6BAOQ,MAAA,MAPR,mCASY,WAAA,KACA,aAAA,KACA,cAAA,MAShB,YAEQ,aAAA,KACA,YAAA,EAHR,iBAQgB,YAAA,EACA,aAAA,QAThB,sBAagB,YAAA,YACA,aAAA,IAAA,MAAA,qBAShB,oBACI,MAAA,MAGJ,cACI,YAAA,EACA,aAAA,KAGJ,YACI,YAAA,EACA,aAAA", "file": "app-rtl.min.css", "sourcesContent": ["//\r\n// Google font - Poppins\r\n//\r\n\r\n@import url('https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700');\r\n\r\n\r\n//\r\n// Premium font - Cerebri Sans\r\n//\r\n\r\n@font-face {\r\n    font-family: \"Cerebri Sans,sans-serif\";\r\n    src: url(\"../fonts/cerebrisans-light.eot\");\r\n    src: local('Cerebri-sans Light'), url(\"../fonts/cerebrisans-light.woff\") format(\"woff\");\r\n    font-weight: 300;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"Cerebri Sans,sans-serif\";\r\n    src: url(\"../fonts/cerebrisans-regular.eot\");\r\n    src: local('Cerebri-sans Regular'), url(\"../fonts/cerebrisans-regular.woff\") format(\"woff\");\r\n    font-weight: 400;\r\n}\r\n@font-face {\r\n    font-family: \"Cerebri Sans,sans-serif\";\r\n    src: url(\"../fonts/cerebrisans-medium.eot\");\r\n    src: local('Cerebri-sans Medium'), url(\"../fonts/cerebrisans-medium.woff\") format(\"woff\");\r\n    font-weight: 500;\r\n}\r\n@font-face {\r\n    font-family: \"Cerebri Sans,sans-serif\";\r\n    src: url(\"../fonts/cerebrisans-semibold.eot\");\r\n    src: local('Cerebri-sans Semibold'), url(\"../fonts/cerebrisans-semibold.woff\") format(\"woff\");\r\n    font-weight: 600;\r\n}\r\n@font-face {\r\n    font-family: \"Cerebri Sans,sans-serif\";\r\n    src: url(\"../fonts/cerebrisans-bold.eot\");\r\n    src: local('Cerebri-sans Bold'), url(\"../fonts/cerebrisans-bold.woff\") format(\"woff\");\r\n    font-weight: 700;\r\n}\r\n", "// \r\n// general.scss\r\n//\r\n\r\nhtml {\r\n  position: relative;\r\n  min-height: 100%;\r\n}\r\n\r\nbody {\r\n  padding-bottom: 60px;\r\n  overflow-x: hidden;\r\n}", "// \r\n// menu.scss\r\n//\r\n\r\n// Metis Menu Overwrite\r\n\r\n.metismenu {\r\n    padding: 0;\r\n\r\n    li {\r\n        list-style: none;\r\n    }\r\n    ul {\r\n        padding: 0;\r\n        li {\r\n            width: 100%;\r\n        }\r\n    }\r\n}\r\n\r\n.nav-second-level,\r\n.nav-thrid-level {\r\n    li {\r\n        a {\r\n            padding: 8px 20px;\r\n            color: $menu-item;\r\n            display: block;\r\n            position: relative;\r\n            transition: all 0.4s;\r\n            font-size: 0.812rem;\r\n            &:focus,\r\n            &:hover {\r\n                color: $menu-item-hover;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.nav-second-level,\r\n.nav-third-level {\r\n    li.active {\r\n        >a {\r\n            color: $menu-item-active;\r\n        }\r\n    }\r\n}\r\n\r\n// Wrapper\r\n#wrapper {\r\n    height: 100%;\r\n    overflow: hidden;\r\n    width: 100%;\r\n}\r\n\r\n//Content Page\r\n.content-page {\r\n    margin-left: $leftbar-width;\r\n    overflow: hidden;\r\n    padding: 0 15px 5px 15px;\r\n    min-height: 80vh;\r\n    margin-top: $topbar-height;\r\n}\r\n\r\n// Sidemenu\r\n.left-side-menu {\r\n    width: $leftbar-width;\r\n    background: $bg-leftbar;\r\n    bottom: 0;\r\n    padding: 20px 0;\r\n    position: fixed;\r\n    transition: all .2s ease-out;\r\n    top: $topbar-height;\r\n    box-shadow: $shadow;\r\n}\r\n\r\n// Sidebar\r\n#sidebar-menu {\r\n    >ul {\r\n        >li {\r\n            >a {\r\n                color: $menu-item;\r\n                display: block;\r\n                padding: 13px 20px;\r\n                position: relative;\r\n                transition: all 0.4s;\r\n                font-family: $font-family-secondary;\r\n                font-size: .875rem;\r\n\r\n                &:hover,\r\n                &:focus,\r\n                &:active {\r\n                    color: $menu-item-hover;\r\n                    text-decoration: none;\r\n                }\r\n                >span {\r\n                    vertical-align: middle;\r\n                }\r\n\r\n                i {\r\n                    display: inline-block;\r\n                    line-height: 1.0625rem;\r\n                    margin: 0 10px 0 3px;\r\n                    text-align: center;\r\n                    vertical-align: middle;\r\n                    width: 20px;\r\n                }\r\n                .drop-arrow {\r\n                    float: right;\r\n                    i {\r\n                        margin-right: 0;\r\n                    }\r\n                }\r\n            }\r\n            > a.active {\r\n                color: $menu-item-active;\r\n            }\r\n\r\n            > ul {\r\n                padding-left: 40px;\r\n\r\n                ul {\r\n                    padding-left: 20px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .menu-arrow {\r\n        transition: transform .15s;\r\n        position: absolute;\r\n        right: 20px;\r\n        display: inline-block;\r\n        font-family: 'Material Design Icons';\r\n        text-rendering: auto;\r\n        line-height: 1.5rem;\r\n        font-size: 1.1rem;\r\n        transform: translate(0, 0);\r\n        &:before {\r\n            content: \"\\F142\";\r\n        }\r\n    }\r\n    .badge{\r\n        margin-top: 4px;\r\n    }\r\n\r\n    li.active {\r\n        > a {\r\n            > span.menu-arrow {\r\n                transform: rotate(90deg);\r\n            }\r\n        }\r\n    }\r\n\r\n    .menu-title {\r\n        padding: 10px 20px;\r\n        letter-spacing: .05em;\r\n        pointer-events: none;\r\n        cursor: default;\r\n        font-size: 0.6875rem;\r\n        text-transform: uppercase;\r\n        color: $menu-item;\r\n        font-weight: $font-weight-semibold;\r\n    }\r\n}\r\n\r\n// Enlarge menu\r\n.enlarged {\r\n\r\n    .logo-box {\r\n        width: $leftbar-width-collapsed !important;\r\n    }\r\n\r\n    .logo {\r\n        span.logo-lg {\r\n            display: none;\r\n        }\r\n        span.logo-sm {\r\n            display: block;\r\n        }\r\n    }\r\n    // Side menu\r\n    .left-side-menu {\r\n        position: absolute;\r\n        padding-top: 0;\r\n        width: $leftbar-width-collapsed !important;\r\n        z-index: 5;\r\n\r\n        .slimScrollDiv,\r\n        .slimscroll-menu {\r\n            overflow: inherit !important;\r\n            height: auto !important;\r\n        }\r\n        .slimScrollBar {\r\n            visibility: hidden;\r\n        }\r\n\r\n        // Sidebar Menu\r\n        #sidebar-menu {\r\n            .menu-title,\r\n            .menu-arrow,\r\n            .label,\r\n            .badge,\r\n            .collapse.in {\r\n                display: none !important;\r\n            }\r\n            .nav.collapse {\r\n                height: inherit !important;\r\n            }\r\n            \r\n            > ul {\r\n                > li {\r\n                    position: relative;\r\n                    white-space: nowrap;\r\n\r\n                    > a {\r\n                        padding: 15px 20px;\r\n                        min-height: 54px;\r\n            \r\n                        &:hover,\r\n                        &:active,\r\n                        &:focus {\r\n                            color: $menu-item-hover;\r\n                        }\r\n                        i {\r\n                            font-size: 1.125rem;\r\n                            margin-right: 20px;\r\n                        }\r\n\r\n                        span {\r\n                            display: none;\r\n                            padding-left: 25px;\r\n                        }\r\n                    }\r\n                    \r\n                    &:hover  {\r\n                        > a {\r\n                            position: relative;\r\n                            width: calc(190px + #{$leftbar-width-collapsed});\r\n                            color: $menu-item-active;\r\n                            background-color: $bg-leftbar;\r\n                            transition: none;\r\n\r\n                            span {\r\n                                display: inline;\r\n                            }\r\n                        }\r\n\r\n                        a.open,a.active {\r\n                            :after {\r\n                                display: none;\r\n                            }\r\n                        }\r\n\r\n                        > ul {\r\n                            display: block;\r\n                            left: $leftbar-width-collapsed;\r\n                            position: absolute;\r\n                            width: 190px;\r\n                            height: auto !important;\r\n                            box-shadow: 3px 5px 10px 0 rgba(154,161,171,.2);\r\n\r\n                            ul {\r\n                                box-shadow: 3px 5px 10px 0 rgba(154,161,171,.2);\r\n                            }\r\n                            a {\r\n                                box-shadow: none;\r\n                                padding: 8px 20px;\r\n                                position: relative;\r\n                                width: 190px;\r\n                                z-index: 6;\r\n                                &:hover {\r\n                                    color: $menu-item-hover;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul {\r\n                    padding: 5px 0;\r\n                    z-index: 9999;\r\n                    background-color: $bg-leftbar;\r\n\r\n                    li {\r\n                        &:hover {\r\n                            > ul {\r\n                                display: block;\r\n                                left: 190px;\r\n                                margin-top: -36px;\r\n                                position: absolute;\r\n                                width: 190px;\r\n                            }\r\n                        }\r\n\r\n                        > a {\r\n                            span.pull-right {\r\n                                position: absolute;\r\n                                right: 20px;\r\n                                top: 12px;\r\n                                transform: rotate(270deg);\r\n                            }\r\n                        }\r\n                    }\r\n                    li.active {\r\n                        a {\r\n                            color: $menu-item-active;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // Content Page\r\n    .content-page {\r\n        margin-left: $leftbar-width-collapsed !important;\r\n    }\r\n\r\n    //Footer\r\n    .footer {\r\n        left: $leftbar-width-collapsed !important;\r\n    }\r\n\r\n    //User box\r\n    .user-box {\r\n        display: none;\r\n    }\r\n}\r\n\r\n// Body min-height set\r\nbody.enlarged {\r\n    min-height: 1200px;\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n    body {\r\n        overflow-x: hidden;\r\n        padding-bottom: 80px;\r\n    }\r\n    .left-side-menu {\r\n        display: none;\r\n        z-index: 10 !important;\r\n    }\r\n    .sidebar-enable {\r\n        .left-side-menu {\r\n            display: block;\r\n        }\r\n    }\r\n    .content-page,.enlarged .content-page {\r\n        margin-left: 0 !important;\r\n        padding: 0 10px;\r\n    }\r\n    .pro-user-name {\r\n        display: none;\r\n    }\r\n    .logo-box {\r\n        display: none;\r\n    }\r\n}\r\n\r\n/* =============\r\n  Small Menu\r\n============= */\r\n\r\n.left-side-menu-sm {\r\n    .logo-box {\r\n        width: $leftbar-width-sm;\r\n    }\r\n    .left-side-menu {\r\n        width: $leftbar-width-sm;\r\n        text-align: center;\r\n        #sidebar-menu {\r\n            > ul {\r\n\r\n                > li {\r\n                    > a {\r\n                        > i {\r\n                            display: block;\r\n                            font-size: 18px;\r\n                            line-height: 24px;\r\n                            width: 100%;\r\n                            margin: 0;\r\n                        }\r\n                    }\r\n                }\r\n                ul  {\r\n                    padding-left: 0;\r\n                    a {\r\n                        padding: 10px 20px;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .menu-arrow,\r\n        .badge {\r\n            display: none !important;\r\n        }\r\n        &+.content-page {\r\n            margin-left: $leftbar-width-sm;\r\n        }\r\n        + .content-page .footer {\r\n            left: $leftbar-width-sm;\r\n        }\r\n\r\n        .menu-title {\r\n            background-color: $gray-100;\r\n        }\r\n    }\r\n}\r\n\r\n.enlarged.left-side-menu-sm {\r\n    #wrapper {\r\n        .left-side-menu {\r\n            text-align: left;\r\n\r\n            ul {\r\n                li {\r\n                    a {\r\n                        i {\r\n                            display: inline-block;\r\n                            font-size: 18px;\r\n                            line-height: 17px;\r\n                            margin-left: 3px;\r\n                            margin-right: 15px;\r\n                            vertical-align: middle;\r\n                            width: 20px;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Leftbar-dark\r\n.left-side-menu-dark {\r\n    \r\n    .navbar-custom {\r\n        background-color: $blue;\r\n    }\r\n\r\n    .left-side-menu {\r\n        background-color: $bg-leftbar-dark;\r\n        box-shadow: none;\r\n        border-right: 2px solid lighten($bg-leftbar-dark,2%);\r\n\r\n        #sidebar-menu {\r\n            > ul {\r\n                > li{\r\n                    > a {\r\n                        color: $menu-item-color-dark;\r\n        \r\n                        &:hover,\r\n                        &:focus,\r\n                        &:active {\r\n                            color: $menu-item-hover-color-dark;\r\n                        }\r\n                    }\r\n                    > a.active {\r\n                        color: $menu-item-active-color-dark;\r\n                        background-color: lighten($bg-leftbar-dark, 5%);\r\n                        border-right-color: $menu-item-active-color-dark;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .menu-title {\r\n                color: $gray-500;\r\n            }\r\n        }\r\n\r\n        .nav-second-level,\r\n        .nav-thrid-level {\r\n            li {\r\n                a {\r\n                    color: $menu-item-color-dark;\r\n                    &:focus,\r\n                    &:hover {\r\n                        background-color: transparent;\r\n                        color: $menu-item-hover-color-dark;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        .nav-second-level,\r\n        .nav-third-level {\r\n            li.active {\r\n                >a {\r\n                    color: $menu-item-active-color-dark;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.enlarged.left-side-menu-dark #wrapper {\r\n    .left-side-menu {\r\n        #sidebar-menu {\r\n            > ul {\r\n                > li {\r\n                    &:hover >a {\r\n                        background-color: lighten($bg-leftbar-dark,5%);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Leftbar with user\r\n.user-pro-dropdown {\r\n    background-color: $gray-100;\r\n    box-shadow: none;\r\n    padding: 15px 5px;\r\n    width: 90%;\r\n    margin-left: 5%;\r\n    margin-top: 10px;\r\n\r\n    .dropdown-item {\r\n        border-radius: 3px;\r\n        \r\n        &:hover {\r\n            background-color: $primary;\r\n            color: $white;\r\n        }\r\n    }\r\n}", "/*\r\nTemplate Name: Ubold - Responsive Bootstrap 4 Admin Dashboard\r\nAuthor: CoderThemes\r\nVersion: 3.1.0\r\nWebsite: https://coderthemes.com/\r\nContact: <EMAIL>\r\nFile: Main Css File\r\n*/\n@import url(\"https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700\");\n@font-face {\n  font-family: \"Cerebri Sans,sans-serif\";\n  src: url(\"../fonts/cerebrisans-light.eot\");\n  src: local(\"Cerebri-sans Light\"), url(\"../fonts/cerebrisans-light.woff\") format(\"woff\");\n  font-weight: 300; }\n\n@font-face {\n  font-family: \"Cerebri Sans,sans-serif\";\n  src: url(\"../fonts/cerebrisans-regular.eot\");\n  src: local(\"Cerebri-sans Regular\"), url(\"../fonts/cerebrisans-regular.woff\") format(\"woff\");\n  font-weight: 400; }\n\n@font-face {\n  font-family: \"Cerebri Sans,sans-serif\";\n  src: url(\"../fonts/cerebrisans-medium.eot\");\n  src: local(\"Cerebri-sans Medium\"), url(\"../fonts/cerebrisans-medium.woff\") format(\"woff\");\n  font-weight: 500; }\n\n@font-face {\n  font-family: \"Cerebri Sans,sans-serif\";\n  src: url(\"../fonts/cerebrisans-semibold.eot\");\n  src: local(\"Cerebri-sans Semibold\"), url(\"../fonts/cerebrisans-semibold.woff\") format(\"woff\");\n  font-weight: 600; }\n\n@font-face {\n  font-family: \"Cerebri Sans,sans-serif\";\n  src: url(\"../fonts/cerebrisans-bold.eot\");\n  src: local(\"Cerebri-sans Bold\"), url(\"../fonts/cerebrisans-bold.woff\") format(\"woff\");\n  font-weight: 700; }\n\nhtml {\n  position: relative;\n  min-height: 100%; }\n\nbody {\n  padding-bottom: 60px;\n  overflow-x: hidden; }\n\n.metismenu {\n  padding: 0; }\n  .metismenu li {\n    list-style: none; }\n  .metismenu ul {\n    padding: 0; }\n    .metismenu ul li {\n      width: 100%; }\n\n.nav-second-level li a,\n.nav-thrid-level li a {\n  padding: 8px 20px;\n  color: #6e768e;\n  display: block;\n  position: relative;\n  transition: all 0.4s;\n  font-size: 0.812rem; }\n  .nav-second-level li a:focus, .nav-second-level li a:hover,\n  .nav-thrid-level li a:focus,\n  .nav-thrid-level li a:hover {\n    color: #7e57c2; }\n\n.nav-second-level li.active > a,\n.nav-third-level li.active > a {\n  color: #7e57c2; }\n\n#wrapper {\n  height: 100%;\n  overflow: hidden;\n  width: 100%; }\n\n.content-page {\n  margin-left: 240px;\n  overflow: hidden;\n  padding: 0 15px 5px 15px;\n  min-height: 80vh;\n  margin-top: 70px; }\n\n.left-side-menu {\n  width: 240px;\n  background: #ffffff;\n  bottom: 0;\n  padding: 20px 0;\n  position: fixed;\n  transition: all .2s ease-out;\n  top: 70px;\n  box-shadow: 0 2px 6px -2px rgba(0, 0, 0, 0.16), 0 2px 6px 0 rgba(0, 0, 0, 0.12); }\n\n#sidebar-menu > ul > li > a {\n  color: #6e768e;\n  display: block;\n  padding: 13px 20px;\n  position: relative;\n  transition: all 0.4s;\n  font-family: \"Poppins\", sans-serif;\n  font-size: .875rem; }\n  #sidebar-menu > ul > li > a:hover, #sidebar-menu > ul > li > a:focus, #sidebar-menu > ul > li > a:active {\n    color: #7e57c2;\n    text-decoration: none; }\n  #sidebar-menu > ul > li > a > span {\n    vertical-align: middle; }\n  #sidebar-menu > ul > li > a i {\n    display: inline-block;\n    line-height: 1.0625rem;\n    margin: 0 10px 0 3px;\n    text-align: center;\n    vertical-align: middle;\n    width: 20px; }\n  #sidebar-menu > ul > li > a .drop-arrow {\n    float: right; }\n    #sidebar-menu > ul > li > a .drop-arrow i {\n      margin-right: 0; }\n\n#sidebar-menu > ul > li > a.active {\n  color: #7e57c2; }\n\n#sidebar-menu > ul > li > ul {\n  padding-left: 40px; }\n  #sidebar-menu > ul > li > ul ul {\n    padding-left: 20px; }\n\n#sidebar-menu .menu-arrow {\n  transition: transform .15s;\n  position: absolute;\n  right: 20px;\n  display: inline-block;\n  font-family: 'Material Design Icons';\n  text-rendering: auto;\n  line-height: 1.5rem;\n  font-size: 1.1rem;\n  transform: translate(0, 0); }\n  #sidebar-menu .menu-arrow:before {\n    content: \"\\F142\"; }\n\n#sidebar-menu .badge {\n  margin-top: 4px; }\n\n#sidebar-menu li.active > a > span.menu-arrow {\n  transform: rotate(90deg); }\n\n#sidebar-menu .menu-title {\n  padding: 10px 20px;\n  letter-spacing: .05em;\n  pointer-events: none;\n  cursor: default;\n  font-size: 0.6875rem;\n  text-transform: uppercase;\n  color: #6e768e;\n  font-weight: 600; }\n\n.enlarged .logo-box {\n  width: 70px !important; }\n\n.enlarged .logo span.logo-lg {\n  display: none; }\n\n.enlarged .logo span.logo-sm {\n  display: block; }\n\n.enlarged .left-side-menu {\n  position: absolute;\n  padding-top: 0;\n  width: 70px !important;\n  z-index: 5; }\n  .enlarged .left-side-menu .slimScrollDiv,\n  .enlarged .left-side-menu .slimscroll-menu {\n    overflow: inherit !important;\n    height: auto !important; }\n  .enlarged .left-side-menu .slimScrollBar {\n    visibility: hidden; }\n  .enlarged .left-side-menu #sidebar-menu .menu-title,\n  .enlarged .left-side-menu #sidebar-menu .menu-arrow,\n  .enlarged .left-side-menu #sidebar-menu .label,\n  .enlarged .left-side-menu #sidebar-menu .badge,\n  .enlarged .left-side-menu #sidebar-menu .collapse.in {\n    display: none !important; }\n  .enlarged .left-side-menu #sidebar-menu .nav.collapse {\n    height: inherit !important; }\n  .enlarged .left-side-menu #sidebar-menu > ul > li {\n    position: relative;\n    white-space: nowrap; }\n    .enlarged .left-side-menu #sidebar-menu > ul > li > a {\n      padding: 15px 20px;\n      min-height: 54px; }\n      .enlarged .left-side-menu #sidebar-menu > ul > li > a:hover, .enlarged .left-side-menu #sidebar-menu > ul > li > a:active, .enlarged .left-side-menu #sidebar-menu > ul > li > a:focus {\n        color: #7e57c2; }\n      .enlarged .left-side-menu #sidebar-menu > ul > li > a i {\n        font-size: 1.125rem;\n        margin-right: 20px; }\n      .enlarged .left-side-menu #sidebar-menu > ul > li > a span {\n        display: none;\n        padding-left: 25px; }\n    .enlarged .left-side-menu #sidebar-menu > ul > li:hover > a {\n      position: relative;\n      width: calc(190px + 70px);\n      color: #7e57c2;\n      background-color: #ffffff;\n      transition: none; }\n      .enlarged .left-side-menu #sidebar-menu > ul > li:hover > a span {\n        display: inline; }\n    .enlarged .left-side-menu #sidebar-menu > ul > li:hover a.open :after, .enlarged .left-side-menu #sidebar-menu > ul > li:hover a.active :after {\n      display: none; }\n    .enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul {\n      display: block;\n      left: 70px;\n      position: absolute;\n      width: 190px;\n      height: auto !important;\n      box-shadow: 3px 5px 10px 0 rgba(154, 161, 171, 0.2); }\n      .enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul ul {\n        box-shadow: 3px 5px 10px 0 rgba(154, 161, 171, 0.2); }\n      .enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul a {\n        box-shadow: none;\n        padding: 8px 20px;\n        position: relative;\n        width: 190px;\n        z-index: 6; }\n        .enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul a:hover {\n          color: #7e57c2; }\n  .enlarged .left-side-menu #sidebar-menu > ul ul {\n    padding: 5px 0;\n    z-index: 9999;\n    background-color: #ffffff; }\n    .enlarged .left-side-menu #sidebar-menu > ul ul li:hover > ul {\n      display: block;\n      left: 190px;\n      margin-top: -36px;\n      position: absolute;\n      width: 190px; }\n    .enlarged .left-side-menu #sidebar-menu > ul ul li > a span.pull-right {\n      position: absolute;\n      right: 20px;\n      top: 12px;\n      transform: rotate(270deg); }\n    .enlarged .left-side-menu #sidebar-menu > ul ul li.active a {\n      color: #7e57c2; }\n\n.enlarged .content-page {\n  margin-left: 70px !important; }\n\n.enlarged .footer {\n  left: 70px !important; }\n\n.enlarged .user-box {\n  display: none; }\n\nbody.enlarged {\n  min-height: 1200px; }\n\n@media (max-width: 767.98px) {\n  body {\n    overflow-x: hidden;\n    padding-bottom: 80px; }\n  .left-side-menu {\n    display: none;\n    z-index: 10 !important; }\n  .sidebar-enable .left-side-menu {\n    display: block; }\n  .content-page, .enlarged .content-page {\n    margin-left: 0 !important;\n    padding: 0 10px; }\n  .pro-user-name {\n    display: none; }\n  .logo-box {\n    display: none; } }\n\n/* =============\r\n  Small Menu\r\n============= */\n.left-side-menu-sm .logo-box {\n  width: 160px; }\n\n.left-side-menu-sm .left-side-menu {\n  width: 160px;\n  text-align: center; }\n  .left-side-menu-sm .left-side-menu #sidebar-menu > ul > li > a > i {\n    display: block;\n    font-size: 18px;\n    line-height: 24px;\n    width: 100%;\n    margin: 0; }\n  .left-side-menu-sm .left-side-menu #sidebar-menu > ul ul {\n    padding-left: 0; }\n    .left-side-menu-sm .left-side-menu #sidebar-menu > ul ul a {\n      padding: 10px 20px; }\n  .left-side-menu-sm .left-side-menu .menu-arrow,\n  .left-side-menu-sm .left-side-menu .badge {\n    display: none !important; }\n  .left-side-menu-sm .left-side-menu + .content-page {\n    margin-left: 160px; }\n  .left-side-menu-sm .left-side-menu + .content-page .footer {\n    left: 160px; }\n  .left-side-menu-sm .left-side-menu .menu-title {\n    background-color: #f1f5f7; }\n\n.enlarged.left-side-menu-sm #wrapper .left-side-menu {\n  text-align: left; }\n  .enlarged.left-side-menu-sm #wrapper .left-side-menu ul li a i {\n    display: inline-block;\n    font-size: 18px;\n    line-height: 17px;\n    margin-left: 3px;\n    margin-right: 15px;\n    vertical-align: middle;\n    width: 20px; }\n\n.left-side-menu-dark .navbar-custom {\n  background-color: #4a81d4; }\n\n.left-side-menu-dark .left-side-menu {\n  background-color: #38414a;\n  box-shadow: none;\n  border-right: 2px solid #3c4650; }\n  .left-side-menu-dark .left-side-menu #sidebar-menu > ul > li > a {\n    color: #9097a7; }\n    .left-side-menu-dark .left-side-menu #sidebar-menu > ul > li > a:hover, .left-side-menu-dark .left-side-menu #sidebar-menu > ul > li > a:focus, .left-side-menu-dark .left-side-menu #sidebar-menu > ul > li > a:active {\n      color: #c8cddc; }\n  .left-side-menu-dark .left-side-menu #sidebar-menu > ul > li > a.active {\n    color: #ffffff;\n    background-color: #434e59;\n    border-right-color: #ffffff; }\n  .left-side-menu-dark .left-side-menu #sidebar-menu .menu-title {\n    color: #adb5bd; }\n  .left-side-menu-dark .left-side-menu .nav-second-level li a,\n  .left-side-menu-dark .left-side-menu .nav-thrid-level li a {\n    color: #9097a7; }\n    .left-side-menu-dark .left-side-menu .nav-second-level li a:focus, .left-side-menu-dark .left-side-menu .nav-second-level li a:hover,\n    .left-side-menu-dark .left-side-menu .nav-thrid-level li a:focus,\n    .left-side-menu-dark .left-side-menu .nav-thrid-level li a:hover {\n      background-color: transparent;\n      color: #c8cddc; }\n  .left-side-menu-dark .left-side-menu .nav-second-level li.active > a,\n  .left-side-menu-dark .left-side-menu .nav-third-level li.active > a {\n    color: #ffffff; }\n\n.enlarged.left-side-menu-dark #wrapper .left-side-menu #sidebar-menu > ul > li:hover > a {\n  background-color: #434e59; }\n\n.user-pro-dropdown {\n  background-color: #f1f5f7;\n  box-shadow: none;\n  padding: 15px 5px;\n  width: 90%;\n  margin-left: 5%;\n  margin-top: 10px; }\n  .user-pro-dropdown .dropdown-item {\n    border-radius: 3px; }\n    .user-pro-dropdown .dropdown-item:hover {\n      background-color: #7e57c2;\n      color: #fff; }\n\n.logo {\n  display: block;\n  line-height: 70px; }\n  .logo span.logo-lg {\n    display: block; }\n  .logo span.logo-sm {\n    display: none; }\n  .logo .logo-lg-text-dark {\n    color: #323a46;\n    font-weight: 700;\n    font-size: 22px;\n    text-transform: uppercase; }\n  .logo .logo-lg-text-light {\n    color: #fff;\n    font-weight: 700;\n    font-size: 22px;\n    text-transform: uppercase; }\n\n.logo-box {\n  height: 70px;\n  width: 240px;\n  float: left; }\n\n.navbar-custom {\n  background-color: #7e57c2;\n  box-shadow: 0 2px 6px -2px rgba(0, 0, 0, 0.16), 0 2px 6px 0 rgba(0, 0, 0, 0.12);\n  padding: 0 10px 0 0;\n  position: fixed;\n  left: 0;\n  right: 0;\n  height: 70px;\n  z-index: 100;\n  /* Search */ }\n  .navbar-custom .topnav-menu > li {\n    float: left; }\n  .navbar-custom .topnav-menu .nav-link {\n    padding: 0 15px;\n    color: rgba(255, 255, 255, 0.6);\n    min-width: 32px;\n    display: block;\n    line-height: 70px;\n    text-align: center;\n    max-height: 70px; }\n  .navbar-custom .dropdown.show .nav-link {\n    background-color: rgba(255, 255, 255, 0.05); }\n  .navbar-custom .app-search {\n    overflow: hidden;\n    height: 70px;\n    display: table;\n    max-width: 180px;\n    margin-right: 20px; }\n    .navbar-custom .app-search .app-search-box {\n      display: table-cell;\n      vertical-align: middle; }\n      .navbar-custom .app-search .app-search-box input::-webkit-input-placeholder {\n        font-size: 0.8125rem;\n        color: rgba(255, 255, 255, 0.3); }\n    .navbar-custom .app-search .form-control {\n      border: none;\n      height: 38px;\n      padding-left: 20px;\n      padding-right: 0;\n      color: #fff;\n      background-color: rgba(255, 255, 255, 0.07);\n      box-shadow: none;\n      border-radius: 30px 0 0 30px; }\n    .navbar-custom .app-search .input-group-append {\n      margin-left: 0;\n      z-index: 4; }\n    .navbar-custom .app-search .btn {\n      background-color: rgba(255, 255, 255, 0.07);\n      border-color: transparent;\n      color: rgba(255, 255, 255, 0.3);\n      border-radius: 0 30px 30px 0;\n      box-shadow: none !important; }\n  .navbar-custom .button-menu-mobile {\n    border: none;\n    color: #fff;\n    display: inline-block;\n    height: 70px;\n    line-height: 70px;\n    width: 60px;\n    background-color: transparent;\n    font-size: 24px;\n    cursor: pointer; }\n  .navbar-custom .button-menu-mobile.disable-btn {\n    display: none; }\n\n/* Notification */\n.noti-scroll {\n  max-height: 230px; }\n\n.notification-list {\n  margin-left: 0; }\n  .notification-list .noti-title {\n    background-color: #fff;\n    padding: 15px 20px; }\n  .notification-list .noti-icon {\n    font-size: 21px;\n    vertical-align: middle; }\n  .notification-list .noti-icon-badge {\n    display: inline-block;\n    position: absolute;\n    top: 16px;\n    right: 10px; }\n  .notification-list .notify-item {\n    padding: 12px 20px; }\n    .notification-list .notify-item .notify-icon {\n      float: left;\n      height: 36px;\n      width: 36px;\n      font-size: 18px;\n      line-height: 36px;\n      text-align: center;\n      margin-right: 10px;\n      border-radius: 50%;\n      color: #fff; }\n    .notification-list .notify-item .notify-details {\n      margin-bottom: 5px;\n      overflow: hidden;\n      margin-left: 45px;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n      color: #343a40; }\n      .notification-list .notify-item .notify-details b {\n        font-weight: 500; }\n      .notification-list .notify-item .notify-details small {\n        display: block; }\n      .notification-list .notify-item .notify-details span {\n        display: block;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n        font-size: 13px; }\n    .notification-list .notify-item .user-msg {\n      margin-left: 45px;\n      white-space: normal;\n      line-height: 16px; }\n  .notification-list .profile-dropdown .notify-item {\n    padding: 7px 20px; }\n\n.profile-dropdown {\n  width: 170px; }\n  .profile-dropdown i {\n    vertical-align: middle;\n    margin-right: 5px; }\n\n.nav-user {\n  padding: 0 12px !important; }\n  .nav-user img {\n    height: 32px;\n    width: 32px; }\n\n.navbar-custom-light {\n  background-color: #ffffff !important;\n  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.1);\n  /* Search */ }\n  .navbar-custom-light .topnav-menu .nav-link {\n    color: #72747b; }\n  .navbar-custom-light .dropdown.show .nav-link {\n    background-color: rgba(50, 58, 70, 0.03); }\n  .navbar-custom-light .button-menu-mobile {\n    color: #323a46; }\n  .navbar-custom-light .app-search input::-webkit-input-placeholder {\n    color: #adb5bd !important; }\n  .navbar-custom-light .app-search .form-control {\n    color: #323a46;\n    background-color: #f1f5f7;\n    border-color: #f1f5f7; }\n  .navbar-custom-light .app-search .btn {\n    background-color: #f1f5f7;\n    color: #ced4da; }\n\n.page-title-box .page-title {\n  font-size: 1.25rem;\n  margin: 0;\n  line-height: 75px;\n  color: #323a46; }\n\n.page-title-box .page-title-right {\n  float: right;\n  margin-top: 22px; }\n\n.page-title-box .breadcrumb {\n  padding-top: 5px; }\n\n@media (max-width: 767.98px) {\n  .page-title-box .page-title {\n    display: block;\n    white-space: nowrap;\n    text-overflow: ellipsis;\n    overflow: hidden;\n    line-height: 70px; }\n  .page-title-box .breadcrumb {\n    display: none; } }\n\n@media (max-width: 640px) {\n  .page-title-box .page-title-right {\n    display: none; } }\n\n@media (max-width: 419px) {\n  .page-title-box .breadcrumb {\n    display: none; } }\n\n.footer {\n  bottom: 0;\n  padding: 19px 15px 20px;\n  position: absolute;\n  right: 0;\n  color: #98a6ad;\n  left: 240px;\n  background-color: #edf1f4; }\n  .footer .footer-links a {\n    color: #98a6ad;\n    margin-left: 1.5rem;\n    transition: all .4s; }\n    .footer .footer-links a:hover {\n      color: #323a46; }\n    .footer .footer-links a:first-of-type {\n      margin-left: 0; }\n\n.footer-alt {\n  left: 0 !important;\n  text-align: center;\n  background-color: transparent; }\n\n@media (max-width: 767.98px) {\n  .footer {\n    left: 0 !important;\n    text-align: center; } }\n\n.right-bar {\n  background-color: #fff;\n  box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);\n  display: block;\n  position: fixed;\n  transition: all 200ms ease-out;\n  width: 260px;\n  z-index: 9999;\n  float: right !important;\n  right: -270px;\n  top: 0;\n  bottom: 0; }\n  .right-bar .rightbar-title {\n    background-color: #7e57c2;\n    padding: 27px 25px;\n    color: #fff; }\n  .right-bar .right-bar-toggle {\n    background-color: #414b5b;\n    height: 24px;\n    width: 24px;\n    line-height: 27px;\n    color: #fff;\n    text-align: center;\n    border-radius: 50%;\n    margin-top: -4px; }\n    .right-bar .right-bar-toggle:hover {\n      background-color: #475364; }\n  .right-bar .user-box {\n    padding: 25px;\n    text-align: center; }\n    .right-bar .user-box .user-img {\n      position: relative;\n      height: 64px;\n      width: 64px;\n      margin: 0 auto 15px auto; }\n      .right-bar .user-box .user-img .user-edit {\n        position: absolute;\n        right: -5px;\n        bottom: 0px;\n        height: 24px;\n        width: 24px;\n        background-color: #fff;\n        line-height: 24px;\n        border-radius: 50%;\n        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12); }\n    .right-bar .user-box h5 {\n      margin-bottom: 2px; }\n      .right-bar .user-box h5 a {\n        color: #323a46; }\n\n.rightbar-overlay {\n  background-color: rgba(50, 58, 70, 0.55);\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  display: none;\n  z-index: 9998;\n  transition: all .2s ease-out; }\n\n.right-bar-enabled .right-bar {\n  right: 0; }\n\n.right-bar-enabled .rightbar-overlay {\n  display: block; }\n\n@media (max-width: 767.98px) {\n  .right-bar {\n    overflow: auto; }\n    .right-bar .slimscroll-menu {\n      height: auto !important; } }\n\nbody.boxed-layout #wrapper {\n  max-width: 1300px;\n  margin: 0 auto;\n  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12); }\n\nbody.boxed-layout .navbar-custom {\n  max-width: 1300px;\n  margin: 0 auto; }\n\nbody.boxed-layout .footer {\n  margin: 0 auto;\n  max-width: calc(1300px - 240px); }\n\nbody.boxed-layout.enlarged .footer {\n  max-width: calc(1300px - 70px); }\n\n.width-xs {\n  min-width: 80px; }\n\n.width-sm {\n  min-width: 100px; }\n\n.width-md {\n  min-width: 120px; }\n\n.width-lg {\n  min-width: 140px; }\n\n.width-xl {\n  min-width: 160px; }\n\n.font-family-secondary {\n  font-family: \"Poppins\", sans-serif; }\n\n.avatar-xs {\n  height: 1.5rem;\n  width: 1.5rem; }\n\n.avatar-sm {\n  height: 2.25rem;\n  width: 2.25rem; }\n\n.avatar-md {\n  height: 3.5rem;\n  width: 3.5rem; }\n\n.avatar-lg {\n  height: 4.5rem;\n  width: 4.5rem; }\n\n.avatar-xl {\n  height: 6rem;\n  width: 6rem; }\n\n.avatar-xxl {\n  height: 7.5rem;\n  width: 7.5rem; }\n\n.avatar-title {\n  align-items: center;\n  color: #fff;\n  display: flex;\n  height: 100%;\n  justify-content: center;\n  width: 100%; }\n\n.avatar-group {\n  padding-left: 12px; }\n  .avatar-group .avatar-group-item {\n    margin: 0 0 10px -12px;\n    display: inline-block;\n    border: 2px solid #fff;\n    border-radius: 50%; }\n\n.sp-line-1,\n.sp-line-2,\n.sp-line-3,\n.sp-line-4 {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-box-orient: vertical; }\n\n.sp-line-1 {\n  -webkit-line-clamp: 1; }\n\n.sp-line-2 {\n  -webkit-line-clamp: 2; }\n\n.sp-line-3 {\n  -webkit-line-clamp: 3; }\n\n.sp-line-4 {\n  -webkit-line-clamp: 4; }\n\n.social-list-item {\n  height: 2rem;\n  width: 2rem;\n  line-height: calc(2rem - 2px);\n  display: block;\n  border: 2px solid #adb5bd;\n  border-radius: 50%;\n  color: #adb5bd; }\n\n.widget-flat {\n  position: relative;\n  overflow: hidden; }\n  .widget-flat i.widget-icon {\n    font-size: 36px; }\n\n.inbox-widget .inbox-item {\n  border-bottom: 1px solid white;\n  overflow: hidden;\n  padding: 0.625rem 0;\n  position: relative; }\n  .inbox-widget .inbox-item:last-of-type {\n    border-bottom: none; }\n  .inbox-widget .inbox-item .inbox-item-img {\n    display: block;\n    float: left;\n    margin-right: 15px;\n    width: 40px; }\n    .inbox-widget .inbox-item .inbox-item-img img {\n      width: 40px; }\n  .inbox-widget .inbox-item .inbox-item-author {\n    color: #323a46;\n    display: block;\n    margin-bottom: 3px; }\n  .inbox-widget .inbox-item .inbox-item-text {\n    color: #98a6ad;\n    display: block;\n    font-size: 0.8125rem;\n    margin: 0;\n    overflow: hidden; }\n  .inbox-widget .inbox-item .inbox-item-date {\n    color: #98a6ad;\n    font-size: 0.6875rem;\n    position: absolute;\n    right: 5px;\n    top: 10px; }\n\n/* Chat widget */\n.conversation-list {\n  list-style: none;\n  height: 332px;\n  padding: 0 20px; }\n  .conversation-list li {\n    margin-bottom: 24px; }\n  .conversation-list .chat-avatar {\n    float: left;\n    text-align: center;\n    width: 42px; }\n    .conversation-list .chat-avatar img {\n      border-radius: 100%;\n      width: 100%; }\n    .conversation-list .chat-avatar i {\n      font-size: 12px;\n      font-style: normal; }\n  .conversation-list .ctext-wrap {\n    background: #f7f7f7;\n    border-radius: 3px;\n    display: inline-block;\n    padding: 12px;\n    position: relative; }\n    .conversation-list .ctext-wrap i {\n      display: block;\n      font-size: 12px;\n      font-style: normal;\n      font-weight: 600;\n      position: relative; }\n    .conversation-list .ctext-wrap p {\n      margin: 0;\n      padding-top: 3px; }\n    .conversation-list .ctext-wrap:after {\n      right: 99%;\n      top: 0;\n      border: solid transparent;\n      content: \" \";\n      height: 0;\n      width: 0;\n      position: absolute;\n      pointer-events: none;\n      border-top-color: #f7f7f7;\n      border-width: 8px;\n      margin-left: -1px;\n      border-right-color: #f7f7f7; }\n  .conversation-list .conversation-text {\n    float: left;\n    font-size: 12px;\n    margin-left: 12px;\n    width: 70%; }\n  .conversation-list .odd .chat-avatar {\n    float: right !important; }\n  .conversation-list .odd .conversation-text {\n    float: right !important;\n    margin-right: 12px;\n    text-align: right;\n    width: 70% !important; }\n  .conversation-list .odd .ctext-wrap {\n    background-color: #fef5e4; }\n    .conversation-list .odd .ctext-wrap:after {\n      border-color: transparent;\n      border-left-color: #fef5e4;\n      border-top-color: #fef5e4;\n      left: 99% !important; }\n\n.checkbox label {\n  display: inline-block;\n  padding-left: 8px;\n  position: relative;\n  font-weight: normal;\n  margin-bottom: 0; }\n  .checkbox label::before {\n    background-color: #fff;\n    border-radius: 3px;\n    border: 2px solid #98a6ad;\n    content: \"\";\n    display: inline-block;\n    height: 18px;\n    left: 0;\n    margin-left: -18px;\n    position: absolute;\n    transition: 0.3s ease-in-out;\n    width: 18px;\n    outline: none !important;\n    top: 1px; }\n  .checkbox label::after {\n    color: #72747b;\n    display: inline-block;\n    font-size: 11px;\n    height: 18px;\n    left: 0;\n    margin-left: -18px;\n    padding-left: 3px;\n    padding-top: 2px;\n    position: absolute;\n    top: 0;\n    width: 18px; }\n\n.checkbox input[type=\"checkbox\"] {\n  cursor: pointer;\n  opacity: 0;\n  z-index: 1;\n  outline: none !important; }\n  .checkbox input[type=\"checkbox\"]:disabled + label {\n    opacity: 0.65; }\n\n.checkbox input[type=\"checkbox\"]:focus + label::before {\n  outline-offset: -2px;\n  outline: none; }\n\n.checkbox input[type=\"checkbox\"]:checked + label::after {\n  content: \"\";\n  position: absolute;\n  top: 5px;\n  left: 7px;\n  display: table;\n  width: 4px;\n  height: 8px;\n  border: 2px solid #72747b;\n  border-top-width: 0;\n  border-left-width: 0;\n  -webkit-transform: rotate(45deg);\n  -ms-transform: rotate(45deg);\n  -o-transform: rotate(45deg);\n  transform: rotate(45deg); }\n\n.checkbox input[type=\"checkbox\"]:disabled + label::before {\n  background-color: #f1f5f7;\n  cursor: not-allowed; }\n\n.checkbox.checkbox-circle label::before {\n  border-radius: 50%; }\n\n.checkbox.checkbox-inline {\n  margin-top: 0; }\n\n.checkbox.checkbox-single input {\n  height: 18px;\n  width: 18px;\n  position: absolute; }\n\n.checkbox.checkbox-single label {\n  height: 18px;\n  width: 18px; }\n  .checkbox.checkbox-single label:before {\n    margin-left: 0; }\n  .checkbox.checkbox-single label:after {\n    margin-left: 0; }\n\n.checkbox-primary input[type=\"checkbox\"]:checked + label::before {\n  background-color: #7e57c2;\n  border-color: #7e57c2; }\n\n.checkbox-primary input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-secondary input[type=\"checkbox\"]:checked + label::before {\n  background-color: #72747b;\n  border-color: #72747b; }\n\n.checkbox-secondary input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-success input[type=\"checkbox\"]:checked + label::before {\n  background-color: #1abc9c;\n  border-color: #1abc9c; }\n\n.checkbox-success input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-info input[type=\"checkbox\"]:checked + label::before {\n  background-color: #4fc6e1;\n  border-color: #4fc6e1; }\n\n.checkbox-info input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-warning input[type=\"checkbox\"]:checked + label::before {\n  background-color: #f7b84b;\n  border-color: #f7b84b; }\n\n.checkbox-warning input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-danger input[type=\"checkbox\"]:checked + label::before {\n  background-color: #f1556c;\n  border-color: #f1556c; }\n\n.checkbox-danger input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-light input[type=\"checkbox\"]:checked + label::before {\n  background-color: #f1f5f7;\n  border-color: #f1f5f7; }\n\n.checkbox-light input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-dark input[type=\"checkbox\"]:checked + label::before {\n  background-color: #323a46;\n  border-color: #323a46; }\n\n.checkbox-dark input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-pink input[type=\"checkbox\"]:checked + label::before {\n  background-color: #f672a7;\n  border-color: #f672a7; }\n\n.checkbox-pink input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-blue input[type=\"checkbox\"]:checked + label::before {\n  background-color: #4a81d4;\n  border-color: #4a81d4; }\n\n.checkbox-blue input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.radio label {\n  display: inline-block;\n  padding-left: 8px;\n  position: relative;\n  font-weight: normal;\n  margin-bottom: 0; }\n  .radio label::before {\n    -o-transition: border 0.5s ease-in-out;\n    -webkit-transition: border 0.5s ease-in-out;\n    background-color: #fff;\n    border-radius: 50%;\n    border: 2px solid #98a6ad;\n    content: \"\";\n    display: inline-block;\n    height: 18px;\n    left: 0;\n    margin-left: -18px;\n    position: absolute;\n    transition: border 0.5s ease-in-out;\n    width: 18px;\n    outline: none !important; }\n  .radio label::after {\n    -moz-transition: -moz-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\n    -ms-transform: scale(0, 0);\n    -o-transform: scale(0, 0);\n    -o-transition: -o-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\n    -webkit-transform: scale(0, 0);\n    -webkit-transition: -webkit-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\n    background-color: #72747b;\n    border-radius: 50%;\n    content: \" \";\n    display: inline-block;\n    height: 10px;\n    left: 6px;\n    margin-left: -20px;\n    position: absolute;\n    top: 4px;\n    transform: scale(0, 0);\n    transition: transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\n    width: 10px; }\n\n.radio input[type=\"radio\"] {\n  cursor: pointer;\n  opacity: 0;\n  z-index: 1;\n  outline: none !important; }\n  .radio input[type=\"radio\"]:disabled + label {\n    opacity: 0.65; }\n\n.radio input[type=\"radio\"]:focus + label::before {\n  outline-offset: -2px;\n  outline: 5px auto -webkit-focus-ring-color;\n  outline: thin dotted; }\n\n.radio input[type=\"radio\"]:checked + label::after {\n  -ms-transform: scale(1, 1);\n  -o-transform: scale(1, 1);\n  -webkit-transform: scale(1, 1);\n  transform: scale(1, 1); }\n\n.radio input[type=\"radio\"]:disabled + label::before {\n  cursor: not-allowed; }\n\n.radio.radio-inline {\n  margin-top: 0; }\n\n.radio.radio-single label {\n  height: 17px; }\n\n.radio-primary input[type=\"radio\"] + label::after {\n  background-color: #7e57c2; }\n\n.radio-primary input[type=\"radio\"]:checked + label::before {\n  border-color: #7e57c2; }\n\n.radio-primary input[type=\"radio\"]:checked + label::after {\n  background-color: #7e57c2; }\n\n.radio-secondary input[type=\"radio\"] + label::after {\n  background-color: #72747b; }\n\n.radio-secondary input[type=\"radio\"]:checked + label::before {\n  border-color: #72747b; }\n\n.radio-secondary input[type=\"radio\"]:checked + label::after {\n  background-color: #72747b; }\n\n.radio-success input[type=\"radio\"] + label::after {\n  background-color: #1abc9c; }\n\n.radio-success input[type=\"radio\"]:checked + label::before {\n  border-color: #1abc9c; }\n\n.radio-success input[type=\"radio\"]:checked + label::after {\n  background-color: #1abc9c; }\n\n.radio-info input[type=\"radio\"] + label::after {\n  background-color: #4fc6e1; }\n\n.radio-info input[type=\"radio\"]:checked + label::before {\n  border-color: #4fc6e1; }\n\n.radio-info input[type=\"radio\"]:checked + label::after {\n  background-color: #4fc6e1; }\n\n.radio-warning input[type=\"radio\"] + label::after {\n  background-color: #f7b84b; }\n\n.radio-warning input[type=\"radio\"]:checked + label::before {\n  border-color: #f7b84b; }\n\n.radio-warning input[type=\"radio\"]:checked + label::after {\n  background-color: #f7b84b; }\n\n.radio-danger input[type=\"radio\"] + label::after {\n  background-color: #f1556c; }\n\n.radio-danger input[type=\"radio\"]:checked + label::before {\n  border-color: #f1556c; }\n\n.radio-danger input[type=\"radio\"]:checked + label::after {\n  background-color: #f1556c; }\n\n.radio-light input[type=\"radio\"] + label::after {\n  background-color: #f1f5f7; }\n\n.radio-light input[type=\"radio\"]:checked + label::before {\n  border-color: #f1f5f7; }\n\n.radio-light input[type=\"radio\"]:checked + label::after {\n  background-color: #f1f5f7; }\n\n.radio-dark input[type=\"radio\"] + label::after {\n  background-color: #323a46; }\n\n.radio-dark input[type=\"radio\"]:checked + label::before {\n  border-color: #323a46; }\n\n.radio-dark input[type=\"radio\"]:checked + label::after {\n  background-color: #323a46; }\n\n.radio-pink input[type=\"radio\"] + label::after {\n  background-color: #f672a7; }\n\n.radio-pink input[type=\"radio\"]:checked + label::before {\n  border-color: #f672a7; }\n\n.radio-pink input[type=\"radio\"]:checked + label::after {\n  background-color: #f672a7; }\n\n.radio-blue input[type=\"radio\"] + label::after {\n  background-color: #4a81d4; }\n\n.radio-blue input[type=\"radio\"]:checked + label::before {\n  border-color: #4a81d4; }\n\n.radio-blue input[type=\"radio\"]:checked + label::after {\n  background-color: #4a81d4; }\n\n.ribbon-box {\n  position: relative;\n  /* Ribbon two */ }\n  .ribbon-box .ribbon {\n    position: relative;\n    clear: both;\n    padding: 5px 12px;\n    margin-bottom: 15px;\n    box-shadow: 2px 5px 10px rgba(50, 58, 70, 0.15);\n    color: #fff;\n    font-size: 13px;\n    font-weight: 600; }\n    .ribbon-box .ribbon:before {\n      content: \" \";\n      border-style: solid;\n      border-width: 10px;\n      display: block;\n      position: absolute;\n      bottom: -10px;\n      left: 0;\n      margin-bottom: -10px;\n      z-index: -1; }\n    .ribbon-box .ribbon.float-left {\n      margin-left: -30px;\n      border-radius: 0 3px 3px 0; }\n    .ribbon-box .ribbon.float-right {\n      margin-right: -30px;\n      border-radius: 3px 0 0 3px; }\n      .ribbon-box .ribbon.float-right:before {\n        right: 0; }\n    .ribbon-box .ribbon.float-center span {\n      margin: 0 auto 20px auto; }\n  .ribbon-box .ribbon-content {\n    clear: both; }\n  .ribbon-box .ribbon-primary {\n    background: #7e57c2; }\n    .ribbon-box .ribbon-primary:before {\n      border-color: #643da9 transparent transparent; }\n  .ribbon-box .ribbon-secondary {\n    background: #72747b; }\n    .ribbon-box .ribbon-secondary:before {\n      border-color: #595b61 transparent transparent; }\n  .ribbon-box .ribbon-success {\n    background: #1abc9c; }\n    .ribbon-box .ribbon-success:before {\n      border-color: #148f77 transparent transparent; }\n  .ribbon-box .ribbon-info {\n    background: #4fc6e1; }\n    .ribbon-box .ribbon-info:before {\n      border-color: #25b7d8 transparent transparent; }\n  .ribbon-box .ribbon-warning {\n    background: #f7b84b; }\n    .ribbon-box .ribbon-warning:before {\n      border-color: #f5a51a transparent transparent; }\n  .ribbon-box .ribbon-danger {\n    background: #f1556c; }\n    .ribbon-box .ribbon-danger:before {\n      border-color: #ed2643 transparent transparent; }\n  .ribbon-box .ribbon-light {\n    background: #f1f5f7; }\n    .ribbon-box .ribbon-light:before {\n      border-color: #d1dee4 transparent transparent; }\n  .ribbon-box .ribbon-dark {\n    background: #323a46; }\n    .ribbon-box .ribbon-dark:before {\n      border-color: #1d2128 transparent transparent; }\n  .ribbon-box .ribbon-pink {\n    background: #f672a7; }\n    .ribbon-box .ribbon-pink:before {\n      border-color: #f34289 transparent transparent; }\n  .ribbon-box .ribbon-blue {\n    background: #4a81d4; }\n    .ribbon-box .ribbon-blue:before {\n      border-color: #2d67be transparent transparent; }\n  .ribbon-box .ribbon-two {\n    position: absolute;\n    left: -5px;\n    top: -5px;\n    z-index: 1;\n    overflow: hidden;\n    width: 75px;\n    height: 75px;\n    text-align: right; }\n    .ribbon-box .ribbon-two span {\n      font-size: 13px;\n      color: #fff;\n      text-align: center;\n      line-height: 20px;\n      transform: rotate(-45deg);\n      -webkit-transform: rotate(-45deg);\n      width: 100px;\n      display: block;\n      box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);\n      position: absolute;\n      top: 19px;\n      left: -21px;\n      font-weight: 600; }\n      .ribbon-box .ribbon-two span:before {\n        content: \"\";\n        position: absolute;\n        left: 0;\n        top: 100%;\n        z-index: -1;\n        border-right: 3px solid transparent;\n        border-bottom: 3px solid transparent; }\n      .ribbon-box .ribbon-two span:after {\n        content: \"\";\n        position: absolute;\n        right: 0;\n        top: 100%;\n        z-index: -1;\n        border-left: 3px solid transparent;\n        border-bottom: 3px solid transparent; }\n  .ribbon-box .ribbon-two-primary span {\n    background: #7e57c2; }\n    .ribbon-box .ribbon-two-primary span:before {\n      border-left: 3px solid #593696;\n      border-top: 3px solid #593696; }\n    .ribbon-box .ribbon-two-primary span:after {\n      border-right: 3px solid #593696;\n      border-top: 3px solid #593696; }\n  .ribbon-box .ribbon-two-secondary span {\n    background: #72747b; }\n    .ribbon-box .ribbon-two-secondary span:before {\n      border-left: 3px solid #4d4f53;\n      border-top: 3px solid #4d4f53; }\n    .ribbon-box .ribbon-two-secondary span:after {\n      border-right: 3px solid #4d4f53;\n      border-top: 3px solid #4d4f53; }\n  .ribbon-box .ribbon-two-success span {\n    background: #1abc9c; }\n    .ribbon-box .ribbon-two-success span:before {\n      border-left: 3px solid #117964;\n      border-top: 3px solid #117964; }\n    .ribbon-box .ribbon-two-success span:after {\n      border-right: 3px solid #117964;\n      border-top: 3px solid #117964; }\n  .ribbon-box .ribbon-two-info span {\n    background: #4fc6e1; }\n    .ribbon-box .ribbon-two-info span:before {\n      border-left: 3px solid #21a5c2;\n      border-top: 3px solid #21a5c2; }\n    .ribbon-box .ribbon-two-info span:after {\n      border-right: 3px solid #21a5c2;\n      border-top: 3px solid #21a5c2; }\n  .ribbon-box .ribbon-two-warning span {\n    background: #f7b84b; }\n    .ribbon-box .ribbon-two-warning span:before {\n      border-left: 3px solid #eb990a;\n      border-top: 3px solid #eb990a; }\n    .ribbon-box .ribbon-two-warning span:after {\n      border-right: 3px solid #eb990a;\n      border-top: 3px solid #eb990a; }\n  .ribbon-box .ribbon-two-danger span {\n    background: #f1556c; }\n    .ribbon-box .ribbon-two-danger span:before {\n      border-left: 3px solid #e71332;\n      border-top: 3px solid #e71332; }\n    .ribbon-box .ribbon-two-danger span:after {\n      border-right: 3px solid #e71332;\n      border-top: 3px solid #e71332; }\n  .ribbon-box .ribbon-two-light span {\n    background: #f1f5f7; }\n    .ribbon-box .ribbon-two-light span:before {\n      border-left: 3px solid #c0d2db;\n      border-top: 3px solid #c0d2db; }\n    .ribbon-box .ribbon-two-light span:after {\n      border-right: 3px solid #c0d2db;\n      border-top: 3px solid #c0d2db; }\n  .ribbon-box .ribbon-two-dark span {\n    background: #323a46; }\n    .ribbon-box .ribbon-two-dark span:before {\n      border-left: 3px solid #121519;\n      border-top: 3px solid #121519; }\n    .ribbon-box .ribbon-two-dark span:after {\n      border-right: 3px solid #121519;\n      border-top: 3px solid #121519; }\n  .ribbon-box .ribbon-two-pink span {\n    background: #f672a7; }\n    .ribbon-box .ribbon-two-pink span:before {\n      border-left: 3px solid #f12a7a;\n      border-top: 3px solid #f12a7a; }\n    .ribbon-box .ribbon-two-pink span:after {\n      border-right: 3px solid #f12a7a;\n      border-top: 3px solid #f12a7a; }\n  .ribbon-box .ribbon-two-blue span {\n    background: #4a81d4; }\n    .ribbon-box .ribbon-two-blue span:before {\n      border-left: 3px solid #285ca9;\n      border-top: 3px solid #285ca9; }\n    .ribbon-box .ribbon-two-blue span:after {\n      border-right: 3px solid #285ca9;\n      border-top: 3px solid #285ca9; }\n\n@media print {\n  .left-side-menu,\n  .right-bar,\n  .page-title-box,\n  .navbar-custom,\n  .footer {\n    display: none; }\n  .card-body,\n  .content-page,\n  .right-bar,\n  .content,\n  body {\n    padding: 0;\n    margin: 0; } }\n\n#preloader {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: #fff;\n  z-index: 9999; }\n\n#status {\n  width: 40px;\n  height: 40px;\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  margin: -20px 0 0 -20px; }\n\n.spinner {\n  margin: 0 auto;\n  font-size: 10px;\n  position: relative;\n  text-indent: -9999em;\n  border-top: 5px solid #dee2e6;\n  border-right: 5px solid #dee2e6;\n  border-bottom: 5px solid #dee2e6;\n  border-left: 5px solid #7e57c2;\n  transform: translateZ(0);\n  animation: SpinnerAnimation 1.1s infinite linear; }\n\n.spinner,\n.spinner:after {\n  border-radius: 50%;\n  width: 40px;\n  height: 40px; }\n\n@-webkit-keyframes SpinnerAnimation {\n  0% {\n    -webkit-transform: rotate(0deg);\n    transform: rotate(0deg); }\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg); } }\n\n@keyframes SpinnerAnimation {\n  0% {\n    -webkit-transform: rotate(0deg);\n    transform: rotate(0deg); }\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg); } }\n\n.authentication-bg.enlarged {\n  min-height: 100px; }\n\n.bg-pattern {\n  background-image: url(\"../images/bg-pattern-2.png\");\n  background-size: cover; }\n\nbody.authentication-bg {\n  background-color: #f7f7f7;\n  background-size: cover;\n  background-position: center; }\n\nbody.authentication-bg-pattern {\n  background-image: url(\"../images/bg-pattern.png\");\n  background-color: #7e57c2; }\n\n.logout-icon {\n  width: 140px; }\n\n.button-list {\n  margin-left: -8px;\n  margin-bottom: -12px; }\n  .button-list .btn {\n    margin-bottom: 12px;\n    margin-left: 8px; }\n\n.icons-list-demo div {\n  cursor: pointer;\n  line-height: 45px;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  display: block;\n  overflow: hidden; }\n  .icons-list-demo div p {\n    margin-bottom: 0;\n    line-height: inherit; }\n\n.icons-list-demo i {\n  text-align: center;\n  vertical-align: middle;\n  font-size: 22px;\n  width: 50px;\n  height: 50px;\n  line-height: 50px;\n  margin-right: 12px;\n  color: rgba(50, 58, 70, 0.7);\n  border-radius: 3px;\n  display: inline-block;\n  transition: all 0.2s; }\n\n.icons-list-demo .col-md-4 {\n  -webkit-border-radius: 3px;\n  border-radius: 3px;\n  -moz-border-radius: 3px;\n  background-clip: padding-box;\n  margin-bottom: 10px; }\n  .icons-list-demo .col-md-4:hover,\n  .icons-list-demo .col-md-4:hover i {\n    color: #7e57c2; }\n\n.scrollspy-example {\n  position: relative;\n  height: 200px;\n  margin-top: .5rem;\n  overflow: auto; }\n\n.text-error {\n  color: #7e57c2;\n  text-shadow: rgba(126, 87, 194, 0.3) 5px 1px, rgba(126, 87, 194, 0.2) 10px 3px;\n  font-size: 84px;\n  line-height: 90px;\n  font-family: \"Poppins\", sans-serif; }\n\n.error-text-box {\n  font-size: 10rem;\n  font-family: \"Poppins\", sans-serif;\n  min-height: 200px; }\n  .error-text-box .text {\n    fill: none;\n    stroke-width: 6;\n    stroke-linejoin: round;\n    stroke-dasharray: 30 100;\n    stroke-dashoffset: 0;\n    animation: stroke 9s infinite linear; }\n  .error-text-box .text:nth-child(5n + 1) {\n    stroke: #f1556c;\n    animation-delay: -1.2s; }\n  .error-text-box .text:nth-child(5n + 2) {\n    stroke: #f7b84b;\n    animation-delay: -2.4s; }\n  .error-text-box .text:nth-child(5n + 3) {\n    stroke: #7e57c2;\n    animation-delay: -3.6s; }\n  .error-text-box .text:nth-child(5n + 4) {\n    stroke: #4fc6e1;\n    animation-delay: -4.8s; }\n  .error-text-box .text:nth-child(5n + 5) {\n    stroke: #1abc9c;\n    animation-delay: -6s; }\n\n@-webkit-keyframes stroke {\n  100% {\n    stroke-dashoffset: -400; } }\n\n@keyframes stroke {\n  100% {\n    stroke-dashoffset: -400; } }\n\n@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {\n  .error-text-box .text {\n    fill: #f1556c;\n    stroke: #f1556c;\n    stroke-width: 6;\n    stroke-dasharray: 0 0;\n    stroke-dashoffset: 0;\n    animation: none; } }\n\n.logout-checkmark {\n  width: 100px;\n  margin: 0 auto;\n  padding: 20px 0; }\n  .logout-checkmark .path {\n    stroke-dasharray: 1000;\n    stroke-dashoffset: 0;\n    animation: dash 2s ease-in-out; }\n  .logout-checkmark .spin {\n    animation: spin 2s;\n    transform-origin: 50% 50%; }\n\n@-webkit-keyframes dash {\n  0% {\n    stroke-dashoffset: 1000; }\n  100% {\n    stroke-dashoffset: 0; } }\n\n@keyframes dash {\n  0% {\n    stroke-dashoffset: 1000; }\n  100% {\n    stroke-dashoffset: 0; } }\n\n@-webkit-keyframes spin {\n  0% {\n    -webkit-transform: rotate(0deg); }\n  100% {\n    -webkit-transform: rotate(360deg); } }\n\n@keyframes spin {\n  0% {\n    -webkit-transform: rotate(0deg); }\n  100% {\n    -webkit-transform: rotate(360deg); } }\n\n@-webkit-keyframes text {\n  0% {\n    opacity: 0; }\n  100% {\n    opacity: 1; } }\n\n@keyframes text {\n  0% {\n    opacity: 0; }\n  100% {\n    opacity: 1; } }\n\n.faq-question-q-box {\n  height: 30px;\n  width: 30px;\n  color: #7e57c2;\n  text-align: center;\n  border-radius: 50%;\n  float: left;\n  font-weight: 700;\n  line-height: 30px;\n  background-color: rgba(126, 87, 194, 0.15); }\n\n.faq-question {\n  margin-top: 0;\n  margin-left: 50px;\n  font-weight: 400;\n  font-size: 16px; }\n\n.faq-answer {\n  margin-left: 50px;\n  color: #98a6ad; }\n\n.svg-computer {\n  stroke-dasharray: 1134;\n  stroke-dashoffset: -1134;\n  animation: draw-me 5s infinite;\n  animation-direction: normal;\n  height: 160px; }\n\n@keyframes draw-me {\n  from {\n    stroke-dashoffset: -1134; }\n  to {\n    stroke-dashoffset: 0; } }\n\n@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {\n  .svg-computer {\n    stroke-dasharray: 0;\n    stroke-dashoffset: 0;\n    animation: none;\n    animation-direction: normal; } }\n\n.timeline {\n  margin-bottom: 50px;\n  position: relative; }\n  .timeline:before {\n    background-color: #dee2e6;\n    bottom: 0;\n    content: \"\";\n    left: 50%;\n    position: absolute;\n    top: 30px;\n    width: 2px;\n    z-index: 0; }\n  .timeline .time-show {\n    margin-bottom: 30px;\n    margin-top: 30px;\n    position: relative; }\n  .timeline .timeline-box {\n    background: #fff;\n    display: block;\n    margin: 15px 0;\n    position: relative;\n    padding: 20px; }\n  .timeline .timeline-album {\n    margin-top: 12px; }\n    .timeline .timeline-album a {\n      display: inline-block;\n      margin-right: 5px; }\n    .timeline .timeline-album img {\n      height: 36px;\n      width: auto;\n      border-radius: 3px; }\n\n@media (min-width: 768px) {\n  .timeline .time-show {\n    margin-right: -69px;\n    text-align: right; }\n  .timeline .timeline-box {\n    margin-left: 45px; }\n  .timeline .timeline-icon {\n    background: #dee2e6;\n    border-radius: 50%;\n    display: block;\n    height: 20px;\n    left: -54px;\n    margin-top: -10px;\n    position: absolute;\n    text-align: center;\n    top: 50%;\n    width: 20px; }\n    .timeline .timeline-icon i {\n      color: #98a6ad;\n      font-size: 13px;\n      position: absolute;\n      left: 4px;\n      margin-top: 1px; }\n  .timeline .timeline-desk {\n    display: table-cell;\n    vertical-align: top;\n    width: 50%; }\n  .timeline-item {\n    display: table-row; }\n    .timeline-item:before {\n      content: \"\";\n      display: block;\n      width: 50%; }\n    .timeline-item .timeline-desk .arrow {\n      border-bottom: 12px solid transparent;\n      border-right: 12px solid #fff !important;\n      border-top: 12px solid transparent;\n      display: block;\n      height: 0;\n      left: -12px;\n      margin-top: -12px;\n      position: absolute;\n      top: 50%;\n      width: 0; }\n    .timeline-item.timeline-item-left:after {\n      content: \"\";\n      display: block;\n      width: 50%; }\n    .timeline-item.timeline-item-left .timeline-desk .arrow-alt {\n      border-bottom: 12px solid transparent;\n      border-left: 12px solid #fff !important;\n      border-top: 12px solid transparent;\n      display: block;\n      height: 0;\n      left: auto;\n      margin-top: -12px;\n      position: absolute;\n      right: -12px;\n      top: 50%;\n      width: 0; }\n    .timeline-item.timeline-item-left .timeline-desk .album {\n      float: right;\n      margin-top: 20px; }\n      .timeline-item.timeline-item-left .timeline-desk .album a {\n        float: right;\n        margin-left: 5px; }\n    .timeline-item.timeline-item-left .timeline-icon {\n      left: auto;\n      right: -56px; }\n    .timeline-item.timeline-item-left:before {\n      display: none; }\n    .timeline-item.timeline-item-left .timeline-box {\n      margin-right: 45px;\n      margin-left: 0;\n      text-align: right; } }\n\n@media (max-width: 767.98px) {\n  .timeline .time-show {\n    text-align: center;\n    position: relative; }\n  .timeline .timeline-icon {\n    display: none; } }\n\n.timeline-sm {\n  padding-left: 110px; }\n  .timeline-sm .timeline-sm-item {\n    position: relative;\n    padding-bottom: 20px;\n    padding-left: 40px;\n    border-left: 2px solid #dee2e6; }\n    .timeline-sm .timeline-sm-item:after {\n      content: \"\";\n      display: block;\n      position: absolute;\n      top: 3px;\n      left: -7px;\n      width: 12px;\n      height: 12px;\n      border-radius: 50%;\n      background: #fff;\n      border: 2px solid #7e57c2; }\n    .timeline-sm .timeline-sm-item .timeline-sm-date {\n      position: absolute;\n      left: -104px; }\n\n@media (max-width: 420px) {\n  .timeline-sm {\n    padding-left: 0px; }\n    .timeline-sm .timeline-sm-date {\n      position: relative !important;\n      display: block;\n      left: 0px !important;\n      margin-bottom: 10px; } }\n\n.inbox-leftbar {\n  width: 240px;\n  float: left;\n  padding: 0 20px 20px 10px; }\n\n.inbox-rightbar {\n  margin: -1.5rem 0 -1.5rem 250px;\n  border-left: 5px solid #f2f5f7;\n  padding: 1.5rem 0 1.5rem 25px; }\n\n.message-list {\n  display: block;\n  padding-left: 0; }\n  .message-list li {\n    position: relative;\n    display: block;\n    height: 51px;\n    line-height: 50px;\n    cursor: default;\n    transition-duration: .3s; }\n    .message-list li a {\n      color: #72747b; }\n    .message-list li:hover {\n      background: #f1f5f7;\n      transition-duration: .05s; }\n    .message-list li .col-mail {\n      float: left;\n      position: relative; }\n    .message-list li .col-mail-1 {\n      width: 320px; }\n      .message-list li .col-mail-1 .star-toggle,\n      .message-list li .col-mail-1 .checkbox-wrapper-mail,\n      .message-list li .col-mail-1 .dot {\n        display: block;\n        float: left; }\n      .message-list li .col-mail-1 .dot {\n        border: 4px solid transparent;\n        border-radius: 100px;\n        margin: 22px 26px 0;\n        height: 0;\n        width: 0;\n        line-height: 0;\n        font-size: 0; }\n      .message-list li .col-mail-1 .checkbox-wrapper-mail {\n        margin: 15px 10px 0 20px; }\n      .message-list li .col-mail-1 .star-toggle {\n        margin-top: 18px;\n        color: #adb5bd;\n        margin-left: 10px; }\n      .message-list li .col-mail-1 .title {\n        position: absolute;\n        top: 0;\n        left: 100px;\n        right: 0;\n        text-overflow: ellipsis;\n        overflow: hidden;\n        white-space: nowrap;\n        margin-bottom: 0;\n        line-height: 50px; }\n    .message-list li .col-mail-2 {\n      position: absolute;\n      top: 0;\n      left: 320px;\n      right: 0;\n      bottom: 0; }\n      .message-list li .col-mail-2 .subject,\n      .message-list li .col-mail-2 .date {\n        position: absolute;\n        top: 0; }\n      .message-list li .col-mail-2 .subject {\n        left: 0;\n        right: 110px;\n        text-overflow: ellipsis;\n        overflow: hidden;\n        white-space: nowrap; }\n      .message-list li .col-mail-2 .date {\n        right: 0;\n        width: 100px;\n        padding-left: 10px; }\n  .message-list li.active,\n  .message-list li.mail-selected {\n    background: #f1f5f7;\n    transition-duration: .05s; }\n  .message-list li.active,\n  .message-list li.active:hover {\n    box-shadow: inset 3px 0 0 #4fc6e1; }\n  .message-list li.unread a {\n    font-weight: 600;\n    color: #272e37; }\n  .message-list .checkbox-wrapper-mail {\n    cursor: pointer;\n    height: 20px;\n    width: 20px;\n    position: relative;\n    display: inline-block;\n    box-shadow: inset 0 0 0 2px #ced4da;\n    border-radius: 3px; }\n    .message-list .checkbox-wrapper-mail input {\n      opacity: 0;\n      cursor: pointer; }\n    .message-list .checkbox-wrapper-mail input:checked ~ label {\n      opacity: 1; }\n    .message-list .checkbox-wrapper-mail label {\n      position: absolute;\n      top: 3px;\n      left: 3px;\n      right: 3px;\n      bottom: 3px;\n      cursor: pointer;\n      background: #98a6ad;\n      opacity: 0;\n      margin-bottom: 0 !important;\n      transition-duration: .05s; }\n    .message-list .checkbox-wrapper-mail label:active {\n      background: #87949b; }\n\n.mail-list a {\n  color: #72747b;\n  padding: 7px 10px;\n  display: block; }\n\n.reply-box {\n  border: 2px solid #f1f5f7; }\n\n@media (max-width: 648px) {\n  .inbox-leftbar {\n    width: 100%;\n    float: none; }\n  .inbox-rightbar {\n    margin-left: 0;\n    border: 0;\n    padding-left: 0; }\n  .message-list li .col-mail-1 .checkbox-wrapper-mail {\n    margin-left: 0; } }\n\n@media (max-width: 520px) {\n  .inbox-rightbar > .btn-group {\n    margin-bottom: 10px; }\n  .message-list li .col-mail-1 {\n    width: 150px; }\n    .message-list li .col-mail-1 .title {\n      left: 80px; }\n  .message-list li .col-mail-2 {\n    left: 160px; }\n    .message-list li .col-mail-2 .date {\n      text-align: right;\n      padding-right: 10px;\n      padding-left: 20px; } }\n\n.sitemap {\n  list-style: none;\n  padding-left: 0; }\n  .sitemap > li > ul {\n    margin-top: 1rem;\n    padding-left: 0; }\n  .sitemap li {\n    line-height: 2rem;\n    vertical-align: top;\n    list-style: none;\n    position: relative; }\n    .sitemap li a {\n      text-decoration: none;\n      color: #72747b;\n      display: block;\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis; }\n      .sitemap li a:hover {\n        color: #7e57c2; }\n  .sitemap ul {\n    margin-left: 1rem;\n    margin-bottom: 1rem;\n    padding-top: 10px; }\n    .sitemap ul li {\n      position: relative; }\n      .sitemap ul li a {\n        margin-left: 2.75rem; }\n      .sitemap ul li:before {\n        content: \"\";\n        display: inline-block;\n        width: 2rem;\n        height: 2rem;\n        border-bottom: 1px #ccc solid;\n        border-left: 1px solid rgba(152, 166, 173, 0.5);\n        position: absolute;\n        top: -1rem; }\n\n.search-result-box .tab-content {\n  padding: 30px 30px 10px 30px;\n  -webkit-box-shadow: none;\n  box-shadow: none;\n  -moz-box-shadow: none; }\n\n.search-result-box .search-item {\n  padding-bottom: 20px;\n  border-bottom: 1px solid #f1f5f7;\n  margin-bottom: 20px; }\n\n.search-result-box .nav-bordered .nav-link {\n  padding: 10px 5px !important;\n  margin-right: 10px; }\n\n.card-pricing {\n  position: relative; }\n  .card-pricing .card-pricing-plan-name {\n    padding-bottom: 20px; }\n  .card-pricing .card-pricing-icon {\n    font-size: 22px;\n    background-color: rgba(126, 87, 194, 0.1);\n    height: 60px;\n    display: inline-block;\n    width: 60px;\n    line-height: 62px;\n    border-radius: 50%; }\n  .card-pricing .card-pricing-price {\n    padding: 30px 0 0; }\n    .card-pricing .card-pricing-price span {\n      font-size: 40%;\n      color: #98a6ad;\n      letter-spacing: 2px;\n      text-transform: uppercase; }\n  .card-pricing .card-pricing-features {\n    color: #98a6ad;\n    list-style: none;\n    margin: 0;\n    padding: 20px 0 0 0; }\n    .card-pricing .card-pricing-features li {\n      padding: 10px; }\n\n.card-pricing-recommended {\n  background-color: #7e57c2;\n  color: #fff; }\n  .card-pricing-recommended .card-pricing-icon {\n    background-color: rgba(255, 255, 255, 0.1); }\n  .card-pricing-recommended .card-pricing-features, .card-pricing-recommended .card-pricing-price span {\n    color: #dee2e6; }\n\n.filter-menu {\n  margin-bottom: 20px; }\n  .filter-menu a {\n    transition: all 0.3s ease-out;\n    color: #323a46;\n    border-radius: 3px;\n    padding: 5px 10px;\n    display: inline-block;\n    margin-bottom: 5px;\n    font-weight: 500;\n    font-family: \"Poppins\", sans-serif; }\n    .filter-menu a:hover {\n      background-color: #7e57c2;\n      color: #fff; }\n  .filter-menu a.active {\n    background-color: #7e57c2;\n    color: #fff; }\n\n.gal-box {\n  background-color: #fff;\n  border-radius: 3px;\n  box-shadow: 0 2px 6px -2px rgba(0, 0, 0, 0.16), 0 2px 6px 0 rgba(0, 0, 0, 0.12);\n  margin-bottom: 24px; }\n  .gal-box .image-popup {\n    padding: 10px;\n    display: block; }\n    .gal-box .image-popup img {\n      cursor: zoom-in; }\n  .gal-box .gall-info {\n    padding: 15px;\n    border-top: 1px solid #f7f7f7;\n    position: relative; }\n    .gal-box .gall-info h4 {\n      display: block;\n      overflow: hidden;\n      white-space: nowrap;\n      text-overflow: ellipsis; }\n    .gal-box .gall-info .gal-like-btn {\n      position: absolute;\n      right: 15px;\n      font-size: 22px;\n      top: 24px; }\n\n.counter-number {\n  font-size: 32px;\n  font-weight: 600; }\n  .counter-number span {\n    font-size: 15px;\n    font-weight: 400;\n    display: block; }\n\n.coming-box {\n  float: left;\n  width: 25%; }\n\n.svg-rocket {\n  height: 80px; }\n\n.rocket-clouds__bubble,\n.rocket-clouds__cloud,\n.rocket-rocket,\n.rocket-inner__rocket-and-lines {\n  fill: #7e57c2; }\n\n.post-user-comment-box {\n  background-color: #f1f5f7;\n  margin: 0 -.75rem;\n  padding: 1rem;\n  margin-top: 20px; }\n\n.taskList {\n  min-height: 40px;\n  margin-bottom: 0; }\n\n.taskList li {\n  background-color: #fff;\n  border: 1px solid #dee2e6;\n  padding: 20px;\n  margin-bottom: 15px;\n  border-radius: 3px; }\n\n.taskList li:last-of-type {\n  margin-bottom: 0; }\n\n.taskList li .btn-sm {\n  padding: 2px 8px;\n  font-size: 12px; }\n\n.taskList .checkbox {\n  margin-left: 20px;\n  margin-top: 5px; }\n\n.task-placeholder {\n  border: 1px dashed #dee2e6 !important;\n  background-color: #f1f5f7 !important;\n  padding: 20px; }\n\n.product-box {\n  position: relative;\n  overflow: hidden; }\n  .product-box .product-action {\n    position: absolute;\n    right: 0;\n    top: 0;\n    padding: 1.5rem 1.5rem 0 1.5rem;\n    z-index: 3;\n    opacity: 0;\n    visibility: hidden;\n    transform: translateX(100%);\n    transition: all 0.3s ease 0s; }\n  .product-box:hover .product-action {\n    opacity: 1;\n    visibility: visible;\n    transform: translateX(0); }\n  .product-box .product-info {\n    padding-top: 1.5rem; }\n  .product-box .product-price-tag {\n    height: 48px;\n    line-height: 48px;\n    font-weight: 700;\n    font-size: 20px;\n    background-color: #f1f5f7;\n    text-align: center;\n    padding: 0 10px;\n    border-radius: 3px; }\n\n.product-thumb {\n  padding: 3px;\n  margin-top: 3px; }\n  .product-thumb.active {\n    background-color: #72747b !important; }\n\n/*!\n* metismenu - v2.7.9\n* A jQuery menu plugin\n* https://github.com/onokumus/metismenu#readme\n*\n* Made by Osman Nuri Okumus <<EMAIL>> (https://github.com/onokumus)\n* Under MIT License\n*/\n.metismenu .arrow {\n  float: right;\n  line-height: 1.42857; }\n\n*[dir=\"rtl\"] .metismenu .arrow {\n  float: left; }\n\n/*\n * Require Bootstrap 3.x\n * https://github.com/twbs/bootstrap\n*/\n.metismenu .glyphicon.arrow:before {\n  content: \"\\e079\"; }\n\n.metismenu .active > a > .glyphicon.arrow:before {\n  content: \"\\e114\"; }\n\n/*\n * Require Font-Awesome\n * http://fortawesome.github.io/Font-Awesome/\n*/\n.metismenu .fa.arrow:before {\n  content: \"\\f104\"; }\n\n.metismenu .active > a > .fa.arrow:before {\n  content: \"\\f107\"; }\n\n/*\n * Require Ionicons\n * http://ionicons.com/\n*/\n.metismenu .ion.arrow:before {\n  content: \"\\f3d2\"; }\n\n.metismenu .active > a > .ion.arrow:before {\n  content: \"\\f3d0\"; }\n\n.metismenu .plus-times {\n  float: right; }\n\n*[dir=\"rtl\"] .metismenu .plus-times {\n  float: left; }\n\n.metismenu .fa.plus-times:before {\n  content: \"\\f067\"; }\n\n.metismenu .active > a > .fa.plus-times {\n  -webkit-transform: rotate(45deg);\n  transform: rotate(45deg); }\n\n.metismenu .plus-minus {\n  float: right; }\n\n*[dir=\"rtl\"] .metismenu .plus-minus {\n  float: left; }\n\n.metismenu .fa.plus-minus:before {\n  content: \"\\f067\"; }\n\n.metismenu .active > a > .fa.plus-minus:before {\n  content: \"\\f068\"; }\n\n.metismenu .collapse {\n  display: none; }\n\n.metismenu .collapse.in {\n  display: block; }\n\n.metismenu .collapsing {\n  position: relative;\n  height: 0;\n  overflow: hidden;\n  transition-timing-function: ease;\n  transition-duration: .35s;\n  transition-property: height, visibility; }\n\n.metismenu .has-arrow {\n  position: relative; }\n\n.metismenu .has-arrow::after {\n  position: absolute;\n  content: '';\n  width: .5em;\n  height: .5em;\n  border-width: 1px 0 0 1px;\n  border-style: solid;\n  border-color: initial;\n  right: 1em;\n  -webkit-transform: rotate(-45deg) translate(0, -50%);\n  transform: rotate(-45deg) translate(0, -50%);\n  -webkit-transform-origin: top;\n  transform-origin: top;\n  top: 50%;\n  transition: all .3s ease-out; }\n\n*[dir=\"rtl\"] .metismenu .has-arrow::after {\n  right: auto;\n  left: 1em;\n  -webkit-transform: rotate(135deg) translate(0, -50%);\n  transform: rotate(135deg) translate(0, -50%); }\n\n.metismenu .active > .has-arrow::after,\n.metismenu .has-arrow[aria-expanded=\"true\"]::after {\n  -webkit-transform: rotate(-135deg) translate(0, -50%);\n  transform: rotate(-135deg) translate(0, -50%); }\n\n*[dir=\"rtl\"] .metismenu .active > .has-arrow::after,\n*[dir=\"rtl\"] .metismenu .has-arrow[aria-expanded=\"true\"]::after {\n  -webkit-transform: rotate(225deg) translate(0, -50%);\n  transform: rotate(225deg) translate(0, -50%); }\n\n/*# sourceMappingURL=metisMenu.css.map */\n/*!\r\n * Waves v0.7.6\r\n * http://fian.my.id/Waves \r\n * \r\n * Copyright 2014-2018 Alfiana E. Sibuea and other contributors \r\n * Released under the MIT license \r\n * https://github.com/fians/Waves/blob/master/LICENSE */\n.waves-effect {\n  position: relative;\n  cursor: pointer;\n  display: inline-block;\n  overflow: hidden;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  -webkit-tap-highlight-color: transparent; }\n\n.waves-effect .waves-ripple {\n  position: absolute;\n  border-radius: 50%;\n  width: 100px;\n  height: 100px;\n  margin-top: -50px;\n  margin-left: -50px;\n  opacity: 0;\n  background: rgba(0, 0, 0, 0.2);\n  background: -webkit-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -o-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -moz-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  -webkit-transition: all 0.5s ease-out;\n  -moz-transition: all 0.5s ease-out;\n  -o-transition: all 0.5s ease-out;\n  transition: all 0.5s ease-out;\n  -webkit-transition-property: -webkit-transform, opacity;\n  -moz-transition-property: -moz-transform, opacity;\n  -o-transition-property: -o-transform, opacity;\n  transition-property: transform, opacity;\n  -webkit-transform: scale(0) translate(0, 0);\n  -moz-transform: scale(0) translate(0, 0);\n  -ms-transform: scale(0) translate(0, 0);\n  -o-transform: scale(0) translate(0, 0);\n  transform: scale(0) translate(0, 0);\n  pointer-events: none; }\n\n.waves-effect.waves-light .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n  background: -webkit-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -o-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -moz-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%); }\n\n.waves-effect.waves-classic .waves-ripple {\n  background: rgba(0, 0, 0, 0.2); }\n\n.waves-effect.waves-classic.waves-light .waves-ripple {\n  background: rgba(255, 255, 255, 0.4); }\n\n.waves-notransition {\n  -webkit-transition: none !important;\n  -moz-transition: none !important;\n  -o-transition: none !important;\n  transition: none !important; }\n\n.waves-button,\n.waves-circle {\n  -webkit-transform: translateZ(0);\n  -moz-transform: translateZ(0);\n  -ms-transform: translateZ(0);\n  -o-transform: translateZ(0);\n  transform: translateZ(0);\n  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%); }\n\n.waves-button,\n.waves-button:hover,\n.waves-button:visited,\n.waves-button-input {\n  white-space: nowrap;\n  vertical-align: middle;\n  cursor: pointer;\n  border: none;\n  outline: none;\n  color: inherit;\n  background-color: rgba(0, 0, 0, 0);\n  font-size: 1em;\n  line-height: 1em;\n  text-align: center;\n  text-decoration: none;\n  z-index: 1; }\n\n.waves-button {\n  padding: 0.85em 1.1em;\n  border-radius: 0.2em; }\n\n.waves-button-input {\n  margin: 0;\n  padding: 0.85em 1.1em; }\n\n.waves-input-wrapper {\n  border-radius: 0.2em;\n  vertical-align: bottom; }\n\n.waves-input-wrapper.waves-button {\n  padding: 0; }\n\n.waves-input-wrapper .waves-button-input {\n  position: relative;\n  top: 0;\n  left: 0;\n  z-index: 1; }\n\n.waves-circle {\n  text-align: center;\n  width: 2.5em;\n  height: 2.5em;\n  line-height: 2.5em;\n  border-radius: 50%; }\n\n.waves-float {\n  -webkit-mask-image: none;\n  -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\n  box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\n  -webkit-transition: all 300ms;\n  -moz-transition: all 300ms;\n  -o-transition: all 300ms;\n  transition: all 300ms; }\n\n.waves-float:active {\n  -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\n  box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3); }\n\n.waves-block {\n  display: block; }\n\n.irs--flat .irs-bar,\n.irs--flat .irs-from,\n.irs--flat .irs-to,\n.irs--flat .irs-single,\n.irs--modern .irs-bar,\n.irs--modern .irs-from,\n.irs--modern .irs-to,\n.irs--modern .irs-single {\n  background-color: #7e57c2; }\n\n.irs--flat .irs-handle.state_hover > i:first-child,\n.irs--flat .irs-handle:hover > i:first-child,\n.irs--flat .irs-handle > i:first-child,\n.irs--modern .irs-handle.state_hover > i:first-child,\n.irs--modern .irs-handle:hover > i:first-child,\n.irs--modern .irs-handle > i:first-child {\n  background-color: #7044bb; }\n\n.irs--flat .irs-from:before,\n.irs--flat .irs-to:before,\n.irs--flat .irs-single:before,\n.irs--modern .irs-from:before,\n.irs--modern .irs-to:before,\n.irs--modern .irs-single:before {\n  border-top-color: #7e57c2; }\n\n.irs--modern .irs-line {\n  border: none; }\n\n.irs--modern .irs-bar {\n  background: #7e57c2;\n  background: linear-gradient(to bottom, #7e57c2 0%, #643da9 100%); }\n\n.irs--sharp .irs-from,\n.irs--sharp .irs-to,\n.irs--sharp .irs-single,\n.irs--sharp .irs-min,\n.irs--sharp .irs-max,\n.irs--sharp .irs-handle,\n.irs--sharp .irs-bar {\n  background-color: #7e57c2; }\n\n.irs--sharp .irs-line {\n  background-color: #dee2e6; }\n\n.irs--sharp .irs-from:before,\n.irs--sharp .irs-to:before,\n.irs--sharp .irs-single:before,\n.irs--sharp .irs-handle > i:first-child {\n  border-top-color: #7e57c2; }\n\n.irs--sharp .irs-handle.state_hover,\n.irs--sharp .irs-handle:hover {\n  background-color: #593696; }\n\n.irs--sharp .irs-handle.state_hover > i:first-child,\n.irs--sharp .irs-handle:hover > i:first-child {\n  border-top-color: #593696; }\n\n.irs--round .irs-from,\n.irs--round .irs-to,\n.irs--round .irs-single,\n.irs--round .irs-bar {\n  background-color: #7e57c2; }\n  .irs--round .irs-from:before,\n  .irs--round .irs-to:before,\n  .irs--round .irs-single:before,\n  .irs--round .irs-bar:before {\n    border-top-color: #7e57c2; }\n\n.irs--round .irs-handle {\n  border: 4px solid #7e57c2;\n  box-shadow: 0 1px 3px rgba(126, 87, 194, 0.3); }\n\n.irs--round .irs-min,\n.irs--round .irs-max {\n  color: #72747b;\n  background-color: #dee2e6; }\n\n.irs--square .irs-from,\n.irs--square .irs-to,\n.irs--square .irs-single,\n.irs--square .irs-bar {\n  background-color: #343a40; }\n\n.irs--square .irs-handle {\n  border: 3px solid #343a40; }\n\n.calendar {\n  float: left;\n  margin-bottom: 0; }\n\n.fc-view {\n  margin-top: 30px; }\n\n.none-border .modal-footer {\n  border-top: none; }\n\n.fc-toolbar {\n  margin: 15px 0 5px 0; }\n  .fc-toolbar h2 {\n    font-size: 1.25rem;\n    line-height: 1.875rem;\n    text-transform: uppercase; }\n\n.fc-day-grid-event .fc-time {\n  font-weight: 700; }\n\n.fc-day {\n  background: #fff; }\n\n.fc-toolbar .fc-state-active,\n.fc-toolbar .ui-state-active,\n.fc-toolbar button:focus,\n.fc-toolbar button:hover,\n.fc-toolbar .ui-state-hover {\n  z-index: 0; }\n\n.fc th.fc-widget-header {\n  background: #f1f5f7;\n  font-size: 13px;\n  line-height: 20px;\n  padding: 10px 0;\n  text-transform: uppercase; }\n\n.fc-unthemed th,\n.fc-unthemed td,\n.fc-unthemed thead,\n.fc-unthemed tbody,\n.fc-unthemed .fc-divider,\n.fc-unthemed .fc-row,\n.fc-unthemed .fc-popover {\n  border-color: #dee2e6; }\n\n.fc-ltr .fc-basic-view .fc-day-top .fc-day-number {\n  float: right;\n  height: 20px;\n  width: 20px;\n  text-align: center;\n  line-height: 20px;\n  background-color: #f1f5f7;\n  border-radius: 50%;\n  margin: 5px;\n  font-family: \"Poppins\", sans-serif;\n  font-size: 12px; }\n\n.fc-button {\n  background: #f1f5f7;\n  border: none;\n  color: #72747b;\n  text-transform: capitalize;\n  box-shadow: none;\n  border-radius: 3px;\n  margin: 0 3px;\n  padding: 6px 12px;\n  height: auto; }\n\n.fc-text-arrow {\n  font-family: inherit;\n  font-size: 1rem; }\n\n.fc-state-hover {\n  background: #f1f5f7; }\n\n.fc-state-highlight {\n  background: #dee2e6; }\n\n.fc-state-down,\n.fc-state-active,\n.fc-state-disabled {\n  background-color: #7e57c2;\n  color: #fff;\n  text-shadow: none; }\n\n.fc-cell-overlay {\n  background: #dee2e6; }\n\n.fc-unthemed .fc-today {\n  background: #fff; }\n\n.fc-event {\n  border-radius: 2px;\n  border: none;\n  cursor: move;\n  font-size: 0.8125rem;\n  margin: 5px 7px;\n  padding: 5px 5px;\n  text-align: center; }\n\n.external-event {\n  cursor: move;\n  margin: 10px 0;\n  padding: 8px 10px;\n  color: #fff; }\n\n.fc-basic-view td.fc-week-number span {\n  padding-right: 8px; }\n\n.fc-basic-view td.fc-day-number {\n  padding-right: 8px; }\n\n.fc-basic-view .fc-content {\n  color: #fff; }\n\n.fc-time-grid-event .fc-content {\n  color: #fff; }\n\n@media (max-width: 767.98px) {\n  .fc-toolbar .fc-left, .fc-toolbar .fc-right, .fc-toolbar .fc-center {\n    float: none;\n    display: block;\n    clear: both;\n    margin: 10px 0; }\n  .fc .fc-toolbar > * > * {\n    float: none; }\n  .fc-today-button {\n    display: none; } }\n\n.jqstooltip {\n  box-sizing: content-box;\n  width: auto !important;\n  height: auto !important;\n  background-color: #fff !important;\n  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);\n  padding: 5px 10px !important;\n  border-radius: 3px;\n  border-color: #fff !important; }\n\n.jqsfield {\n  color: #323a46 !important;\n  font-size: 12px !important;\n  line-height: 18px !important;\n  font-family: \"Poppins\", sans-serif !important;\n  font-weight: 600 !important; }\n\n.dataTables_wrapper.container-fluid {\n  padding: 0; }\n\ntable.dataTable {\n  border-collapse: collapse !important;\n  margin-bottom: 15px !important; }\n  table.dataTable tbody > tr.selected, table.dataTable tbody > tr > .selected {\n    background-color: #7e57c2; }\n    table.dataTable tbody > tr.selected td, table.dataTable tbody > tr > .selected td {\n      border-color: #7e57c2; }\n  table.dataTable tbody td:focus {\n    outline: none !important; }\n  table.dataTable tbody th.focus, table.dataTable tbody td.focus {\n    outline: 2px solid #7e57c2 !important;\n    outline-offset: -1px;\n    color: #7e57c2;\n    background-color: rgba(126, 87, 194, 0.15); }\n\n.dataTables_info {\n  font-weight: 600; }\n\ntable.dataTable.dtr-inline.collapsed > tbody > tr[role=row] > td:first-child:before, table.dataTable.dtr-inline.collapsed > tbody > tr[role=row] > th:first-child:before {\n  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);\n  background-color: #1abc9c;\n  top: 0.85rem; }\n\ntable.dataTable.dtr-inline.collapsed > tbody > tr.parent > td:first-child:before, table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th:first-child:before {\n  background-color: #f1556c;\n  top: 0.85rem; }\n\ndiv.dt-button-info {\n  background-color: #7e57c2;\n  border: none;\n  color: #fff;\n  box-shadow: none;\n  border-radius: 3px;\n  text-align: center;\n  z-index: 21; }\n  div.dt-button-info h2 {\n    border-bottom: none;\n    background-color: rgba(255, 255, 255, 0.2);\n    color: #fff; }\n\n@media (max-width: 767.98px) {\n  li.paginate_button.previous, li.paginate_button.next {\n    display: inline-block;\n    font-size: 1.5rem; }\n  li.paginate_button {\n    display: none; }\n  .dataTables_paginate ul {\n    text-align: center;\n    display: block;\n    margin: 1.5rem 0 0 !important; }\n  div.dt-buttons {\n    display: inline-table;\n    margin-bottom: 1.5rem; } }\n\n.activate-select .sorting_1 {\n  background-color: #f1f5f7; }\n\n.daterangepicker {\n  font-family: \"Poppins\", sans-serif; }\n  .daterangepicker td.active, .daterangepicker td.active:hover, .daterangepicker .ranges li.active {\n    background-color: #7e57c2; }\n\n.datepicker {\n  padding: 10px !important; }\n  .datepicker td,\n  .datepicker th {\n    width: 30px;\n    height: 30px;\n    border-radius: 50%; }\n  .datepicker table tr td.active.active, .datepicker table tr td.active.disabled, .datepicker table tr td.active.disabled.active, .datepicker table tr td.active.disabled.disabled, .datepicker table tr td.active.disabled:active, .datepicker table tr td.active.disabled:hover, .datepicker table tr td.active.disabled:hover.active, .datepicker table tr td.active.disabled:hover.disabled, .datepicker table tr td.active.disabled:hover:active, .datepicker table tr td.active.disabled:hover:hover,\n  .datepicker table tr td .active.disabled:hover[disabled],\n  .datepicker table tr td .active.disabled[disabled],\n  .datepicker table tr td .active:active,\n  .datepicker table tr td .active:hover,\n  .datepicker table tr td .active:hover.active,\n  .datepicker table tr td .active:hover.disabled,\n  .datepicker table tr td .active:hover:active,\n  .datepicker table tr td .active:hover:hover,\n  .datepicker table tr td .active:hover[disabled],\n  .datepicker table tr td .active[disabled],\n  .datepicker table tr td span.active.active,\n  .datepicker table tr td span.active.disabled,\n  .datepicker table tr td span.active.disabled.active,\n  .datepicker table tr td span.active.disabled.disabled,\n  .datepicker table tr td span.active.disabled:active,\n  .datepicker table tr td span.active.disabled:hover,\n  .datepicker table tr td span.active.disabled:hover.active,\n  .datepicker table tr td span.active.disabled:hover.disabled,\n  .datepicker table tr td span.active.disabled:hover:active,\n  .datepicker table tr td span.active.disabled:hover:hover,\n  .datepicker table tr td span.active.disabled:hover[disabled],\n  .datepicker table tr td span.active.disabled[disabled],\n  .datepicker table tr td span.active:active,\n  .datepicker table tr td span.active:hover,\n  .datepicker table tr td span.active:hover.active,\n  .datepicker table tr td span.active:hover.disabled,\n  .datepicker table tr td span.active:hover:active,\n  .datepicker table tr td span.active:hover:hover,\n  .datepicker table tr td span.active:hover[disabled],\n  .datepicker table tr td span.active[disabled], .datepicker table tr td.today, .datepicker table tr td.today.disabled, .datepicker table tr td.today.disabled:hover, .datepicker table tr td.today:hover {\n    background-color: #7e57c2 !important;\n    background-image: none !important;\n    color: #fff; }\n  .datepicker table tr td.day.focused, .datepicker table tr td.day:hover,\n  .datepicker table tr td span.focused,\n  .datepicker table tr td span:hover {\n    background: #f7f7f7; }\n  .datepicker table tr td.new, .datepicker table tr td.old,\n  .datepicker table tr td span.new,\n  .datepicker table tr td span.old {\n    color: #72747b;\n    opacity: 0.4; }\n  .datepicker .datepicker-switch:hover,\n  .datepicker .next:hover,\n  .datepicker .prev:hover,\n  .datepicker tfoot tr th:hover {\n    background: #f7f7f7; }\n  .datepicker .datepicker-switch:hover {\n    background: none; }\n\n.datepicker-dropdown:after {\n  border-bottom: 6px solid #fff; }\n\n.datepicker-dropdown:before {\n  border-bottom-color: #e7eef1; }\n\n.datepicker-dropdown.datepicker-orient-top:before {\n  border-top: 7px solid #e7eef1; }\n\n.datepicker-dropdown.datepicker-orient-top:after {\n  border-top: 6px solid #fff; }\n\n.form-wizard-header {\n  margin-left: -1.5rem;\n  margin-right: -1.5rem; }\n\n.select2-container .select2-selection--single {\n  border: 1px solid #ced4da;\n  height: 38px;\n  outline: none; }\n  .select2-container .select2-selection--single .select2-selection__rendered {\n    line-height: 36px;\n    padding-left: 12px; }\n  .select2-container .select2-selection--single .select2-selection__arrow {\n    height: 34px;\n    width: 34px;\n    right: 3px; }\n    .select2-container .select2-selection--single .select2-selection__arrow b {\n      border-color: #c0d2db transparent transparent transparent;\n      border-width: 6px 6px 0 6px; }\n\n.select2-container--open .select2-selection--single .select2-selection__arrow b {\n  border-color: transparent transparent #c0d2db transparent !important;\n  border-width: 0 6px 6px 6px !important; }\n\n.select2-results__option {\n  padding: 6px 12px; }\n\n.select2-dropdown {\n  border: 1px solid #e1e9ee;\n  box-shadow: 0 2px 6px -2px rgba(0, 0, 0, 0.16), 0 2px 6px 0 rgba(0, 0, 0, 0.12); }\n\n.select2-container--default .select2-search--dropdown {\n  padding: 10px;\n  background-color: white; }\n  .select2-container--default .select2-search--dropdown .select2-search__field {\n    border: 1px solid #e1e9ee;\n    outline: none; }\n\n.select2-container--default .select2-results__option--highlighted[aria-selected] {\n  background-color: #7e57c2; }\n\n.select2-container--default .select2-results__option[aria-selected=true] {\n  background-color: #f1f5f7;\n  color: #323a46; }\n  .select2-container--default .select2-results__option[aria-selected=true]:hover {\n    background-color: #7e57c2;\n    color: #fff; }\n\n.select2-container .select2-selection--multiple {\n  min-height: 38px;\n  border: 1px solid #ced4da !important; }\n  .select2-container .select2-selection--multiple .select2-selection__rendered {\n    padding: 1px 10px; }\n  .select2-container .select2-selection--multiple .select2-search__field {\n    border: 0; }\n  .select2-container .select2-selection--multiple .select2-selection__choice {\n    background-color: #7e57c2;\n    border: none;\n    color: #fff;\n    border-radius: 3px;\n    padding: 0 7px;\n    margin-top: 7px; }\n  .select2-container .select2-selection--multiple .select2-selection__choice__remove {\n    color: #fff;\n    margin-right: 5px; }\n    .select2-container .select2-selection--multiple .select2-selection__choice__remove:hover {\n      color: #fff; }\n\n.slimScrollDiv {\n  height: auto !important; }\n\n.jq-toast-single {\n  padding: 15px;\n  font-family: \"Poppins\", sans-serif;\n  background-color: #7e57c2;\n  font-size: 13px;\n  line-height: 22px; }\n  .jq-toast-single h2 {\n    font-family: \"Poppins\", sans-serif; }\n  .jq-toast-single a {\n    font-size: 0.8125rem; }\n    .jq-toast-single a:hover {\n      color: #fff; }\n\n.jq-has-icon {\n  padding: 10px 10px 10px 50px; }\n\n.close-jq-toast-single {\n  position: absolute;\n  top: -12px;\n  right: -12px;\n  font-size: 20px;\n  cursor: pointer;\n  height: 32px;\n  width: 32px;\n  background-color: #323a46;\n  border-radius: 50%;\n  text-align: center;\n  line-height: 32px; }\n\n.jq-toast-loader {\n  height: 3px;\n  top: 0;\n  border-radius: 0; }\n\n.jq-icon-primary {\n  background-color: #7e57c2;\n  color: #fff;\n  border-color: #7e57c2; }\n\n.jq-icon-secondary {\n  background-color: #72747b;\n  color: #fff;\n  border-color: #72747b; }\n\n.jq-icon-success {\n  background-color: #1abc9c;\n  color: #fff;\n  border-color: #1abc9c; }\n\n.jq-icon-info {\n  background-color: #4fc6e1;\n  color: #fff;\n  border-color: #4fc6e1; }\n\n.jq-icon-warning {\n  background-color: #f7b84b;\n  color: #fff;\n  border-color: #f7b84b; }\n\n.jq-icon-danger {\n  background-color: #f1556c;\n  color: #fff;\n  border-color: #f1556c; }\n\n.jq-icon-light {\n  background-color: #f1f5f7;\n  color: #fff;\n  border-color: #f1f5f7; }\n\n.jq-icon-dark {\n  background-color: #323a46;\n  color: #fff;\n  border-color: #323a46; }\n\n.jq-icon-pink {\n  background-color: #f672a7;\n  color: #fff;\n  border-color: #f672a7; }\n\n.jq-icon-blue {\n  background-color: #4a81d4;\n  color: #fff;\n  border-color: #4a81d4; }\n\n.jq-icon-error {\n  background-color: #f1556c;\n  color: #fff;\n  border-color: #f1556c; }\n\n.swal2-modal {\n  font-family: \"Poppins\", sans-serif;\n  box-shadow: 0 10px 33px rgba(0, 0, 0, 0.1); }\n  .swal2-modal .swal2-title {\n    font-size: 24px; }\n  .swal2-modal .swal2-content {\n    font-size: 16px; }\n  .swal2-modal .swal2-spacer {\n    margin: 10px 0; }\n  .swal2-modal .swal2-file, .swal2-modal .swal2-input, .swal2-modal .swal2-textarea {\n    border: 2px solid #dee2e6;\n    font-size: 16px;\n    box-shadow: none; }\n  .swal2-modal .swal2-confirm.btn-confirm {\n    background-color: #7e57c2 !important;\n    font-size: 0.8125rem; }\n  .swal2-modal .swal2-cancel.btn-cancel {\n    background-color: #f1556c !important;\n    font-size: 0.8125rem; }\n  .swal2-modal .swal2-styled:focus {\n    box-shadow: none !important; }\n\n.swal2-icon.swal2-question {\n  color: #7e57c2;\n  border-color: #7e57c2; }\n\n.swal2-icon.swal2-success {\n  border-color: #1abc9c; }\n  .swal2-icon.swal2-success .line, .swal2-icon.swal2-success [class^=swal2-success-line][class$=long],\n  .swal2-icon.swal2-success [class^=swal2-success-line] {\n    background-color: #1abc9c; }\n  .swal2-icon.swal2-success .placeholder, .swal2-icon.swal2-success .swal2-success-ring {\n    border-color: #1abc9c; }\n\n.swal2-icon.swal2-warning {\n  color: #f7b84b;\n  border-color: #f7b84b; }\n\n.swal2-icon.swal2-error {\n  border-color: #f1556c; }\n  .swal2-icon.swal2-error .line {\n    background-color: #f1556c; }\n\n.swal2-modal .swal2-file:focus, .swal2-modal .swal2-input:focus, .swal2-modal .swal2-textarea:focus {\n  outline: 0;\n  border: 2px solid #7e57c2; }\n\n.swal2-container.swal2-shown {\n  background-color: rgba(50, 58, 70, 0.9); }\n\n[data-plugin=\"customselect\"] {\n  display: none; }\n\n.nice-select {\n  line-height: 36px;\n  height: 38px;\n  border-color: #ced4da;\n  border-radius: 0.2rem; }\n\n.nice-select.open, .nice-select:active, .nice-select:focus {\n  border-color: #b1bbc4; }\n\n.nice-select.small {\n  height: 32px;\n  line-height: 30px; }\n\n.tippy-tooltip {\n  font-size: 0.875rem; }\n  .tippy-tooltip .light-theme[data-animatefill] {\n    background-color: transparent; }\n\n.light-theme {\n  color: #323a46;\n  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);\n  background-color: #fff; }\n  .light-theme .tippy-backdrop {\n    background-color: #fff; }\n  .light-theme .tippy-roundarrow {\n    fill: #fff; }\n\n.gradient-theme .tippy-backdrop {\n  background: #7e57c2;\n  /* fallback for old browsers */\n  background: linear-gradient(to left, #f1556c, #7e57c2);\n  /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */ }\n\n.tippy-popper[x-placement^=top] .tippy-tooltip.light-theme .tippy-arrow {\n  border-top: 7px solid #fff;\n  border-right: 7px solid transparent;\n  border-left: 7px solid transparent; }\n\n.tippy-popper[x-placement^=bottom] .tippy-tooltip.light-theme .tippy-arrow {\n  border-bottom: 7px solid #fff;\n  border-right: 7px solid transparent;\n  border-left: 7px solid transparent; }\n\n.tippy-popper[x-placement^=left] .tippy-tooltip.light-theme .tippy-arrow {\n  border-left: 7px solid #fff;\n  border-top: 7px solid transparent;\n  border-bottom: 7px solid transparent; }\n\n.tippy-popper[x-placement^=right] .tippy-tooltip.light-theme .tippy-arrow {\n  border-right: 7px solid #fff;\n  border-top: 7px solid transparent;\n  border-bottom: 7px solid transparent; }\n\n.dd-list .dd-item .dd-handle {\n  background: #f1f5f7;\n  border: none;\n  padding: 8px 16px;\n  height: auto;\n  font-weight: 600;\n  border-radius: 3px; }\n  .dd-list .dd-item .dd-handle:hover {\n    color: #7e57c2; }\n\n.dd-list .dd-item button {\n  height: 36px;\n  font-size: 17px;\n  margin: 0;\n  color: #98a6ad;\n  width: 36px; }\n\n.dd-list .dd3-item {\n  margin: 5px 0; }\n  .dd-list .dd3-item .dd-item button {\n    width: 36px;\n    height: 36px; }\n\n.dd-list .dd3-handle {\n  margin: 0;\n  height: 36px !important;\n  float: left; }\n\n.dd-list .dd3-content {\n  height: auto;\n  border: none;\n  padding: 8px 16px 8px 46px;\n  background: #f1f5f7;\n  font-weight: 600; }\n  .dd-list .dd3-content:hover {\n    color: #7e57c2; }\n\n.dd-list .dd3-handle:before {\n  content: \"\\F35C\";\n  font-family: \"Material Design Icons\";\n  color: #adb5bd; }\n\n.dd-empty,\n.dd-placeholder {\n  background: rgba(206, 212, 218, 0.2); }\n\n.dd-dragel .dd-handle {\n  box-shadow: 0 2px 6px -2px rgba(0, 0, 0, 0.16), 0 2px 6px 0 rgba(0, 0, 0, 0.12); }\n\ndiv.hopscotch-bubble {\n  border: 3px solid #7e57c2;\n  border-radius: 5px; }\n  div.hopscotch-bubble .hopscotch-next,\n  div.hopscotch-bubble .hopscotch-prev {\n    background-color: #7e57c2 !important;\n    background-image: none !important;\n    border-color: #7e57c2 !important;\n    text-shadow: none !important;\n    margin: 0 0 0 5px !important;\n    font-family: \"Poppins\", sans-serif;\n    color: #fff !important; }\n  div.hopscotch-bubble .hopscotch-bubble-number {\n    background: #f1556c;\n    padding: 0;\n    border-radius: 50%; }\n  div.hopscotch-bubble .hopscotch-bubble-arrow-container.left .hopscotch-bubble-arrow-border {\n    border-right: 19px solid #7e57c2; }\n  div.hopscotch-bubble .hopscotch-bubble-arrow-container.left .hopscotch-bubble-arrow {\n    border: none; }\n  div.hopscotch-bubble .hopscotch-bubble-arrow-container.right .hopscotch-bubble-arrow {\n    border-left: 19px solid #7e57c2;\n    left: -2px; }\n  div.hopscotch-bubble .hopscotch-bubble-arrow-container.right .hopscotch-bubble-arrow-border {\n    border-left: 0 solid #7e57c2; }\n  div.hopscotch-bubble .hopscotch-bubble-arrow-container.up .hopscotch-bubble-arrow {\n    border-bottom: 19px solid #7e57c2;\n    top: 0; }\n  div.hopscotch-bubble .hopscotch-bubble-arrow-container.up .hopscotch-bubble-arrow-border {\n    border-bottom: 0 solid rgba(0, 0, 0, 0.5); }\n  div.hopscotch-bubble .hopscotch-bubble-arrow-container.down .hopscotch-bubble-arrow {\n    border-top: 19px solid #7e57c2;\n    top: -2px; }\n  div.hopscotch-bubble .hopscotch-bubble-arrow-container.down .hopscotch-bubble-arrow-border {\n    border-top: 0 solid rgba(0, 0, 0, 0.5); }\n  div.hopscotch-bubble h3 {\n    font-family: \"Poppins\", sans-serif;\n    margin-bottom: 10px; }\n  div.hopscotch-bubble .hopscotch-content {\n    font-family: \"Poppins\", sans-serif; }\n\n.flotTip {\n  padding: 8px 12px;\n  background-color: #323a46;\n  z-index: 100;\n  color: #fff;\n  opacity: 1;\n  border-radius: 3px; }\n\n.legend tr {\n  height: 30px;\n  font-family: \"Poppins\", sans-serif; }\n\n.legendLabel {\n  padding-left: 5px !important;\n  line-height: 10px;\n  padding-right: 20px;\n  font-size: 13px;\n  font-weight: 500;\n  color: #98a6ad;\n  text-transform: uppercase; }\n\n.legendColorBox div div {\n  border-radius: 50%; }\n\n@media (max-width: 767.98px) {\n  .legendLabel {\n    display: none; } }\n\n.morris-chart text {\n  font-family: \"Poppins\", sans-serif !important; }\n\n.morris-hover {\n  position: absolute;\n  z-index: 10; }\n  .morris-hover.morris-default-style {\n    font-size: 12px;\n    text-align: center;\n    border-radius: 5px;\n    padding: 10px 12px;\n    background: #323a46;\n    color: #fff;\n    font-family: \"Poppins\", sans-serif; }\n    .morris-hover.morris-default-style .morris-hover-row-label {\n      font-weight: bold;\n      margin: 0.25em 0;\n      font-family: \"Poppins\", sans-serif; }\n    .morris-hover.morris-default-style .morris-hover-point {\n      white-space: nowrap;\n      margin: 0.1em 0;\n      color: #fff; }\n\n.chartjs-chart {\n  margin: auto;\n  position: relative;\n  width: 100%; }\n\n.ct-golden-section:before {\n  float: none; }\n\n.ct-chart {\n  max-height: 300px; }\n  .ct-chart .ct-label {\n    fill: #adb5bd;\n    color: #adb5bd;\n    font-size: 12px;\n    line-height: 1; }\n\n.ct-chart.simple-pie-chart-chartist .ct-label {\n  color: #fff;\n  fill: #fff;\n  font-size: 16px; }\n\n.ct-chart .ct-series.ct-series-a .ct-bar,\n.ct-chart .ct-series.ct-series-a .ct-line,\n.ct-chart .ct-series.ct-series-a .ct-point,\n.ct-chart .ct-series.ct-series-a .ct-slice-donut {\n  stroke: #7e57c2; }\n\n.ct-chart .ct-series.ct-series-b .ct-bar,\n.ct-chart .ct-series.ct-series-b .ct-line,\n.ct-chart .ct-series.ct-series-b .ct-point,\n.ct-chart .ct-series.ct-series-b .ct-slice-donut {\n  stroke: #4fc6e1; }\n\n.ct-chart .ct-series.ct-series-c .ct-bar,\n.ct-chart .ct-series.ct-series-c .ct-line,\n.ct-chart .ct-series.ct-series-c .ct-point,\n.ct-chart .ct-series.ct-series-c .ct-slice-donut {\n  stroke: #f672a7; }\n\n.ct-chart .ct-series.ct-series-d .ct-bar,\n.ct-chart .ct-series.ct-series-d .ct-line,\n.ct-chart .ct-series.ct-series-d .ct-point,\n.ct-chart .ct-series.ct-series-d .ct-slice-donut {\n  stroke: #4a81d4; }\n\n.ct-chart .ct-series.ct-series-e .ct-bar,\n.ct-chart .ct-series.ct-series-e .ct-line,\n.ct-chart .ct-series.ct-series-e .ct-point,\n.ct-chart .ct-series.ct-series-e .ct-slice-donut {\n  stroke: #323a46; }\n\n.ct-chart .ct-series.ct-series-f .ct-bar,\n.ct-chart .ct-series.ct-series-f .ct-line,\n.ct-chart .ct-series.ct-series-f .ct-point,\n.ct-chart .ct-series.ct-series-f .ct-slice-donut {\n  stroke: #1abc9c; }\n\n.ct-chart .ct-series.ct-series-g .ct-bar,\n.ct-chart .ct-series.ct-series-g .ct-line,\n.ct-chart .ct-series.ct-series-g .ct-point,\n.ct-chart .ct-series.ct-series-g .ct-slice-donut {\n  stroke: #f7b84b; }\n\n.ct-series-a .ct-area,\n.ct-series-a .ct-slice-pie {\n  fill: #7e57c2; }\n\n.ct-series-b .ct-area,\n.ct-series-b .ct-slice-pie {\n  fill: #4fc6e1; }\n\n.ct-series-c .ct-area,\n.ct-series-c .ct-slice-pie {\n  fill: #f672a7; }\n\n.ct-series-d .ct-area,\n.ct-series-d .ct-slice-pie {\n  fill: #4a81d4; }\n\n.ct-area {\n  fill-opacity: .33; }\n\n.chartist-tooltip {\n  position: absolute;\n  display: inline-block;\n  opacity: 0;\n  min-width: 10px;\n  padding: 2px 10px;\n  border-radius: 3px;\n  background: #323a46;\n  color: #fff;\n  text-align: center;\n  pointer-events: none;\n  z-index: 1;\n  transition: opacity .2s linear; }\n  .chartist-tooltip.tooltip-show {\n    opacity: 1; }\n\n.c3-tooltip {\n  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);\n  opacity: 1; }\n  .c3-tooltip td {\n    border-left: none;\n    font-family: \"Poppins\", sans-serif; }\n    .c3-tooltip td > span {\n      background: #323a46; }\n  .c3-tooltip tr {\n    border: none !important; }\n  .c3-tooltip th {\n    background-color: #323a46; }\n\n.c3-chart-arcs-title {\n  font-size: 18px;\n  font-weight: 600; }\n\n.c3 text {\n  font-family: \"Poppins\", sans-serif;\n  color: #ced4da; }\n\n.c3-legend-item {\n  font-family: \"Poppins\", sans-serif;\n  font-size: 14px; }\n\n.c3 line, .c3 path {\n  stroke: #ced4da; }\n\n.c3-chart-arc.c3-target g path {\n  stroke: #fff; }\n\n#legend {\n  background: white;\n  position: absolute;\n  top: 0;\n  right: 15px; }\n\n#legend .line {\n  color: #323a46; }\n\n.rickshaw_graph svg {\n  max-width: 100%; }\n\n.rickshaw_legend .label {\n  font-family: inherit;\n  letter-spacing: 0.01em;\n  font-weight: 600; }\n\n.rickshaw_graph .detail .item,\n.rickshaw_graph .detail .x_label,\n.rickshaw_graph .x_tick .title {\n  font-family: \"Poppins\", sans-serif; }\n\n.gauge-chart text {\n  font-family: \"Poppins\", sans-serif !important; }\n\n.responsive-table-plugin .dropdown-menu li.checkbox-row {\n  padding: 7px 15px; }\n\n.responsive-table-plugin .table-responsive {\n  border: none;\n  margin-bottom: 0; }\n\n.responsive-table-plugin .btn-toolbar {\n  display: block; }\n\n.responsive-table-plugin tbody th {\n  font-size: 14px;\n  font-weight: normal; }\n\n.responsive-table-plugin .checkbox-row {\n  padding-left: 40px; }\n  .responsive-table-plugin .checkbox-row label {\n    display: inline-block;\n    padding-left: 5px;\n    position: relative;\n    margin-bottom: 0; }\n    .responsive-table-plugin .checkbox-row label::before {\n      background-color: #fff;\n      border-radius: 3px;\n      border: 1px solid #ced4da;\n      content: \"\";\n      display: inline-block;\n      height: 17px;\n      left: 0;\n      margin-left: -20px;\n      position: absolute;\n      transition: 0.3s ease-in-out;\n      width: 17px;\n      outline: none; }\n    .responsive-table-plugin .checkbox-row label::after {\n      color: #ced4da;\n      display: inline-block;\n      font-size: 11px;\n      height: 16px;\n      left: 0;\n      margin-left: -20px;\n      padding-left: 3px;\n      padding-top: 1px;\n      position: absolute;\n      top: -2px;\n      width: 16px; }\n  .responsive-table-plugin .checkbox-row input[type=\"checkbox\"] {\n    cursor: pointer;\n    opacity: 0;\n    z-index: 1;\n    outline: none; }\n    .responsive-table-plugin .checkbox-row input[type=\"checkbox\"]:disabled + label {\n      opacity: 0.65; }\n  .responsive-table-plugin .checkbox-row input[type=\"checkbox\"]:focus + label::before {\n    outline-offset: -2px;\n    outline: none; }\n  .responsive-table-plugin .checkbox-row input[type=\"checkbox\"]:checked + label::after {\n    content: \"\\f00c\";\n    font-family: 'Font Awesome 5 Free';\n    font-weight: 900; }\n  .responsive-table-plugin .checkbox-row input[type=\"checkbox\"]:disabled + label::before {\n    background-color: #dee2e6;\n    cursor: not-allowed; }\n  .responsive-table-plugin .checkbox-row input[type=\"checkbox\"]:checked + label::before {\n    background-color: #fff;\n    border-color: #7e57c2; }\n  .responsive-table-plugin .checkbox-row input[type=\"checkbox\"]:checked + label::after {\n    color: #7e57c2; }\n\n.responsive-table-plugin table.focus-on tbody tr.focused th,\n.responsive-table-plugin table.focus-on tbody tr.focused td,\n.responsive-table-plugin .sticky-table-header {\n  background: #7e57c2;\n  color: #fff;\n  border-color: #7e57c2; }\n  .responsive-table-plugin table.focus-on tbody tr.focused th table,\n  .responsive-table-plugin table.focus-on tbody tr.focused td table,\n  .responsive-table-plugin .sticky-table-header table {\n    color: #fff; }\n\n.responsive-table-plugin .fixed-solution .sticky-table-header {\n  top: 70px !important; }\n\n.responsive-table-plugin .btn-default {\n  background-color: #fff;\n  color: #323a46;\n  border: 1px solid rgba(50, 58, 70, 0.3); }\n  .responsive-table-plugin .btn-default.btn-primary {\n    background-color: #7e57c2;\n    border-color: #7e57c2;\n    color: #fff;\n    box-shadow: 0 0 0 2px rgba(126, 87, 194, 0.5); }\n\n.responsive-table-plugin .btn-group.pull-right {\n  float: right; }\n  .responsive-table-plugin .btn-group.pull-right .dropdown-menu {\n    left: auto;\n    right: 0; }\n\n@font-face {\n  font-family: 'footable';\n  src: url(\"../fonts/footable.eot\");\n  src: url(\"../fonts/footable.eot?#iefix\") format(\"embedded-opentype\"), url(\"../fonts/footable.woff\") format(\"woff\"), url(\"../fonts/footable.ttf\") format(\"truetype\"), url(\"../fonts/footable.svg#footable\") format(\"svg\");\n  font-weight: normal;\n  font-style: normal; }\n\n@media screen and (-webkit-min-device-pixel-ratio: 0) {\n  @font-face {\n    font-family: 'footable';\n    src: url(\"../fonts/footable.svg#footable\") format(\"svg\");\n    font-weight: normal;\n    font-style: normal; } }\n\n.footable-odd {\n  background-color: #fff; }\n\n.footable-detail-show {\n  background-color: #f1f5f7; }\n\n.footable-row-detail {\n  background-color: #F0F4F7; }\n\n.footable-pagination li {\n  margin-left: 5px;\n  float: left; }\n  .footable-pagination li a {\n    position: relative;\n    display: block;\n    padding: .5rem .75rem;\n    margin-left: -1px;\n    line-height: 1.25;\n    color: #323a46;\n    background-color: #fff;\n    border: 1px solid #f1f5f7;\n    border-radius: 2px; }\n\n.footable-pagination li.active a {\n  color: #fff;\n  background-color: #7e57c2;\n  border-color: #7e57c2; }\n\n.footable > thead > tr > th > span.footable-sort-indicator {\n  float: right; }\n\n.bootstrap-table .table:not(.table-sm) > tbody > tr > td,\n.bootstrap-table .table:not(.table-sm) > tbody > tr > th,\n.bootstrap-table .table:not(.table-sm) > tfoot > tr > td,\n.bootstrap-table .table:not(.table-sm) > tfoot > tr > th,\n.bootstrap-table .table:not(.table-sm) > thead > tr > td {\n  padding: 0.85rem; }\n\n.bootstrap-table .table {\n  border-bottom: none; }\n\n.bootstrap-table .table > thead > tr > th {\n  border-bottom: 2px solid transparent; }\n\ntable[data-toggle=\"table\"] {\n  display: none; }\n\n.fixed-table-pagination .pagination-detail,\n.fixed-table-pagination div.pagination {\n  margin-top: 20px;\n  margin-bottom: 0; }\n\n.fixed-table-pagination .pagination .page-link {\n  border-radius: 30px !important;\n  margin: 0 3px;\n  border: none; }\n\n.fixed-table-container {\n  border: none; }\n  .fixed-table-container tbody td {\n    border-left: none; }\n  .fixed-table-container thead th .th-inner {\n    padding: 0.85rem; }\n\n.fixed-table-toolbar .fa {\n  font-family: 'Font Awesome 5 Free';\n  font-weight: 400; }\n\n.fixed-table-toolbar .fa-toggle-down:before {\n  content: \"\\f150\"; }\n\n.fixed-table-toolbar .fa-toggle-up:before {\n  content: \"\\f151\"; }\n\n.fixed-table-toolbar .fa-refresh:before {\n  content: \"\\f01e\";\n  font-weight: 900; }\n\n.fixed-table-toolbar .fa-th-list:before {\n  content: \"\\f0ca\";\n  font-weight: 900; }\n\n.tablesaw thead {\n  background: #f1f5f7;\n  background-image: none;\n  border: none; }\n  .tablesaw thead th {\n    text-shadow: none; }\n  .tablesaw thead tr:first-child th {\n    border: none;\n    font-weight: 500;\n    font-family: \"Poppins\", sans-serif; }\n\n.tablesaw td {\n  border-top: 1px solid #f1f5f7 !important; }\n\n.tablesaw td,\n.tablesaw tbody th {\n  font-size: inherit;\n  line-height: inherit;\n  padding: 10px !important; }\n\n.tablesaw-stack tbody tr,\n.tablesaw tbody tr {\n  border-bottom: none; }\n\n.tablesaw-bar .btn-select.btn-small:after,\n.tablesaw-bar .btn-select.btn-micro:after {\n  font-size: 8px;\n  padding-right: 10px; }\n\n.tablesaw-swipe .tablesaw-cell-persist {\n  box-shadow: none;\n  border-color: #f1f5f7; }\n\n.tablesaw-enhanced .tablesaw-bar .btn {\n  text-shadow: none;\n  background-image: none;\n  text-transform: none;\n  border: 1px solid #dee2e6;\n  padding: 3px 10px;\n  color: #323a46; }\n  .tablesaw-enhanced .tablesaw-bar .btn:after {\n    display: none; }\n\n.tablesaw-enhanced .tablesaw-bar .btn.btn-select:hover {\n  background: #fff; }\n\n.tablesaw-enhanced .tablesaw-bar .btn:hover,\n.tablesaw-enhanced .tablesaw-bar .btn:focus,\n.tablesaw-enhanced .tablesaw-bar .btn:active {\n  color: #7e57c2 !important;\n  background-color: #f1f5f7;\n  outline: none !important;\n  box-shadow: none !important;\n  background-image: none; }\n\n.tablesaw-columntoggle-popup .btn-group {\n  display: block; }\n\n.tablesaw-swipe .tablesaw-swipe-cellpersist {\n  border-right: 2px solid #f1f5f7; }\n\n.tablesaw-sortable-btn {\n  cursor: pointer; }\n\n.jsgrid-cell {\n  padding: 0.85rem; }\n\n.jsgrid-grid-header,\n.jsgrid-grid-body,\n.jsgrid-header-row > .jsgrid-header-cell,\n.jsgrid-filter-row > .jsgrid-cell,\n.jsgrid-insert-row > .jsgrid-cell,\n.jsgrid-edit-row > .jsgrid-cell {\n  border: none; }\n\n.jsgrid-alt-row > .jsgrid-cell {\n  background: #fff; }\n\n.jsgrid-selected-row > .jsgrid-cell {\n  background: #f1f5f7;\n  border-color: #f1f5f7; }\n\n.jsgrid-header-row > .jsgrid-header-cell {\n  background: #f1f5f7;\n  text-align: center !important; }\n\n.jsgrid-filter-row > .jsgrid-cell {\n  background: #f7fafb; }\n\n.jsgrid-edit-row > .jsgrid-cell,\n.jsgrid-insert-row > .jsgrid-cell {\n  background: #f1f5f7; }\n\n.jsgrid input,\n.jsgrid select,\n.jsgrid textarea {\n  border: 1px solid #f1f5f7;\n  padding: .4em .6em;\n  outline: none !important; }\n\n.jsgrid-pager-container {\n  margin-top: 10px; }\n\n.jsgrid-pager-page {\n  padding: 0;\n  margin: 0 2px; }\n  .jsgrid-pager-page.jsgrid-pager-current-page {\n    background-color: #7e57c2;\n    color: #fff; }\n\n.jsgrid-pager-page a,\n.jsgrid-pager-current-page {\n  background-color: #f1f5f7;\n  border-radius: 50%;\n  height: 24px;\n  width: 24px;\n  display: inline-block;\n  text-align: center;\n  line-height: 24px;\n  color: #72747b; }\n\n.jsgrid-pager-nav-button a {\n  color: #72747b;\n  font-weight: 600; }\n  .jsgrid-pager-nav-button a:hover {\n    color: #7e57c2; }\n\n.jsgrid .jsgrid-button {\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background-image: url(\"../images/jsgrid.png\");\n  background-color: #f7f7f7;\n  outline: none !important; }\n  .jsgrid .jsgrid-button:hover {\n    opacity: 0.9;\n    background-color: #f1f5f7; }\n\n.jsgrid-search-mode-button {\n  background-position: 0 -295px; }\n\n.jsgrid-insert-button {\n  background-position: 0 -160px; }\n\n.jsgrid-header-sort:before {\n  position: absolute; }\n\n.ms-container {\n  background: transparent url(\"../images/multiple-arrow.png\") no-repeat 50% 50%;\n  width: auto;\n  max-width: 370px; }\n  .ms-container .ms-list {\n    box-shadow: none;\n    border: 1px solid #ced4da; }\n    .ms-container .ms-list.ms-focus {\n      box-shadow: none;\n      border: 1px solid #b1bbc4; }\n  .ms-container .ms-selectable li.ms-elem-selectable {\n    border: none;\n    padding: 5px 10px; }\n  .ms-container .ms-selectable li.ms-hover {\n    background-color: #7e57c2; }\n  .ms-container .ms-selection li.ms-elem-selection {\n    border: none;\n    padding: 5px 10px; }\n  .ms-container .ms-selection li.ms-hover {\n    background-color: #7e57c2; }\n\n.ms-selectable {\n  box-shadow: none;\n  outline: none !important; }\n\n.ms-optgroup-label {\n  font-weight: 500;\n  font-family: \"Poppins\", sans-serif;\n  color: #323a46 !important;\n  font-size: 13px; }\n\n.autocomplete-suggestions {\n  border: 1px solid #f9f9f9;\n  background: #fff;\n  cursor: default;\n  overflow: auto;\n  max-height: 200px !important;\n  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.15); }\n\n.autocomplete-suggestion {\n  padding: 5px 10px;\n  white-space: nowrap;\n  overflow: hidden; }\n\n.autocomplete-no-suggestion {\n  padding: 5px; }\n\n.autocomplete-selected {\n  background: #f7f7f7;\n  cursor: pointer; }\n\n.autocomplete-suggestions strong {\n  font-weight: bold;\n  color: #323a46; }\n\n.autocomplete-group {\n  padding: 5px;\n  font-weight: 500;\n  font-family: \"Poppins\", sans-serif; }\n\n.autocomplete-group strong {\n  font-weight: bold;\n  font-size: 16px;\n  color: #323a46;\n  display: block; }\n\n.bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {\n  width: 100% !important; }\n\n.bootstrap-select .dropdown-toggle:before {\n  content: \"\\F140\";\n  display: inline-block;\n  font-family: \"Material Design Icons\"; }\n\n.bootstrap-select .dropdown-toggle:focus {\n  outline: none !important;\n  outline-offset: 0; }\n\n.bootstrap-select a {\n  outline: none !important; }\n\n.bootstrap-select .inner {\n  overflow-y: inherit !important; }\n\n.bootstrap-touchspin .btn .input-group-text {\n  padding: 0;\n  border: none;\n  background-color: transparent;\n  color: inherit; }\n\n.parsley-errors-list {\n  margin: 0;\n  padding: 0; }\n  .parsley-errors-list > li {\n    list-style: none;\n    color: #f1556c;\n    margin-top: 5px;\n    padding-left: 20px;\n    position: relative; }\n    .parsley-errors-list > li:before {\n      content: \"\\F159\";\n      font-family: \"Material Design Icons\";\n      position: absolute;\n      left: 2px;\n      top: -1px; }\n\n.parsley-error {\n  border-color: #f1556c; }\n\n.parsley-success {\n  border-color: #1abc9c; }\n\n.flatpickr-day.selected, .flatpickr-day.startRange, .flatpickr-day.endRange, .flatpickr-day.selected.inRange, .flatpickr-day.startRange.inRange, .flatpickr-day.endRange.inRange, .flatpickr-day.selected:focus, .flatpickr-day.startRange:focus, .flatpickr-day.endRange:focus, .flatpickr-day.selected:hover, .flatpickr-day.startRange:hover, .flatpickr-day.endRange:hover, .flatpickr-day.selected.prevMonthDay, .flatpickr-day.startRange.prevMonthDay, .flatpickr-day.endRange.prevMonthDay, .flatpickr-day.selected.nextMonthDay, .flatpickr-day.startRange.nextMonthDay, .flatpickr-day.endRange.nextMonthDay {\n  background: #7e57c2;\n  border-color: #7e57c2; }\n\n.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n+1)),\n.flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n+1)),\n.flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n+1)) {\n  box-shadow: -10px 0 0 #7e57c2; }\n\n.flatpickr-time input:hover,\n.flatpickr-time .flatpickr-am-pm:hover,\n.flatpickr-time input:focus,\n.flatpickr-time .flatpickr-am-pm:focus {\n  background: #f1f5f7; }\n\n.flatpickr-months .flatpickr-month {\n  height: 36px; }\n\n.clockpicker-popover .btn-default {\n  background-color: #7e57c2;\n  color: #fff; }\n\n@font-face {\n  font-family: \"summernote\";\n  font-style: normal;\n  font-weight: normal;\n  src: url(\"../fonts/summernote.eot\");\n  src: url(\"../fonts/summernote.eot?#iefix\") format(\"embedded-opentype\"), url(\"../fonts/summernote.woff?\") format(\"woff\"), url(\"../fonts/summernote.ttf?\") format(\"truetype\"); }\n\n.note-editor.note-frame {\n  border: 1px solid #ced4da;\n  box-shadow: none;\n  margin: 0; }\n  .note-editor.note-frame .note-statusbar {\n    background-color: #fcfcfc;\n    border-top: 1px solid #f7f7f7; }\n  .note-editor.note-frame .note-editable {\n    border: none; }\n\n.note-status-output {\n  display: none; }\n\n.note-editable {\n  border: 1px solid #ced4da;\n  border-radius: 0.2rem;\n  padding: 0.45rem 0.9rem; }\n  .note-editable p:last-of-type {\n    margin-bottom: 0; }\n\n.note-popover .popover-content .note-color .dropdown-menu,\n.card-header.note-toolbar .note-color .dropdown-menu {\n  min-width: 344px; }\n\n.note-toolbar {\n  z-index: 1; }\n\n.ql-container {\n  font-family: \"Poppins\", sans-serif; }\n\n.ql-bubble {\n  border: 1px solid #ced4da;\n  border-radius: 0.2rem; }\n\n.ql-toolbar {\n  font-family: \"Poppins\", sans-serif !important; }\n  .ql-toolbar span {\n    outline: none !important; }\n\n.dropzone {\n  border: 2px dashed rgba(50, 58, 70, 0.3);\n  background: #fff;\n  border-radius: 6px; }\n\n@font-face {\n  font-family: 'dropify';\n  src: url(\"../fonts/dropify.eot\");\n  src: url(\"../fonts/dropify.eot#iefix\") format(\"embedded-opentype\"), url(\"../fonts/dropify.woff\") format(\"woff\"), url(\"../fonts/dropify.ttf\") format(\"truetype\"), url(\"../fonts/dropify.svg#dropify\") format(\"svg\");\n  font-weight: normal;\n  font-style: normal; }\n\n.dropify-wrapper {\n  border: 2px dashed rgba(50, 58, 70, 0.3);\n  background: #fff;\n  border-radius: 6px;\n  font-family: \"Poppins\", sans-serif; }\n\n.editable-clear-x {\n  background: url(\"../images/clear.png\") center center no-repeat; }\n\n.editableform-loading {\n  background: url(\"../images/loading.gif\") center center no-repeat; }\n\n.editable-checklist label {\n  display: block; }\n\n.image-crop-preview .img-preview {\n  float: left;\n  margin-bottom: .5rem;\n  margin-right: .5rem;\n  overflow: hidden;\n  background-color: #f1f5f7;\n  text-align: center;\n  width: 100%; }\n  .image-crop-preview .img-preview > img {\n    max-width: 100%; }\n\n.image-crop-preview .preview-lg {\n  height: 9rem;\n  width: 16rem; }\n\n.image-crop-preview .preview-md {\n  height: 4.5rem;\n  width: 8rem; }\n\n.image-crop-preview .preview-sm {\n  height: 2.25rem;\n  width: 4rem; }\n\n.image-crop-preview .preview-xs {\n  height: 1.125rem;\n  margin-right: 0;\n  width: 2rem; }\n\n.img-crop-preview-btns > .btn,\n.img-crop-preview-btns > .btn-group {\n  margin-bottom: 8px;\n  margin-right: 8px; }\n\n.docs-cropped .modal-body > img,\n.docs-cropped .modal-body > canvas {\n  max-width: 100%; }\n\n.docs-drop-options {\n  max-height: 400px;\n  overflow-y: auto; }\n\n.gmaps, .gmaps-panaroma {\n  height: 300px;\n  background: #f1f5f7;\n  border-radius: 3px; }\n\n.gmaps-overlay {\n  display: block;\n  text-align: center;\n  color: #fff;\n  font-size: 16px;\n  line-height: 40px;\n  background: #7e57c2;\n  border-radius: 4px;\n  padding: 10px 20px; }\n\n.gmaps-overlay_arrow {\n  left: 50%;\n  margin-left: -16px;\n  width: 0;\n  height: 0;\n  position: absolute; }\n  .gmaps-overlay_arrow.above {\n    bottom: -15px;\n    border-left: 16px solid transparent;\n    border-right: 16px solid transparent;\n    border-top: 16px solid #7e57c2; }\n  .gmaps-overlay_arrow.below {\n    top: -15px;\n    border-left: 16px solid transparent;\n    border-right: 16px solid transparent;\n    border-bottom: 16px solid #7e57c2; }\n\n.jvectormap-label {\n  border: none;\n  background: #323a46;\n  color: #fff;\n  font-family: \"Poppins\", sans-serif;\n  font-size: 0.8125rem;\n  padding: 5px 8px; }\n\n.mapael .map {\n  position: relative; }\n  .mapael .map .zoomIn {\n    top: 25px; }\n  .mapael .map .zoomOut {\n    top: 50px; }\n\n.mapael .mapTooltip {\n  position: absolute;\n  background-color: #7e57c2;\n  opacity: 0.95;\n  border-radius: 3px;\n  padding: 2px 10px;\n  z-index: 1000;\n  max-width: 200px;\n  display: none;\n  color: #fff;\n  font-family: \"Poppins\", sans-serif; }\n\n.mapael .zoomIn,\n.mapael .zoomOut,\n.mapael .zoomReset {\n  display: inline-block;\n  text-align: center;\n  vertical-align: middle;\n  border-radius: 2px;\n  font-weight: 500;\n  cursor: pointer;\n  background-color: #7e57c2;\n  text-decoration: none;\n  color: #fff;\n  font-size: 14px;\n  position: absolute;\n  top: 0;\n  left: 10px;\n  width: 24px;\n  height: 24px;\n  line-height: 24px; }\n\n.mapael .plotLegend text {\n  font-family: \"Poppins\", sans-serif !important; }\n\nhtml {\n  direction: rtl; }\n\nbody {\n  text-align: right; }\n\n.card-widgets {\n  float: left; }\n\n.dropdown-menu {\n  text-align: right;\n  left: auto !important;\n  right: 0;\n  bottom: auto; }\n  .dropdown-menu.dropdown-megamenu {\n    background-image: none;\n    left: 20px !important;\n    right: 20px !important; }\n\n.dropdown-menu-right {\n  right: auto !important;\n  left: 0 !important; }\n\nul {\n  padding-right: 0; }\n\n.btn-label {\n  margin: -.55rem -.9rem -.55rem .9rem; }\n\n.btn-label-right {\n  margin: -0.45rem 0.9rem -0.45rem -0.9rem; }\n\n.btn-group,\n.btn-group-vertical {\n  direction: ltr; }\n\n.modal-header .close {\n  margin: -1rem auto -1rem -1rem; }\n\n.modal-footer > :not(:first-child) {\n  margin-right: .25rem;\n  margin-left: 0; }\n\n.modal-footer > :not(:last-child) {\n  margin-left: .25rem;\n  margin-right: 0; }\n\n.alert-dismissible {\n  padding-left: 3.9rem;\n  padding-right: 1.25rem; }\n  .alert-dismissible .close {\n    left: 0;\n    right: auto; }\n\n.breadcrumb-item + .breadcrumb-item::before {\n  padding-left: 0.5rem;\n  content: \"\\F141\"; }\n\n.custom-control {\n  padding-right: 1.5rem;\n  padding-left: 0; }\n\n.custom-control-label::before {\n  left: auto;\n  right: -1.5rem; }\n\n.custom-control-label::after {\n  left: auto;\n  right: -1.5rem; }\n\n.custom-switch {\n  padding-right: 2.25rem;\n  padding-left: 0; }\n  .custom-switch .custom-control-label::before {\n    right: -2.25rem;\n    left: auto; }\n  .custom-switch .custom-control-label::after {\n    right: calc(-2.25rem + 2px);\n    left: auto; }\n  .custom-switch .custom-control-input:checked ~ .custom-control-label::after {\n    transform: translateX(-0.75rem); }\n\n.custom-file-label::after {\n  right: auto;\n  left: 0;\n  border-right: inherit; }\n\n.input-group-prepend {\n  margin-left: -1px;\n  margin-right: 0; }\n\n.input-group-append {\n  margin-right: -1px;\n  margin-left: 0; }\n\n.input-group > .input-group-prepend > .btn,\n.input-group > .input-group-prepend > .input-group-text,\n.input-group > .input-group-append:not(:last-child) > .btn,\n.input-group > .input-group-append:not(:last-child) > .input-group-text,\n.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle),\n.input-group > .input-group-append:last-child > .input-group-text:not(:last-child),\n.input-group > .custom-select:not(:last-child),\n.input-group > .form-control:not(:last-child) {\n  border-top-right-radius: 0.2rem;\n  border-bottom-right-radius: 0.2rem;\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0; }\n\n.input-group > .input-group-append > .btn,\n.input-group > .input-group-append > .input-group-text,\n.input-group > .input-group-prepend:not(:first-child) > .btn,\n.input-group > .input-group-prepend:not(:first-child) > .input-group-text,\n.input-group > .input-group-prepend:first-child > .btn:not(:first-child),\n.input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child),\n.input-group > .custom-select:not(:first-child),\n.input-group > .form-control:not(:first-child) {\n  border-top-left-radius: 0.2rem;\n  border-bottom-left-radius: 0.2rem;\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0; }\n\n.m-0 {\n  margin: 0 !important; }\n\n.mt-0,\n.my-0 {\n  margin-top: 0 !important; }\n\n.mr-0,\n.mx-0 {\n  margin-left: 0 !important;\n  margin-right: 0 !important; }\n\n.mb-0,\n.my-0 {\n  margin-bottom: 0 !important; }\n\n.ml-0,\n.mx-0 {\n  margin-right: 0 !important;\n  margin-left: 0 !important; }\n\n.m-1 {\n  margin: 0.375rem !important; }\n\n.mt-1,\n.my-1 {\n  margin-top: 0.375rem !important; }\n\n.mr-1,\n.mx-1 {\n  margin-left: 0.375rem !important;\n  margin-right: 0 !important; }\n\n.mb-1,\n.my-1 {\n  margin-bottom: 0.375rem !important; }\n\n.ml-1,\n.mx-1 {\n  margin-right: 0.375rem !important;\n  margin-left: 0 !important; }\n\n.m-2 {\n  margin: 0.75rem !important; }\n\n.mt-2,\n.my-2 {\n  margin-top: 0.75rem !important; }\n\n.mr-2,\n.mx-2 {\n  margin-left: 0.75rem !important;\n  margin-right: 0 !important; }\n\n.mb-2,\n.my-2 {\n  margin-bottom: 0.75rem !important; }\n\n.ml-2,\n.mx-2 {\n  margin-right: 0.75rem !important;\n  margin-left: 0 !important; }\n\n.m-3 {\n  margin: 1.5rem !important; }\n\n.mt-3,\n.my-3 {\n  margin-top: 1.5rem !important; }\n\n.mr-3,\n.mx-3 {\n  margin-left: 1.5rem !important;\n  margin-right: 0 !important; }\n\n.mb-3,\n.my-3 {\n  margin-bottom: 1.5rem !important; }\n\n.ml-3,\n.mx-3 {\n  margin-right: 1.5rem !important;\n  margin-left: 0 !important; }\n\n.m-4 {\n  margin: 2.25rem !important; }\n\n.mt-4,\n.my-4 {\n  margin-top: 2.25rem !important; }\n\n.mr-4,\n.mx-4 {\n  margin-left: 2.25rem !important;\n  margin-right: 0 !important; }\n\n.mb-4,\n.my-4 {\n  margin-bottom: 2.25rem !important; }\n\n.ml-4,\n.mx-4 {\n  margin-right: 2.25rem !important;\n  margin-left: 0 !important; }\n\n.m-5 {\n  margin: 4.5rem !important; }\n\n.mt-5,\n.my-5 {\n  margin-top: 4.5rem !important; }\n\n.mr-5,\n.mx-5 {\n  margin-left: 4.5rem !important;\n  margin-right: 0 !important; }\n\n.mb-5,\n.my-5 {\n  margin-bottom: 4.5rem !important; }\n\n.ml-5,\n.mx-5 {\n  margin-right: 4.5rem !important;\n  margin-left: 0 !important; }\n\n.p-0 {\n  padding: 0 !important; }\n\n.pt-0,\n.py-0 {\n  padding-top: 0 !important; }\n\n.pr-0,\n.px-0 {\n  padding-left: 0 !important;\n  padding-right: 0 !important; }\n\n.pb-0,\n.py-0 {\n  padding-bottom: 0 !important; }\n\n.pl-0,\n.px-0 {\n  padding-right: 0 !important;\n  padding-left: 0 !important; }\n\n.p-1 {\n  padding: 0.375rem !important; }\n\n.pt-1,\n.py-1 {\n  padding-top: 0.375rem !important; }\n\n.pr-1,\n.px-1 {\n  padding-left: 0.375rem !important;\n  padding-right: 0 !important; }\n\n.pb-1,\n.py-1 {\n  padding-bottom: 0.375rem !important; }\n\n.pl-1,\n.px-1 {\n  padding-right: 0.375rem !important;\n  padding-left: 0 !important; }\n\n.p-2 {\n  padding: 0.75rem !important; }\n\n.pt-2,\n.py-2 {\n  padding-top: 0.75rem !important; }\n\n.pr-2,\n.px-2 {\n  padding-left: 0.75rem !important;\n  padding-right: 0 !important; }\n\n.pb-2,\n.py-2 {\n  padding-bottom: 0.75rem !important; }\n\n.pl-2,\n.px-2 {\n  padding-right: 0.75rem !important;\n  padding-left: 0 !important; }\n\n.p-3 {\n  padding: 1.5rem !important; }\n\n.pt-3,\n.py-3 {\n  padding-top: 1.5rem !important; }\n\n.pr-3,\n.px-3 {\n  padding-left: 1.5rem !important;\n  padding-right: 0 !important; }\n\n.pb-3,\n.py-3 {\n  padding-bottom: 1.5rem !important; }\n\n.pl-3,\n.px-3 {\n  padding-right: 1.5rem !important;\n  padding-left: 0 !important; }\n\n.p-4 {\n  padding: 2.25rem !important; }\n\n.pt-4,\n.py-4 {\n  padding-top: 2.25rem !important; }\n\n.pr-4,\n.px-4 {\n  padding-left: 2.25rem !important;\n  padding-right: 0 !important; }\n\n.pb-4,\n.py-4 {\n  padding-bottom: 2.25rem !important; }\n\n.pl-4,\n.px-4 {\n  padding-right: 2.25rem !important;\n  padding-left: 0 !important; }\n\n.p-5 {\n  padding: 4.5rem !important; }\n\n.pt-5,\n.py-5 {\n  padding-top: 4.5rem !important; }\n\n.pr-5,\n.px-5 {\n  padding-left: 4.5rem !important;\n  padding-right: 0 !important; }\n\n.pb-5,\n.py-5 {\n  padding-bottom: 4.5rem !important; }\n\n.pl-5,\n.px-5 {\n  padding-right: 4.5rem !important;\n  padding-left: 0 !important; }\n\n.m-n1 {\n  margin: -0.375rem !important; }\n\n.mt-n1,\n.my-n1 {\n  margin-top: -0.375rem !important; }\n\n.mr-n1,\n.mx-n1 {\n  margin-right: -0.375rem !important; }\n\n.mb-n1,\n.my-n1 {\n  margin-bottom: -0.375rem !important; }\n\n.ml-n1,\n.mx-n1 {\n  margin-left: -0.375rem !important; }\n\n.m-n2 {\n  margin: -0.75rem !important; }\n\n.mt-n2,\n.my-n2 {\n  margin-top: -0.75rem !important; }\n\n.mr-n2,\n.mx-n2 {\n  margin-right: -0.75rem !important; }\n\n.mb-n2,\n.my-n2 {\n  margin-bottom: -0.75rem !important; }\n\n.ml-n2,\n.mx-n2 {\n  margin-left: -0.75rem !important; }\n\n.m-n3 {\n  margin: -1.5rem !important; }\n\n.mt-n3,\n.my-n3 {\n  margin-top: -1.5rem !important; }\n\n.mr-n3,\n.mx-n3 {\n  margin-right: -1.5rem !important; }\n\n.mb-n3,\n.my-n3 {\n  margin-bottom: -1.5rem !important; }\n\n.ml-n3,\n.mx-n3 {\n  margin-left: -1.5rem !important; }\n\n.m-n4 {\n  margin: -2.25rem !important; }\n\n.mt-n4,\n.my-n4 {\n  margin-top: -2.25rem !important; }\n\n.mr-n4,\n.mx-n4 {\n  margin-right: -2.25rem !important; }\n\n.mb-n4,\n.my-n4 {\n  margin-bottom: -2.25rem !important; }\n\n.ml-n4,\n.mx-n4 {\n  margin-left: -2.25rem !important; }\n\n.m-n5 {\n  margin: -4.5rem !important; }\n\n.mt-n5,\n.my-n5 {\n  margin-top: -4.5rem !important; }\n\n.mr-n5,\n.mx-n5 {\n  margin-right: -4.5rem !important; }\n\n.mb-n5,\n.my-n5 {\n  margin-bottom: -4.5rem !important; }\n\n.ml-n5,\n.mx-n5 {\n  margin-left: -4.5rem !important; }\n\n.m-auto {\n  margin: auto !important; }\n\n.mt-auto,\n.my-auto {\n  margin-top: auto !important; }\n\n.mr-auto,\n.mx-auto {\n  margin-left: auto !important;\n  margin-right: inherit !important; }\n\n.mb-auto,\n.my-auto {\n  margin-bottom: auto !important; }\n\n.ml-auto,\n.mx-auto {\n  margin-right: auto !important;\n  margin-left: auto !important; }\n\n@media (min-width: 576px) {\n  .m-sm-0 {\n    margin: 0 !important; }\n  .mt-sm-0,\n  .my-sm-0 {\n    margin-top: 0 !important; }\n  .mr-sm-0,\n  .mx-sm-0 {\n    margin-left: 0 !important;\n    margin-right: 0 !important; }\n  .mb-sm-0,\n  .my-sm-0 {\n    margin-bottom: 0 !important; }\n  .ml-sm-0,\n  .mx-sm-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important; }\n  .m-sm-1 {\n    margin: 0.375rem !important; }\n  .mt-sm-1,\n  .my-sm-1 {\n    margin-top: 0.375rem !important; }\n  .mr-sm-1,\n  .mx-sm-1 {\n    margin-left: 0.375rem !important;\n    margin-right: 0 !important; }\n  .mb-sm-1,\n  .my-sm-1 {\n    margin-bottom: 0.375rem !important; }\n  .ml-sm-1,\n  .mx-sm-1 {\n    margin-right: 0.375rem !important;\n    margin-left: 0 !important; }\n  .m-sm-2 {\n    margin: 0.75rem !important; }\n  .mt-sm-2,\n  .my-sm-2 {\n    margin-top: 0.75rem !important; }\n  .mr-sm-2,\n  .mx-sm-2 {\n    margin-left: 0.75rem !important;\n    margin-right: 0 !important; }\n  .mb-sm-2,\n  .my-sm-2 {\n    margin-bottom: 0.75rem !important; }\n  .ml-sm-2,\n  .mx-sm-2 {\n    margin-right: 0.75rem !important;\n    margin-left: 0 !important; }\n  .m-sm-3 {\n    margin: 1.5rem !important; }\n  .mt-sm-3,\n  .my-sm-3 {\n    margin-top: 1.5rem !important; }\n  .mr-sm-3,\n  .mx-sm-3 {\n    margin-left: 1.5rem !important;\n    margin-right: 0 !important; }\n  .mb-sm-3,\n  .my-sm-3 {\n    margin-bottom: 1.5rem !important; }\n  .ml-sm-3,\n  .mx-sm-3 {\n    margin-right: 1.5rem !important;\n    margin-left: 0 !important; }\n  .m-sm-4 {\n    margin: 2.25rem !important; }\n  .mt-sm-4,\n  .my-sm-4 {\n    margin-top: 2.25rem !important; }\n  .mr-sm-4,\n  .mx-sm-4 {\n    margin-left: 2.25rem !important;\n    margin-right: 0 !important; }\n  .mb-sm-4,\n  .my-sm-4 {\n    margin-bottom: 2.25rem !important; }\n  .ml-sm-4,\n  .mx-sm-4 {\n    margin-right: 2.25rem !important;\n    margin-left: 0 !important; }\n  .m-sm-5 {\n    margin: 4.5rem !important; }\n  .mt-sm-5,\n  .my-sm-5 {\n    margin-top: 4.5rem !important; }\n  .mr-sm-5,\n  .mx-sm-5 {\n    margin-left: 4.5rem !important;\n    margin-right: 0 !important; }\n  .mb-sm-5,\n  .my-sm-5 {\n    margin-bottom: 4.5rem !important; }\n  .ml-sm-5,\n  .mx-sm-5 {\n    margin-right: 4.5rem !important;\n    margin-left: 0 !important; }\n  .p-sm-0 {\n    padding: 0 !important; }\n  .pt-sm-0,\n  .py-sm-0 {\n    padding-top: 0 !important; }\n  .pr-sm-0,\n  .px-sm-0 {\n    padding-left: 0 !important;\n    padding-right: 0 !important; }\n  .pb-sm-0,\n  .py-sm-0 {\n    padding-bottom: 0 !important; }\n  .pl-sm-0,\n  .px-sm-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important; }\n  .p-sm-1 {\n    padding: 0.375rem !important; }\n  .pt-sm-1,\n  .py-sm-1 {\n    padding-top: 0.375rem !important; }\n  .pr-sm-1,\n  .px-sm-1 {\n    padding-left: 0.375rem !important;\n    padding-right: 0 !important; }\n  .pb-sm-1,\n  .py-sm-1 {\n    padding-bottom: 0.375rem !important; }\n  .pl-sm-1,\n  .px-sm-1 {\n    padding-right: 0.375rem !important;\n    padding-left: 0 !important; }\n  .p-sm-2 {\n    padding: 0.75rem !important; }\n  .pt-sm-2,\n  .py-sm-2 {\n    padding-top: 0.75rem !important; }\n  .pr-sm-2,\n  .px-sm-2 {\n    padding-left: 0.75rem !important;\n    padding-right: 0 !important; }\n  .pb-sm-2,\n  .py-sm-2 {\n    padding-bottom: 0.75rem !important; }\n  .pl-sm-2,\n  .px-sm-2 {\n    padding-right: 0.75rem !important;\n    padding-left: 0 !important; }\n  .p-sm-3 {\n    padding: 1.5rem !important; }\n  .pt-sm-3,\n  .py-sm-3 {\n    padding-top: 1.5rem !important; }\n  .pr-sm-3,\n  .px-sm-3 {\n    padding-left: 1.5rem !important;\n    padding-right: 0 !important; }\n  .pb-sm-3,\n  .py-sm-3 {\n    padding-bottom: 1.5rem !important; }\n  .pl-sm-3,\n  .px-sm-3 {\n    padding-right: 1.5rem !important;\n    padding-left: 0 !important; }\n  .p-sm-4 {\n    padding: 2.25rem !important; }\n  .pt-sm-4,\n  .py-sm-4 {\n    padding-top: 2.25rem !important; }\n  .pr-sm-4,\n  .px-sm-4 {\n    padding-left: 2.25rem !important;\n    padding-right: 0 !important; }\n  .pb-sm-4,\n  .py-sm-4 {\n    padding-bottom: 2.25rem !important; }\n  .pl-sm-4,\n  .px-sm-4 {\n    padding-right: 2.25rem !important;\n    padding-left: 0 !important; }\n  .p-sm-5 {\n    padding: 4.5rem !important; }\n  .pt-sm-5,\n  .py-sm-5 {\n    padding-top: 4.5rem !important; }\n  .pr-sm-5,\n  .px-sm-5 {\n    padding-left: 4.5rem !important;\n    padding-right: 0 !important; }\n  .pb-sm-5,\n  .py-sm-5 {\n    padding-bottom: 4.5rem !important; }\n  .pl-sm-5,\n  .px-sm-5 {\n    padding-right: 4.5rem !important;\n    padding-left: 0 !important; }\n  .m-sm-n1 {\n    margin: -0.375rem !important; }\n  .mt-sm-n1,\n  .my-sm-n1 {\n    margin-top: -0.375rem !important; }\n  .mr-sm-n1,\n  .mx-sm-n1 {\n    margin-right: -0.375rem !important; }\n  .mb-sm-n1,\n  .my-sm-n1 {\n    margin-bottom: -0.375rem !important; }\n  .ml-sm-n1,\n  .mx-sm-n1 {\n    margin-left: -0.375rem !important; }\n  .m-sm-n2 {\n    margin: -0.75rem !important; }\n  .mt-sm-n2,\n  .my-sm-n2 {\n    margin-top: -0.75rem !important; }\n  .mr-sm-n2,\n  .mx-sm-n2 {\n    margin-right: -0.75rem !important; }\n  .mb-sm-n2,\n  .my-sm-n2 {\n    margin-bottom: -0.75rem !important; }\n  .ml-sm-n2,\n  .mx-sm-n2 {\n    margin-left: -0.75rem !important; }\n  .m-sm-n3 {\n    margin: -1.5rem !important; }\n  .mt-sm-n3,\n  .my-sm-n3 {\n    margin-top: -1.5rem !important; }\n  .mr-sm-n3,\n  .mx-sm-n3 {\n    margin-right: -1.5rem !important; }\n  .mb-sm-n3,\n  .my-sm-n3 {\n    margin-bottom: -1.5rem !important; }\n  .ml-sm-n3,\n  .mx-sm-n3 {\n    margin-left: -1.5rem !important; }\n  .m-sm-n4 {\n    margin: -2.25rem !important; }\n  .mt-sm-n4,\n  .my-sm-n4 {\n    margin-top: -2.25rem !important; }\n  .mr-sm-n4,\n  .mx-sm-n4 {\n    margin-right: -2.25rem !important; }\n  .mb-sm-n4,\n  .my-sm-n4 {\n    margin-bottom: -2.25rem !important; }\n  .ml-sm-n4,\n  .mx-sm-n4 {\n    margin-left: -2.25rem !important; }\n  .m-sm-n5 {\n    margin: -4.5rem !important; }\n  .mt-sm-n5,\n  .my-sm-n5 {\n    margin-top: -4.5rem !important; }\n  .mr-sm-n5,\n  .mx-sm-n5 {\n    margin-right: -4.5rem !important; }\n  .mb-sm-n5,\n  .my-sm-n5 {\n    margin-bottom: -4.5rem !important; }\n  .ml-sm-n5,\n  .mx-sm-n5 {\n    margin-left: -4.5rem !important; }\n  .m-sm-auto {\n    margin: auto !important; }\n  .mt-sm-auto,\n  .my-sm-auto {\n    margin-top: auto !important; }\n  .mr-sm-auto,\n  .mx-sm-auto {\n    margin-left: auto !important;\n    margin-right: inherit !important; }\n  .mb-sm-auto,\n  .my-sm-auto {\n    margin-bottom: auto !important; }\n  .ml-sm-auto,\n  .mx-sm-auto {\n    margin-right: auto !important;\n    margin-left: auto !important; } }\n\n@media (min-width: 768px) {\n  .m-md-0 {\n    margin: 0 !important; }\n  .mt-md-0,\n  .my-md-0 {\n    margin-top: 0 !important; }\n  .mr-md-0,\n  .mx-md-0 {\n    margin-left: 0 !important;\n    margin-right: 0 !important; }\n  .mb-md-0,\n  .my-md-0 {\n    margin-bottom: 0 !important; }\n  .ml-md-0,\n  .mx-md-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important; }\n  .m-md-1 {\n    margin: 0.375rem !important; }\n  .mt-md-1,\n  .my-md-1 {\n    margin-top: 0.375rem !important; }\n  .mr-md-1,\n  .mx-md-1 {\n    margin-left: 0.375rem !important;\n    margin-right: 0 !important; }\n  .mb-md-1,\n  .my-md-1 {\n    margin-bottom: 0.375rem !important; }\n  .ml-md-1,\n  .mx-md-1 {\n    margin-right: 0.375rem !important;\n    margin-left: 0 !important; }\n  .m-md-2 {\n    margin: 0.75rem !important; }\n  .mt-md-2,\n  .my-md-2 {\n    margin-top: 0.75rem !important; }\n  .mr-md-2,\n  .mx-md-2 {\n    margin-left: 0.75rem !important;\n    margin-right: 0 !important; }\n  .mb-md-2,\n  .my-md-2 {\n    margin-bottom: 0.75rem !important; }\n  .ml-md-2,\n  .mx-md-2 {\n    margin-right: 0.75rem !important;\n    margin-left: 0 !important; }\n  .m-md-3 {\n    margin: 1.5rem !important; }\n  .mt-md-3,\n  .my-md-3 {\n    margin-top: 1.5rem !important; }\n  .mr-md-3,\n  .mx-md-3 {\n    margin-left: 1.5rem !important;\n    margin-right: 0 !important; }\n  .mb-md-3,\n  .my-md-3 {\n    margin-bottom: 1.5rem !important; }\n  .ml-md-3,\n  .mx-md-3 {\n    margin-right: 1.5rem !important;\n    margin-left: 0 !important; }\n  .m-md-4 {\n    margin: 2.25rem !important; }\n  .mt-md-4,\n  .my-md-4 {\n    margin-top: 2.25rem !important; }\n  .mr-md-4,\n  .mx-md-4 {\n    margin-left: 2.25rem !important;\n    margin-right: 0 !important; }\n  .mb-md-4,\n  .my-md-4 {\n    margin-bottom: 2.25rem !important; }\n  .ml-md-4,\n  .mx-md-4 {\n    margin-right: 2.25rem !important;\n    margin-left: 0 !important; }\n  .m-md-5 {\n    margin: 4.5rem !important; }\n  .mt-md-5,\n  .my-md-5 {\n    margin-top: 4.5rem !important; }\n  .mr-md-5,\n  .mx-md-5 {\n    margin-left: 4.5rem !important;\n    margin-right: 0 !important; }\n  .mb-md-5,\n  .my-md-5 {\n    margin-bottom: 4.5rem !important; }\n  .ml-md-5,\n  .mx-md-5 {\n    margin-right: 4.5rem !important;\n    margin-left: 0 !important; }\n  .p-md-0 {\n    padding: 0 !important; }\n  .pt-md-0,\n  .py-md-0 {\n    padding-top: 0 !important; }\n  .pr-md-0,\n  .px-md-0 {\n    padding-left: 0 !important;\n    padding-right: 0 !important; }\n  .pb-md-0,\n  .py-md-0 {\n    padding-bottom: 0 !important; }\n  .pl-md-0,\n  .px-md-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important; }\n  .p-md-1 {\n    padding: 0.375rem !important; }\n  .pt-md-1,\n  .py-md-1 {\n    padding-top: 0.375rem !important; }\n  .pr-md-1,\n  .px-md-1 {\n    padding-left: 0.375rem !important;\n    padding-right: 0 !important; }\n  .pb-md-1,\n  .py-md-1 {\n    padding-bottom: 0.375rem !important; }\n  .pl-md-1,\n  .px-md-1 {\n    padding-right: 0.375rem !important;\n    padding-left: 0 !important; }\n  .p-md-2 {\n    padding: 0.75rem !important; }\n  .pt-md-2,\n  .py-md-2 {\n    padding-top: 0.75rem !important; }\n  .pr-md-2,\n  .px-md-2 {\n    padding-left: 0.75rem !important;\n    padding-right: 0 !important; }\n  .pb-md-2,\n  .py-md-2 {\n    padding-bottom: 0.75rem !important; }\n  .pl-md-2,\n  .px-md-2 {\n    padding-right: 0.75rem !important;\n    padding-left: 0 !important; }\n  .p-md-3 {\n    padding: 1.5rem !important; }\n  .pt-md-3,\n  .py-md-3 {\n    padding-top: 1.5rem !important; }\n  .pr-md-3,\n  .px-md-3 {\n    padding-left: 1.5rem !important;\n    padding-right: 0 !important; }\n  .pb-md-3,\n  .py-md-3 {\n    padding-bottom: 1.5rem !important; }\n  .pl-md-3,\n  .px-md-3 {\n    padding-right: 1.5rem !important;\n    padding-left: 0 !important; }\n  .p-md-4 {\n    padding: 2.25rem !important; }\n  .pt-md-4,\n  .py-md-4 {\n    padding-top: 2.25rem !important; }\n  .pr-md-4,\n  .px-md-4 {\n    padding-left: 2.25rem !important;\n    padding-right: 0 !important; }\n  .pb-md-4,\n  .py-md-4 {\n    padding-bottom: 2.25rem !important; }\n  .pl-md-4,\n  .px-md-4 {\n    padding-right: 2.25rem !important;\n    padding-left: 0 !important; }\n  .p-md-5 {\n    padding: 4.5rem !important; }\n  .pt-md-5,\n  .py-md-5 {\n    padding-top: 4.5rem !important; }\n  .pr-md-5,\n  .px-md-5 {\n    padding-left: 4.5rem !important;\n    padding-right: 0 !important; }\n  .pb-md-5,\n  .py-md-5 {\n    padding-bottom: 4.5rem !important; }\n  .pl-md-5,\n  .px-md-5 {\n    padding-right: 4.5rem !important;\n    padding-left: 0 !important; }\n  .m-md-n1 {\n    margin: -0.375rem !important; }\n  .mt-md-n1,\n  .my-md-n1 {\n    margin-top: -0.375rem !important; }\n  .mr-md-n1,\n  .mx-md-n1 {\n    margin-right: -0.375rem !important; }\n  .mb-md-n1,\n  .my-md-n1 {\n    margin-bottom: -0.375rem !important; }\n  .ml-md-n1,\n  .mx-md-n1 {\n    margin-left: -0.375rem !important; }\n  .m-md-n2 {\n    margin: -0.75rem !important; }\n  .mt-md-n2,\n  .my-md-n2 {\n    margin-top: -0.75rem !important; }\n  .mr-md-n2,\n  .mx-md-n2 {\n    margin-right: -0.75rem !important; }\n  .mb-md-n2,\n  .my-md-n2 {\n    margin-bottom: -0.75rem !important; }\n  .ml-md-n2,\n  .mx-md-n2 {\n    margin-left: -0.75rem !important; }\n  .m-md-n3 {\n    margin: -1.5rem !important; }\n  .mt-md-n3,\n  .my-md-n3 {\n    margin-top: -1.5rem !important; }\n  .mr-md-n3,\n  .mx-md-n3 {\n    margin-right: -1.5rem !important; }\n  .mb-md-n3,\n  .my-md-n3 {\n    margin-bottom: -1.5rem !important; }\n  .ml-md-n3,\n  .mx-md-n3 {\n    margin-left: -1.5rem !important; }\n  .m-md-n4 {\n    margin: -2.25rem !important; }\n  .mt-md-n4,\n  .my-md-n4 {\n    margin-top: -2.25rem !important; }\n  .mr-md-n4,\n  .mx-md-n4 {\n    margin-right: -2.25rem !important; }\n  .mb-md-n4,\n  .my-md-n4 {\n    margin-bottom: -2.25rem !important; }\n  .ml-md-n4,\n  .mx-md-n4 {\n    margin-left: -2.25rem !important; }\n  .m-md-n5 {\n    margin: -4.5rem !important; }\n  .mt-md-n5,\n  .my-md-n5 {\n    margin-top: -4.5rem !important; }\n  .mr-md-n5,\n  .mx-md-n5 {\n    margin-right: -4.5rem !important; }\n  .mb-md-n5,\n  .my-md-n5 {\n    margin-bottom: -4.5rem !important; }\n  .ml-md-n5,\n  .mx-md-n5 {\n    margin-left: -4.5rem !important; }\n  .m-md-auto {\n    margin: auto !important; }\n  .mt-md-auto,\n  .my-md-auto {\n    margin-top: auto !important; }\n  .mr-md-auto,\n  .mx-md-auto {\n    margin-left: auto !important;\n    margin-right: inherit !important; }\n  .mb-md-auto,\n  .my-md-auto {\n    margin-bottom: auto !important; }\n  .ml-md-auto,\n  .mx-md-auto {\n    margin-right: auto !important;\n    margin-left: auto !important; } }\n\n@media (min-width: 992px) {\n  .m-lg-0 {\n    margin: 0 !important; }\n  .mt-lg-0,\n  .my-lg-0 {\n    margin-top: 0 !important; }\n  .mr-lg-0,\n  .mx-lg-0 {\n    margin-left: 0 !important;\n    margin-right: 0 !important; }\n  .mb-lg-0,\n  .my-lg-0 {\n    margin-bottom: 0 !important; }\n  .ml-lg-0,\n  .mx-lg-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important; }\n  .m-lg-1 {\n    margin: 0.375rem !important; }\n  .mt-lg-1,\n  .my-lg-1 {\n    margin-top: 0.375rem !important; }\n  .mr-lg-1,\n  .mx-lg-1 {\n    margin-left: 0.375rem !important;\n    margin-right: 0 !important; }\n  .mb-lg-1,\n  .my-lg-1 {\n    margin-bottom: 0.375rem !important; }\n  .ml-lg-1,\n  .mx-lg-1 {\n    margin-right: 0.375rem !important;\n    margin-left: 0 !important; }\n  .m-lg-2 {\n    margin: 0.75rem !important; }\n  .mt-lg-2,\n  .my-lg-2 {\n    margin-top: 0.75rem !important; }\n  .mr-lg-2,\n  .mx-lg-2 {\n    margin-left: 0.75rem !important;\n    margin-right: 0 !important; }\n  .mb-lg-2,\n  .my-lg-2 {\n    margin-bottom: 0.75rem !important; }\n  .ml-lg-2,\n  .mx-lg-2 {\n    margin-right: 0.75rem !important;\n    margin-left: 0 !important; }\n  .m-lg-3 {\n    margin: 1.5rem !important; }\n  .mt-lg-3,\n  .my-lg-3 {\n    margin-top: 1.5rem !important; }\n  .mr-lg-3,\n  .mx-lg-3 {\n    margin-left: 1.5rem !important;\n    margin-right: 0 !important; }\n  .mb-lg-3,\n  .my-lg-3 {\n    margin-bottom: 1.5rem !important; }\n  .ml-lg-3,\n  .mx-lg-3 {\n    margin-right: 1.5rem !important;\n    margin-left: 0 !important; }\n  .m-lg-4 {\n    margin: 2.25rem !important; }\n  .mt-lg-4,\n  .my-lg-4 {\n    margin-top: 2.25rem !important; }\n  .mr-lg-4,\n  .mx-lg-4 {\n    margin-left: 2.25rem !important;\n    margin-right: 0 !important; }\n  .mb-lg-4,\n  .my-lg-4 {\n    margin-bottom: 2.25rem !important; }\n  .ml-lg-4,\n  .mx-lg-4 {\n    margin-right: 2.25rem !important;\n    margin-left: 0 !important; }\n  .m-lg-5 {\n    margin: 4.5rem !important; }\n  .mt-lg-5,\n  .my-lg-5 {\n    margin-top: 4.5rem !important; }\n  .mr-lg-5,\n  .mx-lg-5 {\n    margin-left: 4.5rem !important;\n    margin-right: 0 !important; }\n  .mb-lg-5,\n  .my-lg-5 {\n    margin-bottom: 4.5rem !important; }\n  .ml-lg-5,\n  .mx-lg-5 {\n    margin-right: 4.5rem !important;\n    margin-left: 0 !important; }\n  .p-lg-0 {\n    padding: 0 !important; }\n  .pt-lg-0,\n  .py-lg-0 {\n    padding-top: 0 !important; }\n  .pr-lg-0,\n  .px-lg-0 {\n    padding-left: 0 !important;\n    padding-right: 0 !important; }\n  .pb-lg-0,\n  .py-lg-0 {\n    padding-bottom: 0 !important; }\n  .pl-lg-0,\n  .px-lg-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important; }\n  .p-lg-1 {\n    padding: 0.375rem !important; }\n  .pt-lg-1,\n  .py-lg-1 {\n    padding-top: 0.375rem !important; }\n  .pr-lg-1,\n  .px-lg-1 {\n    padding-left: 0.375rem !important;\n    padding-right: 0 !important; }\n  .pb-lg-1,\n  .py-lg-1 {\n    padding-bottom: 0.375rem !important; }\n  .pl-lg-1,\n  .px-lg-1 {\n    padding-right: 0.375rem !important;\n    padding-left: 0 !important; }\n  .p-lg-2 {\n    padding: 0.75rem !important; }\n  .pt-lg-2,\n  .py-lg-2 {\n    padding-top: 0.75rem !important; }\n  .pr-lg-2,\n  .px-lg-2 {\n    padding-left: 0.75rem !important;\n    padding-right: 0 !important; }\n  .pb-lg-2,\n  .py-lg-2 {\n    padding-bottom: 0.75rem !important; }\n  .pl-lg-2,\n  .px-lg-2 {\n    padding-right: 0.75rem !important;\n    padding-left: 0 !important; }\n  .p-lg-3 {\n    padding: 1.5rem !important; }\n  .pt-lg-3,\n  .py-lg-3 {\n    padding-top: 1.5rem !important; }\n  .pr-lg-3,\n  .px-lg-3 {\n    padding-left: 1.5rem !important;\n    padding-right: 0 !important; }\n  .pb-lg-3,\n  .py-lg-3 {\n    padding-bottom: 1.5rem !important; }\n  .pl-lg-3,\n  .px-lg-3 {\n    padding-right: 1.5rem !important;\n    padding-left: 0 !important; }\n  .p-lg-4 {\n    padding: 2.25rem !important; }\n  .pt-lg-4,\n  .py-lg-4 {\n    padding-top: 2.25rem !important; }\n  .pr-lg-4,\n  .px-lg-4 {\n    padding-left: 2.25rem !important;\n    padding-right: 0 !important; }\n  .pb-lg-4,\n  .py-lg-4 {\n    padding-bottom: 2.25rem !important; }\n  .pl-lg-4,\n  .px-lg-4 {\n    padding-right: 2.25rem !important;\n    padding-left: 0 !important; }\n  .p-lg-5 {\n    padding: 4.5rem !important; }\n  .pt-lg-5,\n  .py-lg-5 {\n    padding-top: 4.5rem !important; }\n  .pr-lg-5,\n  .px-lg-5 {\n    padding-left: 4.5rem !important;\n    padding-right: 0 !important; }\n  .pb-lg-5,\n  .py-lg-5 {\n    padding-bottom: 4.5rem !important; }\n  .pl-lg-5,\n  .px-lg-5 {\n    padding-right: 4.5rem !important;\n    padding-left: 0 !important; }\n  .m-lg-n1 {\n    margin: -0.375rem !important; }\n  .mt-lg-n1,\n  .my-lg-n1 {\n    margin-top: -0.375rem !important; }\n  .mr-lg-n1,\n  .mx-lg-n1 {\n    margin-right: -0.375rem !important; }\n  .mb-lg-n1,\n  .my-lg-n1 {\n    margin-bottom: -0.375rem !important; }\n  .ml-lg-n1,\n  .mx-lg-n1 {\n    margin-left: -0.375rem !important; }\n  .m-lg-n2 {\n    margin: -0.75rem !important; }\n  .mt-lg-n2,\n  .my-lg-n2 {\n    margin-top: -0.75rem !important; }\n  .mr-lg-n2,\n  .mx-lg-n2 {\n    margin-right: -0.75rem !important; }\n  .mb-lg-n2,\n  .my-lg-n2 {\n    margin-bottom: -0.75rem !important; }\n  .ml-lg-n2,\n  .mx-lg-n2 {\n    margin-left: -0.75rem !important; }\n  .m-lg-n3 {\n    margin: -1.5rem !important; }\n  .mt-lg-n3,\n  .my-lg-n3 {\n    margin-top: -1.5rem !important; }\n  .mr-lg-n3,\n  .mx-lg-n3 {\n    margin-right: -1.5rem !important; }\n  .mb-lg-n3,\n  .my-lg-n3 {\n    margin-bottom: -1.5rem !important; }\n  .ml-lg-n3,\n  .mx-lg-n3 {\n    margin-left: -1.5rem !important; }\n  .m-lg-n4 {\n    margin: -2.25rem !important; }\n  .mt-lg-n4,\n  .my-lg-n4 {\n    margin-top: -2.25rem !important; }\n  .mr-lg-n4,\n  .mx-lg-n4 {\n    margin-right: -2.25rem !important; }\n  .mb-lg-n4,\n  .my-lg-n4 {\n    margin-bottom: -2.25rem !important; }\n  .ml-lg-n4,\n  .mx-lg-n4 {\n    margin-left: -2.25rem !important; }\n  .m-lg-n5 {\n    margin: -4.5rem !important; }\n  .mt-lg-n5,\n  .my-lg-n5 {\n    margin-top: -4.5rem !important; }\n  .mr-lg-n5,\n  .mx-lg-n5 {\n    margin-right: -4.5rem !important; }\n  .mb-lg-n5,\n  .my-lg-n5 {\n    margin-bottom: -4.5rem !important; }\n  .ml-lg-n5,\n  .mx-lg-n5 {\n    margin-left: -4.5rem !important; }\n  .m-lg-auto {\n    margin: auto !important; }\n  .mt-lg-auto,\n  .my-lg-auto {\n    margin-top: auto !important; }\n  .mr-lg-auto,\n  .mx-lg-auto {\n    margin-left: auto !important;\n    margin-right: inherit !important; }\n  .mb-lg-auto,\n  .my-lg-auto {\n    margin-bottom: auto !important; }\n  .ml-lg-auto,\n  .mx-lg-auto {\n    margin-right: auto !important;\n    margin-left: auto !important; } }\n\n@media (min-width: 1200px) {\n  .m-xl-0 {\n    margin: 0 !important; }\n  .mt-xl-0,\n  .my-xl-0 {\n    margin-top: 0 !important; }\n  .mr-xl-0,\n  .mx-xl-0 {\n    margin-left: 0 !important;\n    margin-right: 0 !important; }\n  .mb-xl-0,\n  .my-xl-0 {\n    margin-bottom: 0 !important; }\n  .ml-xl-0,\n  .mx-xl-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important; }\n  .m-xl-1 {\n    margin: 0.375rem !important; }\n  .mt-xl-1,\n  .my-xl-1 {\n    margin-top: 0.375rem !important; }\n  .mr-xl-1,\n  .mx-xl-1 {\n    margin-left: 0.375rem !important;\n    margin-right: 0 !important; }\n  .mb-xl-1,\n  .my-xl-1 {\n    margin-bottom: 0.375rem !important; }\n  .ml-xl-1,\n  .mx-xl-1 {\n    margin-right: 0.375rem !important;\n    margin-left: 0 !important; }\n  .m-xl-2 {\n    margin: 0.75rem !important; }\n  .mt-xl-2,\n  .my-xl-2 {\n    margin-top: 0.75rem !important; }\n  .mr-xl-2,\n  .mx-xl-2 {\n    margin-left: 0.75rem !important;\n    margin-right: 0 !important; }\n  .mb-xl-2,\n  .my-xl-2 {\n    margin-bottom: 0.75rem !important; }\n  .ml-xl-2,\n  .mx-xl-2 {\n    margin-right: 0.75rem !important;\n    margin-left: 0 !important; }\n  .m-xl-3 {\n    margin: 1.5rem !important; }\n  .mt-xl-3,\n  .my-xl-3 {\n    margin-top: 1.5rem !important; }\n  .mr-xl-3,\n  .mx-xl-3 {\n    margin-left: 1.5rem !important;\n    margin-right: 0 !important; }\n  .mb-xl-3,\n  .my-xl-3 {\n    margin-bottom: 1.5rem !important; }\n  .ml-xl-3,\n  .mx-xl-3 {\n    margin-right: 1.5rem !important;\n    margin-left: 0 !important; }\n  .m-xl-4 {\n    margin: 2.25rem !important; }\n  .mt-xl-4,\n  .my-xl-4 {\n    margin-top: 2.25rem !important; }\n  .mr-xl-4,\n  .mx-xl-4 {\n    margin-left: 2.25rem !important;\n    margin-right: 0 !important; }\n  .mb-xl-4,\n  .my-xl-4 {\n    margin-bottom: 2.25rem !important; }\n  .ml-xl-4,\n  .mx-xl-4 {\n    margin-right: 2.25rem !important;\n    margin-left: 0 !important; }\n  .m-xl-5 {\n    margin: 4.5rem !important; }\n  .mt-xl-5,\n  .my-xl-5 {\n    margin-top: 4.5rem !important; }\n  .mr-xl-5,\n  .mx-xl-5 {\n    margin-left: 4.5rem !important;\n    margin-right: 0 !important; }\n  .mb-xl-5,\n  .my-xl-5 {\n    margin-bottom: 4.5rem !important; }\n  .ml-xl-5,\n  .mx-xl-5 {\n    margin-right: 4.5rem !important;\n    margin-left: 0 !important; }\n  .p-xl-0 {\n    padding: 0 !important; }\n  .pt-xl-0,\n  .py-xl-0 {\n    padding-top: 0 !important; }\n  .pr-xl-0,\n  .px-xl-0 {\n    padding-left: 0 !important;\n    padding-right: 0 !important; }\n  .pb-xl-0,\n  .py-xl-0 {\n    padding-bottom: 0 !important; }\n  .pl-xl-0,\n  .px-xl-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important; }\n  .p-xl-1 {\n    padding: 0.375rem !important; }\n  .pt-xl-1,\n  .py-xl-1 {\n    padding-top: 0.375rem !important; }\n  .pr-xl-1,\n  .px-xl-1 {\n    padding-left: 0.375rem !important;\n    padding-right: 0 !important; }\n  .pb-xl-1,\n  .py-xl-1 {\n    padding-bottom: 0.375rem !important; }\n  .pl-xl-1,\n  .px-xl-1 {\n    padding-right: 0.375rem !important;\n    padding-left: 0 !important; }\n  .p-xl-2 {\n    padding: 0.75rem !important; }\n  .pt-xl-2,\n  .py-xl-2 {\n    padding-top: 0.75rem !important; }\n  .pr-xl-2,\n  .px-xl-2 {\n    padding-left: 0.75rem !important;\n    padding-right: 0 !important; }\n  .pb-xl-2,\n  .py-xl-2 {\n    padding-bottom: 0.75rem !important; }\n  .pl-xl-2,\n  .px-xl-2 {\n    padding-right: 0.75rem !important;\n    padding-left: 0 !important; }\n  .p-xl-3 {\n    padding: 1.5rem !important; }\n  .pt-xl-3,\n  .py-xl-3 {\n    padding-top: 1.5rem !important; }\n  .pr-xl-3,\n  .px-xl-3 {\n    padding-left: 1.5rem !important;\n    padding-right: 0 !important; }\n  .pb-xl-3,\n  .py-xl-3 {\n    padding-bottom: 1.5rem !important; }\n  .pl-xl-3,\n  .px-xl-3 {\n    padding-right: 1.5rem !important;\n    padding-left: 0 !important; }\n  .p-xl-4 {\n    padding: 2.25rem !important; }\n  .pt-xl-4,\n  .py-xl-4 {\n    padding-top: 2.25rem !important; }\n  .pr-xl-4,\n  .px-xl-4 {\n    padding-left: 2.25rem !important;\n    padding-right: 0 !important; }\n  .pb-xl-4,\n  .py-xl-4 {\n    padding-bottom: 2.25rem !important; }\n  .pl-xl-4,\n  .px-xl-4 {\n    padding-right: 2.25rem !important;\n    padding-left: 0 !important; }\n  .p-xl-5 {\n    padding: 4.5rem !important; }\n  .pt-xl-5,\n  .py-xl-5 {\n    padding-top: 4.5rem !important; }\n  .pr-xl-5,\n  .px-xl-5 {\n    padding-left: 4.5rem !important;\n    padding-right: 0 !important; }\n  .pb-xl-5,\n  .py-xl-5 {\n    padding-bottom: 4.5rem !important; }\n  .pl-xl-5,\n  .px-xl-5 {\n    padding-right: 4.5rem !important;\n    padding-left: 0 !important; }\n  .m-xl-n1 {\n    margin: -0.375rem !important; }\n  .mt-xl-n1,\n  .my-xl-n1 {\n    margin-top: -0.375rem !important; }\n  .mr-xl-n1,\n  .mx-xl-n1 {\n    margin-right: -0.375rem !important; }\n  .mb-xl-n1,\n  .my-xl-n1 {\n    margin-bottom: -0.375rem !important; }\n  .ml-xl-n1,\n  .mx-xl-n1 {\n    margin-left: -0.375rem !important; }\n  .m-xl-n2 {\n    margin: -0.75rem !important; }\n  .mt-xl-n2,\n  .my-xl-n2 {\n    margin-top: -0.75rem !important; }\n  .mr-xl-n2,\n  .mx-xl-n2 {\n    margin-right: -0.75rem !important; }\n  .mb-xl-n2,\n  .my-xl-n2 {\n    margin-bottom: -0.75rem !important; }\n  .ml-xl-n2,\n  .mx-xl-n2 {\n    margin-left: -0.75rem !important; }\n  .m-xl-n3 {\n    margin: -1.5rem !important; }\n  .mt-xl-n3,\n  .my-xl-n3 {\n    margin-top: -1.5rem !important; }\n  .mr-xl-n3,\n  .mx-xl-n3 {\n    margin-right: -1.5rem !important; }\n  .mb-xl-n3,\n  .my-xl-n3 {\n    margin-bottom: -1.5rem !important; }\n  .ml-xl-n3,\n  .mx-xl-n3 {\n    margin-left: -1.5rem !important; }\n  .m-xl-n4 {\n    margin: -2.25rem !important; }\n  .mt-xl-n4,\n  .my-xl-n4 {\n    margin-top: -2.25rem !important; }\n  .mr-xl-n4,\n  .mx-xl-n4 {\n    margin-right: -2.25rem !important; }\n  .mb-xl-n4,\n  .my-xl-n4 {\n    margin-bottom: -2.25rem !important; }\n  .ml-xl-n4,\n  .mx-xl-n4 {\n    margin-left: -2.25rem !important; }\n  .m-xl-n5 {\n    margin: -4.5rem !important; }\n  .mt-xl-n5,\n  .my-xl-n5 {\n    margin-top: -4.5rem !important; }\n  .mr-xl-n5,\n  .mx-xl-n5 {\n    margin-right: -4.5rem !important; }\n  .mb-xl-n5,\n  .my-xl-n5 {\n    margin-bottom: -4.5rem !important; }\n  .ml-xl-n5,\n  .mx-xl-n5 {\n    margin-left: -4.5rem !important; }\n  .m-xl-auto {\n    margin: auto !important; }\n  .mt-xl-auto,\n  .my-xl-auto {\n    margin-top: auto !important; }\n  .mr-xl-auto,\n  .mx-xl-auto {\n    margin-left: auto !important;\n    margin-right: inherit !important; }\n  .mb-xl-auto,\n  .my-xl-auto {\n    margin-bottom: auto !important; }\n  .ml-xl-auto,\n  .mx-xl-auto {\n    margin-right: auto !important;\n    margin-left: auto !important; } }\n\n.float-left {\n  float: right !important; }\n\n.float-right {\n  float: left !important; }\n\n.float-none {\n  float: none !important; }\n\n@media (min-width: 576px) {\n  .float-sm-left {\n    float: right !important; }\n  .float-sm-right {\n    float: left !important; }\n  .float-sm-none {\n    float: none !important; } }\n\n@media (min-width: 768px) {\n  .float-md-left {\n    float: right !important; }\n  .float-md-right {\n    float: left !important; }\n  .float-md-none {\n    float: none !important; } }\n\n@media (min-width: 992px) {\n  .float-lg-left {\n    float: right !important; }\n  .float-lg-right {\n    float: left !important; }\n  .float-lg-none {\n    float: none !important; } }\n\n@media (min-width: 1200px) {\n  .float-xl-left {\n    float: right !important; }\n  .float-xl-right {\n    float: left !important; }\n  .float-xl-none {\n    float: none !important; } }\n\n.text-monospace {\n  font-family: SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !important; }\n\n.text-justify {\n  text-align: justify !important; }\n\n.text-wrap {\n  white-space: normal !important; }\n\n.text-nowrap {\n  white-space: nowrap !important; }\n\n.text-truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap; }\n\n.text-left {\n  text-align: right !important; }\n\n.text-right {\n  text-align: left !important; }\n\n.text-center {\n  text-align: center !important; }\n\n@media (min-width: 576px) {\n  .text-sm-left {\n    text-align: right !important; }\n  .text-sm-right {\n    text-align: left !important; }\n  .text-sm-center {\n    text-align: center !important; } }\n\n@media (min-width: 768px) {\n  .text-md-left {\n    text-align: right !important; }\n  .text-md-right {\n    text-align: left !important; }\n  .text-md-center {\n    text-align: center !important; } }\n\n@media (min-width: 992px) {\n  .text-lg-left {\n    text-align: right !important; }\n  .text-lg-right {\n    text-align: left !important; }\n  .text-lg-center {\n    text-align: center !important; } }\n\n@media (min-width: 1200px) {\n  .text-xl-left {\n    text-align: right !important; }\n  .text-xl-right {\n    text-align: left !important; }\n  .text-xl-center {\n    text-align: center !important; } }\n\n.text-lowercase {\n  text-transform: lowercase !important; }\n\n.text-uppercase {\n  text-transform: uppercase !important; }\n\n.text-capitalize {\n  text-transform: capitalize !important; }\n\n.font-weight-light {\n  font-weight: 300 !important; }\n\n.font-weight-lighter {\n  font-weight: lighter !important; }\n\n.font-weight-normal {\n  font-weight: 400 !important; }\n\n.font-weight-bold {\n  font-weight: 700 !important; }\n\n.font-weight-bolder {\n  font-weight: bolder !important; }\n\n.font-italic {\n  font-style: italic !important; }\n\n.text-white {\n  color: #fff !important; }\n\n.text-primary {\n  color: #7e57c2 !important; }\n\na.text-primary:hover, a.text-primary:focus {\n  color: #593696 !important; }\n\n.text-secondary {\n  color: #72747b !important; }\n\na.text-secondary:hover, a.text-secondary:focus {\n  color: #4d4f53 !important; }\n\n.text-success {\n  color: #1abc9c !important; }\n\na.text-success:hover, a.text-success:focus {\n  color: #117964 !important; }\n\n.text-info {\n  color: #4fc6e1 !important; }\n\na.text-info:hover, a.text-info:focus {\n  color: #21a5c2 !important; }\n\n.text-warning {\n  color: #f7b84b !important; }\n\na.text-warning:hover, a.text-warning:focus {\n  color: #eb990a !important; }\n\n.text-danger {\n  color: #f1556c !important; }\n\na.text-danger:hover, a.text-danger:focus {\n  color: #e71332 !important; }\n\n.text-light {\n  color: #f1f5f7 !important; }\n\na.text-light:hover, a.text-light:focus {\n  color: #c0d2db !important; }\n\n.text-dark {\n  color: #323a46 !important; }\n\na.text-dark:hover, a.text-dark:focus {\n  color: #121519 !important; }\n\n.text-pink {\n  color: #f672a7 !important; }\n\na.text-pink:hover, a.text-pink:focus {\n  color: #f12a7a !important; }\n\n.text-blue {\n  color: #4a81d4 !important; }\n\na.text-blue:hover, a.text-blue:focus {\n  color: #285ca9 !important; }\n\n.text-body {\n  color: #72747b !important; }\n\n.text-muted {\n  color: #98a6ad !important; }\n\n.text-black-50 {\n  color: rgba(0, 0, 0, 0.5) !important; }\n\n.text-white-50 {\n  color: rgba(255, 255, 255, 0.5) !important; }\n\n.text-hide {\n  font: 0/0 a;\n  color: transparent;\n  text-shadow: none;\n  background-color: transparent;\n  border: 0; }\n\n.text-decoration-none {\n  text-decoration: none !important; }\n\n.text-break {\n  word-break: break-word !important;\n  overflow-wrap: break-word !important; }\n\n.text-reset {\n  color: inherit !important; }\n\n.logo-box {\n  float: right; }\n\n.navbar-custom {\n  padding: 0 0 0 10px;\n  /* Search */ }\n  .navbar-custom .topnav-menu > li {\n    float: right; }\n  .navbar-custom .topnav-menu .nav-link {\n    direction: ltr; }\n  .navbar-custom .app-search {\n    margin-left: 20px; }\n    .navbar-custom .app-search .form-control {\n      padding-right: 20px;\n      padding-left: 0;\n      border-radius: 0 30px 30px 0 !important; }\n    .navbar-custom .app-search .input-group-append {\n      margin-right: 0; }\n    .navbar-custom .app-search .btn {\n      border-radius: 30px 0 0 30px !important; }\n\n/* Notification */\n.notification-list .noti-icon-badge {\n  left: 10px;\n  right: auto; }\n\n.notification-list .notify-item {\n  padding: 12px 20px; }\n  .notification-list .notify-item .notify-icon {\n    float: right;\n    margin-left: 10px;\n    margin-right: 0; }\n  .notification-list .notify-item .notify-details,\n  .notification-list .notify-item .user-msg {\n    margin-left: 0;\n    margin-right: 45px; }\n\n.notification-list .profile-dropdown .notify-item {\n  padding: 7px 20px; }\n\n.profile-dropdown i {\n  vertical-align: middle;\n  margin: 5px 0 0 10px;\n  float: right; }\n\n.page-title-box .page-title-right {\n  float: left; }\n\n.content-page {\n  margin-right: 240px;\n  margin-left: 0; }\n\n#sidebar-menu > ul > li > a i {\n  margin: 0 3px 0 10px; }\n\n#sidebar-menu > ul > li > a .drop-arrow {\n  float: left; }\n  #sidebar-menu > ul > li > a .drop-arrow i {\n    margin-left: 0; }\n\n#sidebar-menu > ul > li > ul {\n  padding-right: 40px;\n  padding-left: 0; }\n  #sidebar-menu > ul > li > ul ul {\n    padding-right: 20px;\n    padding-left: 0; }\n\n#sidebar-menu .menu-arrow {\n  left: 20px;\n  right: auto; }\n  #sidebar-menu .menu-arrow:before {\n    content: \"\\F141\"; }\n\n#sidebar-menu li.active > a > span.menu-arrow {\n  transform: rotate(-90deg); }\n\n.enlarged .left-side-menu #sidebar-menu > ul > li > a i {\n  margin-left: 20px;\n  margin-right: 5px; }\n\n.enlarged .left-side-menu #sidebar-menu > ul > li > a span {\n  padding-right: 25px;\n  padding-left: 0; }\n\n.enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul {\n  right: 70px;\n  left: auto; }\n\n.enlarged .left-side-menu #sidebar-menu > ul ul li:hover > ul {\n  right: 190px;\n  margin-top: -36px; }\n\n.enlarged .left-side-menu #sidebar-menu > ul ul li > a span.pull-right {\n  left: 20px;\n  right: 0; }\n\n.enlarged .content-page {\n  margin-right: 70px !important;\n  margin-left: 0 !important; }\n\n.enlarged .footer {\n  left: 0 !important;\n  right: 70px !important; }\n\n@media (max-width: 767.98px) {\n  .content-page,\n  .enlarged .content-page {\n    margin-right: 0 !important; } }\n\n/* =============\r\n  Small Menu\r\n============= */\n.left-side-menu-sm .left-side-menu #sidebar-menu > ul ul {\n  padding-right: 0; }\n\n.left-side-menu-sm .left-side-menu + .content-page {\n  margin-right: 160px;\n  margin-left: 0; }\n\n.left-side-menu-sm .left-side-menu + .content-page .footer {\n  left: auto;\n  right: 160px; }\n\n.enlarged.left-side-menu-sm #wrapper .left-side-menu {\n  text-align: right; }\n  .enlarged.left-side-menu-sm #wrapper .left-side-menu ul li a i {\n    margin-right: 3px;\n    margin-left: 15px; }\n\n.user-pro-dropdown {\n  margin-left: 0;\n  margin-right: 5%; }\n\n.footer {\n  left: 0;\n  right: 240px; }\n\n.footer-alt {\n  right: 0 !important; }\n\n@media (max-width: 767.98px) {\n  .footer {\n    right: 0 !important; } }\n\n.right-bar {\n  float: left !important;\n  left: -270px;\n  right: auto; }\n  .right-bar .user-box .user-img .user-edit {\n    right: 0;\n    left: -5px; }\n\n.right-bar-enabled .right-bar {\n  left: 0;\n  right: auto; }\n\n.nice-select {\n  float: right; }\n\n.dd-item > button {\n  float: right; }\n\n.dd-item .dd-list {\n  padding-right: 30px;\n  padding-left: 0; }\n\n.dd-list .dd3-handle {\n  float: right; }\n\n.select2-container .select2-selection--single .select2-selection__rendered {\n  padding-right: 12px; }\n\n.select2-container .select2-selection--single .select2-selection__arrow {\n  left: 3px;\n  right: auto; }\n\n.select2-container .select2-selection--multiple .select2-selection__choice {\n  float: right;\n  margin-left: 5px;\n  margin-right: 0; }\n\n.select2-container .select2-search--inline {\n  float: right; }\n\n.ms-container .ms-optgroup-label {\n  padding: 5px 5px 0px 0; }\n\n.bootstrap-select .dropdown-toggle:before {\n  float: left; }\n\n.bootstrap-select .dropdown-toggle .filter-option {\n  text-align: right; }\n\n.bootstrap-select .dropdown-toggle .filter-option-inner {\n  padding-right: 0;\n  padding-left: inherit; }\n\n.parsley-errors-list > li {\n  padding-left: 0;\n  padding-right: 20px; }\n  .parsley-errors-list > li:before {\n    left: auto;\n    right: 2px; }\n\n.ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg {\n  left: 0;\n  right: auto; }\n\n.ql-editor {\n  direction: rtl;\n  text-align: right; }\n\n.editable-buttons {\n  margin-left: 0;\n  margin-right: 7px; }\n  .editable-buttons .editable-cancel {\n    margin-left: 0;\n    margin-right: 7px; }\n\n.footable.breakpoint > tbody > tr > td > span.footable-toggle {\n  padding-left: 5px;\n  padding-right: 0; }\n\n.ribbon-box {\n  /* Ribbon two */ }\n  .ribbon-box .ribbon:before {\n    right: 0;\n    left: auto; }\n  .ribbon-box .ribbon.float-left {\n    margin-right: -30px;\n    margin-left: auto;\n    border-radius: 3px 0 0 3px; }\n  .ribbon-box .ribbon.float-right {\n    margin-left: -30px;\n    margin-right: 0;\n    border-radius: 0 3px 3px 0; }\n    .ribbon-box .ribbon.float-right:before {\n      left: 0;\n      right: auto; }\n  .ribbon-box .ribbon-two {\n    right: -5px;\n    left: auto;\n    text-align: left; }\n    .ribbon-box .ribbon-two span {\n      transform: rotate(45deg);\n      right: -21px;\n      left: auto; }\n\n.inbox-widget .inbox-item .inbox-item-img {\n  float: right;\n  margin-left: 15px;\n  margin-right: 0; }\n\n.inbox-widget .inbox-item .inbox-item-date {\n  right: auto;\n  left: 5px; }\n\n.conversation-list .chat-avatar {\n  float: right; }\n\n.conversation-list .ctext-wrap:after {\n  left: 99%;\n  right: auto;\n  margin-left: 0;\n  margin-right: -1px;\n  border-right-color: transparent;\n  border-left-color: #f7f7f7; }\n\n.conversation-list .conversation-text {\n  float: right;\n  margin-right: 12px;\n  margin-left: 0; }\n\n.conversation-list .odd .chat-avatar {\n  float: left !important; }\n\n.conversation-list .odd .conversation-text {\n  float: left !important;\n  margin-right: 0;\n  margin-left: 12px;\n  text-align: left; }\n\n.conversation-list .odd .ctext-wrap:after {\n  border-color: transparent;\n  border-left-color: transparent;\n  border-right-color: #fef5e4;\n  border-top-color: #fef5e4;\n  left: auto !important;\n  right: 99% !important; }\n\n.checkbox label {\n  padding-right: 8px;\n  padding-left: 0; }\n  .checkbox label::before {\n    left: auto;\n    right: 0;\n    margin-left: 0;\n    margin-right: -18px; }\n  .checkbox label::after {\n    left: auto;\n    right: 0;\n    margin-right: -18px;\n    margin-left: 0;\n    padding-left: 0;\n    padding-right: 3px; }\n\n.checkbox input[type=\"checkbox\"]:checked + label::after {\n  left: auto;\n  right: 7px;\n  transform: rotate(45deg); }\n\n.checkbox.checkbox-single label:before {\n  margin-right: 0; }\n\n.checkbox.checkbox-single label:after {\n  margin-right: 0; }\n\n.radio label {\n  padding-left: 0;\n  padding-right: 8px; }\n  .radio label::before {\n    left: auto;\n    right: 0;\n    margin-left: 0;\n    margin-right: -18px; }\n  .radio label::after {\n    left: 0;\n    right: 6px;\n    margin-left: 0;\n    margin-right: -20px; }\n\n.taskList .checkbox {\n  margin-right: 20px;\n  margin-left: 0;\n  margin-top: 5px; }\n\n.inbox-leftbar {\n  float: right;\n  padding: 0 10px 20px 20px; }\n\n.inbox-rightbar {\n  margin: -1.5rem 250px -1.5rem 0;\n  border-right: 5px solid #f2f5f7;\n  border-left: 0;\n  padding: 1.5rem 25px 1.5rem 0; }\n\n.message-list {\n  display: block;\n  padding-right: 0; }\n  .message-list li .col-mail {\n    float: right; }\n  .message-list li .col-mail-1 {\n    width: 320px; }\n    .message-list li .col-mail-1 .star-toggle,\n    .message-list li .col-mail-1 .checkbox-wrapper-mail,\n    .message-list li .col-mail-1 .dot {\n      float: right; }\n    .message-list li .col-mail-1 .checkbox-wrapper-mail {\n      margin: 15px 20px 0 10px; }\n    .message-list li .col-mail-1 .star-toggle {\n      margin-right: 10px;\n      margin-left: 0; }\n    .message-list li .col-mail-1 .title {\n      right: 100px;\n      left: 0; }\n  .message-list li .col-mail-2 {\n    right: 320px;\n    left: 0; }\n    .message-list li .col-mail-2 .subject {\n      right: 0;\n      left: 110px; }\n    .message-list li .col-mail-2 .date {\n      left: 0;\n      right: auto;\n      padding-right: 10px;\n      padding-left: 0; }\n\n@media (max-width: 648px) {\n  .inbox-rightbar {\n    margin-right: 0;\n    border: 0;\n    padding-right: 0; }\n  .message-list li .col-mail-1 .checkbox-wrapper-mail {\n    margin-right: 0; } }\n\n@media (max-width: 520px) {\n  .message-list li .col-mail-1 .title {\n    right: 80px; }\n  .message-list li .col-mail-2 {\n    right: 160px; }\n    .message-list li .col-mail-2 .date {\n      text-align: left;\n      padding-left: 10px;\n      padding-right: 20px; } }\n\n.sitemap ul {\n  margin-right: 1rem;\n  margin-left: 0; }\n  .sitemap ul li a {\n    margin-left: 0;\n    margin-right: 2.75rem; }\n  .sitemap ul li:before {\n    border-left: transparent;\n    border-right: 1px solid rgba(152, 166, 173, 0.5); }\n\n.faq-question-q-box {\n  float: right; }\n\n.faq-question {\n  margin-left: 0;\n  margin-right: 50px; }\n\n.faq-answer {\n  margin-left: 0;\n  margin-right: 50px; }\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n != null and $n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width. Null for the largest (last) breakpoint.\n// The maximum value is calculated as the minimum of the next one less 0.02px\n// to work around the limitations of `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $next: breakpoint-next($name, $breakpoints);\n  @return if($next, breakpoint-min($next, $breakpoints) - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  $max: breakpoint-max($name, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($name, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "// \r\n// topbar.scss\r\n//\r\n\r\n// Logo\r\n.logo {\r\n    display: block;\r\n    line-height: $topbar-height;\r\n    span.logo-lg {\r\n        display: block;\r\n    }\r\n    span.logo-sm {\r\n        display: none;\r\n    }\r\n    .logo-lg-text-dark {\r\n        color: $dark;\r\n        font-weight: $font-weight-bold;\r\n        font-size: 22px;\r\n        text-transform: uppercase;\r\n    }\r\n    .logo-lg-text-light {\r\n        color: $white;\r\n        font-weight: $font-weight-bold;\r\n        font-size: 22px;\r\n        text-transform: uppercase;\r\n    }\r\n}\r\n\r\n.logo-box {\r\n    height: $topbar-height;\r\n    width: $leftbar-width;\r\n    float: left;\r\n}\r\n\r\n.navbar-custom {\r\n    background-color: $bg-topbar-dark;\r\n    box-shadow: $shadow;\r\n    padding: 0 10px 0 0;\r\n    position: fixed;\r\n    left: 0;\r\n    right: 0;\r\n    height: $topbar-height;\r\n    z-index: 100;\r\n\r\n    .topnav-menu {\r\n        > li {\r\n            float: left;\r\n        }\r\n        .nav-link {\r\n            padding: 0 15px;\r\n            color: rgba($white,0.6);\r\n            min-width: 32px;\r\n            display: block;\r\n            line-height: $topbar-height;\r\n            text-align: center;\r\n            max-height: $topbar-height;\r\n        }\r\n    }\r\n    .dropdown.show {\r\n        .nav-link {\r\n            background-color: rgba($white,0.05);\r\n        }   \r\n    }\r\n\r\n    /* Search */\r\n    .app-search {\r\n        overflow: hidden;\r\n        height: $topbar-height;\r\n        display: table;\r\n        max-width: 180px;\r\n        margin-right: 20px;\r\n        \r\n        .app-search-box {\r\n            display: table-cell;\r\n            vertical-align: middle;\r\n\r\n            input::-webkit-input-placeholder {\r\n                font-size: 0.8125rem;\r\n                color: rgba($white,0.3);\r\n            }\r\n        }\r\n        .form-control {\r\n            border: none;\r\n            height: 38px;\r\n            padding-left: 20px;\r\n            padding-right: 0;\r\n            color: $white;\r\n            background-color: rgba($white,0.07);\r\n            box-shadow: none;\r\n            border-radius: 30px 0 0 30px;\r\n        }\r\n        .input-group-append {\r\n            margin-left: 0;\r\n            z-index: 4;\r\n        }\r\n\r\n        .btn {\r\n            background-color: rgba($white,0.07);\r\n            border-color: transparent;\r\n            color: rgba($white,0.3);\r\n            border-radius: 0 30px 30px 0;\r\n            box-shadow: none !important;\r\n        }\r\n    }\r\n\r\n    .button-menu-mobile {\r\n        border: none;\r\n        color: $white;\r\n        display: inline-block;\r\n        height: $topbar-height;\r\n        line-height: $topbar-height;\r\n        width: 60px;\r\n        background-color: transparent;\r\n        font-size: 24px;\r\n        cursor: pointer;\r\n    }\r\n    \r\n    .button-menu-mobile.disable-btn {\r\n        display: none;\r\n    }\r\n}\r\n\r\n\r\n/* Notification */\r\n.noti-scroll {\r\n    max-height: 230px;\r\n}\r\n\r\n.notification-list {\r\n    margin-left: 0;\r\n\r\n    .noti-title {\r\n        background-color: $white;\r\n        padding: 15px 20px;\r\n    }\r\n\r\n    .noti-icon {\r\n        font-size: 21px;\r\n        vertical-align: middle;\r\n    }\r\n\r\n    .noti-icon-badge {\r\n        display: inline-block;\r\n        position: absolute;\r\n        top: 16px;\r\n        right: 10px;\r\n    }\r\n\r\n    .notify-item {\r\n        padding: 12px 20px;\r\n\r\n        .notify-icon {\r\n            float: left;\r\n            height: 36px;\r\n            width: 36px;\r\n            font-size: 18px;\r\n            line-height: 36px;\r\n            text-align: center;\r\n            margin-right: 10px;\r\n            border-radius: 50%;\r\n            color: $white;\r\n        }\r\n\r\n        .notify-details {\r\n            margin-bottom: 5px;\r\n            overflow: hidden;\r\n            margin-left: 45px;\r\n            text-overflow: ellipsis;\r\n            white-space: nowrap;\r\n            color: $gray-800;\r\n\r\n            b {\r\n                font-weight: 500;\r\n            }\r\n            small {\r\n                display: block;\r\n            }\r\n            span {\r\n                display: block;\r\n                overflow: hidden;\r\n                text-overflow: ellipsis;\r\n                white-space: nowrap;\r\n                font-size: 13px;\r\n            }\r\n        }\r\n        \r\n        .user-msg {\r\n            margin-left: 45px;\r\n            white-space: normal;\r\n            line-height: 16px;\r\n        }\r\n    }\r\n    .profile-dropdown {\r\n        .notify-item {\r\n            padding: 7px 20px;\r\n        }\r\n    }\r\n}\r\n\r\n.profile-dropdown {\r\n    width: 170px;\r\n    i {\r\n        vertical-align: middle;\r\n        margin-right: 5px;\r\n    }\r\n}\r\n\r\n.nav-user {\r\n    padding: 0 12px !important;\r\n    img {\r\n        height: 32px;\r\n        width: 32px;\r\n    }\r\n}\r\n\r\n// Topbar light\r\n.navbar-custom-light {\r\n    background-color: $bg-topbar-light !important;\r\n    box-shadow: $shadow-sm;\r\n    \r\n    .topnav-menu {\r\n        .nav-link {\r\n            color: $gray-700;\r\n        }\r\n    }\r\n    .dropdown.show {\r\n        .nav-link {\r\n            background-color: rgba($dark,0.03);\r\n        }   \r\n    }\r\n\r\n    .button-menu-mobile {\r\n        color: $dark;\r\n    }\r\n\r\n    /* Search */\r\n    .app-search {\r\n        input::-webkit-input-placeholder {\r\n            color: $gray-500 !important;\r\n        }\r\n        .form-control {\r\n            color: $dark;\r\n            background-color: $gray-100;\r\n            border-color: $gray-100;\r\n        }\r\n        .btn {\r\n            background-color: $gray-100;\r\n            color: $gray-400;\r\n        }\r\n    }\r\n}", "// \r\n// page-title.scss\r\n//\r\n\r\n.page-title-box {\r\n    .page-title {\r\n        font-size: 1.25rem;\r\n        margin: 0;\r\n        line-height: 75px;\r\n        color: $gray-900;\r\n    }\r\n    .page-title-right {\r\n        float: right;\r\n        margin-top: 22px;\r\n    }\r\n\r\n    .breadcrumb {\r\n        padding-top: 5px;\r\n    }\r\n}\r\n\r\n\r\n@include media-breakpoint-down(sm) {\r\n    .page-title-box {\r\n        .page-title {\r\n            display: block;\r\n            white-space: nowrap;\r\n            text-overflow: ellipsis;\r\n            overflow: hidden;\r\n            line-height: 70px;\r\n        }\r\n        .breadcrumb {\r\n            display: none;\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 640px) {\r\n    .page-title-box {\r\n        .page-title-right {\r\n            display: none;\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 419px) {\r\n    .page-title-box .breadcrumb {\r\n        display: none;\r\n    }\r\n}", "// \r\n// footer.scss\r\n//\r\n\r\n.footer {\r\n    bottom: 0;\r\n    padding: 19px 15px 20px;\r\n    position: absolute;\r\n    right: 0;\r\n    color: $gray-600;\r\n    left: $leftbar-width;\r\n    background-color: darken($body-bg,1.5%);\r\n\r\n    .footer-links {\r\n        a {\r\n            color: $gray-600;\r\n            margin-left: 1.5rem;\r\n            transition: all .4s;\r\n            &:hover {\r\n                color: $gray-900;\r\n            }\r\n            &:first-of-type {\r\n                margin-left: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.footer-alt {\r\n    left: 0 !important;\r\n    text-align: center;\r\n    background-color: transparent;\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n    .footer {\r\n        left: 0 !important;\r\n        text-align: center;\r\n    }\r\n}", "//\r\n// right-sidebar.scss\r\n//\r\n\r\n.right-bar {\r\n    background-color: $white;\r\n    box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);\r\n    display: block;\r\n    position: fixed;\r\n    transition: all 200ms ease-out;\r\n    width: $rightbar-width;\r\n    z-index: 9999;\r\n    float: right !important;\r\n    right: -($rightbar-width + 10px);\r\n    top: 0;\r\n    bottom: 0;\r\n\r\n    .rightbar-title {\r\n        background-color: $primary;\r\n        padding: 27px 25px;\r\n        color: $white;\r\n    }\r\n    .right-bar-toggle {\r\n        background-color: lighten($dark, 7%);\r\n        height: 24px;\r\n        width: 24px;\r\n        line-height: 27px;\r\n        color: $white;\r\n        text-align: center;\r\n        border-radius: 50%;\r\n        margin-top: -4px;\r\n\r\n        &:hover {\r\n            background-color: lighten($dark, 10%);\r\n        }\r\n    }\r\n    .user-box {\r\n        padding: 25px;\r\n        text-align: center;\r\n        .user-img {\r\n            position: relative;\r\n            height: 64px;\r\n            width: 64px;\r\n            margin: 0 auto 15px auto;\r\n            .user-edit {\r\n                position: absolute;\r\n                right: -5px;\r\n                bottom: 0px;\r\n                height: 24px;\r\n                width: 24px;\r\n                background-color: $white;\r\n                line-height: 24px;\r\n                border-radius: 50%;\r\n                box-shadow: $shadow-lg;\r\n            }\r\n        }\r\n        h5 {\r\n            margin-bottom: 2px;\r\n            a {\r\n                color: $dark;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Rightbar overlay\r\n.rightbar-overlay {\r\n    background-color: rgba($dark, 0.55);\r\n    position: absolute;\r\n    left: 0;\r\n    right: 0;\r\n    top: 0;\r\n    bottom: 0;\r\n    display: none;\r\n    z-index: 9998;\r\n    transition: all .2s ease-out;\r\n}\r\n\r\n.right-bar-enabled {\r\n    .right-bar {\r\n        right: 0;\r\n    }\r\n    .rightbar-overlay {\r\n        display: block;\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n    .right-bar {\r\n        overflow: auto;\r\n        .slimscroll-menu {\r\n            height: auto !important;\r\n        }\r\n    }\r\n}", "//\r\n// layouts.scss\r\n//\r\n\r\nbody {\r\n    &.boxed-layout {\r\n        #wrapper {\r\n            max-width: $boxed-layout-width;\r\n            margin: 0 auto;\r\n            box-shadow: $shadow-lg;\r\n        }\r\n\r\n        .navbar-custom {\r\n            max-width: $boxed-layout-width;\r\n            margin: 0 auto;\r\n        }\r\n\r\n        .footer {\r\n            margin: 0 auto;\r\n            max-width: calc(#{$boxed-layout-width} - #{$leftbar-width});\r\n        }\r\n\r\n        &.enlarged {\r\n            .footer {\r\n                max-width: calc(#{$boxed-layout-width} - #{$leftbar-width-collapsed});\r\n            }\r\n        }\r\n    }\r\n}", "//\n// helper.scss\n//\n\n// Minimum width\n\n.width-xs {\n  min-width: 80px;\n}\n\n.width-sm {\n  min-width: 100px;\n}\n\n.width-md {\n  min-width: 120px;\n}\n\n.width-lg {\n  min-width: 140px;\n}\n\n.width-xl {\n  min-width: 160px;\n}\n\n\n// Font Family\n.font-family-secondary {\n  font-family: $font-family-secondary;\n}\n\n// avatar height\n.avatar-xs {\n  height: 1.5rem;\n  width: 1.5rem;\n}\n\n.avatar-sm {\n  height: 2.25rem;\n  width: 2.25rem;\n}\n\n.avatar-md {\n  height: 3.5rem;\n  width: 3.5rem;\n}\n\n.avatar-lg {\n  height: 4.5rem;\n  width: 4.5rem;\n}\n\n.avatar-xl {\n  height: 6rem;\n  width: 6rem;\n}\n\n.avatar-xxl {\n  height: 7.5rem;\n  width: 7.5rem;\n}\n\n.avatar-title {\n  align-items: center;\n  color: $white;\n  display: flex;\n  height: 100%;\n  justify-content: center;\n  width: 100%;\n}\n\n.avatar-group {\n  padding-left: 12px;\n  .avatar-group-item {\n    margin: 0 0 10px -12px;\n    display: inline-block;\n    border: 2px solid $white;\n    border-radius: 50%;\n  }\n}\n\n// Text specify lines (Only chrome browser support)\n\n.sp-line-1,\n.sp-line-2,\n.sp-line-3,\n.sp-line-4 {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n}\n\n.sp-line-1 {\n  -webkit-line-clamp: 1;\n}\n\n.sp-line-2 {\n  -webkit-line-clamp: 2;\n}\n\n\n.sp-line-3 {\n  -webkit-line-clamp: 3;\n}\n\n.sp-line-4 {\n  -webkit-line-clamp: 4;\n}", "\r\n// \r\n// social.scss\r\n//\r\n\r\n.social-list-item {\r\n    height: 2rem;\r\n    width: 2rem;\r\n    line-height: calc(2rem - 2px);\r\n    display: block;\r\n    border: 2px solid $gray-500;\r\n    border-radius: 50%;\r\n    color: $gray-500;\r\n}  ", "// \r\n// widgets.scss\r\n//\r\n\r\n// Simple tile box\r\n.widget-flat {\r\n    position: relative;\r\n    overflow: hidden;\r\n    i.widget-icon {\r\n        font-size: 36px;\r\n    }\r\n}\r\n\r\n// Inbox-widget(Used Profile)\r\n.inbox-widget {\r\n    .inbox-item {\r\n        border-bottom: 1px solid lighten($light, 5%);\r\n        overflow: hidden;\r\n        padding: 0.625rem 0;\r\n        position: relative;\r\n        &:last-of-type {\r\n            border-bottom: none;\r\n        }\r\n        .inbox-item-img {\r\n            display: block;\r\n            float: left;\r\n            margin-right: 15px;\r\n            width: 40px;\r\n            img {\r\n                width: 40px;\r\n            }\r\n        }\r\n        .inbox-item-author {\r\n            color: $dark;\r\n            display: block;\r\n            margin-bottom: 3px;\r\n        }\r\n        .inbox-item-text {\r\n            color: $gray-600;\r\n            display: block;\r\n            font-size: 0.8125rem;\r\n            margin: 0;\r\n            overflow: hidden;\r\n        }\r\n        .inbox-item-date {\r\n            color: $gray-600;\r\n            font-size: 0.6875rem;\r\n            position: absolute;\r\n            right: 5px;\r\n            top: 10px;\r\n        }\r\n    }\r\n}\r\n\r\n/* Chat widget */\r\n\r\n.conversation-list {\r\n    list-style: none;\r\n    height: 332px;\r\n    padding: 0 20px;\r\n    li {\r\n        margin-bottom: 24px;\r\n    }\r\n    .chat-avatar {\r\n        float: left;\r\n        text-align: center;\r\n        width: 42px;\r\n        img {\r\n            border-radius: 100%;\r\n            width: 100%;\r\n        }\r\n        i {\r\n            font-size: 12px;\r\n            font-style: normal;\r\n        }\r\n    }\r\n    .ctext-wrap {\r\n        background: $gray-200;\r\n        border-radius: 3px;\r\n        display: inline-block;\r\n        padding: 12px;\r\n        position: relative;\r\n        i {\r\n            display: block;\r\n            font-size: 12px;\r\n            font-style: normal;\r\n            font-weight: $font-weight-semibold;\r\n            position: relative;\r\n        }\r\n        p {\r\n            margin: 0;\r\n            padding-top: 3px;\r\n        }\r\n        &:after {\r\n            right: 99%;\r\n            top: 0;\r\n            border: solid transparent;\r\n            content: \" \";\r\n            height: 0;\r\n            width: 0;\r\n            position: absolute;\r\n            pointer-events: none;\r\n            border-top-color: $gray-200;\r\n            border-width: 8px;\r\n            margin-left: -1px;\r\n            border-right-color: $gray-200;\r\n        }\r\n    }\r\n    .conversation-text {\r\n        float: left;\r\n        font-size: 12px;\r\n        margin-left: 12px;\r\n        width: 70%;\r\n    }\r\n    .odd {\r\n        .chat-avatar {\r\n            float: right !important;\r\n        }\r\n        .conversation-text {\r\n            float: right !important;\r\n            margin-right: 12px;\r\n            text-align: right;\r\n            width: 70% !important;\r\n        }\r\n        .ctext-wrap {\r\n            background-color: #fef5e4;\r\n            &:after {\r\n                border-color: transparent;\r\n                border-left-color: #fef5e4;\r\n                border-top-color: #fef5e4;\r\n                left: 99% !important;\r\n            }\r\n        }\r\n    }\r\n}", "// \r\n// Custom-radio.scss\r\n//\r\n\r\n.checkbox {\r\n    label {\r\n        display: inline-block;\r\n        padding-left: 8px;\r\n        position: relative;\r\n        font-weight: normal;\r\n        margin-bottom: 0;\r\n        &::before {\r\n            background-color: $white;\r\n            border-radius: 3px;\r\n            border: 2px solid $gray-600;\r\n            content: \"\";\r\n            display: inline-block;\r\n            height: 18px;\r\n            left: 0;\r\n            margin-left: -18px;\r\n            position: absolute;\r\n            transition: 0.3s ease-in-out;\r\n            width: 18px;\r\n            outline: none !important;\r\n            top: 1px;\r\n        }\r\n        &::after {\r\n            color: $gray-700;\r\n            display: inline-block;\r\n            font-size: 11px;\r\n            height: 18px;\r\n            left: 0;\r\n            margin-left: -18px;\r\n            padding-left: 3px;\r\n            padding-top: 2px;\r\n            position: absolute;\r\n            top: 0;\r\n            width: 18px;\r\n        }\r\n    }\r\n    input[type=\"checkbox\"] {\r\n        cursor: pointer;\r\n        opacity: 0;\r\n        z-index: 1;\r\n        outline: none !important;\r\n        &:disabled+label {\r\n            opacity: 0.65;\r\n        }\r\n    }\r\n    input[type=\"checkbox\"]:focus+label {\r\n        &::before {\r\n            outline-offset: -2px;\r\n            outline: none;\r\n        }\r\n    }\r\n    input[type=\"checkbox\"]:checked+label {\r\n        &::after {\r\n            content: \"\";\r\n            position: absolute;\r\n            top: 5px;\r\n            left: 7px;\r\n            display: table;\r\n            width: 4px;\r\n            height: 8px;\r\n            border: 2px solid $gray-700;\r\n            border-top-width: 0;\r\n            border-left-width: 0;\r\n            -webkit-transform: rotate(45deg);\r\n            -ms-transform: rotate(45deg);\r\n            -o-transform: rotate(45deg);\r\n            transform: rotate(45deg);\r\n        }\r\n    }\r\n    input[type=\"checkbox\"]:disabled+label {\r\n        &::before {\r\n            background-color: $light;\r\n            cursor: not-allowed;\r\n        }\r\n    }\r\n}\r\n\r\n.checkbox.checkbox-circle {\r\n    label {\r\n        &::before {\r\n            border-radius: 50%;\r\n        }\r\n    }\r\n}\r\n\r\n.checkbox.checkbox-inline {\r\n    margin-top: 0;\r\n}\r\n\r\n.checkbox.checkbox-single {\r\n    input {\r\n        height: 18px;\r\n        width: 18px;\r\n        position: absolute;\r\n    }\r\n    label {\r\n        height: 18px;\r\n        width: 18px;\r\n        &:before {\r\n            margin-left: 0;\r\n        }\r\n        &:after {\r\n            margin-left: 0;\r\n        }\r\n    }\r\n}\r\n\r\n@each $color,\r\n$value in $theme-colors {\r\n    .checkbox-#{$color} {\r\n        input[type=\"checkbox\"]:checked+label {\r\n            &::before {\r\n                background-color: $value;\r\n                border-color: $value;\r\n            }\r\n            &::after {\r\n                border-color: $white;\r\n            }\r\n        }\r\n    }\r\n}", "// \r\n// custom-radio.scss\r\n//\r\n\r\n.radio {\r\n    label {\r\n        display: inline-block;\r\n        padding-left: 8px;\r\n        position: relative;\r\n        font-weight: normal;\r\n        margin-bottom: 0;\r\n        &::before {\r\n            -o-transition: border 0.5s ease-in-out;\r\n            -webkit-transition: border 0.5s ease-in-out;\r\n            background-color: $white;\r\n            border-radius: 50%;\r\n            border: 2px solid $gray-600;\r\n            content: \"\";\r\n            display: inline-block;\r\n            height: 18px;\r\n            left: 0;\r\n            margin-left: -18px;\r\n            position: absolute;\r\n            transition: border 0.5s ease-in-out;\r\n            width: 18px;\r\n            outline: none !important;\r\n        }\r\n        &::after {\r\n            -moz-transition: -moz-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\r\n            -ms-transform: scale(0, 0);\r\n            -o-transform: scale(0, 0);\r\n            -o-transition: -o-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\r\n            -webkit-transform: scale(0, 0);\r\n            -webkit-transition: -webkit-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\r\n            background-color: $gray-700;\r\n            border-radius: 50%;\r\n            content: \" \";\r\n            display: inline-block;\r\n            height: 10px;\r\n            left: 6px;\r\n            margin-left: -20px;\r\n            position: absolute;\r\n            top: 4px;\r\n            transform: scale(0, 0);\r\n            transition: transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\r\n            width: 10px;\r\n        }\r\n    }\r\n    input[type=\"radio\"] {\r\n        cursor: pointer;\r\n        opacity: 0;\r\n        z-index: 1;\r\n        outline: none !important;\r\n        &:disabled+label {\r\n            opacity: 0.65;\r\n        }\r\n    }\r\n    input[type=\"radio\"]:focus+label {\r\n        &::before {\r\n            outline-offset: -2px;\r\n            outline: 5px auto -webkit-focus-ring-color;\r\n            outline: thin dotted;\r\n        }\r\n    }\r\n    input[type=\"radio\"]:checked+label {\r\n        &::after {\r\n            -ms-transform: scale(1, 1);\r\n            -o-transform: scale(1, 1);\r\n            -webkit-transform: scale(1, 1);\r\n            transform: scale(1, 1);\r\n        }\r\n    }\r\n    input[type=\"radio\"]:disabled+label {\r\n        &::before {\r\n            cursor: not-allowed;\r\n        }\r\n    }\r\n}\r\n\r\n.radio.radio-inline {\r\n    margin-top: 0;\r\n}\r\n\r\n.radio.radio-single {\r\n    label {\r\n        height: 17px;\r\n    }\r\n}\r\n\r\n@each $color,\r\n$value in $theme-colors {\r\n    .radio-#{$color} {\r\n        input[type=\"radio\"]+label {\r\n            &::after {\r\n                background-color: $value;\r\n            }\r\n        }\r\n        input[type=\"radio\"]:checked+label {\r\n            &::before {\r\n                border-color: $value;\r\n            }\r\n            &::after {\r\n                background-color: $value;\r\n            }\r\n        }\r\n    }\r\n}", "// \r\n// ribbons.scss\r\n//\r\n\r\n.ribbon-box {\r\n  position: relative;\r\n\r\n  .ribbon{\r\n    position: relative;\r\n    clear: both;\r\n    padding: 5px 12px;\r\n    margin-bottom: 15px;\r\n    box-shadow: 2px 5px 10px rgba($dark, 0.15);\r\n    color: $white;\r\n    font-size: 13px;\r\n    font-weight: $font-weight-semibold;\r\n\r\n    &:before{\r\n      content: \" \";\r\n      border-style: solid;\r\n      border-width: 10px;\r\n      display: block;\r\n      position: absolute;\r\n      bottom: -10px;\r\n      left: 0;\r\n      margin-bottom: -10px;\r\n      z-index: -1;\r\n    }\r\n    &.float-left {\r\n      margin-left: -30px;\r\n      border-radius: 0 3px 3px 0;\r\n    }\r\n    &.float-right {\r\n      margin-right: -30px;\r\n      border-radius: 3px 0 0 3px;\r\n\r\n      &:before{\r\n        right: 0;\r\n      }\r\n    }\r\n    &.float-center {\r\n\r\n      span {\r\n        margin: 0 auto 20px auto;\r\n      }\r\n    }\r\n  }\r\n\r\n  .ribbon-content {\r\n    clear: both;\r\n  }\r\n  \r\n  @each $color, $value in $theme-colors {\r\n    .ribbon-#{$color} {\r\n        background: ($value);\r\n\r\n        &:before {\r\n          border-color: darken(($value),10%) transparent transparent;\r\n        }\r\n    }\r\n  }\r\n\r\n\r\n  /* Ribbon two */\r\n  .ribbon-two {\r\n    position: absolute;\r\n    left: -5px;\r\n    top: -5px;\r\n    z-index: 1;\r\n    overflow: hidden;\r\n    width: 75px;\r\n    height: 75px;\r\n    text-align: right;\r\n\r\n    span {\r\n      font-size: 13px;\r\n      color: $white;\r\n      text-align: center;\r\n      line-height: 20px;\r\n      transform: rotate(-45deg);\r\n      -webkit-transform: rotate(-45deg);\r\n      width: 100px;\r\n      display: block;\r\n      box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);\r\n      position: absolute;\r\n      top: 19px;\r\n      left: -21px;\r\n      font-weight: $font-weight-semibold;\r\n\r\n      &:before {\r\n        content: \"\";\r\n        position: absolute;\r\n        left: 0;\r\n        top: 100%;\r\n        z-index: -1;\r\n        border-right: 3px solid transparent;\r\n        border-bottom: 3px solid transparent;\r\n      }\r\n\r\n      &:after {\r\n        content: \"\";\r\n        position: absolute;\r\n        right: 0;\r\n        top: 100%;\r\n        z-index: -1;\r\n        border-left: 3px solid transparent;\r\n        border-bottom: 3px solid transparent;\r\n      }\r\n    }\r\n  }\r\n\r\n  @each $color, $value in $theme-colors {\r\n    .ribbon-two-#{$color} {\r\n\r\n      span {\r\n        background: ($value);\r\n  \r\n        &:before {\r\n          border-left: 3px solid darken(($value),15%);\r\n          border-top: 3px solid darken(($value),15%);\r\n        }\r\n  \r\n        &:after {\r\n          border-right: 3px solid darken(($value),15%);\r\n          border-top: 3px solid darken(($value),15%);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n", "// \r\n// print.scss\r\n//\r\n\r\n// Used invoice page\r\n@media print {\r\n    .left-side-menu,\r\n    .right-bar,\r\n    .page-title-box,\r\n    .navbar-custom,\r\n    .footer {\r\n        display: none;\r\n    }\r\n    .card-body,\r\n    .content-page,\r\n    .right-bar,\r\n    .content,\r\n    body {\r\n        padding: 0;\r\n        margin: 0;\r\n    }\r\n}", "// \r\n// preloader.scss\r\n//\r\n\r\n#preloader {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background-color: $white;\r\n    z-index: 9999;\r\n}\r\n\r\n#status {\r\n    width: 40px;\r\n    height: 40px;\r\n    position: absolute;\r\n    left: 50%;\r\n    top: 50%;\r\n    margin: -20px 0 0 -20px;\r\n}\r\n\r\n.spinner {\r\n    margin: 0 auto;\r\n    font-size: 10px;\r\n    position: relative;\r\n    text-indent: -9999em;\r\n    border-top: 5px solid $gray-300;\r\n    border-right: 5px solid $gray-300;\r\n    border-bottom: 5px solid $gray-300;\r\n    border-left: 5px solid $primary;\r\n    transform: translateZ(0);\r\n    animation: SpinnerAnimation 1.1s infinite linear;\r\n}\r\n\r\n.spinner,\r\n.spinner:after {\r\n    border-radius: 50%;\r\n    width: 40px;\r\n    height: 40px;\r\n}\r\n\r\n@-webkit-keyframes SpinnerAnimation {\r\n    0% {\r\n        -webkit-transform: rotate(0deg);\r\n        transform: rotate(0deg);\r\n    }\r\n    100% {\r\n        -webkit-transform: rotate(360deg);\r\n        transform: rotate(360deg);\r\n    }\r\n}\r\n\r\n@keyframes SpinnerAnimation {\r\n    0% {\r\n        -webkit-transform: rotate(0deg);\r\n        transform: rotate(0deg);\r\n    }\r\n    100% {\r\n        -webkit-transform: rotate(360deg);\r\n        transform: rotate(360deg);\r\n    }\r\n}", "// \r\n// authentication.scss\r\n//\r\n\r\n.authentication-bg.enlarged {\r\n    min-height: 100px;\r\n}\r\n\r\n.bg-pattern {\r\n    background-image: url(\"../images/bg-pattern-2.png\");\r\n    background-size: cover;\r\n}\r\n\r\n// authentication pages background\r\nbody.authentication-bg {\r\n    background-color: $gray-200;\r\n    background-size: cover;\r\n    background-position: center;\r\n}\r\n\r\nbody.authentication-bg-pattern {\r\n    background-image: url(\"../images/bg-pattern.png\");\r\n    background-color: $primary;\r\n}\r\n\r\n// Logout page\r\n.logout-icon {\r\n    width: 140px;\r\n}", "// \r\n// components-demo.scss\r\n//\r\n\r\n// Demo Only\r\n.button-list {\r\n    margin-left: -8px;\r\n    margin-bottom: -12px;\r\n    \r\n    .btn {\r\n        margin-bottom: 12px;\r\n        margin-left: 8px;\r\n    }\r\n}\r\n\r\n\r\n// Icon demo ( Demo only )\r\n.icons-list-demo {\r\n    div {\r\n        cursor: pointer;\r\n        line-height: 45px;\r\n        white-space: nowrap;\r\n        text-overflow: ellipsis;\r\n        display: block;\r\n        overflow: hidden;\r\n        p {\r\n            margin-bottom: 0;\r\n            line-height: inherit;\r\n        }\r\n    }\r\n    i {\r\n        text-align: center;\r\n        vertical-align: middle;\r\n        font-size: 22px;\r\n        width: 50px;\r\n        height: 50px;\r\n        line-height: 50px;\r\n        margin-right: 12px;\r\n        color: rgba($dark, 0.7);\r\n        border-radius: 3px;\r\n        display: inline-block;\r\n        transition: all 0.2s;\r\n    }\r\n    .col-md-4 {\r\n        -webkit-border-radius: 3px;\r\n        border-radius: 3px;\r\n        -moz-border-radius: 3px;\r\n        background-clip: padding-box;\r\n        margin-bottom: 10px;\r\n        &:hover,\r\n        &:hover i {\r\n            color: $primary;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Scrollspy\r\n\r\n.scrollspy-example {\r\n    position: relative;\r\n    height: 200px;\r\n    margin-top: .5rem;\r\n    overflow: auto;\r\n}", "// \r\n// error.scss\r\n//\r\n\r\n// Error text with shadow\r\n.text-error {\r\n    color: $primary;\r\n    text-shadow: rgba($primary,0.3) 5px 1px, rgba($primary,0.2) 10px 3px;\r\n    font-size: 84px;\r\n    line-height: 90px;\r\n    font-family: $font-family-secondary;\r\n}\r\n\r\n// Animation text\r\n.error-text-box {\r\n    font-size: 10rem;\r\n    font-family: $font-family-secondary;\r\n    min-height: 200px;\r\n\r\n    .text {\r\n        fill: none;\r\n        stroke-width: 6;\r\n        stroke-linejoin: round;\r\n        stroke-dasharray: 30 100;\r\n        stroke-dashoffset: 0;\r\n        animation: stroke 9s infinite linear;\r\n    }\r\n    \r\n    .text:nth-child(5n + 1) {\r\n        stroke: $danger;\r\n        animation-delay: -1.2s;\r\n    }\r\n    \r\n    .text:nth-child(5n + 2) {\r\n        stroke: $warning;\r\n        animation-delay: -2.4s;\r\n    }\r\n    \r\n    .text:nth-child(5n + 3) {\r\n        stroke: $primary;\r\n        animation-delay: -3.6s;\r\n    }\r\n    \r\n    .text:nth-child(5n + 4) {\r\n        stroke: $info;\r\n        animation-delay: -4.8s;\r\n    }\r\n    \r\n    .text:nth-child(5n + 5) {\r\n        stroke: $success;\r\n        animation-delay: -6s;\r\n    }\r\n}\r\n\r\n@-webkit-keyframes stroke {\r\n    100% {\r\n        stroke-dashoffset: -400;\r\n    }\r\n}\r\n\r\n@keyframes stroke {\r\n    100% {\r\n        stroke-dashoffset: -400;\r\n    }\r\n}\r\n\r\n@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {\r\n    .error-text-box {\r\n        .text {\r\n            fill: $danger;\r\n            stroke: $danger;\r\n            stroke-width: 6;\r\n            stroke-dasharray: 0 0;\r\n            stroke-dashoffset: 0;\r\n            animation: none;\r\n        }\r\n    }\r\n}", "// \r\n// logout.scss\r\n//\r\n\r\n.logout-checkmark {\r\n    width: 100px;\r\n    margin: 0 auto;\r\n    padding: 20px 0;\r\n\r\n    .path {\r\n        stroke-dasharray: 1000;\r\n        stroke-dashoffset: 0;\r\n        animation: dash 2s ease-in-out;\r\n    }\r\n    \r\n    .spin {\r\n        animation: spin 2s;\r\n        transform-origin: 50% 50%;\r\n    }\r\n}\r\n\r\n@-webkit-keyframes dash {\r\n    0% {\r\n        stroke-dashoffset: 1000;\r\n    }\r\n    100% {\r\n        stroke-dashoffset: 0;\r\n    }\r\n}\r\n\r\n@keyframes dash {\r\n    0% {\r\n        stroke-dashoffset: 1000;\r\n    }\r\n    100% {\r\n        stroke-dashoffset: 0;\r\n    }\r\n}\r\n\r\n@-webkit-keyframes spin {\r\n    0% {\r\n        -webkit-transform: rotate(0deg);\r\n    }\r\n    100% {\r\n        -webkit-transform: rotate(360deg);\r\n    }\r\n}\r\n\r\n@keyframes spin {\r\n    0% {\r\n        -webkit-transform: rotate(0deg);\r\n    }\r\n    100% {\r\n        -webkit-transform: rotate(360deg);\r\n    }\r\n}\r\n\r\n@-webkit-keyframes text {\r\n    0% {\r\n        opacity: 0;\r\n    }\r\n    100% {\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n@keyframes text {\r\n    0% {\r\n        opacity: 0;\r\n    }\r\n    100% {\r\n        opacity: 1;\r\n    }\r\n}", "// \r\n// faq.scss\r\n//\r\n\r\n.faq-question-q-box {\r\n    height: 30px;\r\n    width: 30px;\r\n    color: $primary;\r\n    text-align: center;\r\n    border-radius: 50%;\r\n    float: left;\r\n    font-weight: $font-weight-bold;\r\n    line-height: 30px;\r\n    background-color: rgba($primary,0.15);\r\n}\r\n\r\n.faq-question {\r\n    margin-top: 0;\r\n    margin-left: 50px;\r\n    font-weight: 400;\r\n    font-size: 16px;\r\n}\r\n\r\n.faq-answer {\r\n    margin-left: 50px;\r\n    color: $gray-600;\r\n}", "// \r\n// maintenance.scss\r\n//\r\n\r\n.svg-computer {\r\n    stroke-dasharray: 1134;\r\n    stroke-dashoffset: -1134;\r\n    animation: draw-me 5s infinite;\r\n    animation-direction: normal;\r\n    height: 160px;\r\n}\r\n\r\n@keyframes draw-me {\r\n    from {\r\n        stroke-dashoffset: -1134;\r\n    }\r\n    to {\r\n        stroke-dashoffset: 0;\r\n    }\r\n}\r\n\r\n@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {\r\n    .svg-computer {\r\n        stroke-dasharray: 0;\r\n        stroke-dashoffset: 0;\r\n        animation: none;\r\n        animation-direction: normal;\r\n    }\r\n}", "// \r\n// timeline.scss\r\n//\r\n\r\n.timeline {\r\n    margin-bottom: 50px;\r\n    position: relative;\r\n    &:before {\r\n        background-color: $gray-300;\r\n        bottom: 0;\r\n        content: \"\";\r\n        left: 50%;\r\n        position: absolute;\r\n        top: 30px;\r\n        width: 2px;\r\n        z-index: 0;\r\n    }\r\n    .time-show {\r\n        margin-bottom: 30px;\r\n        margin-top: 30px;\r\n        position: relative;\r\n    }\r\n    .timeline-box {\r\n        background: $white;\r\n        display: block;\r\n        margin: 15px 0;\r\n        position: relative;\r\n        padding: 20px;\r\n    }\r\n    .timeline-album {\r\n        margin-top: 12px;\r\n        a {\r\n            display: inline-block;\r\n            margin-right: 5px;\r\n        }\r\n        img {\r\n            height: 36px;\r\n            width: auto;\r\n            border-radius: 3px;\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-up(md) {\r\n    .timeline {\r\n        .time-show {\r\n            margin-right: -69px;\r\n            text-align: right;\r\n        }\r\n        .timeline-box {\r\n            margin-left: 45px;\r\n        }\r\n        .timeline-icon {\r\n            background: $gray-300;\r\n            border-radius: 50%;\r\n            display: block;\r\n            height: 20px;\r\n            left: -54px;\r\n            margin-top: -10px;\r\n            position: absolute;\r\n            text-align: center;\r\n            top: 50%;\r\n            width: 20px;\r\n            i {\r\n                color: $gray-600;\r\n                font-size: 13px;\r\n                position: absolute;\r\n                left: 4px;\r\n                margin-top: 1px;\r\n            }\r\n        }\r\n        .timeline-desk {\r\n            display: table-cell;\r\n            vertical-align: top;\r\n            width: 50%;\r\n        }\r\n    }\r\n    .timeline-item {\r\n        display: table-row;\r\n        &:before {\r\n            content: \"\";\r\n            display: block;\r\n            width: 50%;\r\n        }\r\n        .timeline-desk {\r\n            .arrow {\r\n                border-bottom: 12px solid transparent;\r\n                border-right: 12px solid $white !important;\r\n                border-top: 12px solid transparent;\r\n                display: block;\r\n                height: 0;\r\n                left: -12px;\r\n                margin-top: -12px;\r\n                position: absolute;\r\n                top: 50%;\r\n                width: 0;\r\n            }\r\n        }\r\n        &.timeline-item-left {\r\n            &:after {\r\n                content: \"\";\r\n                display: block;\r\n                width: 50%;\r\n            }\r\n            .timeline-desk {\r\n                .arrow-alt {\r\n                    border-bottom: 12px solid transparent;\r\n                    border-left: 12px solid $white !important;\r\n                    border-top: 12px solid transparent;\r\n                    display: block;\r\n                    height: 0;\r\n                    left: auto;\r\n                    margin-top: -12px;\r\n                    position: absolute;\r\n                    right: -12px;\r\n                    top: 50%;\r\n                    width: 0;\r\n                }\r\n                .album {\r\n                    float: right;\r\n                    margin-top: 20px;\r\n                    a {\r\n                        float: right;\r\n                        margin-left: 5px;\r\n                    }\r\n                }\r\n            }\r\n            .timeline-icon {\r\n                left: auto;\r\n                right: -56px;\r\n            }\r\n            &:before {\r\n                display: none;\r\n            }\r\n            .timeline-box {\r\n                margin-right: 45px;\r\n                margin-left: 0;\r\n                text-align: right;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n    .timeline {\r\n        .time-show {\r\n            text-align: center;\r\n            position: relative;\r\n        }\r\n        .timeline-icon {\r\n            display: none;\r\n        }\r\n    }\r\n}\r\n\r\n// timeline small\r\n.timeline-sm {\r\n    padding-left: 110px;\r\n    .timeline-sm-item {\r\n        position: relative;\r\n        padding-bottom: 20px;\r\n        padding-left: 40px;\r\n        border-left: 2px solid $gray-300;\r\n        &:after {\r\n            content: \"\";\r\n            display: block;\r\n            position: absolute;\r\n            top: 3px;\r\n            left: -7px;\r\n            width: 12px;\r\n            height: 12px;\r\n            border-radius: 50%;\r\n            background: $white;\r\n            border: 2px solid $primary;\r\n        }\r\n        .timeline-sm-date {\r\n            position: absolute;\r\n            left: -104px;\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 420px) {\r\n    .timeline-sm {\r\n        padding-left: 0px;\r\n        .timeline-sm-date {\r\n            position: relative !important;\r\n            display: block;\r\n            left: 0px !important;\r\n            margin-bottom: 10px;\r\n        }\r\n    }\r\n}  ", "// \r\n// email.scss\r\n//\r\n\r\n.inbox-leftbar {\r\n    width: 240px;\r\n    float: left;\r\n    padding: 0 20px 20px 10px;\r\n}\r\n\r\n.inbox-rightbar {\r\n    margin: -1.5rem 0 -1.5rem 250px;\r\n    border-left: 5px solid $body-bg;\r\n    padding: 1.5rem 0 1.5rem 25px;\r\n}\r\n\r\n.message-list {\r\n    display: block;\r\n    padding-left: 0;\r\n    li {\r\n        position: relative;\r\n        display: block;\r\n        height: 51px;\r\n        line-height: 50px;\r\n        cursor: default;\r\n        transition-duration: .3s;\r\n        a {\r\n            color: $gray-700;\r\n        }\r\n        &:hover {\r\n            background: $gray-100;\r\n            transition-duration: .05s;\r\n        }\r\n        .col-mail {\r\n            float: left;\r\n            position: relative;\r\n        }\r\n        .col-mail-1 {\r\n            width: 320px;\r\n            .star-toggle,\r\n            .checkbox-wrapper-mail,\r\n            .dot {\r\n                display: block;\r\n                float: left;\r\n            }\r\n            .dot {\r\n                border: 4px solid transparent;\r\n                border-radius: 100px;\r\n                margin: 22px 26px 0;\r\n                height: 0;\r\n                width: 0;\r\n                line-height: 0;\r\n                font-size: 0;\r\n            }\r\n            .checkbox-wrapper-mail {\r\n                margin: 15px 10px 0 20px;\r\n            }\r\n            .star-toggle {\r\n                margin-top: 18px;\r\n                color: $gray-500;\r\n                margin-left: 10px;\r\n            }\r\n            .title {\r\n                position: absolute;\r\n                top: 0;\r\n                left: 100px;\r\n                right: 0;\r\n                text-overflow: ellipsis;\r\n                overflow: hidden;\r\n                white-space: nowrap;\r\n                margin-bottom: 0;\r\n                line-height: 50px;\r\n            }\r\n        }\r\n        .col-mail-2 {\r\n            position: absolute;\r\n            top: 0;\r\n            left: 320px;\r\n            right: 0;\r\n            bottom: 0;\r\n            .subject,\r\n            .date {\r\n                position: absolute;\r\n                top: 0;\r\n            }\r\n            .subject {\r\n                left: 0;\r\n                right: 110px;\r\n                text-overflow: ellipsis;\r\n                overflow: hidden;\r\n                white-space: nowrap;\r\n            }\r\n            .date {\r\n                right: 0;\r\n                width: 100px;\r\n                padding-left: 10px;\r\n            }\r\n        }\r\n    }\r\n    li.active,\r\n    li.mail-selected {\r\n        background: $gray-100;\r\n        transition-duration: .05s;\r\n    }\r\n    li.active,\r\n    li.active:hover {\r\n        box-shadow: inset 3px 0 0 $cyan;\r\n    }\r\n    li.unread a {\r\n        font-weight: $font-weight-semibold;\r\n        color: darken($dark, 5%);\r\n    }\r\n    \r\n    .checkbox-wrapper-mail {\r\n        cursor: pointer;\r\n        height: 20px;\r\n        width: 20px;\r\n        position: relative;\r\n        display: inline-block;\r\n        box-shadow: inset 0 0 0 2px $gray-400;\r\n        border-radius: 3px;\r\n        input {\r\n            opacity: 0;\r\n            cursor: pointer;\r\n        }\r\n        input:checked~label {\r\n            opacity: 1;\r\n        }\r\n        label {\r\n            position: absolute;\r\n            top: 3px;\r\n            left: 3px;\r\n            right: 3px;\r\n            bottom: 3px;\r\n            cursor: pointer;\r\n            background: $gray-600;\r\n            opacity: 0;\r\n            margin-bottom: 0 !important;\r\n            transition-duration: .05s;\r\n        }\r\n        label:active {\r\n            background: #87949b;\r\n        }\r\n    }\r\n}\r\n\r\n.mail-list {\r\n    a {\r\n        color: $gray-700;\r\n        padding: 7px 10px;\r\n        display: block;\r\n    }\r\n}\r\n\r\n.reply-box {\r\n    border: 2px solid $light;\r\n}\r\n\r\n@media (max-width: 648px) {\r\n    .inbox-leftbar {\r\n        width: 100%;\r\n        float: none;\r\n    }\r\n    .inbox-rightbar {\r\n        margin-left: 0;\r\n        border: 0;\r\n        padding-left: 0;\r\n    }\r\n    .message-list {\r\n        li .col-mail-1  {\r\n            .checkbox-wrapper-mail {\r\n                margin-left: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 520px) {\r\n    .inbox-rightbar {\r\n        > .btn-group {\r\n            margin-bottom: 10px;\r\n        }\r\n    }\r\n    .message-list li {\r\n        .col-mail-1 {\r\n            width: 150px;\r\n            .title {\r\n                left: 80px;\r\n            }\r\n        }\r\n        .col-mail-2 {\r\n            left: 160px;\r\n            .date {\r\n                text-align: right;\r\n                padding-right: 10px;\r\n                padding-left: 20px;\r\n            }\r\n        }\r\n    }\r\n}", "// \r\n// sitemap.scss\r\n//\r\n\r\n.sitemap {\r\n    list-style: none;\r\n    padding-left: 0;\r\n\r\n    > li {\r\n        > ul {\r\n            margin-top: 1rem;\r\n            padding-left: 0;\r\n        }\r\n    }\r\n\r\n    li {\r\n        line-height: 2rem;\r\n        vertical-align: top;\r\n        list-style: none;\r\n        position: relative;\r\n\r\n        a {\r\n            text-decoration: none;\r\n            color: $gray-700;\r\n            display: block;\r\n            white-space: nowrap;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n\r\n            &:hover {\r\n                color: $primary;\r\n            }\r\n        }   \r\n    }\r\n    ul {\r\n        margin-left: 1rem;\r\n        margin-bottom: 1rem;\r\n        padding-top: 10px;\r\n\r\n        li {\r\n            position: relative;\r\n\r\n            a {\r\n                margin-left: 2.75rem;\r\n            }\r\n\r\n            &:before {\r\n                content: \"\";\r\n                display: inline-block;\r\n                width: 2rem;\r\n                height: 2rem;\r\n                border-bottom: 1px #ccc solid;\r\n                border-left: 1px solid rgba(152, 166, 173, 0.5);\r\n                position: absolute;\r\n                top: -1rem;\r\n            }\r\n        }\r\n    }\r\n}", "// \r\n// search-results.scss\r\n//\r\n\r\n.search-result-box {\r\n    .tab-content {\r\n        padding: 30px 30px 10px 30px;\r\n        -webkit-box-shadow: none;\r\n        box-shadow: none;\r\n        -moz-box-shadow: none;\r\n    }\r\n    .search-item {\r\n        padding-bottom: 20px;\r\n        border-bottom: 1px solid $light;\r\n        margin-bottom: 20px;\r\n    }\r\n\r\n    .nav-bordered {\r\n        .nav-link {\r\n            padding: 10px 5px!important;\r\n            margin-right: 10px;\r\n        }\r\n    }\r\n}", "// \r\n// pricing.scss\r\n//\r\n\r\n.card-pricing {\r\n    position: relative;\r\n    \r\n    .card-pricing-plan-name {\r\n        padding-bottom: 20px;\r\n    }\r\n\r\n    .card-pricing-icon {\r\n        font-size: 22px;\r\n        background-color: rgba($primary,0.1);\r\n        height: 60px;\r\n        display: inline-block;\r\n        width: 60px;\r\n        line-height: 62px;\r\n        border-radius: 50%;\r\n    }\r\n\r\n    .card-pricing-price {\r\n        padding: 30px 0 0;\r\n\r\n        span {\r\n            font-size: 40%;\r\n            color: $gray-600;\r\n            letter-spacing: 2px;\r\n            text-transform: uppercase;\r\n        }\r\n    }\r\n    .card-pricing-features {\r\n        color: $gray-600;\r\n        list-style: none;\r\n        margin: 0;\r\n        padding: 20px 0 0 0;\r\n\r\n        li {\r\n            padding: 10px;\r\n        }\r\n    }\r\n}\r\n\r\n.card-pricing-recommended {\r\n    background-color: $primary;\r\n    color: $white;\r\n\r\n    .card-pricing-icon {\r\n        background-color: rgba($white,0.1);\r\n    }\r\n    .card-pricing-features,.card-pricing-price span {\r\n        color: $gray-300;\r\n    }\r\n}", "// \r\n// gallery.scss\r\n//\r\n\r\n.filter-menu {\r\n    margin-bottom: 20px;\r\n\r\n    a {\r\n        transition: all 0.3s ease-out;\r\n        color: $dark;\r\n        border-radius: 3px;\r\n        padding: 5px 10px;\r\n        display: inline-block;\r\n        margin-bottom: 5px;\r\n        font-weight: $font-weight-medium;\r\n        font-family: $font-family-secondary;\r\n        &:hover {\r\n            background-color: $primary;\r\n            color: $white;\r\n        }\r\n    }\r\n    a.active {\r\n        background-color: $primary;\r\n        color: $white;\r\n    }\r\n}\r\n\r\n// Gallary Thumb\r\n.gal-box {\r\n    background-color: $white;\r\n    border-radius: 3px;\r\n    box-shadow: $shadow;\r\n    margin-bottom: $grid-gutter-width;\r\n\r\n    .image-popup {\r\n        padding: 10px;\r\n        display: block;\r\n\r\n        img {\r\n            cursor: zoom-in;\r\n        }\r\n    }\r\n\r\n    .gall-info {\r\n        padding: 15px;\r\n        border-top: 1px solid $gray-200;\r\n        position: relative;\r\n\r\n        h4 {\r\n            display: block;\r\n            overflow: hidden;\r\n            white-space: nowrap;\r\n            text-overflow: ellipsis;\r\n        }\r\n\r\n        .gal-like-btn {\r\n            position: absolute;\r\n            right: 15px;\r\n            font-size: 22px;\r\n            top: 24px;\r\n        }\r\n    }\r\n}", "// \r\n// coming-soon.scss\r\n//\r\n\r\n.counter-number {\r\n    font-size: 32px;\r\n    font-weight: $font-weight-semibold;\r\n    span {\r\n        font-size: 15px;\r\n        font-weight: $font-weight-normal;\r\n        display: block;\r\n    }\r\n}\r\n\r\n.coming-box {\r\n    float: left;\r\n    width: 25%;\r\n}\r\n\r\n.svg-rocket {\r\n    height: 80px;\r\n}\r\n\r\n.rocket-clouds__bubble,\r\n.rocket-clouds__cloud,\r\n.rocket-rocket,\r\n.rocket-inner__rocket-and-lines {\r\n    fill: $primary;\r\n}", "// \r\n// profile.scss\r\n//\r\n\r\n.post-user-comment-box {\r\n    background-color: $gray-100;\r\n    margin: 0 -.75rem;\r\n    padding: 1rem;\r\n    margin-top: 20px;\r\n}", "// \r\n// taskboard.scss\r\n//\r\n\r\n.taskList {\r\n    min-height: 40px;\r\n    margin-bottom: 0;\r\n  }\r\n  \r\n  .taskList li {\r\n    background-color: $white;\r\n    border: 1px solid $gray-300;\r\n    padding: 20px;\r\n    margin-bottom: 15px;\r\n    border-radius: 3px;\r\n  }\r\n  \r\n  .taskList li:last-of-type {\r\n    margin-bottom: 0;\r\n  }\r\n  \r\n  .taskList li .btn-sm {\r\n    padding: 2px 8px;\r\n    font-size: 12px;\r\n  }\r\n  \r\n  .taskList .checkbox {\r\n    margin-left: 20px;\r\n    margin-top: 5px;\r\n  }\r\n  \r\n  .task-placeholder {\r\n    border: 1px dashed $gray-300 !important;\r\n    background-color: $gray-100 !important;\r\n    padding: 20px;\r\n  }", "// \r\n// ecommerce.scss\r\n//\r\n\r\n.product-box {\r\n    position: relative;\r\n    overflow: hidden;\r\n\r\n    .product-action {\r\n        position: absolute;\r\n        right: 0;\r\n        top: 0;\r\n        padding: $card-spacer-x $card-spacer-x 0 $card-spacer-x;\r\n        z-index: 3;\r\n        opacity: 0;\r\n        visibility: hidden;\r\n        transform: translateX(100%);\r\n        transition: all 0.3s ease 0s;\r\n    }\r\n\r\n    &:hover {\r\n        .product-action {\r\n            opacity: 1;\r\n            visibility: visible;\r\n            transform: translateX(0);\r\n        }\r\n    }\r\n\r\n    .product-info {\r\n        padding-top: $card-spacer-x;\r\n    }\r\n\r\n    .product-price-tag {\r\n        height: 48px;\r\n        line-height: 48px;\r\n        font-weight: 700;\r\n        font-size: 20px;\r\n        background-color: $gray-100;\r\n        text-align: center;\r\n        padding: 0 10px;\r\n        border-radius: 3px;\r\n    }\r\n}\r\n\r\n// Product detail page\r\n.product-thumb {\r\n    padding: 3px;\r\n    margin-top: 3px;\r\n\r\n    &.active {\r\n        background-color: $gray-700 !important;\r\n    }\r\n}", "/*!\n* metismenu - v2.7.9\n* A jQuery menu plugin\n* https://github.com/onokumus/metismenu#readme\n*\n* Made by <PERSON><PERSON> <<EMAIL>> (https://github.com/onokumus)\n* Under MIT License\n*/\n.metismenu .arrow {\n  float: right;\n  line-height: 1.42857;\n}\n*[dir=\"rtl\"] .metismenu .arrow {\n  float: left;\n}\n\n/*\n * Require Bootstrap 3.x\n * https://github.com/twbs/bootstrap\n*/\n\n.metismenu .glyphicon.arrow:before {\n  content: \"\\e079\";\n}\n.metismenu .active > a > .glyphicon.arrow:before {\n  content: \"\\e114\";\n}\n\n/*\n * Require Font-Awesome\n * http://fortawesome.github.io/Font-Awesome/\n*/\n\n.metismenu .fa.arrow:before {\n  content: \"\\f104\";\n}\n.metismenu .active > a > .fa.arrow:before {\n  content: \"\\f107\";\n}\n\n/*\n * Require Ionicons\n * http://ionicons.com/\n*/\n\n.metismenu .ion.arrow:before {\n  content: \"\\f3d2\"\n}\n.metismenu .active > a > .ion.arrow:before {\n  content: \"\\f3d0\";\n}\n.metismenu .plus-times {\n  float: right;\n}\n*[dir=\"rtl\"] .metismenu .plus-times {\n  float: left;\n}\n.metismenu .fa.plus-times:before {\n  content: \"\\f067\";\n}\n.metismenu .active > a > .fa.plus-times {\n  -webkit-transform: rotate(45deg);\n  transform: rotate(45deg);\n}\n.metismenu .plus-minus {\n  float: right;\n}\n*[dir=\"rtl\"] .metismenu .plus-minus {\n  float: left;\n}\n.metismenu .fa.plus-minus:before {\n  content: \"\\f067\";\n}\n.metismenu .active > a > .fa.plus-minus:before {\n  content: \"\\f068\";\n}\n.metismenu .collapse {\n  display: none;\n}\n.metismenu .collapse.in {\n  display: block;\n}\n.metismenu .collapsing {\n  position: relative;\n  height: 0;\n  overflow: hidden;\n  transition-timing-function: ease;\n  transition-duration: .35s;\n  transition-property: height, visibility;\n}\n\n.metismenu .has-arrow {\n  position: relative;\n}\n\n.metismenu .has-arrow::after {\n  position: absolute;\n  content: '';\n  width: .5em;\n  height: .5em;\n  border-width: 1px 0 0 1px;\n  border-style: solid;\n  border-color: initial;\n  right: 1em;\n  -webkit-transform: rotate(-45deg) translate(0, -50%);\n  transform: rotate(-45deg) translate(0, -50%);\n  -webkit-transform-origin: top;\n  transform-origin: top;\n  top: 50%;\n  transition: all .3s ease-out;\n}\n\n*[dir=\"rtl\"] .metismenu .has-arrow::after {\n  right: auto;\n  left: 1em;\n  -webkit-transform: rotate(135deg) translate(0, -50%);\n  transform: rotate(135deg) translate(0, -50%);\n}\n\n.metismenu .active > .has-arrow::after,\n.metismenu .has-arrow[aria-expanded=\"true\"]::after {\n  -webkit-transform: rotate(-135deg) translate(0, -50%);\n  transform: rotate(-135deg) translate(0, -50%);\n}\n\n*[dir=\"rtl\"] .metismenu .active > .has-arrow::after,\n*[dir=\"rtl\"] .metismenu .has-arrow[aria-expanded=\"true\"]::after {\n  -webkit-transform: rotate(225deg) translate(0, -50%);\n  transform: rotate(225deg) translate(0, -50%);\n}\n\n/*# sourceMappingURL=metisMenu.css.map */", "/*!\r\n * Waves v0.7.6\r\n * http://fian.my.id/Waves \r\n * \r\n * Copyright 2014-2018 <PERSON><PERSON><PERSON> Si<PERSON> and other contributors \r\n * Released under the MIT license \r\n * https://github.com/fians/Waves/blob/master/LICENSE */\r\n .waves-effect {\r\n  position: relative;\r\n  cursor: pointer;\r\n  display: inline-block;\r\n  overflow: hidden;\r\n  -webkit-user-select: none;\r\n  -moz-user-select: none;\r\n  -ms-user-select: none;\r\n  user-select: none;\r\n  -webkit-tap-highlight-color: transparent;\r\n}\r\n.waves-effect .waves-ripple {\r\n  position: absolute;\r\n  border-radius: 50%;\r\n  width: 100px;\r\n  height: 100px;\r\n  margin-top: -50px;\r\n  margin-left: -50px;\r\n  opacity: 0;\r\n  background: rgba(0, 0, 0, 0.2);\r\n  background: -webkit-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  background: -o-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  background: -moz-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  -webkit-transition: all 0.5s ease-out;\r\n  -moz-transition: all 0.5s ease-out;\r\n  -o-transition: all 0.5s ease-out;\r\n  transition: all 0.5s ease-out;\r\n  -webkit-transition-property: -webkit-transform, opacity;\r\n  -moz-transition-property: -moz-transform, opacity;\r\n  -o-transition-property: -o-transform, opacity;\r\n  transition-property: transform, opacity;\r\n  -webkit-transform: scale(0) translate(0, 0);\r\n  -moz-transform: scale(0) translate(0, 0);\r\n  -ms-transform: scale(0) translate(0, 0);\r\n  -o-transform: scale(0) translate(0, 0);\r\n  transform: scale(0) translate(0, 0);\r\n  pointer-events: none;\r\n}\r\n.waves-effect.waves-light .waves-ripple {\r\n  background: rgba(255, 255, 255, 0.4);\r\n  background: -webkit-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  background: -o-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  background: -moz-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n}\r\n.waves-effect.waves-classic .waves-ripple {\r\n  background: rgba(0, 0, 0, 0.2);\r\n}\r\n.waves-effect.waves-classic.waves-light .waves-ripple {\r\n  background: rgba(255, 255, 255, 0.4);\r\n}\r\n.waves-notransition {\r\n  -webkit-transition: none !important;\r\n  -moz-transition: none !important;\r\n  -o-transition: none !important;\r\n  transition: none !important;\r\n}\r\n.waves-button,\r\n.waves-circle {\r\n  -webkit-transform: translateZ(0);\r\n  -moz-transform: translateZ(0);\r\n  -ms-transform: translateZ(0);\r\n  -o-transform: translateZ(0);\r\n  transform: translateZ(0);\r\n  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);\r\n}\r\n.waves-button,\r\n.waves-button:hover,\r\n.waves-button:visited,\r\n.waves-button-input {\r\n  white-space: nowrap;\r\n  vertical-align: middle;\r\n  cursor: pointer;\r\n  border: none;\r\n  outline: none;\r\n  color: inherit;\r\n  background-color: rgba(0, 0, 0, 0);\r\n  font-size: 1em;\r\n  line-height: 1em;\r\n  text-align: center;\r\n  text-decoration: none;\r\n  z-index: 1;\r\n}\r\n.waves-button {\r\n  padding: 0.85em 1.1em;\r\n  border-radius: 0.2em;\r\n}\r\n.waves-button-input {\r\n  margin: 0;\r\n  padding: 0.85em 1.1em;\r\n}\r\n.waves-input-wrapper {\r\n  border-radius: 0.2em;\r\n  vertical-align: bottom;\r\n}\r\n.waves-input-wrapper.waves-button {\r\n  padding: 0;\r\n}\r\n.waves-input-wrapper .waves-button-input {\r\n  position: relative;\r\n  top: 0;\r\n  left: 0;\r\n  z-index: 1;\r\n}\r\n.waves-circle {\r\n  text-align: center;\r\n  width: 2.5em;\r\n  height: 2.5em;\r\n  line-height: 2.5em;\r\n  border-radius: 50%;\r\n}\r\n.waves-float {\r\n  -webkit-mask-image: none;\r\n  -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\r\n  box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\r\n  -webkit-transition: all 300ms;\r\n  -moz-transition: all 300ms;\r\n  -o-transition: all 300ms;\r\n  transition: all 300ms;\r\n}\r\n.waves-float:active {\r\n  -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\r\n  box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\r\n}\r\n.waves-block {\r\n  display: block;\r\n}\r\n", "//\r\n// ion-rangeslider.scss\r\n//\r\n.irs--flat,\r\n.irs--modern {\r\n    .irs-bar,\r\n    .irs-from,\r\n    .irs-to,\r\n    .irs-single {\r\n        background-color: $primary;\r\n    }\r\n    .irs-handle {\r\n        &.state_hover>i:first-child,\r\n        &:hover>i:first-child,\r\n        &>i:first-child {\r\n            background-color: darken($primary, 5%);\r\n        }\r\n    }\r\n    .irs-from,\r\n    .irs-to,\r\n    .irs-single {\r\n        &:before {\r\n            border-top-color: $primary;\r\n        }\r\n    }\r\n}\r\n\r\n.irs--modern {\r\n    .irs-line {\r\n        border: none;\r\n    }\r\n    .irs-bar {\r\n        background: $primary;\r\n        background: linear-gradient(to bottom, $primary 0%, darken($primary, 10%) 100%);\r\n    }\r\n}\r\n\r\n.irs--sharp {\r\n    .irs-from,\r\n    .irs-to,\r\n    .irs-single,\r\n    .irs-min,\r\n    .irs-max,\r\n    .irs-handle,\r\n    .irs-bar {\r\n        background-color: $primary;\r\n    }\r\n    .irs-line {\r\n        background-color: $gray-300;\r\n    }\r\n    .irs-from:before,\r\n    .irs-to:before,\r\n    .irs-single:before,\r\n    .irs-handle>i:first-child {\r\n        border-top-color: $primary;\r\n    }\r\n    .irs-handle.state_hover,\r\n    .irs-handle:hover\r\n        {\r\n        background-color: darken($primary, 15%);\r\n    }\r\n    .irs-handle.state_hover>i:first-child,\r\n    .irs-handle:hover>i:first-child {\r\n        border-top-color: darken($primary, 15%);\r\n    }\r\n}\r\n\r\n.irs--round {\r\n    .irs-from,\r\n    .irs-to,\r\n    .irs-single,\r\n    .irs-bar {\r\n        background-color: $primary;\r\n        &:before {\r\n            border-top-color: $primary;\r\n        }\r\n    }\r\n    .irs-handle {\r\n        border: 4px solid $primary;\r\n        box-shadow: 0 1px 3px rgba($primary, 0.3);\r\n    }\r\n    .irs-min,\r\n    .irs-max {\r\n        color: $gray-700;\r\n        background-color: $gray-300;\r\n    }\r\n}\r\n\r\n.irs--square {\r\n    .irs-from,\r\n    .irs-to,\r\n    .irs-single,\r\n    .irs-bar {\r\n        background-color: $gray-800;\r\n    }\r\n    .irs-handle {\r\n        border: 3px solid $gray-800;\r\n    }\r\n}", "// \r\n// calendar.scss\r\n//\r\n\r\n.calendar {\r\n    float: left;\r\n    margin-bottom: 0;\r\n}\r\n\r\n.fc-view {\r\n    margin-top: 30px;\r\n}\r\n\r\n.none-border {\r\n    .modal-footer {\r\n        border-top: none;\r\n    }\r\n}\r\n\r\n.fc-toolbar {\r\n    margin: 15px 0 5px 0;\r\n    h2 {\r\n        font-size: 1.25rem;\r\n        line-height: 1.875rem;\r\n        text-transform: uppercase;\r\n    }\r\n}\r\n\r\n.fc-day-grid-event {\r\n    .fc-time {\r\n        font-weight: $font-weight-bold;\r\n    }\r\n}\r\n\r\n.fc-day {\r\n    background: $white;\r\n}\r\n\r\n.fc-toolbar {\r\n    .fc-state-active,\r\n    .ui-state-active,\r\n    button:focus,\r\n    button:hover,\r\n    .ui-state-hover {\r\n        z-index: 0;\r\n    }\r\n}\r\n\r\n.fc {\r\n    th.fc-widget-header {\r\n        background: $gray-100;\r\n        font-size: 13px;\r\n        line-height: 20px;\r\n        padding: 10px 0;\r\n        text-transform: uppercase;\r\n    }\r\n}\r\n\r\n.fc-unthemed {\r\n    th,\r\n    td,\r\n    thead,\r\n    tbody,\r\n    .fc-divider,\r\n    .fc-row,\r\n    .fc-popover {\r\n        border-color: $gray-300;\r\n    }\r\n}\r\n\r\n.fc-ltr {\r\n    .fc-basic-view {\r\n        .fc-day-top {\r\n            .fc-day-number {\r\n                float: right;\r\n                height: 20px;\r\n                width: 20px;\r\n                text-align: center;\r\n                line-height: 20px;\r\n                background-color: $gray-100;\r\n                border-radius: 50%;\r\n                margin: 5px;\r\n                font-family: $font-family-secondary;\r\n                font-size: 12px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.fc-button {\r\n    background: $gray-100;\r\n    border: none;\r\n    color: $gray-700;\r\n    text-transform: capitalize;\r\n    box-shadow: none;\r\n    border-radius: 3px;\r\n    margin: 0 3px;\r\n    padding: 6px 12px;\r\n    height: auto;\r\n}\r\n\r\n.fc-text-arrow {\r\n    font-family: inherit;\r\n    font-size: 1rem;\r\n}\r\n\r\n.fc-state-hover {\r\n    background: $gray-100;\r\n}\r\n\r\n.fc-state-highlight {\r\n    background: $gray-300;\r\n}\r\n\r\n.fc-state-down,\r\n.fc-state-active,\r\n.fc-state-disabled {\r\n    background-color: $primary;\r\n    color: $white;\r\n    text-shadow: none;\r\n}\r\n\r\n.fc-cell-overlay {\r\n    background: $gray-300;\r\n}\r\n\r\n.fc-unthemed {\r\n    .fc-today {\r\n        background: $white;\r\n    }\r\n}\r\n\r\n.fc-event {\r\n    border-radius: 2px;\r\n    border: none;\r\n    cursor: move;\r\n    font-size: 0.8125rem;\r\n    margin: 5px 7px;\r\n    padding: 5px 5px;\r\n    text-align: center;\r\n}\r\n\r\n.external-event {\r\n    cursor: move;\r\n    margin: 10px 0;\r\n    padding: 8px 10px;\r\n    color: $white;\r\n}\r\n\r\n.fc-basic-view {\r\n    td.fc-week-number {\r\n        span {\r\n            padding-right: 8px;\r\n        }\r\n    }\r\n    td.fc-day-number {\r\n        padding-right: 8px;\r\n    }\r\n    .fc-content {\r\n        color: $white;\r\n    }\r\n}\r\n\r\n.fc-time-grid-event {\r\n    .fc-content {\r\n        color: $white;\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n    .fc-toolbar {\r\n        .fc-left,.fc-right,.fc-center {\r\n            float: none;\r\n            display: block;\r\n            clear: both;\r\n            margin: 10px 0;\r\n        }\r\n    }\r\n    .fc {\r\n        .fc-toolbar{\r\n            >* {\r\n                >* {\r\n                    float: none;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .fc-today-button {\r\n        display: none;\r\n    }\r\n}", "//\r\n// sparkline.scss\r\n//\r\n\r\n.jqstooltip {\r\n  box-sizing: content-box;\r\n  width: auto !important;\r\n  height: auto !important;\r\n  background-color: $white !important;\r\n  box-shadow: $shadow-lg;\r\n  padding: 5px 10px !important;\r\n  border-radius: 3px;\r\n  border-color: $white !important;\r\n}\r\n\r\n.jqsfield {\r\n  color: $dark !important;\r\n  font-size: 12px !important;\r\n  line-height: 18px !important;\r\n  font-family: $font-family-base !important;\r\n  font-weight: $font-weight-semibold !important;\r\n}", "//\r\n// datatable.scss\r\n//\r\n\r\n.dataTables_wrapper.container-fluid {\r\n    padding: 0;\r\n}\r\n\r\ntable.dataTable {\r\n    border-collapse: collapse !important;\r\n    margin-bottom: 15px !important;\r\n\r\n    tbody {\r\n        // Multi select table\r\n\r\n        > tr.selected, >tr>.selected {\r\n            background-color: $primary;\r\n            \r\n            td {\r\n                border-color: $primary;\r\n            }\r\n        }\r\n        td {\r\n            &:focus {\r\n                outline: none !important;\r\n            }\r\n        }\r\n        // Key Tables\r\n        th.focus,td.focus{\r\n            outline: 2px solid $primary !important;\r\n            outline-offset: -1px;\r\n            color: $primary;\r\n            background-color: rgba($primary, 0.15);\r\n        }\r\n    }\r\n}\r\n\r\n.dataTables_info {\r\n    font-weight: $font-weight-semibold;\r\n}\r\n\r\n\r\n// Responsive data table\r\ntable.dataTable.dtr-inline.collapsed {\r\n    > tbody {\r\n        >tr[role=row] {\r\n            > td, > th {\r\n                &:first-child{\r\n                    &:before{\r\n                        box-shadow: $shadow-lg;\r\n                        background-color: $success;\r\n                        top: $table-cell-padding;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        >tr.parent {\r\n            > td, > th {\r\n                &:first-child{\r\n                    &:before{\r\n                        background-color: $danger;\r\n                        top: $table-cell-padding;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Data Table copy button\r\ndiv.dt-button-info {\r\n    background-color: $primary;\r\n    border: none;\r\n    color: $white;\r\n    box-shadow: none;\r\n    border-radius: 3px;\r\n    text-align: center;\r\n    z-index: 21;\r\n\r\n    h2 {\r\n        border-bottom: none;\r\n        background-color: rgba($white, 0.2);\r\n        color: $white;\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n    li.paginate_button.previous,li.paginate_button.next {\r\n        display: inline-block;\r\n        font-size: 1.5rem;\r\n    }\r\n \r\n    li.paginate_button {\r\n        display: none;\r\n    }\r\n    .dataTables_paginate {\r\n        ul {\r\n            text-align: center;\r\n            display: block;\r\n            margin: $spacer 0 0 !important;\r\n        }\r\n    }\r\n    div.dt-buttons {\r\n        display: inline-table;\r\n        margin-bottom: $spacer;\r\n    }\r\n}\r\n\r\n.activate-select {\r\n    .sorting_1 {\r\n        background-color: $gray-100;\r\n    }\r\n}", "//\r\n// Daterange.scss\r\n//\r\n\r\n.daterangepicker {\r\n    font-family: $font-family-base;\r\n\r\n    td.active,td.active:hover,.ranges li.active {\r\n        background-color: $primary;\r\n    }\r\n}", "//\r\n// Datepicker\r\n//\r\n.datepicker {\r\n    padding: 10px !important;\r\n    td,\r\n    th {\r\n        width: 30px;\r\n        height: 30px;\r\n        border-radius: 50%;\r\n    }\r\n\r\n    table {\r\n        tr {\r\n            td {\r\n                &.active.active,\r\n                &.active.disabled,\r\n                &.active.disabled.active,\r\n                &.active.disabled.disabled,\r\n                &.active.disabled:active,\r\n                &.active.disabled:hover,\r\n                &.active.disabled:hover.active,\r\n                &.active.disabled:hover.disabled,\r\n                &.active.disabled:hover:active,\r\n                &.active.disabled:hover:hover,\r\n                .active.disabled:hover[disabled],\r\n                .active.disabled[disabled],\r\n                .active:active,\r\n                .active:hover,\r\n                .active:hover.active,\r\n                .active:hover.disabled,\r\n                .active:hover:active,\r\n                .active:hover:hover,\r\n                .active:hover[disabled],\r\n                .active[disabled],\r\n                span.active.active,\r\n                span.active.disabled,\r\n                span.active.disabled.active,\r\n                span.active.disabled.disabled,\r\n                span.active.disabled:active,\r\n                span.active.disabled:hover,\r\n                span.active.disabled:hover.active,\r\n                span.active.disabled:hover.disabled,\r\n                span.active.disabled:hover:active,\r\n                span.active.disabled:hover:hover,\r\n                span.active.disabled:hover[disabled],\r\n                span.active.disabled[disabled],\r\n                span.active:active,\r\n                span.active:hover,\r\n                span.active:hover.active,\r\n                span.active:hover.disabled,\r\n                span.active:hover:active,\r\n                span.active:hover:hover,\r\n                span.active:hover[disabled],\r\n                span.active[disabled],\r\n                &.today,\r\n                &.today.disabled,\r\n                &.today.disabled:hover,\r\n                &.today:hover {\r\n                    background-color: $primary !important;\r\n                    background-image: none !important;\r\n                    color: $white;\r\n                }\r\n\r\n                &.day.focused,\r\n                &.day:hover,\r\n                span.focused,\r\n                span:hover {\r\n                    background: $gray-200;\r\n                }\r\n\r\n                &.new,\r\n                &.old,\r\n                span.new,\r\n                span.old {\r\n                    color: $gray-700;\r\n                    opacity: 0.4;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    \r\n    .datepicker-switch:hover,\r\n    .next:hover,\r\n    .prev:hover,\r\n    tfoot tr th:hover {\r\n        background: $gray-200;\r\n    }\r\n    .datepicker-switch {\r\n        &:hover {\r\n            background: none;\r\n        }\r\n    }\r\n}\r\n\r\n.datepicker-dropdown {\r\n\r\n    &:after {\r\n        border-bottom: 6px solid $dropdown-bg;\r\n    }\r\n    &:before {\r\n        border-bottom-color: $dropdown-border-color;\r\n    }\r\n    &.datepicker-orient-top {\r\n        &:before {\r\n            border-top: 7px solid $dropdown-border-color;\r\n        }\r\n        &:after {\r\n            border-top: 6px solid $dropdown-bg;\r\n        }\r\n    }\r\n}", "//\r\n// formm-wizard.scss\r\n//\r\n\r\n.form-wizard-header {\r\n    margin-left: -1.5rem;\r\n    margin-right: -1.5rem;\r\n}", "//\r\n// Select2.scss\r\n//\r\n\r\n.select2-container {\r\n    .select2-selection--single {\r\n        border: 1px solid $input-border-color;\r\n        height: 38px;\r\n        outline: none;\r\n        .select2-selection__rendered {\r\n            line-height: 36px;\r\n            padding-left: 12px;\r\n        }\r\n        .select2-selection__arrow {\r\n            height: 34px;\r\n            width: 34px;\r\n            right: 3px;\r\n            b {\r\n                border-color: darken($light, 15%) transparent transparent transparent;\r\n                border-width: 6px 6px 0 6px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.select2-container--open {\r\n    .select2-selection--single {\r\n        .select2-selection__arrow {\r\n            b {\r\n                border-color: transparent transparent darken($light, 15%) transparent !important;\r\n                border-width: 0 6px 6px 6px !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.select2-results__option {\r\n    padding: 6px 12px;\r\n}\r\n\r\n.select2-dropdown {\r\n    border: 1px solid darken($light, 5%);\r\n    box-shadow: $shadow;\r\n}\r\n\r\n.select2-container--default {\r\n    .select2-search--dropdown {\r\n        padding: 10px;\r\n        background-color: lighten($light, 5%);\r\n        .select2-search__field {\r\n            border: 1px solid darken($light, 5%);\r\n            outline: none;\r\n        }\r\n    }\r\n    .select2-results__option--highlighted[aria-selected] {\r\n        background-color: $primary;\r\n    }\r\n    .select2-results__option[aria-selected=true] {\r\n        background-color: $light;\r\n        color: $dark;\r\n        &:hover {\r\n            background-color: $primary;\r\n            color: $white;\r\n        }\r\n    }\r\n}\r\n\r\n.select2-container {\r\n    .select2-selection--multiple {\r\n        min-height: 38px;\r\n        border: 1px solid $input-border-color !important;\r\n        .select2-selection__rendered {\r\n            padding: 1px 10px;\r\n        }\r\n        .select2-search__field {\r\n            border: 0;\r\n        }\r\n        .select2-selection__choice {\r\n            background-color: $primary;\r\n            border: none;\r\n            color: $white;\r\n            border-radius: 3px;\r\n            padding: 0 7px;\r\n            margin-top: 7px;\r\n        }\r\n        .select2-selection__choice__remove {\r\n            color: $white;\r\n            margin-right: 5px;\r\n            &:hover {\r\n                color: $white;\r\n            }\r\n        }\r\n    }\r\n}", "//\r\n// slimscroll.scss\r\n//\r\n\r\n.slimScrollDiv {\r\n    height: auto !important;\r\n}", "//\r\n// toastr.scss\r\n//\r\n\r\n.jq-toast-single {\r\n    padding: 15px;\r\n    font-family: $font-family-base;\r\n    background-color: $primary;\r\n    font-size: 13px;\r\n    line-height: 22px;\r\n    h2 {\r\n        font-family: $font-family-base;\r\n    }\r\n    a {\r\n        font-size: $font-size-base;\r\n        &:hover {\r\n            color: $white;\r\n        }\r\n    }\r\n}\r\n\r\n.jq-has-icon {\r\n    padding: 10px 10px 10px 50px;\r\n}\r\n\r\n.close-jq-toast-single {\r\n    position: absolute;\r\n    top: -12px;\r\n    right: -12px;\r\n    font-size: 20px;\r\n    cursor: pointer;\r\n    height: 32px;\r\n    width: 32px;\r\n    background-color: $dark;\r\n    border-radius: 50%;\r\n    text-align: center;\r\n    line-height: 32px;\r\n}\r\n\r\n.jq-toast-loader {\r\n    height: 3px;\r\n    top: 0;\r\n    border-radius: 0;\r\n}\r\n\r\n@each $color,$value in $theme-colors {\r\n    .jq-icon-#{$color} {\r\n        background-color: #{$value};\r\n        color: $white;\r\n        border-color: #{$value};\r\n    }\r\n}\r\n\r\n// For error\r\n.jq-icon-error {\r\n    background-color: $danger;\r\n    color: $white;\r\n    border-color: $danger;\r\n}", "\r\n// \r\n// sweetalert.scss\r\n//\r\n\r\n.swal2-modal {\r\n  font-family: $font-family-base;\r\n  box-shadow: 0 10px 33px rgba(0,0,0,.1);\r\n\r\n  .swal2-title {\r\n    font-size: 24px;\r\n  }\r\n  .swal2-content {\r\n    font-size: 16px;\r\n  }\r\n  .swal2-spacer {\r\n    margin: 10px 0;\r\n  }\r\n  .swal2-file, .swal2-input, .swal2-textarea {\r\n    border: 2px solid $gray-300;\r\n    font-size: 16px;\r\n    box-shadow: none;\r\n  }\r\n  .swal2-confirm.btn-confirm {\r\n    background-color: $primary !important;\r\n    font-size: $font-size-base;\r\n  }\r\n\r\n  .swal2-cancel.btn-cancel {\r\n    background-color: $danger !important;\r\n    font-size: $font-size-base;\r\n  }\r\n\r\n  .swal2-styled:focus {\r\n    box-shadow: none !important;\r\n  }\r\n}\r\n\r\n.swal2-icon.swal2-question {\r\n  color: $primary;\r\n  border-color: $primary;\r\n}\r\n\r\n.swal2-icon.swal2-success {\r\n  border-color: $success;\r\n\r\n  .line,[class^=swal2-success-line][class$=long],\r\n  [class^=swal2-success-line]{\r\n    background-color: $success;\r\n  }\r\n\r\n  .placeholder,.swal2-success-ring  {\r\n    border-color: $success;\r\n  }\r\n}\r\n\r\n\r\n.swal2-icon.swal2-warning {\r\n  color: $warning;\r\n  border-color: $warning;\r\n}\r\n\r\n.swal2-icon.swal2-error {\r\n  border-color: $danger;\r\n  .line {\r\n    background-color: $danger;\r\n  }\r\n}\r\n.swal2-modal .swal2-file:focus, .swal2-modal .swal2-input:focus, .swal2-modal .swal2-textarea:focus {\r\n  outline: 0;\r\n  border: 2px solid $primary;\r\n}\r\n\r\n.swal2-container.swal2-shown {\r\n  background-color: rgba($dark, 0.9);\r\n}\r\n", "//\r\n// custom-select.scss\r\n//\r\n\r\n[data-plugin=\"customselect\"] {\r\n    display: none;\r\n}\r\n\r\n.nice-select {\r\n    line-height: 36px;\r\n    height: 38px;\r\n    border-color: $input-border-color;\r\n    border-radius: $input-border-radius;\r\n}\r\n\r\n.nice-select.open, .nice-select:active, .nice-select:focus {\r\n    border-color: $input-focus-border-color;\r\n}\r\n\r\n.nice-select.small {\r\n    height: 32px;\r\n    line-height: 30px;\r\n}", "//\r\n// tippy.scss\r\n//\r\n\r\n.tippy-tooltip {\r\n    font-size: $font-size-sm;\r\n    .light-theme[data-animatefill] {\r\n        background-color: transparent;\r\n    }\r\n}\r\n\r\n.light-theme {\r\n    color: $dark;\r\n    box-shadow: $shadow-lg;\r\n    background-color: $white;\r\n\r\n    .tippy-backdrop {\r\n        background-color: $white;\r\n    }\r\n    \r\n    .tippy-roundarrow {\r\n        fill: $white;\r\n    }\r\n}\r\n\r\n.gradient-theme {\r\n    .tippy-backdrop {\r\n        background: $primary;  /* fallback for old browsers */\r\n        background: linear-gradient(to left, $danger, $primary); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */\r\n    }\r\n}\r\n\r\n.tippy-popper{\r\n    &[x-placement^=top] {\r\n        .tippy-tooltip{\r\n            &.light-theme {\r\n                .tippy-arrow {\r\n                    border-top: 7px solid $white;\r\n                    border-right: 7px solid transparent;\r\n                    border-left: 7px solid transparent;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &[x-placement^=bottom] {\r\n        .tippy-tooltip {\r\n            &.light-theme {\r\n                .tippy-arrow {\r\n                    border-bottom: 7px solid $white;\r\n                    border-right: 7px solid transparent;\r\n                    border-left: 7px solid transparent;\r\n                }\r\n            }\r\n        }    \r\n    }\r\n    &[x-placement^=left] {\r\n        .tippy-tooltip{\r\n            &.light-theme {\r\n                .tippy-arrow {\r\n                    border-left: 7px solid $white;\r\n                    border-top: 7px solid transparent;\r\n                    border-bottom: 7px solid transparent;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &[x-placement^=right] {\r\n        .tippy-tooltip {\r\n            &.light-theme {\r\n                .tippy-arrow {\r\n                    border-right: 7px solid $white;\r\n                    border-top: 7px solid transparent;\r\n                    border-bottom: 7px solid transparent;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}", "// \r\n// nestable-list.scss\r\n//\r\n\r\n.dd-list {\r\n    .dd-item {\r\n        .dd-handle {\r\n            background: $gray-100;\r\n            border: none;\r\n            padding: 8px 16px;\r\n            height: auto;\r\n            font-weight: 600;\r\n            border-radius: 3px;\r\n            &:hover {\r\n                color: $primary;\r\n            }\r\n        }\r\n        button {\r\n            height: 36px;\r\n            font-size: 17px;\r\n            margin: 0;\r\n            color: $gray-600;\r\n            width: 36px;\r\n        }\r\n    }\r\n    .dd3-item {\r\n        margin: 5px 0;\r\n\r\n        .dd-item button {\r\n            width: 36px;\r\n            height: 36px;\r\n        }\r\n    }\r\n    .dd3-handle {\r\n        margin: 0;\r\n        height: 36px !important;\r\n        float: left;\r\n    }\r\n    .dd3-content {\r\n        height: auto;\r\n        border: none;\r\n        padding: 8px 16px 8px 46px;\r\n        background: $gray-100;\r\n        font-weight: 600;\r\n        &:hover {\r\n            color: $primary;\r\n        }\r\n    }\r\n    .dd3-handle:before {\r\n        content: \"\\F35C\";\r\n        font-family: \"Material Design Icons\";\r\n        color: $gray-500;\r\n    }\r\n}\r\n\r\n.dd-empty,\r\n.dd-placeholder {\r\n    background: rgba($gray-400,0.2);\r\n}\r\n\r\n.dd-dragel {\r\n    .dd-handle {\r\n        box-shadow: $shadow;\r\n    }\r\n}", "//\r\n// hopscotch.scss\r\n//\r\n\r\ndiv.hopscotch-bubble {\r\n    border: 3px solid $primary;\r\n    border-radius: 5px;\r\n    .hopscotch-next,\r\n    .hopscotch-prev {\r\n        background-color: $primary !important;\r\n        background-image: none !important;\r\n        border-color: $primary !important;\r\n        text-shadow: none !important;\r\n        margin: 0 0 0 5px !important;\r\n        font-family: $font-family-base;\r\n        color: $white !important;\r\n    }\r\n\r\n    .hopscotch-bubble-number {\r\n        background: $danger;\r\n        padding: 0;\r\n        border-radius: 50%;\r\n    }\r\n    \r\n    .hopscotch-bubble-arrow-container {\r\n        &.left {\r\n            .hopscotch-bubble-arrow-border {\r\n                border-right: 19px solid $primary;\r\n            }\r\n\r\n            .hopscotch-bubble-arrow {\r\n                border: none;\r\n            }\r\n        }\r\n\r\n        &.right {\r\n            .hopscotch-bubble-arrow {\r\n                border-left: 19px solid $primary;\r\n                left: -2px;\r\n            }\r\n\r\n            .hopscotch-bubble-arrow-border {\r\n                border-left: 0 solid $primary;\r\n            }\r\n        }\r\n        &.up {\r\n            .hopscotch-bubble-arrow {\r\n                border-bottom: 19px solid $primary;\r\n                top: 0;\r\n            }\r\n\r\n            .hopscotch-bubble-arrow-border {\r\n                border-bottom: 0 solid rgba(0, 0, 0, .5);\r\n            }\r\n        }\r\n        &.down {\r\n            .hopscotch-bubble-arrow {\r\n                border-top: 19px solid $primary;\r\n                top: -2px;\r\n            }\r\n            .hopscotch-bubble-arrow-border {\r\n                border-top: 0 solid rgba(0, 0, 0, .5);\r\n            }\r\n        }\r\n    }\r\n    \r\n    h3 {\r\n        font-family: $font-family-secondary;\r\n        margin-bottom: 10px;\r\n    }\r\n    .hopscotch-content {\r\n        font-family: $font-family-base;\r\n    }\r\n}", "//\r\n// flot.scss\r\n//\r\n\r\n.flotTip {\r\n    padding: 8px 12px;\r\n    background-color: $dark;\r\n    z-index: 100;\r\n    color: $white;\r\n    opacity: 1;\r\n    border-radius: 3px;\r\n}\r\n\r\n.legend {\r\n    tr {\r\n        height: 30px;\r\n        font-family: $font-family-secondary;\r\n    }\r\n}\r\n\r\n.legendLabel {\r\n    padding-left: 5px !important;\r\n    line-height: 10px;\r\n    padding-right: 20px;\r\n    font-size: 13px;\r\n    font-weight: $font-weight-medium;\r\n    color: $gray-600;\r\n    text-transform: uppercase;\r\n}\r\n\r\n.legendColorBox {\r\n    div {\r\n        div {\r\n            border-radius: 50%;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n@include media-breakpoint-down(sm) {\r\n    .legendLabel {\r\n        display: none;\r\n    }\r\n}", "//\r\n// morris.scss\r\n//\r\n\r\n.morris-chart {\r\n    text {\r\n        font-family: $font-family-secondary !important;\r\n    }\r\n}\r\n.morris-hover {\r\n    position: absolute;\r\n    z-index: 10;\r\n\r\n    &.morris-default-style {\r\n        font-size: 12px;\r\n        text-align: center;\r\n        border-radius: 5px;\r\n        padding: 10px 12px;\r\n        background: $dark;\r\n        color: $white;\r\n        font-family: $font-family-base;\r\n\r\n        .morris-hover-row-label {\r\n            font-weight: bold;\r\n            margin: 0.25em 0;\r\n            font-family: $font-family-secondary;\r\n        }\r\n\r\n        .morris-hover-point {\r\n            white-space: nowrap;\r\n            margin: 0.1em 0;\r\n            color: $white;\r\n        }\r\n    }\r\n}", "//\r\n// chartjs.scss\r\n//\r\n\r\n.chartjs-chart {\r\n    margin: auto; \r\n    position: relative; \r\n    width: 100%;\r\n}", "//\r\n// chartist.scss\r\n//\r\n\r\n.ct-golden-section:before {\r\n    float: none;\r\n}\r\n\r\n.ct-chart {\r\n    max-height: 300px;\r\n    .ct-label {\r\n        fill: $gray-500;\r\n        color: $gray-500;\r\n        font-size: 12px;\r\n        line-height: 1;\r\n    }\r\n}\r\n\r\n.ct-chart.simple-pie-chart-chartist {\r\n    .ct-label {\r\n        color: $white;\r\n        fill: $white;\r\n        font-size: 16px;\r\n    }\r\n}\r\n\r\n.ct-chart {\r\n    .ct-series {\r\n        &.ct-series-a {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $primary;\r\n            }\r\n        }\r\n        &.ct-series-b {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $info;\r\n            }\r\n        }\r\n        &.ct-series-c {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $pink;\r\n            }\r\n        }\r\n        &.ct-series-d {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $blue;\r\n            }\r\n        }\r\n        &.ct-series-e {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $dark;\r\n            }\r\n        }\r\n        &.ct-series-f {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $success;\r\n            }\r\n        }\r\n        &.ct-series-g {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $warning;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.ct-series-a {\r\n    .ct-area,\r\n    .ct-slice-pie {\r\n        fill: $primary;\r\n    }\r\n}\r\n\r\n.ct-series-b {\r\n    .ct-area,\r\n    .ct-slice-pie {\r\n        fill: $info;\r\n    }\r\n}\r\n\r\n.ct-series-c {\r\n    .ct-area,\r\n    .ct-slice-pie {\r\n        fill: $pink;\r\n    }\r\n}\r\n\r\n.ct-series-d {\r\n    .ct-area,\r\n    .ct-slice-pie {\r\n        fill: $blue;\r\n    }\r\n}\r\n\r\n.ct-area {\r\n    fill-opacity: .33;\r\n}\r\n\r\n.chartist-tooltip {\r\n    position: absolute;\r\n    display: inline-block;\r\n    opacity: 0;\r\n    min-width: 10px;\r\n    padding: 2px 10px;\r\n    border-radius: 3px;\r\n    background: $dark;\r\n    color: $white;\r\n    text-align: center;\r\n    pointer-events: none;\r\n    z-index: 1;\r\n    transition: opacity .2s linear;\r\n    &.tooltip-show {\r\n        opacity: 1;\r\n    }\r\n}", "//\r\n// c3.scss\r\n//\r\n \r\n.c3-tooltip {\r\n    box-shadow: $shadow-lg;\r\n    opacity: 1;\r\n    td {\r\n        border-left: none;\r\n        font-family: $font-family-secondary;\r\n        >span {\r\n            background: $dark;\r\n        }\r\n    }\r\n    tr {\r\n        border: none !important;\r\n    }    \r\n    th {\r\n        background-color: $dark;\r\n    }\r\n}\r\n\r\n.c3-chart-arcs-title {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n}\r\n\r\n.c3 {\r\n    text {\r\n        font-family: $font-family-base;\r\n        color: $gray-400;\r\n    }\r\n}\r\n.c3-legend-item {\r\n    font-family: $font-family-secondary;\r\n    font-size: 14px;\r\n}\r\n.c3 line, .c3 path {\r\n    stroke: $gray-400;\r\n}\r\n.c3-chart-arc.c3-target g path {\r\n    stroke: $white;\r\n}", "//\r\n// ricksaw.scss\r\n//\r\n \r\n#legend {\r\n    background: white;\r\n    position: absolute;\r\n    top: 0;\r\n    right: 15px;\r\n}\r\n\r\n#legend .line {\r\n    color: $dark;\r\n}\r\n\r\n.rickshaw_graph {\r\n    svg {\r\n        max-width: 100%;\r\n    }\r\n}\r\n\r\n.rickshaw_legend {\r\n    .label {\r\n        font-family: inherit;\r\n        letter-spacing: 0.01em;\r\n        font-weight: 600;\r\n    }\r\n}\r\n\r\n.rickshaw_graph {\r\n    .detail .item, \r\n    .detail .x_label, \r\n    .x_tick .title {\r\n        font-family: $font-family-base;\r\n    }\r\n}\r\n\r\n.gauge-chart {\r\n    text {\r\n        font-family: $font-family-base !important;\r\n    }\r\n}", "// \r\n// responsive-table.scss\r\n//\r\n\r\n.responsive-table-plugin {\r\n    .dropdown-menu li.checkbox-row {\r\n        padding: 7px 15px;\r\n    }\r\n    .table-responsive {\r\n        border: none;\r\n        margin-bottom: 0;\r\n    }\r\n    .btn-toolbar {\r\n        display: block;\r\n    }\r\n    tbody {\r\n        th {\r\n            font-size: 14px;\r\n            font-weight: normal;\r\n        }\r\n    }\r\n    .checkbox-row {\r\n        padding-left: 40px;\r\n        label {\r\n            display: inline-block;\r\n            padding-left: 5px;\r\n            position: relative;\r\n            margin-bottom: 0;\r\n            &::before {\r\n                background-color: $white;\r\n                border-radius: 3px;\r\n                border: 1px solid $gray-400;\r\n                content: \"\";\r\n                display: inline-block;\r\n                height: 17px;\r\n                left: 0;\r\n                margin-left: -20px;\r\n                position: absolute;\r\n                transition: 0.3s ease-in-out;\r\n                width: 17px;\r\n                outline: none;\r\n            }\r\n            &::after {\r\n                color: $gray-400;\r\n                display: inline-block;\r\n                font-size: 11px;\r\n                height: 16px;\r\n                left: 0;\r\n                margin-left: -20px;\r\n                padding-left: 3px;\r\n                padding-top: 1px;\r\n                position: absolute;\r\n                top: -2px;\r\n                width: 16px;\r\n            }\r\n        }\r\n        input[type=\"checkbox\"] {\r\n            cursor: pointer;\r\n            opacity: 0;\r\n            z-index: 1;\r\n            outline: none;\r\n            &:disabled+label {\r\n                opacity: 0.65;\r\n            }\r\n        }\r\n        input[type=\"checkbox\"]:focus+label {\r\n            &::before {\r\n                outline-offset: -2px;\r\n                outline: none;\r\n            }\r\n        }\r\n        input[type=\"checkbox\"]:checked+label {\r\n            &::after {\r\n                content: \"\\f00c\";\r\n                font-family: 'Font Awesome 5 Free';\r\n                font-weight: 900;\r\n            }\r\n        }\r\n        input[type=\"checkbox\"]:disabled+label {\r\n            &::before {\r\n                background-color: $gray-300;\r\n                cursor: not-allowed;\r\n            }\r\n        }\r\n        input[type=\"checkbox\"]:checked+label {\r\n            &::before {\r\n                background-color: $white;\r\n                border-color: $primary;\r\n            }\r\n            &::after {\r\n                color: $primary;\r\n            }\r\n        }\r\n    }\r\n    table.focus-on tbody tr.focused th,\r\n    table.focus-on tbody tr.focused td,\r\n    .sticky-table-header {\r\n        background: $primary;\r\n        color: $white;\r\n        border-color: $primary;\r\n\r\n        table {\r\n            color: $white;\r\n        }\r\n    }\r\n    .fixed-solution {\r\n        .sticky-table-header {\r\n            top: $topbar-height !important;\r\n        }\r\n    }\r\n    .btn-default {\r\n        background-color: $white;\r\n        color: $dark;\r\n        border: 1px solid rgba($dark, 0.3);\r\n        &.btn-primary {\r\n            background-color: $primary;\r\n            border-color: $primary;\r\n            color: $white;\r\n            box-shadow: 0 0 0 2px rgba($primary, .5);\r\n        }\r\n    }\r\n    .btn-group{\r\n        &.pull-right {\r\n            float: right;\r\n            .dropdown-menu {\r\n                left: auto;\r\n                right: 0;\r\n            }\r\n        }\r\n    }\r\n}", "//\r\n// footables.scss\r\n//\r\n\r\n@font-face {\r\n    font-family: 'footable';\r\n    src: url('../fonts/footable.eot');\r\n    src: url('../fonts/footable.eot?#iefix') format('embedded-opentype'), url('../fonts/footable.woff') format('woff'), url('../fonts/footable.ttf') format('truetype'), url('../fonts/footable.svg#footable') format('svg');\r\n    font-weight: normal;\r\n    font-style: normal;\r\n}\r\n\r\n@media screen and (-webkit-min-device-pixel-ratio: 0) {\r\n    @font-face {\r\n        font-family: 'footable';\r\n        src: url('../fonts/footable.svg#footable') format('svg');\r\n        font-weight: normal;\r\n        font-style: normal;\r\n    }\r\n}\r\n\r\n.footable-odd {\r\n    background-color: $white;\r\n}\r\n\r\n.footable-detail-show {\r\n    background-color: $gray-100;\r\n}\r\n\r\n.footable-row-detail {\r\n    background-color: #F0F4F7;\r\n}\r\n\r\n.footable-pagination {\r\n    li {\r\n        margin-left: 5px;\r\n        float: left;\r\n        a {\r\n            position: relative;\r\n            display: block;\r\n            padding: .5rem .75rem;\r\n            margin-left: -1px;\r\n            line-height: 1.25;\r\n            color: $dark;\r\n            background-color: $white;\r\n            border: 1px solid $gray-100;\r\n            border-radius: 2px;\r\n        }\r\n    }\r\n    li.active {\r\n        a {\r\n            color: $white;\r\n            background-color: $primary;\r\n            border-color: $primary;\r\n        }\r\n    }\r\n}\r\n\r\n.footable>thead>tr>th>span.footable-sort-indicator {\r\n    float: right;\r\n}", "// \r\n// bootstrap-tables.scss\r\n//\r\n\r\n.bootstrap-table {\r\n    .table:not(.table-sm) {\r\n        >tbody>tr>td,\r\n        >tbody>tr>th,\r\n        >tfoot>tr>td,\r\n        >tfoot>tr>th,\r\n        >thead>tr>td {\r\n            padding: $table-cell-padding;\r\n        }\r\n    }\r\n    .table {\r\n        border-bottom: none;\r\n    }\r\n    .table>thead>tr>th {\r\n        border-bottom: 2px solid transparent;\r\n    }\r\n}\r\n\r\ntable[data-toggle=\"table\"] {\r\n    display: none;\r\n}\r\n\r\n.fixed-table-pagination {\r\n    .pagination-detail,\r\n    div.pagination {\r\n        margin-top: 20px;\r\n        margin-bottom: 0;\r\n    }\r\n    .pagination {\r\n        .page-link {\r\n            border-radius: 30px !important;\r\n            margin: 0 3px;\r\n            border: none;\r\n        }\r\n    }\r\n}\r\n\r\n.fixed-table-container {\r\n    border: none;\r\n    tbody {\r\n        td {\r\n            border-left: none;\r\n        }\r\n    }\r\n    thead {\r\n        th {\r\n            .th-inner {\r\n                padding: $table-cell-padding;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.fixed-table-toolbar {\r\n    .fa {\r\n        font-family: 'Font Awesome 5 Free';\r\n        font-weight: 400;\r\n    }\r\n    .fa-toggle-down:before {\r\n        content: \"\\f150\";\r\n    }\r\n    .fa-toggle-up:before {\r\n        content: \"\\f151\";\r\n    }\r\n    .fa-refresh:before {\r\n        content: \"\\f01e\";\r\n        font-weight: 900;\r\n    }\r\n    .fa-th-list:before {\r\n        content: \"\\f0ca\";\r\n        font-weight: 900;\r\n    }\r\n}", "// \r\n// tablesaw.scss\r\n//\r\n\r\n.tablesaw {\r\n    thead {\r\n        background: $gray-100;\r\n        background-image: none;\r\n        border: none;\r\n        th {\r\n            text-shadow: none;\r\n        }\r\n        tr:first-child th {\r\n            border: none;\r\n            font-weight: 500;\r\n            font-family: $font-family-secondary;\r\n        }\r\n    }\r\n    td {\r\n        border-top: 1px solid $gray-100 !important;\r\n    }\r\n}\r\n\r\n.tablesaw td,\r\n.tablesaw tbody th {\r\n    font-size: inherit;\r\n    line-height: inherit;\r\n    padding: 10px !important;\r\n}\r\n\r\n.tablesaw-stack tbody tr,\r\n.tablesaw tbody tr {\r\n    border-bottom: none;\r\n}\r\n\r\n.tablesaw-bar .btn-select.btn-small:after,\r\n.tablesaw-bar .btn-select.btn-micro:after {\r\n    font-size: 8px;\r\n    padding-right: 10px;\r\n}\r\n\r\n.tablesaw-swipe .tablesaw-cell-persist {\r\n    box-shadow: none;\r\n    border-color: $gray-100;\r\n}\r\n\r\n.tablesaw-enhanced .tablesaw-bar .btn {\r\n    text-shadow: none;\r\n    background-image: none;\r\n    text-transform: none;\r\n    border: 1px solid $gray-300;\r\n    padding: 3px 10px;\r\n    color: $dark;\r\n\r\n    &:after {\r\n        display: none;\r\n    }\r\n}\r\n\r\n.tablesaw-enhanced .tablesaw-bar .btn.btn-select {\r\n    &:hover {\r\n        background: $white;\r\n    }\r\n}\r\n\r\n.tablesaw-enhanced .tablesaw-bar .btn:hover,\r\n.tablesaw-enhanced .tablesaw-bar .btn:focus,\r\n.tablesaw-enhanced .tablesaw-bar .btn:active {\r\n    color: $primary !important;\r\n    background-color: $gray-100;\r\n    outline: none !important;\r\n    box-shadow: none !important;\r\n    background-image: none;\r\n}\r\n\r\n.tablesaw-columntoggle-popup {\r\n    .btn-group {\r\n        display: block;\r\n    }\r\n}\r\n\r\n.tablesaw-swipe .tablesaw-swipe-cellpersist {\r\n    border-right: 2px solid $gray-100;\r\n}\r\n\r\n.tablesaw-sortable-btn {\r\n    cursor: pointer;\r\n}", "//\r\n// jsgrid.scss\r\n//\r\n\r\n.jsgrid-cell {\r\n    padding: $table-cell-padding;\r\n}\r\n\r\n.jsgrid-grid-header, \r\n.jsgrid-grid-body, \r\n.jsgrid-header-row > .jsgrid-header-cell, \r\n.jsgrid-filter-row > .jsgrid-cell, \r\n.jsgrid-insert-row > .jsgrid-cell, \r\n.jsgrid-edit-row > .jsgrid-cell {\r\n    border: none;\r\n}\r\n\r\n.jsgrid-alt-row {\r\n    > .jsgrid-cell {\r\n        background: $white;\r\n    }\r\n}\r\n\r\n.jsgrid-selected-row {\r\n    >.jsgrid-cell {\r\n        background: $table-hover-bg;\r\n        border-color: $table-hover-bg;\r\n    }\r\n}\r\n\r\n.jsgrid-header-row {\r\n    > .jsgrid-header-cell {\r\n        background: $table-head-bg;\r\n        text-align: center !important;\r\n    }\r\n}\r\n\r\n.jsgrid-filter-row {\r\n    > .jsgrid-cell {\r\n        background: lighten($table-head-bg, 2%);\r\n    }\r\n}\r\n\r\n.jsgrid-edit-row>.jsgrid-cell,\r\n.jsgrid-insert-row>.jsgrid-cell {\r\n    background: $gray-100;\r\n}\r\n\r\n.jsgrid input,\r\n.jsgrid select,\r\n.jsgrid textarea {\r\n    border: 1px solid $gray-100;\r\n    padding: .4em .6em;\r\n    outline: none !important;\r\n}\r\n\r\n.jsgrid-pager-container {\r\n    margin-top: 10px;\r\n}\r\n\r\n.jsgrid-pager-page {\r\n    padding: 0;\r\n    margin: 0 2px;\r\n    &.jsgrid-pager-current-page {\r\n        background-color: $primary;\r\n        color: $white;\r\n    }\r\n}\r\n\r\n.jsgrid-pager-page a,\r\n.jsgrid-pager-current-page {\r\n    background-color: $gray-100;\r\n    border-radius: 50%;\r\n    height: 24px;\r\n    width: 24px;\r\n    display: inline-block;\r\n    text-align: center;\r\n    line-height: 24px;\r\n    color: $gray-700;\r\n}\r\n\r\n.jsgrid-pager-nav-button {\r\n    a {\r\n        color: $gray-700;\r\n        font-weight: 600;\r\n        &:hover {\r\n            color: $primary;\r\n        }\r\n    }\r\n}\r\n\r\n.jsgrid {\r\n    .jsgrid-button {\r\n        width: 24px;\r\n        height: 24px;\r\n        border-radius: 50%;\r\n        background-image: url(\"../images/jsgrid.png\");\r\n        background-color: $gray-200;\r\n        outline: none !important;\r\n    \r\n        &:hover {\r\n            opacity: 0.9;\r\n            background-color: $gray-100;\r\n        }\r\n    }\r\n}\r\n\r\n.jsgrid-search-mode-button {\r\n    background-position: 0 -295px;\r\n}\r\n\r\n.jsgrid-insert-button {\r\n    background-position: 0 -160px;\r\n}\r\n\r\n.jsgrid-header-sort {\r\n    &:before {\r\n        position: absolute;\r\n    }\r\n}", "//\r\n// multi-select.scss\r\n//\r\n\r\n.ms-container {\r\n    background: transparent url('../images/multiple-arrow.png') no-repeat 50% 50%;\r\n    width: auto;\r\n    max-width: 370px;\r\n    \r\n    .ms-list {\r\n        box-shadow: none;\r\n        border: $input-border-width solid $input-border-color;\r\n\r\n        &.ms-focus {\r\n            box-shadow: none;\r\n            border: $input-border-width solid $input-focus-border-color;\r\n        }\r\n    }\r\n    .ms-selectable {\r\n        li {\r\n            &.ms-elem-selectable {\r\n                border: none;\r\n                padding: 5px 10px;\r\n            }\r\n            &.ms-hover {\r\n                background-color: $primary;\r\n            }\r\n        }\r\n    }\r\n    .ms-selection {\r\n        li.ms-elem-selection {\r\n            border: none;\r\n            padding: 5px 10px;\r\n        }\r\n        li.ms-hover {\r\n            background-color: $primary;\r\n        }\r\n    }\r\n}\r\n\r\n.ms-selectable {\r\n    box-shadow: none;\r\n    outline: none !important;\r\n}\r\n\r\n.ms-optgroup-label {\r\n    font-weight: $font-weight-medium;\r\n    font-family: $font-family-secondary;\r\n    color: $dark !important;\r\n    font-size: 13px;\r\n}", "//\r\n// autocomplete.scss\r\n//\r\n \r\n.autocomplete-suggestions {\r\n    border: 1px solid #f9f9f9;\r\n    background: $white;\r\n    cursor: default;\r\n    overflow: auto;\r\n    max-height: 200px !important;\r\n    box-shadow: 0 2px 2px rgba(0, 0, 0, 0.15);\r\n  }\r\n  \r\n  .autocomplete-suggestion {\r\n    padding: 5px 10px;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n  }\r\n  \r\n  .autocomplete-no-suggestion {\r\n    padding: 5px;\r\n  }\r\n  \r\n  .autocomplete-selected {\r\n    background: $gray-200;\r\n    cursor: pointer;\r\n  }\r\n  \r\n  .autocomplete-suggestions strong {\r\n    font-weight: bold;\r\n    color: $dark;\r\n  }\r\n  \r\n  .autocomplete-group {\r\n    padding: 5px;\r\n    font-weight: $font-weight-medium;\r\n    font-family: $font-family-secondary;\r\n  }\r\n  \r\n  .autocomplete-group strong {\r\n    font-weight: bold;\r\n    font-size: 16px;\r\n    color: $dark;\r\n    display: block;\r\n  }\r\n  ", "//\r\n// bootstrap-select.scss\r\n//\r\n \r\n.bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {\r\n    width: 100% !important;\r\n}\r\n\r\n.bootstrap-select {\r\n    .dropdown-toggle{\r\n        &:before {\r\n            content: \"\\F140\";\r\n            display: inline-block;\r\n            font-family: \"Material Design Icons\";\r\n        }\r\n        &:focus {\r\n            outline: none !important;\r\n            outline-offset: 0;\r\n        }\r\n    }\r\n    a {\r\n        outline: none !important;\r\n    }\r\n    .inner {\r\n        overflow-y: inherit !important;\r\n    }\r\n}", "//\r\n// bootstrap-touchspin.scss\r\n//\r\n \r\n.bootstrap-touchspin {\r\n    .btn {\r\n        .input-group-text {\r\n            padding: 0;\r\n            border: none;\r\n            background-color: transparent;\r\n            color: inherit;\r\n        }\r\n    }\r\n}", "//\r\n// parsley.scss\r\n//\r\n \r\n.parsley-errors-list {\r\n  margin: 0;\r\n  padding: 0;\r\n\r\n  > li {\r\n    list-style: none;\r\n    color: $danger;\r\n    margin-top: 5px;\r\n    padding-left: 20px;\r\n    position: relative;\r\n  \r\n    &:before {\r\n      content: \"\\F159\";\r\n      font-family: \"Material Design Icons\";\r\n      position: absolute;\r\n      left: 2px;\r\n      top: -1px;\r\n    }\r\n  }\r\n}\r\n\r\n.parsley-error {\r\n  border-color: $danger;\r\n}\r\n\r\n.parsley-success {\r\n  border-color: $success;\r\n}", "//\r\n// flatpickr.scss\r\n//\r\n\r\n.flatpickr-day {\r\n    &.selected,\r\n    &.startRange,\r\n    &.endRange,\r\n    &.selected.inRange,\r\n    &.startRange.inRange,\r\n    &.endRange.inRange,\r\n    &.selected:focus,\r\n    &.startRange:focus,\r\n    &.endRange:focus,\r\n    &.selected:hover,\r\n    &.startRange:hover,\r\n    &.endRange:hover,\r\n    &.selected.prevMonthDay,\r\n    &.startRange.prevMonthDay,\r\n    &.endRange.prevMonthDay,\r\n    &.selected.nextMonthDay,\r\n    &.startRange.nextMonthDay,\r\n    &.endRange.nextMonthDay {\r\n        background: $primary;\r\n        border-color: $primary;\r\n    }\r\n    &.selected.startRange+.endRange:not(:nth-child(7n+1)),\r\n    &.startRange.startRange+.endRange:not(:nth-child(7n+1)),\r\n    &.endRange.startRange+.endRange:not(:nth-child(7n+1)) {\r\n        box-shadow: -10px 0 0 $primary;\r\n    }\r\n}\r\n\r\n.flatpickr-time {\r\n    input:hover,\r\n    .flatpickr-am-pm:hover,\r\n    input:focus,\r\n    .flatpickr-am-pm:focus {\r\n        background: $gray-100;\r\n    }\r\n}\r\n\r\n.flatpickr-months {\r\n    .flatpickr-month {\r\n        height: 36px;\r\n    }\r\n}", "//\r\n// clockpicker.scss\r\n//\r\n\r\n.clockpicker-popover {\r\n    .btn-default {\r\n        background-color: $primary;\r\n        color: $white;\r\n    }\r\n}", "//\r\n// summernote.scss\r\n//\r\n\r\n@font-face {\r\n  font-family: \"summernote\";\r\n  font-style: normal;\r\n  font-weight: normal;\r\n  src: url(\"../fonts/summernote.eot\");\r\n  src: url(\"../fonts/summernote.eot?#iefix\") format(\"embedded-opentype\"), url(\"../fonts/summernote.woff?\") format(\"woff\"), url(\"../fonts/summernote.ttf?\") format(\"truetype\")\r\n}\r\n\r\n.note-editor{\r\n  &.note-frame {\r\n    border: 1px solid $input-border-color;\r\n    box-shadow: none;\r\n    margin: 0;\r\n\r\n    .note-statusbar {\r\n      background-color: lighten($gray-200,2%);\r\n      border-top: 1px solid $gray-200;\r\n    }\r\n\r\n    .note-editable {\r\n        border: none;\r\n    }\r\n  }\r\n}\r\n\r\n.note-status-output {\r\n  display: none;\r\n}\r\n\r\n.note-editable {\r\n  border: $input-border-width solid $input-border-color;\r\n  border-radius: $input-border-radius;\r\n  padding: $input-padding-y $input-padding-x;\r\n\r\n  p {\r\n    &:last-of-type {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.note-popover .popover-content .note-color .dropdown-menu,\r\n.card-header.note-toolbar .note-color .dropdown-menu {\r\n    min-width: 344px;\r\n}\r\n\r\n.note-toolbar {\r\n   z-index: 1;\r\n}", "//\r\n// quilljs-editor.scss\r\n//\r\n \r\n.ql-container {\r\n    font-family: $font-family-base;\r\n}\r\n\r\n.ql-bubble {\r\n    border: $input-border-width solid $input-border-color;\r\n    border-radius: $input-border-radius;\r\n}\r\n\r\n.ql-toolbar {\r\n    font-family: $font-family-base !important;\r\n    span {\r\n        outline: none !important;\r\n    }\r\n}", "// \r\n// dropzone.scss\r\n//\r\n\r\n.dropzone {\r\n  border: 2px dashed rgba($dark, 0.3);\r\n  background:$white;\r\n  border-radius: 6px;\r\n}", "//\r\n// dropify.scss\r\n//\r\n\r\n@font-face {\r\n    font-family: 'dropify';\r\n    src: url(\"../fonts/dropify.eot\");\r\n    src: url(\"../fonts/dropify.eot#iefix\") format(\"embedded-opentype\"), url(\"../fonts/dropify.woff\") format(\"woff\"), url(\"../fonts/dropify.ttf\") format(\"truetype\"), url(\"../fonts/dropify.svg#dropify\") format(\"svg\");\r\n    font-weight: normal;\r\n    font-style: normal;\r\n}\r\n\r\n.dropify-wrapper {\r\n    border: 2px dashed rgba($dark, 0.3);\r\n    background:$white;\r\n    border-radius: 6px;\r\n    font-family: $font-family-base;\r\n}", "//\r\n// x-editable.scss\r\n//\r\n\r\n.editable-clear-x {\r\n    background: url(\"../images/clear.png\") center center no-repeat;\r\n}\r\n\r\n.editableform-loading {\r\n    background: url('../images/loading.gif') center center no-repeat;\r\n}\r\n\r\n.editable-checklist label {\r\n    display: block;\r\n}", "//\r\n// cropper.scss\r\n//\r\n\r\n.image-crop-preview {\r\n    .img-preview {\r\n        float: left;\r\n        margin-bottom: .5rem;\r\n        margin-right: .5rem;\r\n        overflow: hidden;\r\n        background-color: $gray-100;\r\n        text-align: center;\r\n        width: 100%;\r\n    \r\n        >img {\r\n            max-width: 100%;\r\n        }\r\n    }\r\n\r\n    .preview-lg {\r\n        height: 9rem;\r\n        width: 16rem;\r\n    }\r\n    .preview-md {\r\n        height: 4.5rem;\r\n        width: 8rem;\r\n    }\r\n    .preview-sm {\r\n        height: 2.25rem;\r\n        width: 4rem;\r\n    }\r\n    .preview-xs {\r\n        height: 1.125rem;\r\n        margin-right: 0;\r\n        width: 2rem;\r\n    }\r\n}\r\n\r\n.img-crop-preview-btns {\r\n    > .btn, \r\n    > .btn-group {\r\n        margin-bottom: 8px;\r\n        margin-right: 8px;\r\n    }\r\n}\r\n\r\n.docs-cropped {\r\n    .modal-body {\r\n        >img,\r\n        >canvas {\r\n            max-width: 100%;\r\n        }\r\n    }\r\n}\r\n\r\n.docs-drop-options {\r\n    max-height: 400px;\r\n    overflow-y: auto;\r\n}", "//\r\n// google-maps.scss\r\n//\r\n \r\n.gmaps, .gmaps-panaroma {\r\n  height: 300px;\r\n  background: $gray-100;\r\n  border-radius: 3px;\r\n}\r\n\r\n.gmaps-overlay {\r\n  display: block;\r\n  text-align: center;\r\n  color: $white;\r\n  font-size: 16px;\r\n  line-height: 40px;\r\n  background: $primary;\r\n  border-radius: 4px;\r\n  padding: 10px 20px;\r\n}\r\n\r\n.gmaps-overlay_arrow {\r\n  left: 50%;\r\n  margin-left: -16px;\r\n  width: 0;\r\n  height: 0;\r\n  position: absolute;\r\n\r\n  &.above {\r\n    bottom: -15px;\r\n    border-left: 16px solid transparent;\r\n    border-right: 16px solid transparent;\r\n    border-top: 16px solid $primary;\r\n  }\r\n  \r\n  &.below {\r\n    top: -15px;\r\n    border-left: 16px solid transparent;\r\n    border-right: 16px solid transparent;\r\n    border-bottom: 16px solid $primary;\r\n  }\r\n}", "//\r\n// vector-maps.scss\r\n//\r\n\r\n.jvectormap-label {\r\n    border: none;\r\n    background: $dark;\r\n    color: $white;\r\n    font-family: $font-family-secondary;\r\n    font-size: $font-size-base;\r\n    padding: 5px 8px;\r\n}", "//\r\n// mapeal-maps.scss\r\n//\r\n\r\n.mapael {\r\n    .map {\r\n        position: relative;\r\n\r\n        .zoomIn {\r\n            top: 25px;\r\n        }\r\n        \r\n        .zoomOut {\r\n            top: 50px;\r\n        }\r\n    }\r\n    .mapTooltip {\r\n        position: absolute;\r\n        background-color: $primary;\r\n        opacity: 0.95;\r\n        border-radius: 3px;\r\n        padding: 2px 10px;\r\n        z-index: 1000;\r\n        max-width: 200px;\r\n        display: none;\r\n        color: $white;\r\n        font-family: $font-family-secondary;\r\n    }\r\n    .zoomIn,\r\n    .zoomOut,\r\n    .zoomReset {\r\n        display: inline-block;\r\n        text-align: center;\r\n        vertical-align: middle;\r\n        border-radius: 2px;\r\n        font-weight: 500;\r\n        cursor: pointer;\r\n        background-color: $primary;\r\n        text-decoration: none;\r\n        color: $white;\r\n        font-size: 14px;\r\n        position: absolute;\r\n        top: 0;\r\n        left: 10px;\r\n        width: 24px;\r\n        height: 24px;\r\n        line-height: 24px;\r\n    }\r\n\r\n    .plotLegend {\r\n        text {\r\n            font-family: $font-family-base !important;\r\n        }\r\n    }\r\n}", "// \r\n// general-rtl.scss\r\n//\r\n\r\nhtml {\r\n    direction: rtl;\r\n}\r\n\r\nbody {\r\n    text-align: right;\r\n}", "// \r\n// bootstrap-rtl.scss\r\n//\r\n\r\n\r\n// Card\r\n\r\n.card-widgets {\r\n    float: left;\r\n}\r\n\r\n\r\n// Dropdowns\r\n\r\n.dropdown-menu {\r\n    text-align: right;\r\n    left: auto !important;\r\n    right: 0;\r\n    bottom: auto;\r\n\r\n    &.dropdown-megamenu {\r\n        background-image: none;\r\n        left: 20px !important;\r\n        right: 20px !important;\r\n    }\r\n}\r\n\r\n.dropdown-menu-right {\r\n    right: auto !important;\r\n    left: 0 !important;\r\n}\r\n\r\n\r\n// List\r\n\r\nul {\r\n    padding-right: 0;\r\n}\r\n\r\n\r\n// Buttons\r\n\r\n.btn-label {\r\n    margin: -.55rem -.9rem -.55rem .9rem;\r\n}\r\n\r\n.btn-label-right {\r\n    margin: (-$btn-padding-y) ($btn-padding-x) (-$btn-padding-y) (-$btn-padding-x);\r\n}\r\n\r\n.btn-group,\r\n.btn-group-vertical {\r\n    direction: ltr;\r\n}\r\n\r\n\r\n// Modal\r\n\r\n.modal-header {\r\n    .close {\r\n        margin: (-$modal-header-padding-y) auto (-$modal-header-padding-x) (-$modal-header-padding-y);\r\n    }\r\n}\r\n\r\n.modal-footer {\r\n    > :not(:first-child) {\r\n        margin-right: .25rem;\r\n        margin-left: 0;\r\n    }\r\n\r\n    > :not(:last-child) {\r\n        margin-left: .25rem;\r\n        margin-right: 0;\r\n    }\r\n}\r\n\r\n\r\n// Alerts\r\n\r\n.alert-dismissible {\r\n    padding-left: $close-font-size + $alert-padding-x * 2;\r\n    padding-right: $alert-padding-x;\r\n\r\n    .close {\r\n        left: 0;\r\n        right: auto;\r\n    }\r\n}\r\n\r\n\r\n// Breadcrumb item arrow\r\n\r\n.breadcrumb-item {\r\n    +.breadcrumb-item {\r\n        &::before {\r\n            padding-left: $breadcrumb-item-padding;\r\n            content: \"\\F141\";\r\n        }\r\n    }\r\n}\r\n\r\n// Custom Checkbox-Radio \r\n\r\n.custom-control {\r\n    padding-right: $custom-control-gutter + $custom-control-indicator-size;\r\n    padding-left: 0;\r\n}\r\n\r\n.custom-control-label {\r\n    &::before {\r\n        left: auto;\r\n        right: -($custom-control-gutter + $custom-control-indicator-size);\r\n    }\r\n\r\n    // Foreground (icon)\r\n    &::after {\r\n        left: auto;\r\n        right: -($custom-control-gutter + $custom-control-indicator-size);\r\n    }\r\n}\r\n\r\n.custom-switch {\r\n    padding-right: $custom-switch-width + $custom-control-gutter;\r\n    padding-left: 0;\r\n\r\n    .custom-control-label {\r\n        &::before {\r\n            right: -($custom-switch-width + $custom-control-gutter);\r\n            left: auto;\r\n        }\r\n\r\n        &::after {\r\n            right: calc(#{-($custom-switch-width + $custom-control-gutter)} + #{$custom-control-indicator-border-width * 2});\r\n            left: auto;\r\n        }\r\n    }\r\n\r\n    .custom-control-input:checked~.custom-control-label {\r\n        &::after {\r\n            transform: translateX(#{-($custom-switch-width - $custom-control-indicator-size)});\r\n        }\r\n    }\r\n}\r\n\r\n.custom-file-label {\r\n    &::after {\r\n        right: auto;\r\n        left: 0;\r\n        border-right: inherit;\r\n    }\r\n}\r\n\r\n\r\n\r\n// Input Group\r\n\r\n.input-group-prepend {\r\n    margin-left: -1px;\r\n    margin-right: 0;\r\n}\r\n\r\n.input-group-append {\r\n    margin-right: -1px;\r\n    margin-left: 0;\r\n}\r\n\r\n.input-group>.input-group-prepend>.btn,\r\n.input-group>.input-group-prepend>.input-group-text,\r\n.input-group>.input-group-append:not(:last-child)>.btn,\r\n.input-group>.input-group-append:not(:last-child)>.input-group-text,\r\n.input-group>.input-group-append:last-child>.btn:not(:last-child):not(.dropdown-toggle),\r\n.input-group>.input-group-append:last-child>.input-group-text:not(:last-child),\r\n.input-group>.custom-select:not(:last-child),\r\n.input-group>.form-control:not(:last-child) {\r\n    border-top-right-radius: $input-border-radius;\r\n    border-bottom-right-radius: $input-border-radius;\r\n    border-top-left-radius: 0;\r\n    border-bottom-left-radius: 0;\r\n}\r\n\r\n.input-group>.input-group-append>.btn,\r\n.input-group>.input-group-append>.input-group-text,\r\n.input-group>.input-group-prepend:not(:first-child)>.btn,\r\n.input-group>.input-group-prepend:not(:first-child)>.input-group-text,\r\n.input-group>.input-group-prepend:first-child>.btn:not(:first-child),\r\n.input-group>.input-group-prepend:first-child>.input-group-text:not(:first-child),\r\n.input-group>.custom-select:not(:first-child),\r\n.input-group>.form-control:not(:first-child) {\r\n    border-top-left-radius: $input-border-radius;\r\n    border-bottom-left-radius: $input-border-radius;\r\n    border-top-right-radius: 0;\r\n    border-bottom-right-radius: 0;\r\n}", "// stylelint-disable declaration-no-important\n\n// <PERSON><PERSON> and Padding\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @each $prop, $abbrev in (margin: m, padding: p) {\n      @each $size, $length in $spacers {\n        .#{$abbrev}#{$infix}-#{$size} { #{$prop}: $length !important; }\n        .#{$abbrev}t#{$infix}-#{$size},\n        .#{$abbrev}y#{$infix}-#{$size} {\n          #{$prop}-top: $length !important;\n        }\n        .#{$abbrev}r#{$infix}-#{$size},\n        .#{$abbrev}x#{$infix}-#{$size} {\n          #{$prop}-left: $length !important;\n          #{$prop}-right: 0 !important;\n        }\n        .#{$abbrev}b#{$infix}-#{$size},\n        .#{$abbrev}y#{$infix}-#{$size} {\n          #{$prop}-bottom: $length !important;\n        }\n        .#{$abbrev}l#{$infix}-#{$size},\n        .#{$abbrev}x#{$infix}-#{$size} {\n          #{$prop}-right: $length !important;\n          #{$prop}-left: 0 !important;\n        }\n      }\n    }\n\n    // Negative margins (e.g., where `.mb-n1` is negative version of `.mb-1`)\n    @each $size, $length in $spacers {\n      @if $size != 0 {\n        .m#{$infix}-n#{$size} { margin: -$length !important; }\n        .mt#{$infix}-n#{$size},\n        .my#{$infix}-n#{$size} {\n          margin-top: -$length !important;\n        }\n        .mr#{$infix}-n#{$size},\n        .mx#{$infix}-n#{$size} {\n          margin-right: -$length !important;\n        }\n        .mb#{$infix}-n#{$size},\n        .my#{$infix}-n#{$size} {\n          margin-bottom: -$length !important;\n        }\n        .ml#{$infix}-n#{$size},\n        .mx#{$infix}-n#{$size} {\n          margin-left: -$length !important;\n        }\n      }\n    }\n\n    // Some special margin utils\n    .m#{$infix}-auto { margin: auto !important; }\n    .mt#{$infix}-auto,\n    .my#{$infix}-auto {\n      margin-top: auto !important;\n    }\n    .mr#{$infix}-auto,\n    .mx#{$infix}-auto {\n      margin-left: auto !important;\n      margin-right: inherit !important;\n    }\n    .mb#{$infix}-auto,\n    .my#{$infix}-auto {\n      margin-bottom: auto !important;\n    }\n    .ml#{$infix}-auto,\n    .mx#{$infix}-auto {\n      margin-right: auto !important;\n      margin-left: auto !important;\n    }\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .float#{$infix}-left  { float: right !important; }\n    .float#{$infix}-right { float: left !important; }\n    .float#{$infix}-none  { float: none !important; }\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n//\n// Text\n//\n\n.text-monospace { font-family: $font-family-monospace !important; }\n\n// Alignment\n\n.text-justify  { text-align: justify !important; }\n.text-wrap     { white-space: normal !important; }\n.text-nowrap   { white-space: nowrap !important; }\n.text-truncate { @include text-truncate; }\n\n// Responsive alignment\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .text#{$infix}-left   { text-align: right !important; }\n    .text#{$infix}-right  { text-align: left !important; }\n    .text#{$infix}-center { text-align: center !important; }\n  }\n}\n\n// Transformation\n\n.text-lowercase  { text-transform: lowercase !important; }\n.text-uppercase  { text-transform: uppercase !important; }\n.text-capitalize { text-transform: capitalize !important; }\n\n// Weight and italics\n\n.font-weight-light   { font-weight: $font-weight-light !important; }\n.font-weight-lighter { font-weight: $font-weight-lighter !important; }\n.font-weight-normal  { font-weight: $font-weight-normal !important; }\n.font-weight-bold    { font-weight: $font-weight-bold !important; }\n.font-weight-bolder  { font-weight: $font-weight-bolder !important; }\n.font-italic         { font-style: italic !important; }\n\n// Contextual colors\n\n.text-white { color: $white !important; }\n\n@each $color, $value in $theme-colors {\n  @include text-emphasis-variant(\".text-#{$color}\", $value);\n}\n\n.text-body { color: $body-color !important; }\n.text-muted { color: $text-muted !important; }\n\n.text-black-50 { color: rgba($black, .5) !important; }\n.text-white-50 { color: rgba($white, .5) !important; }\n\n// Misc\n\n.text-hide {\n  @include text-hide($ignore-warning: true);\n}\n\n.text-decoration-none { text-decoration: none !important; }\n\n.text-break {\n  word-break: break-word !important; // IE & < Edge 18\n  overflow-wrap: break-word !important;\n}\n\n// Reset\n\n.text-reset { color: inherit !important; }\n", "// Text truncate\n// Requires inline-block or block for proper styling\n\n@mixin text-truncate() {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n", "// stylelint-disable declaration-no-important\n\n// Typography\n\n@mixin text-emphasis-variant($parent, $color) {\n  #{$parent} {\n    color: $color !important;\n  }\n  @if $emphasized-link-hover-darken-percentage != 0 {\n    a#{$parent} {\n      @include hover-focus {\n        color: darken($color, $emphasized-link-hover-darken-percentage) !important;\n      }\n    }\n  }\n}\n", "// Hover mixin and `$enable-hover-media-query` are deprecated.\n//\n// Originally added during our alphas and maintained during betas, this mixin was\n// designed to prevent `:hover` stickiness on iOS-an issue where hover styles\n// would persist after initial touch.\n//\n// For backward compatibility, we've kept these mixins and updated them to\n// always return their regular pseudo-classes instead of a shimmed media query.\n//\n// Issue: https://github.com/twbs/bootstrap/issues/25195\n\n@mixin hover {\n  &:hover { @content; }\n}\n\n@mixin hover-focus {\n  &:hover,\n  &:focus {\n    @content;\n  }\n}\n\n@mixin plain-hover-focus {\n  &,\n  &:hover,\n  &:focus {\n    @content;\n  }\n}\n\n@mixin hover-focus-active {\n  &:hover,\n  &:focus,\n  &:active {\n    @content;\n  }\n}\n", "// CSS image replacement\n@mixin text-hide($ignore-warning: false) {\n  // stylelint-disable-next-line font-family-no-missing-generic-family-keyword\n  font: 0/0 a;\n  color: transparent;\n  text-shadow: none;\n  background-color: transparent;\n  border: 0;\n\n  @include deprecate(\"`text-hide()`\", \"v4.1.0\", \"v5\", $ignore-warning);\n}\n", "// \r\n// structure-rtl.scss\r\n//\r\n\r\n// topbar.scss\r\n\r\n.logo-box {\r\n    float: right;\r\n}\r\n\r\n.navbar-custom {\r\n    padding: 0 0 0 10px;\r\n\r\n    .topnav-menu {\r\n        >li {\r\n            float: right;\r\n        }\r\n\r\n        .nav-link {\r\n            direction: ltr;\r\n        }\r\n    }\r\n\r\n    /* Search */\r\n    .app-search {\r\n        margin-left: 20px;\r\n\r\n        .form-control {\r\n            padding-right: 20px;\r\n            padding-left: 0;\r\n            border-radius: 0 30px 30px 0 !important;\r\n        }\r\n\r\n        .input-group-append {\r\n            margin-right: 0;\r\n        }\r\n\r\n        .btn {\r\n            border-radius: 30px 0 0 30px !important;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/* Notification */\r\n.notification-list {\r\n\r\n    .noti-icon-badge {\r\n        left: 10px;\r\n        right: auto;\r\n    }\r\n\r\n    .notify-item {\r\n        padding: 12px 20px;\r\n\r\n        .notify-icon {\r\n            float: right;\r\n            margin-left: 10px;\r\n            margin-right: 0;\r\n        }\r\n\r\n        .notify-details,\r\n        .user-msg {\r\n            margin-left: 0;\r\n            margin-right: 45px;\r\n        }\r\n    }\r\n\r\n    .profile-dropdown {\r\n        .notify-item {\r\n            padding: 7px 20px;\r\n        }\r\n    }\r\n}\r\n\r\n.profile-dropdown {\r\n    i {\r\n        vertical-align: middle;\r\n        margin: 5px 0 0 10px;\r\n        float: right;\r\n    }\r\n}\r\n\r\n// page-title\r\n\r\n.page-title-box {\r\n    .page-title-right {\r\n        float: left;\r\n    }\r\n}\r\n\r\n\r\n// Left-sidebar\r\n\r\n.content-page {\r\n    margin-right: $leftbar-width;\r\n    margin-left: 0;\r\n}\r\n\r\n// Sidebar\r\n#sidebar-menu {\r\n    >ul {\r\n        >li {\r\n            >a {\r\n\r\n                i {\r\n                    margin: 0 3px 0 10px;\r\n                }\r\n\r\n                .drop-arrow {\r\n                    float: left;\r\n\r\n                    i {\r\n                        margin-left: 0;\r\n                    }\r\n                }\r\n            }\r\n\r\n            >ul {\r\n                padding-right: 40px;\r\n                padding-left: 0;\r\n\r\n                ul {\r\n                    padding-right: 20px;\r\n                    padding-left: 0;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .menu-arrow {\r\n        left: 20px;\r\n        right: auto;\r\n\r\n        &:before {\r\n            content: \"\\F141\";\r\n        }\r\n    }\r\n\r\n    li.active {\r\n        >a {\r\n            >span.menu-arrow {\r\n                transform: rotate(-90deg);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Enlarge menu\r\n.enlarged {\r\n\r\n    // Side menu\r\n    .left-side-menu {\r\n\r\n        // Sidebar Menu\r\n        #sidebar-menu {\r\n\r\n            >ul {\r\n                >li {\r\n\r\n                    >a {\r\n                        i {\r\n                            margin-left: 20px;\r\n                            margin-right: 5px;\r\n                        }\r\n\r\n                        span {\r\n                            padding-right: 25px;\r\n                            padding-left: 0;\r\n                        }\r\n                    }\r\n\r\n                    &:hover {\r\n\r\n                        >ul {\r\n                            right: $leftbar-width-collapsed;\r\n                            left: auto;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul {\r\n\r\n                    li {\r\n                        &:hover {\r\n                            >ul {\r\n                                right: 190px;\r\n                                margin-top: -36px;\r\n                            }\r\n                        }\r\n\r\n                        >a {\r\n                            span.pull-right {\r\n                                left: 20px;\r\n                                right: 0;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // Content Page\r\n    .content-page {\r\n        margin-right: $leftbar-width-collapsed !important;\r\n        margin-left: 0 !important;\r\n    }\r\n\r\n    //Footer\r\n    .footer {\r\n        left: 0 !important;\r\n        right: $leftbar-width-collapsed !important;\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n\r\n    .content-page,\r\n    .enlarged .content-page {\r\n        margin-right: 0 !important;\r\n    }\r\n}\r\n\r\n/* =============\r\n  Small Menu\r\n============= */\r\n\r\n.left-side-menu-sm {\r\n\r\n    .left-side-menu {\r\n        #sidebar-menu {\r\n            >ul {\r\n                ul {\r\n                    padding-right: 0;\r\n                }\r\n            }\r\n        }\r\n\r\n        &+.content-page {\r\n            margin-right: $leftbar-width-sm;\r\n            margin-left: 0;\r\n        }\r\n\r\n        +.content-page .footer {\r\n            left: auto;\r\n            right: $leftbar-width-sm;\r\n        }\r\n    }\r\n}\r\n\r\n.enlarged.left-side-menu-sm {\r\n    #wrapper {\r\n        .left-side-menu {\r\n            text-align: right;\r\n\r\n            ul {\r\n                li {\r\n                    a {\r\n                        i {\r\n                            margin-right: 3px;\r\n                            margin-left: 15px;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Leftbar with user\r\n.user-pro-dropdown {\r\n    margin-left: 0;\r\n    margin-right: 5%;\r\n}\r\n\r\n\r\n// footer.scss\r\n\r\n.footer {\r\n    left: 0;\r\n    right: $leftbar-width;\r\n}\r\n\r\n.footer-alt {\r\n    right: 0 !important;\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n    .footer {\r\n        right: 0 !important;\r\n    }\r\n}\r\n\r\n// right-sidebar.scss\r\n//\r\n\r\n.right-bar {\r\n    float: left !important;\r\n    left: -($rightbar-width + 10px);\r\n    right: auto;\r\n\r\n    .user-box {\r\n        .user-img {\r\n            .user-edit {\r\n                right: 0;\r\n                left: -5px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.right-bar-enabled {\r\n    .right-bar {\r\n        left: 0;\r\n        right: auto;\r\n    }\r\n}", "// \r\n// plugins-rtl.scss\r\n//\r\n\r\n\r\n// Nice select\r\n\r\n.nice-select {\r\n    float: right;\r\n}\r\n\r\n\r\n// Nestable-list\r\n\r\n.dd-item {\r\n    >button {\r\n        float: right;\r\n    }\r\n\r\n    .dd-list {\r\n        padding-right: 30px;\r\n        padding-left: 0;\r\n    }\r\n}\r\n\r\n.dd-list {\r\n    .dd3-handle {\r\n        float: right;\r\n    }\r\n}\r\n\r\n\r\n// Select 2\r\n\r\n.select2-container {\r\n    .select2-selection--single {\r\n        .select2-selection__rendered {\r\n            padding-right: 12px;\r\n        }\r\n\r\n        .select2-selection__arrow {\r\n            left: 3px;\r\n            right: auto;\r\n        }\r\n    }\r\n\r\n    .select2-selection--multiple {\r\n        .select2-selection__choice {\r\n            float: right;\r\n            margin-left: 5px;\r\n            margin-right: 0;\r\n        }\r\n    }\r\n\r\n    .select2-search--inline {\r\n        float: right;\r\n    }\r\n}\r\n\r\n\r\n// Multiple select\r\n\r\n.ms-container {\r\n    .ms-optgroup-label {\r\n        padding: 5px 5px 0px 0;\r\n    }\r\n}\r\n\r\n\r\n// Bootstrap select\r\n\r\n.bootstrap-select {\r\n    .dropdown-toggle {\r\n        &:before {\r\n            float: left;\r\n        }\r\n\r\n        .filter-option {\r\n            text-align: right;\r\n        }\r\n\r\n        .filter-option-inner {\r\n            padding-right: 0;\r\n            padding-left: inherit;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Parsley\r\n\r\n.parsley-errors-list {\r\n    >li {\r\n        padding-left: 0;\r\n        padding-right: 20px;\r\n\r\n        &:before {\r\n            left: auto;\r\n            right: 2px;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Quilljs\r\n\r\n.ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg {\r\n    left: 0;\r\n    right: auto;\r\n}\r\n\r\n.ql-editor {\r\n    direction: rtl;\r\n    text-align: right;\r\n}\r\n\r\n\r\n// X-ediatable \r\n\r\n.editable-buttons {\r\n    margin-left: 0;\r\n    margin-right: 7px;\r\n\r\n    .editable-cancel {\r\n        margin-left: 0;\r\n        margin-right: 7px;\r\n    }\r\n}\r\n\r\n\r\n// Foo table\r\n\r\n.footable.breakpoint>tbody>tr>td>span.footable-toggle {\r\n    padding-left: 5px;\r\n    padding-right: 0;\r\n}", "//\n// components-rtl.scss\n//\n\n\n// ribbons\n\n.ribbon-box {\n  .ribbon {\n    &:before {\n      right: 0;\n      left: auto;\n    }\n\n    &.float-left {\n      margin-right: -30px;\n      margin-left: auto;\n      border-radius: 3px 0 0 3px;\n    }\n\n    &.float-right {\n      margin-left: -30px;\n      margin-right: 0;\n      border-radius: 0 3px 3px 0;\n\n      &:before {\n        left: 0;\n        right: auto;\n      }\n    }\n  }\n\n  /* Ribbon two */\n  .ribbon-two {\n    right: -5px;\n    left: auto;\n    text-align: left;\n\n    span {\n      transform: rotate(45deg);\n      right: -21px;\n      left: auto;\n    }\n  }\n}\n\n\n// Inbox-widget\n\n.inbox-widget {\n  .inbox-item {\n    .inbox-item-img {\n      float: right;\n      margin-left: 15px;\n      margin-right: 0;\n    }\n\n    .inbox-item-date {\n      right: auto;\n      left: 5px;\n    }\n  }\n}\n\n\n// Chat widget\n\n.conversation-list {\n  .chat-avatar {\n    float: right;\n  }\n\n  .ctext-wrap {\n    &:after {\n      left: 99%;\n      right: auto;\n      margin-left: 0;\n      margin-right: -1px;\n      border-right-color: transparent;\n      border-left-color: $gray-200;\n    }\n  }\n\n  .conversation-text {\n    float: right;\n    margin-right: 12px;\n    margin-left: 0;\n  }\n\n  .odd {\n    .chat-avatar {\n      float: left !important;\n    }\n\n    .conversation-text {\n      float: left !important;\n      margin-right: 0;\n      margin-left: 12px;\n      text-align: left;\n    }\n\n    .ctext-wrap {\n      &:after {\n        border-color: transparent;\n        border-left-color: transparent;\n        border-right-color: #fef5e4;\n        border-top-color: #fef5e4;\n        left: auto !important;\n        right: 99% !important;\n      }\n    }\n  }\n}\n\n\n// Custom-radio\n\n.checkbox {\n  label {\n    padding-right: 8px;\n    padding-left: 0;\n\n    &::before {\n      left: auto;\n      right: 0;\n      margin-left: 0;\n      margin-right: -18px;\n    }\n\n    &::after {\n      left: auto;\n      right: 0;\n      margin-right: -18px;\n      margin-left: 0;\n      padding-left: 0;\n      padding-right: 3px;\n    }\n  }\n\n  input[type=\"checkbox\"]:checked+label {\n    &::after {\n      left: auto;\n      right: 7px;\n      transform: rotate(45deg);\n    }\n  }\n}\n\n.checkbox.checkbox-single {\n  label {\n    &:before {\n      margin-right: 0;\n    }\n\n    &:after {\n      margin-right: 0;\n    }\n  }\n}\n\n\n// custom-radio\n\n.radio {\n  label {\n    padding-left: 0;\n    padding-right: 8px;\n\n    &::before {\n      left: auto;\n      right: 0;\n      margin-left: 0;\n      margin-right: -18px;\n    }\n\n    &::after {\n      left: 0;\n      right: 6px;\n      margin-left: 0;\n      margin-right: -20px;\n    }\n  }\n}", "// \r\n// pages-rtl.scss\r\n//\r\n\r\n\r\n// taskboard\r\n.taskList .checkbox {\r\n    margin-right: 20px;\r\n    margin-left: 0;\r\n    margin-top: 5px;\r\n}\r\n\r\n\r\n// email\r\n\r\n.inbox-leftbar {\r\n    float: right;\r\n    padding: 0 10px 20px 20px;\r\n}\r\n\r\n.inbox-rightbar {\r\n    margin: -1.5rem 250px -1.5rem 0;\r\n    border-right: 5px solid $body-bg;\r\n    border-left: 0;\r\n    padding: 1.5rem 25px 1.5rem 0;\r\n}\r\n\r\n.message-list {\r\n    display: block;\r\n    padding-right: 0;\r\n    li {\r\n        .col-mail {\r\n            float: right;\r\n        }\r\n        .col-mail-1 {\r\n            width: 320px;\r\n            .star-toggle,\r\n            .checkbox-wrapper-mail,\r\n            .dot {\r\n                float: right;\r\n            }\r\n            .checkbox-wrapper-mail {\r\n                margin: 15px 20px 0 10px;\r\n            }\r\n            .star-toggle {\r\n                margin-right: 10px;\r\n                margin-left: 0;\r\n            }\r\n            .title {\r\n                right: 100px;\r\n                left: 0;\r\n            }\r\n        }\r\n        .col-mail-2 {\r\n            right: 320px;\r\n            left: 0;\r\n            .subject {\r\n                right: 0;\r\n                left: 110px;\r\n            }\r\n            .date {\r\n                left: 0;\r\n                right: auto;\r\n                padding-right: 10px;\r\n                padding-left: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 648px) {\r\n    .inbox-rightbar {\r\n        margin-right: 0;\r\n        border: 0;\r\n        padding-right: 0;\r\n    }\r\n    .message-list {\r\n        li .col-mail-1  {\r\n            .checkbox-wrapper-mail {\r\n                margin-right: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 520px) {\r\n    \r\n    .message-list li {\r\n        .col-mail-1 {\r\n            .title {\r\n                right: 80px;\r\n            }\r\n        }\r\n        .col-mail-2 {\r\n            right: 160px;\r\n            .date {\r\n                text-align: left;\r\n                padding-left: 10px;\r\n                padding-right: 20px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Sitemaps\r\n\r\n.sitemap {\r\n    ul {\r\n        margin-right: 1rem;\r\n        margin-left: 0;\r\n\r\n        li {\r\n\r\n            a {\r\n                margin-left: 0;\r\n                margin-right: 2.75rem;\r\n            }\r\n\r\n            &:before {\r\n                border-left: transparent;\r\n                border-right: 1px solid rgba(152, 166, 173, 0.5);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// FAQ\r\n\r\n.faq-question-q-box {\r\n    float: right;\r\n}\r\n\r\n.faq-question {\r\n    margin-left: 0;\r\n    margin-right: 50px;\r\n}\r\n\r\n.faq-answer {\r\n    margin-left: 0;\r\n    margin-right: 50px;\r\n}"]}