// @ts-ignore
export const options: Partial | (() => Partial) = null;


import {Directive, ElementRef, forwardRef, HostListener, Input} from '@angular/core';
import {AbstractControl, NG_VALIDATORS, Validator} from "@angular/forms";

@Directive({
  // tslint:disable-next-line:directive-selector
  selector: '[logradouroValido][ngModel]',
  providers: [
    { provide: NG_VALIDATORS, useExisting: LogradouroContatoValidator, multi: true }
  ]
})
// tslint:disable-next-line:directive-class-suffix
export class LogradouroContatoValidator  implements  Validator {
  @Input() sistema: string;

  constructor(private el: ElementRef) { }

  validate(c: AbstractControl): { [key: string]: any } {
    const logradouro = c.value;
    let erros: any;

    if(this.sistema === 'ecletica')
      if(logradouro && (logradouro.trim().split(' ').length < 2))
        erros = {"logradouroInvalido":  'Informe um endereço válido. '}


    if( erros ) {
      this.el.nativeElement.setCustomValidity('logradouro invalido');
    } else {
      this.el.nativeElement.setCustomValidity('');
    }

    return erros;
  }

}

@Directive({
  // tslint:disable-next-line:directive-selector
  selector: '[trocoMinimo][ngModel]',
  providers: [
    { provide: NG_VALIDATORS, useExisting: ValorTrocoPedido, multi: true }
  ]
})
// tslint:disable-next-line:directive-class-suffix
export class ValorTrocoPedido  implements  Validator {
  @Input() trocoMinimo: number;

  constructor(private el: ElementRef) {
  }

  validate(c: AbstractControl): { [key: string]: any } {
    const valor = c.value;
   // console.log('troco para: ' + valor)
    //console.log('troco minimo: ' + this.trocoMinimo)

    const erros = (!valor || Number(valor) < Number(this.trocoMinimo)) ? {"trocoMinimo": true} : null;

    if( erros ) {
      this.el.nativeElement.setCustomValidity('erro de mínimo');
    } else {
      this.el.nativeElement.setCustomValidity('');
    }

    return erros;
  }
}



@Directive({
  // tslint:disable-next-line:directive-selector
  selector: '[cpfValido][ngModel]',
  providers: [
    { provide: NG_VALIDATORS, useExisting: forwardRef(() => CPFValidator), multi: true }
  ]
})
export class CPFValidator  implements  Validator {
  @Input("required") required: any;
  constructor() {   }
  validate(c: AbstractControl) {
    if (!this.required && !c.value)
      return null;

    let valido = this.cpfValido(c.value)
    if(valido) return null;

    return { cpfInvalido: true }
  }

  cpfValido(cpf){
    if(!cpf) return false;

    let numeros, digitos, soma, i, resultado, digitos_iguais;

    cpf = cpf.replace(/\D/g, "");

    digitos_iguais = 1;
    if (!cpf || cpf.length < 11)
      return false;
    for (i = 0; i < cpf.length - 1; i++)
      if (cpf.charAt(i) !== cpf.charAt(i + 1))
      {
        digitos_iguais = 0;
        break;
      }
    if (!digitos_iguais)
    {
      numeros = cpf.substring(0, 9);
      digitos = cpf.substring(9);
      soma = 0;
      for (i = 10; i > 1; i--)
        soma += numeros.charAt(10 - i) * i;
      resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;
      if (resultado !== Number(digitos.charAt(0)))
        return false;
      numeros = cpf.substring(0, 10);
      soma = 0;
      for (i = 11; i > 1; i--)
        soma += numeros.charAt(11 - i) * i;
      resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;
      if (resultado !== Number( digitos.charAt(1)))
        return false;
      return true;
    }
    else
      return false;
  }

}
@Directive({
  // tslint:disable-next-line:directive-selector
  selector: '[nomeCompleto][ngModel]',
  providers: [
    { provide: NG_VALIDATORS, useExisting: forwardRef(() => NomeConmpletoValidator), multi: true }
  ]
})
export class NomeConmpletoValidator  implements  Validator {
  // tslint:disable-next-line:no-input-rename
  @Input() validarNome: any = true;
  constructor() {
  }

  validate(c: AbstractControl): { [key: string]: any } {
    let valido = this.nomeValido(c.value)

    if(valido) return null;

    return { nomeCompleto: true }

  }

  nomeValido(nome: string){
    if( !this.validarNome) return true;

    const palavras = nome ? nome.trim().split(/\s+/) : [];

    if(!nome || palavras.length <= 1)
      return false;

    // Rejeita palavras abreviadas (ex: "J.", "M", "C.")
    for (const palavra of palavras) {
      if (  (palavra.endsWith('.') || palavra.length === 1))
        return false;
    }

    return true;
  }
}


@Directive({
  // tslint:disable-next-line:directive-selector
  selector: '[nomeValido][ngModel]',
  providers: [
    { provide: NG_VALIDATORS, useExisting: forwardRef(() => NomeValidoValidator), multi: true }
  ]
})
export class NomeValidoValidator  implements  Validator {
  constructor() {
  }

  validate(c: AbstractControl): { [key: string]: any } {

    let valido = this.nomeValido(c.value)

    if(valido) return null;

    return { nomeValido: true }
  }

  nomeValido(nome){
    const possuiCaracteresInvalidos = (/[0-9@]/g.test(nome));

    return !possuiCaracteresInvalidos;
  }
}



@Directive({
  // tslint:disable-next-line:directive-selector
  selector: '[validadeCartao][ngModel]',
  providers: [
    { provide: NG_VALIDATORS, useExisting: forwardRef(() => ValidadeCartaoValidator), multi: true }
  ]
})
export class ValidadeCartaoValidator implements Validator {
  constructor() {
  }

  validate(c: AbstractControl): { [key: string]: any } {
    if (!c.value) {
      return null; // Se não há valor, deixa a validação required cuidar disso
    }

    let valido = this.validadeValida(c.value);

    if (valido) return null;

    return { validadeExpirada: true };
  }

  validadeValida(dataValidade: Date): boolean {
    if (!dataValidade) return false;

    const hoje = new Date();
    const mesAtual = hoje.getMonth(); // 0-11
    const anoAtual = hoje.getFullYear();

    const mesValidade = dataValidade.getMonth(); // 0-11
    const anoValidade = dataValidade.getFullYear();

    // Se o ano da validade é maior que o atual, é válido
    if (anoValidade > anoAtual) {
      return true;
    }

    // Se o ano da validade é igual ao atual, verifica o mês
    if (anoValidade === anoAtual) {
      return mesValidade >= mesAtual;
    }

    // Se o ano da validade é menor que o atual, é inválido
    return false;
  }
}

@Directive({
  // tslint:disable-next-line:directive-selector
  selector: '[tamanhoMax]'
})
export class MaxlengthDirective {
  @Input() tamanhoMax: number;
  constructor(private el: ElementRef) {  }

  @HostListener('input')
  onInput() {
    let value = this.el.nativeElement.value;
    if (value.length > this.tamanhoMax) {
      value = value.substr(0, this.tamanhoMax);
      this.el.nativeElement.value = value;
    }
  }
}
