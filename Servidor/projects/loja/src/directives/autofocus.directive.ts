import { AfterContentInit, Directive, ElementRef, Input } from '@angular/core';

@Directive({
  selector: '[appAutoFocus]'
})
export class AutofocusDirective implements AfterContentInit {

  @Input() public autoFocus = true;

  public constructor(private el: ElementRef) {

  }

  public ngAfterContentInit() {
    if ( this.autoFocus ) {
      setTimeout( () => {
        let input =  this.el.nativeElement;
        if(input.localName === 'kendo-textbox')
          for(let i = 0; i < input.children.length; i++)
            if(input.children[i].localName === 'input')
                 input =   input.children[i]

        input.focus();

      }, 10);
    }
  }

}
