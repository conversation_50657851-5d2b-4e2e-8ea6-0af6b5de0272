import {Directive, ElementRef, forwardRef, Input} from '@angular/core';
import {AbstractControl, NG_VALIDATORS, Validator} from "@angular/forms";
import {FormaDeEntrega} from "../objeto/FormaDeEntrega";

@Directive({
  selector: '[pagseguro][ngModel]',
  providers: [
    { provide: NG_VALIDATORS, useExisting: ValidadorPagamentoPagSeguroDirective, multi: true }
  ]
})
export class ValidadorPagamentoPagSeguroDirective  implements  Validator {
  @Input() pagseguro: any;

  constructor(private el: ElementRef) {
  }

  validate(c: AbstractControl): { [key: string]: any } {
    const pedido = this.pagseguro.pedido;
    const exigeCartao = this.pagseguro.exigeCartao;
    const dadosCartao = this.pagseguro.dadosCartao;
    const exigeEndereco = this.pagseguro.exigeEndereco;

    const valor = c.value;

    let erros: any = null;

    if( exigeCartao && !pedido.pagamento.dadosCartao ) {
      erros = {dados_cartao_obrigatorio: 'dados_cartao_obrigatorio'};
    }
    else if( exigeCartao && pedido.entrega.formaDeEntrega ) {

      const dadosCartaoPedido = pedido.pagamento.dadosCartao;
      if(exigeEndereco && dadosCartaoPedido && pedido.entrega.formaDeEntrega === FormaDeEntrega.RETIRAR && !dadosCartaoPedido.endereco) {
        erros = {endereco_obrigatorio: 'endereco_obrigatorio'};
      }
    }

    return erros;
  }
}
