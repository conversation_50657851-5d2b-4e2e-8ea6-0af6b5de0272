<div *ngIf="exibirTopo && empresa.tema === 'quiosque'">
  <img src="/assets/fidelidade/alsultan-tela-totem-horizontal.jpg" style="width: 100%;"/>
</div>

<div class="container_total {{empresa.tema}}" *ngIf="exibirTopo && empresa.tema !== 'quiosque'" >
  <div class="cartao conteudo topo pb-1">
    <div class="dados_empresa">
      <img class="imagem_empresa" src="https://promokit.com.br/images/empresa/{{empresa.logo}}"/>
      <div class="detalhes_empresa">
      <span class="nome_empresa" *ngIf="exibirTitulo">
        {{empresa.nome}}
      </span>
        <span class="endereco abreviar mb-1" [class.font-14]="!exibirTitulo">
        {{empresa.descricaoEndereco ? empresa.descricaoEndereco : empresa.endereco}}
        </span>
        <a class="whatsapp" [href]="'http://wa.me/55' + empresa?.numeroWhatsapp?.whatsapp" [hidden]="!empresa?.numeroWhatsapp ||  empresa?.numeroWhatsapp?.ocultar">

          <i class="fab fa-whatsapp fa-lg mr-1 icone tam1 icone-whatsapp"></i>
          <ng-container *ngIf="empresa && empresa?.numeroWhatsapp && empresa?.numeroWhatsapp?.whatsapp.length === 11">
            {{empresa?.numeroWhatsapp?.whatsapp | mask: '(99) 9-9999-9999'}}
          </ng-container>
          <ng-container *ngIf="empresa && empresa?.numeroWhatsapp && empresa?.numeroWhatsapp?.whatsapp.length === 10">
            {{empresa?.numeroWhatsapp?.whatsapp | mask: '(99) 9999-9999'}}
          </ng-container>
        </a>
        <div class="horario" style="line-height: 12px;">
          <div class="  cpointer" (click)="verHorarios(horario)"
               *ngFor="let horario of empresa.horariosDoServico; let i = index;" style="display: inline-block;">


            <div class="descricao float-left">
              <i class="{{horario.icone}} status" kendoTooltip title="{{horario.descricao}}" [ngClass]="{ 'fechado': !horario.estaAberta}"></i>
              <span  > {{horario.label}}:</span>
              <span  >    {{horario.descricaoHorario}}</span>
            </div>
          </div>

          &nbsp;
          <div style="display: inline-block;top: -5px;float: right;margin-right: 5px;position: relative;">
            <a [href]="'https://instagram.com/' + empresa.instagram" target="_blank" *ngIf="empresa.instagram">
              <i class="fab fa-instagram font-24"></i>&nbsp;&nbsp;
            </a>
            <a [href]="'https://facebook.com/' + empresa.facebook" target="_blank" *ngIf="empresa.facebook">
              <i class="fab fa-facebook-f font-24"></i>
            </a>
          </div>
        </div>

        <div class="horario mt-1" *ngIf="empresa.descricaoTempoEntrega || empresa.descricaoTempoRetirada">
          <div class="descricao">
                      <span class=" ml-0  mt-1" *ngIf="empresa.descricaoTempoEntrega"  >
                <i class="fas fa-motorcycle mr-1 fa-lg"></i>{{empresa.descricaoTempoEntrega}}
             </span>
            <span class="  mt-1" *ngIf="empresa.descricaoTempoRetirada"  >
                <i class="ml-1 fas fa-store mr-1"></i>{{empresa.descricaoTempoRetirada}}
             </span>
          </div>
        </div>
      </div>

      <div class="" *ngIf="exibirFormaDeEntrega() && pedido.entrega && pedido.entrega.formaDeEntrega">
        <div class="cpointer" (click)="mudarFormaDeEntrega()">
          <div class="mt-1" style="color: #3e3e3e;" *ngIf="pedido.entrega.formaDeEntrega === 'Retirar'">
            <div class="ml-0 d-inline-block abreviar" style="max-width: 170px;">
              <i class="fas fa-map-marker-alt"></i> Retirar Na Loja &nbsp;
            </div>
            <i class="fas fa-chevron-down" style="top: -5px;position: relative"></i>
          </div>

          <div class="mt-1" style="color: #3e3e3e;" *ngIf="pedido.entrega.formaDeEntrega === 'Receber em casa'">
            <div class="ml-0 d-inline-block abreviar" style="max-width: calc(100% - 45px);">
              <i class="fas fa-map-marker-alt"></i> {{pedido.entrega.endereco.obtenhaEnderecoCompleto()}} &nbsp;
            </div>
            <i class="fas fa-chevron-down" style="top: -5px;position: relative"></i>
          </div>
        </div>
      </div>
    </div>

    <div class="descricao_empresa" *ngIf="!topoReduzido && empresa.descricao">
      {{empresa.descricao}}
    </div>
  </div>

  <div *ngIf="exibirBannerTema && empresa.tema && empresa.tema === 'black_friday_2022'">
    <img src="/assets/images/banner_blackfriday2022_mobile.png" style="width: 100%"/>
  </div>

  <div *ngIf="exibirBannerTema && empresa.tema && empresa.tema === 'cacau_show'">
    <img src="/assets/images/banner_cacau_show_mobile.png" style="width: 100%"/>
  </div>

</div>

<div *ngIf="exibirBannerTema && empresa.tema"
     class="tema_{{empresa.tema}}">
</div>


<div class="fidelidade saldo" *ngIf="carregou && empresa && usuario.saldoFidelidade >=0 && !usuario.idCartao"
     class="{{empresa.tema}}" [hidden]="!exibirTopo">
  <div  >
    <h5 class="font-16">
      <b>Cashback </b><span class="font-16 ml-2"> {{usuario.saldoDescricao}}</span>
    </h5>
  </div>
</div>
