$azul: #0000dd;
$verde: #1a7200;
$rosa: #d64860;
$roxo: #b264da;
$roxo2: #5b1da6;
$vermelho: #7e0101;
$vermelhoclaro: #CE2400;
$preto: #000;
$pretoclaro: #343a40;
$marrom: #593F33;
$marromclaro: #775748;
$branco: #fff;
$ouro: #b57f33;
$amarelo:  #fce101;
.badge-dark{
  margin-left: -15px;
  margin-right: -15px;
  line-height: 15px;
  background: rgba(38, 38, 38, 0.8);
}
.container_total {
  margin-left: -12px;
  margin-right: -12px;

  .fundo-fechado {
    position: absolute;
    background: rgba(38, 38, 38, 0.8);
    top: 0;
    height: 100%;
    width: 100%;
    z-index: 1000;
    text-align: center;
    color: #fff;
    font-size: 20px;
    padding-top: 15%;
  }

  .fa-instagram {
    color: #B92468;
  }

  .fa-facebook-f {
    color: #0D8BF1;
  }

  &.carnaval{
    background: #D825A9  !important;

    .dados_empresa, .cartao.conteudo, &.fidelidade{
      background: #D825A9 !important;
    }

    h1,.endereco, .text-blue, .nome_empresa, .fa-instagram, .fa-facebook-f,
    .forma_entrega, .descricao span, .descricao_empresa{
      color: #fff000 !important;
    }

    .conteudo {
      .whatsapp {
        color: #fff000  !important;
      }
    }

    .horario .status {
      color: #fff000 !important;
    }

    .btn-outline-blue {
      border-color: #fff000  !important;
      color:  #fff000;
    }
  }

  &.tema-personalizado {
    background: var(--cor-fundo, $roxo2) !important;

    .dados_empresa, .cartao.conteudo, &.fidelidade {
      background: var(--cor-fundo, $roxo2) !important;
    }

    .descricao_empresa {
      color: var(--cor-texto-topo, $amarelo) !important;
    }

    h1, .endereco, .text-blue, .nome_empresa, .fa-instagram, .fa-facebook-f,
    .forma_entrega, .descricao span, .status, .whatsapp, .icone-whatsapp {
      color: var(--cor-texto-topo, $amarelo) !important;
    }

    .horario {
      .status {
        color: var(--cor-texto-topo, $amarelo) !important;

        &.fechado {
          color: var(--cor-texto-topo, $amarelo) !important;
        }
      }
    }

    .btn-outline-blue {
      border-color: var(--cor-botao, $amarelo) !important;
      color: var(--cor-texto-botao, $amarelo);
    }
  }

  &.black_friday_2022 {
    background: $preto;
    box-shadow: inset 0 -1px 0 $preto;

    .cartao.conteudo {
      border-bottom-left-radius: 10px;
      border-bottom-right-radius: 10px;
      box-shadow: none;
    }

    .conteudo.topo {
      background: $preto;
      border-radius: 0;
    }

    .nome_empresa {
      color: #F6A844;
    }

    .enderecos {
      color: #999;
    }

    .forma_entrega {
      color: #999;

      .endereco {
        color: #999;
      }
    }

    .text-blue {
      color: #999 !important;
    }

    .descricao {
      color: #999;
    }

    .fa-instagram {
      color: #fff;
    }

    .fa-facebook-f {
      color: #fff;
    }

    .btn-outline-blue {
      border-color: #999;
      color: #fff;
    }

    .conteudo {
      .whatsapp {
        color: #999;
      }
    }

    .tempos_entrega {
      color: #999;
    }
  }

  &.black_friday, &.black {
    .conteudo.topo {
      background:$preto;
      border-radius: 0;
    }

    .conteudo {
      box-shadow: inherit !important;
    }

    .nome_empresa {
      color: #fff;
    }
    .descricao_empresa {
      color: #e3e3e3;
    }
    .endereco {
      color: #e3e3e3;
    }

    .descricao {
      color: #e3e3e3;
    }

    .whatsapp {
      color: #fff;
    }

    .fa-instagram {
      color: #fff;
    }

    .fa-facebook-f {
      color: #fff;
    }
  }

  &.copa_2022 {
    .conteudo.topo {
      background: #1a7200;
      border-radius: 0;
    }

    .linha {
      border-bottom: solid 1px #ffd80e;
    }

    .conteudo {
      box-shadow: inherit !important;
    }

    .nome_empresa {
      color: #ffd80e;
    }
    .descricao_empresa {
      color: #ffd80e;
    }
    .endereco {
      color: #ffd80e;
    }

    .descricao {
      color: #ffd80e;
    }

    .whatsapp {
      color: #ffd80e;
    }

    .fa-instagram {
      color: #ffd80e;
    }

    .fa-facebook-f {
      color: #ffd80e;
    }
  }

  &.natal {
    .conteudo.topo {
      background: $vermelho;
      border-radius: 0;
    }

    .linha {
      border-bottom: solid 1px $vermelho;
    }

    .conteudo {
      box-shadow: inherit !important;
    }

    .nome_empresa {
      color: #f5cb89;
    }
    .descricao_empresa {
      color: #f5cb89;
    }
    .endereco {
      color: #f5cb89;
    }

    .descricao {
      color: #f5cb89;
    }

    .whatsapp {
      color: #f5cb89;
    }

    .fa-instagram {
      color: #f5cb89;
    }

    .fa-facebook-f {
      color: #f5cb89;
    }
  }

  &.arraia {
    .conteudo.topo {
      background: #CE2400;
      border-radius: 0;
    }

    .linha {
      border-bottom: solid 1px #CE2400;
    }

    .conteudo {
      box-shadow: inherit !important;
    }

    .nome_empresa {
      color: #ffffff;
    }
    .descricao_empresa {
      color: #ffffff;
    }
    .endereco {
      color: #ffffff;
    }

    .descricao {
      color: #ffffff;
    }

    .whatsapp {
      color: #ffffff;
    }

    .fa-instagram {
      color: #ffffff;
    }

    .fa-facebook-f {
      color: #ffffff;
    }
  }

  &.ano_novo {
    .conteudo.topo {
      background: #030303;
      border-radius: 0;
    }

    .linha {
      border-bottom: solid 1px #362900;
    }

    .conteudo {
      box-shadow: inherit !important;
    }

    .nome_empresa {
      color: #f1dfa5;
    }
    .descricao_empresa {
      color: #f1dfa5;
    }
    .endereco {
      color: #f1dfa5;
    }

    .descricao {
      color: #f1dfa5;
    }

    .whatsapp {
      color: #f1dfa5;
    }

    .fa-instagram {
      color: #f1dfa5;
    }

    .fa-facebook-f {
      color: #f1dfa5;
    }
  }

  &.pascoa {
    .conteudo.topo {
      background: $marromclaro;
      border-radius: 0;
    }

    .linha {
      border-bottom: solid 1px #7e0101;
    }

    .conteudo {
      box-shadow: inherit !important;
    }

    .nome_empresa {
      color: #fff;
    }
    .descricao_empresa {
      color: #fff;
    }
    .endereco {
      color: #fff;
    }

    .descricao {
      color: #fff;
    }

    .whatsapp {
      color: #fff;
    }

    .fa-instagram {
      color: #fff;
    }

    .fa-facebook-f {
      color: #fff;
    }
  }

  &.dia_maes {
    .conteudo.topo {
      background: #BD3C37;
      border-radius: 0;
    }

    .linha {
      border-bottom: solid 1px #7e0101;
    }

    .conteudo {
      box-shadow: inherit !important;
    }

    .nome_empresa {
      color: #fff;
    }
    .descricao_empresa {
      color: #fff;
    }
    .endereco {
      color: #fff;
    }

    .descricao {
      color: #fff;
    }

    .whatsapp {
      color: #fff;
    }

    .icone-whatsapp {
      filter: brightness(0) invert(1);
    }

    .fa-instagram {
      color: #fff;
    }

    .fa-facebook-f {
      color: #fff;
    }
  }

  &.dia_pais {
    .conteudo.topo {
      background:$azul;
      border-radius: 0;
    }

    .linha {
      border-bottom: solid 1px #7e0101;
    }

    .conteudo {
      box-shadow: inherit !important;
    }

    .nome_empresa {
      color: #fff;
    }
    .descricao_empresa {
      color: #fff;
    }
    .endereco {
      color: #fff;
    }

    .descricao {
      color: #fff;
    }

    .whatsapp {
      color: #fff;
    }

    .icone-whatsapp {
      filter: brightness(0) invert(1);
    }

    .fa-instagram {
      color: #fff;
    }

    .fa-facebook-f {
      color: #fff;
    }
  }

  &.chinainbox{
    .conteudo.topo {
      background: #e52f26;
      color: #fff;
      box-shadow: none !important;
    }

    .bolinha.fechado {
      background: #FF9800;
    }

    .status.fechado  {
      color: #FF9800
    }

    .nome_empresa, .descricao_empresa , .endereco, .descricao,
    .whatsapp,  .icone-whatsapp,  .fa-instagram ,  .fa-facebook-f{
      color: #fff;
    }

    .icone-whatsapp {
      filter: brightness(0) invert(1);
    }

  }

  &.dia_namorados {
    .conteudo.topo {
      background: $roxo;
      border-radius: 0;
    }

    .linha {
      border-bottom: solid 1px $roxo;
    }

    .conteudo {
      box-shadow: inherit !important;
    }

    .nome_empresa {
      color: #fff;
    }
    .descricao_empresa {
      color: #fff;
    }
    .endereco {
      color: #fff;
    }

    .descricao {
      color: #fff;
    }

    .whatsapp {
      color: #fff;
    }

    .icone-whatsapp {
      filter: brightness(0) invert(1);
    }

    .fa-instagram {
      color: #fff;
    }

    .fa-facebook-f {
      color: #fff;
    }
  }

  &.cacau_show {
    background-color: var(--cor-fundo-elementos);
    border-bottom: 1px solid var(--cor-borda);

    .logo {
      color: var(--cor-texto-primaria);
    }

    .menu {
      background-color: var(--cor-fundo-site);

      a {
        color: var(--cor-texto-secundaria);

        &:hover, &.active {
          color: var(--cor-destaque);
        }
      }
    }

    .busca {
      background-color: var(--cor-fundo-site);
      border: 1px solid var(--cor-borda);

      input {
        background-color: transparent;
        color: var(--cor-texto-primaria);
      }

      i {
        color: var(--cor-texto-secundaria);
      }
    }

    .carrinho-icon {
      color: var(--cor-destaque);
    }
  }
}


.chinainbox.fidelidade.saldo{
  border-top: 1px solid #fff;
}

.preco {
  color: #6db31b;
}

.produto {
border-bottom: solid 1px #e8e8e8;
}

.capa_empresa {
height: 250px;
background-size: cover;
}

.capa_empresa.centralizada {
z-index: 10;
background-repeat: no-repeat;
background-position-x: center;
background-size: cover;
}

.cartao {
  background: white;
  margin-left: auto;
  margin-right: auto;
  padding: 15px;
}

.cartao.conteudo {
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 0px;
  border-bottom: 1px solid var(--cor-borda);
}

.cartao.conteudo.topo {
  margin-top: 0px;
  width: 100%;
  position: relative;;
  z-index: 1000;
}

/*
.cartao.conteudo {
box-shadow: 0 4px 10px -2px #E2E3E3;
min-height: 190px;
border-top: 0;
border-bottom-left-radius: 10px;
border-bottom-right-radius: 10px;
}
*/


.cartao.semborda {
  margin-top: 20px;
  border: 0 none;
  background-color: transparent;
  padding: 0;
}

.bg-content {
  display: none;
}



.imagem_empresa {
  width: 120px;
  height: 120px;
  float: left ;
}

.detalhes_empresa  {
  float:left;
  margin-left: 10px;
  display: inline-block;
  width: calc(100% - 130px);
}

.nome_empresa {
  font-size: 24px;
  color: black;
  font-weight: 500;
  display: block;
  line-height: 1.2;
  max-height: 1.2em;
  overflow: hidden;
}

.endereco {
  font-size: 11px;
  display: block;
  width: 100%;
}

.whatsapp {
  display: inline-block;
  margin-bottom: 5px;
  font-size: 0.815rem;
}

.whatsapp span{
  font-size:12px;
  font-weight: 600;

  color: #199D0F
}

.dados_empresa {
  min-height: 90px;
  overflow: hidden;
}

.linha {
  border-bottom: #EFEFEF solid 1px;
}

.descricao_empresa {
  margin: 10px;
  margin-left: 0px;
  margin-bottom: 0px;
  font-size: 12px;
  font-weight: 400;

}

.menu {
  color: #525252;
  margin-top: 15px;

}

.brinde {
  margin-top: 10px;
}
.valor {
  position: absolute;
  color: white;
  font-size: 20px;
  top: 10px;
  width: 100%;
  text-align: center;
}

.row {
  padding-left: 5px;
  padding-right: 5px;
}

.container_selo {
  position: relative;
}

.brinde {
  text-align: center;
  position: relative;
}

.preco_troca {
  font-weight: 600;
}

.nome_brinde {
  display: inline-block;
  margin-top: 5px;
  font-size: 16px;
  background: #4b4b4b;
  color: white;
  margin-left: 2px;
  padding: 5px 10px 5px 10px;
  border-radius: 50px;
  font-weight: 200;
}

.faltam_selos {
  color: #3e48bc;
  margin-bottom: 10px;
}

.container-scroll {
  overflow-x: scroll;
  overflow-y: hidden;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
  margin-right: -20px;
}

.container-scroll .col {
  margin-left: 5px;
  margin-right: 5px;
}


.foto_brinde {
  display: block;
  float:none;
  margin:0 auto;
  border-radius: 30px;
  margin-top: 5px;
  width: 100%;
}


.foto_ambiente {
  display: block;
  float:none;
  margin:0 auto;
  border-radius: 20px;
}


.container-scroll .caixa_brinde {
  border-radius: 5px;
  margin-bottom: 10px;
  height: 250px !important;
  margin-left: -3px;
  margin-right: -3px;
  padding-top: 5px;
  background: white;
}


.container-scroll .foto_brinde {
  width: unset;
  width: 100%;
}

.nome_brinde_pontos {
  font-size: 15px;
  font-weight: 600;

}

.container-scroll .preco_troca {
  font-size: 11px;
  font-weight: 400;
  line-height: 1em;
}

.container-scroll .preco_troca.nao_atingiu {
  color: #F67682;
}

.container-scroll .preco_troca.atingiu {
  color: #6DB31B;
  margin-right: -20px;
}

.botoes {
  margin: 20px;
  text-align: center;
  font-size: 16px;
  font-weight: 400;
}

.botao {
  padding: 15px;
}

.botao.verde{
  background: #6DB31B;
  color:  white;
}

.botao.azul {
  border: #1c95d4 solid 1px;
  margin-top: 10px;
  color: #1c95d4;
}

.icone.whatsapp {
  width: 16px;
  display: inline-block;
  margin: 0
}


.float{
  position:fixed;
  width:60px;
  height:60px;
  bottom:40px;
  right:40px;
  background-color:#25d366;
  color:#FFF;
  border-radius:50px;
  text-align:center;
  font-size:30px;
  box-shadow: 2px 2px 3px #999;
  z-index:100;
}

.my-float{
  margin-top:16px;
}

.container_imagem {
  overflow: hidden;
  display: flex;
  max-width: 100%;
}

.azul .coracao{
  display: inline-block;
  fill: white;
  width: 16px;
  height: 16px;
  vertical-align: middle;
  margin: 0;
}

.azul {
  color: #3B86FF;
  font-weight: bold;
}


.bolinha {
  margin: 5px;
  width: 8px;
  height: 8px;
  background: #6DB31B;
  border-radius: 100px;
  float: left;
}

.horario .status{
  color: #6DB31B;
}

.horario {

  width: 100% !important;
}

.bolinha.fechado {
  background: red;
}

.horario .status.fechado  {
  color: red;
}


.horario .descricao {
  font-size: 11px;
  font-weight: bold;
}

.icon-fechar {
  width: 32px;
  height: 32px;
  float: left;
  margin-left: 5px;
}

.slides-fotos h3, .slides-produtos h3{
  text-align: center;
  line-height: 32px;
}

.slides-fotos, .slides-produtos {
  position: fixed;
  overflow: auto;
  z-index: 1000;
  top:0px;
  background: white;
  height: 100%;
  width: 100%;
}

.slides-produtos {
  padding-top: 60px;
  background: rgba(255, 255, 255, 0.9);
  height: 100%;
}


.container-fotos {
  background: black;
}

.cartao.descricao {
  margin-top: 15px;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-right-radius: 10px;
  box-shadow: 0 4px 10px -2px #E2E3E3;

}

.grande {
  font-size: 18px;
  font-weight: bold;
}

.botao_produto {
  border: 1px solid black;
  padding: 15px;
  border-radius: 30px;
  text-align: center;
  font-weight: 500;
  font-size: 14px;
}

.botao_produto.verde {
  border: 1px solid #3fad36;
  color: #3fad36;

}

.titulo-produtos {
  background: white;
}
.slides-produtos .icon-fechar {
  width: 32px;
  height: 32px;
  float: left;
  margin-left: 0;
  margin-top: 9px;
}


@media screen and (min-width: 768px) {
  .cartao.conteudo, .cartao.semborda, .slides-fotos, .slides-produtos   {
    max-width: 100%;
    /* text-align: center; */
    margin-left: auto;
    margin-right: auto;
  }

  .bg-content {
    z-index: 0;
    position: absolute;
    left: 0px;
    top: 0px;
    right: 0px;
    width: 120%;
    height: 120%;
    margin-left: -10%;
    background-position: 0px 0px, 50% 50%;
    background-size: auto, cover;
    background-repeat: repeat, no-repeat;
    opacity: 1;
    -webkit-filter: blur(24px);
    filter: blur(24px);
    display: block;

  }

  .content {
    position: relative;
    z-index: 10;

  }

  .slides-fotos, .slides-produtos {
    left: 20%;
  }

  .sobre_nos {
    border-radius: 5px;
    box-shadow: 0 4px 10px -2px #E2E3E3;
    padding: 10px;
    padding-bottom: 5px;
    background: white;
    margin-top: 10px;
  }
  .brinde {
    margin-top: 0 ;
  }

  .container-scroll {
    overflow-x: unset;
    overflow-y: unset;
    white-space: unset;
    -webkit-overflow-scrolling: touch;
    margin-right: 0px;
  }

  .capa_empresa.centralizada {
    height: 310px;
    max-width: 90%;
    margin: 0 auto;
    box-shadow: 0 4px 10px -2px #a9aaaa;
  }

  .capa_empresa.desfocada {
    height: 305px;
  }

  .cartao.conteudo {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 30px;
    border-top-right-radius: 0px;
    box-shadow: 0 4px 10px -2px #E2E3E3;
  }

  .cartao.conteudo.topo {
    margin-top: 0px;
  }
}

.icone.insta {
  fill: #525252;
}

.cinza a {
  color: #525252 ;
}

.FlexEmbed {
  display: block;
  overflow: hidden;
  position: relative;
}

.FlexEmbed:before {
  content: "";
  display: block;
  width: 100%;
}


.FlexEmbed--2by1:before {
  padding-bottom: 25%;
}

.FlexEmbed.desfocada {
  background-position: 0px 0px, 50% 50%;
  background-size: auto, cover;
  background-repeat: repeat, no-repeat;
  opacity: 1;
  -webkit-filter: blur(12px);
  filter: blur(12px);
  z-index: -1;
  position: absolute;
  width: 100%;
  max-width: 100%;
  display: block;
  top: 0px;
}

.CoverImage {
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  margin: 0 auto;
  max-height: 350px;
  max-width: 100%;
}

@media (max-width: 992px) {
  .cartao.conteudo.topo {
    z-index: 10000;
  }

  .fundo-fechado{
    padding-top: 10% !important;
    font-size: 15px !important;;
  }

  .nome_empresa {
    font-size: 16px !important;
  }

  .detalhes_empresa{
    .endereco.abreviar{
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      white-space: inherit;
    }
  }

  .cartao.conteudo.topo {
    width: 100%;
  }
  .cartao{
    padding: 10px;
  }
}

@media (min-width: 992px) {
  .cartao.conteudo, .cartao.semborda, .slides-fotos, .slides-produtos {
    max-width: 100%;
    /* text-align: center; */
    margin-left: auto;
    margin-right: auto;
  }

  .capa_empresa.centralizada {
    height: 310px;
    max-width: 100%;
    margin: 0 auto;
    box-shadow: 0 4px 10px -2px #a9aaaa;
  }

  .CoverImage {
    max-width: 100%;
  }
}

.tema_natal {
  background: $vermelho;
  text-align: center;
  background-image: url("/assets/images/banner_natal.png");
  background-size: cover;
  height: 98px;
  background-position: calc(50%);
  margin-left: -12px;
  margin-right: -12px;
  margin-top: 0px;
  z-index: 999999999999;
  border-top: solid 2px #e0bd6c;
  border-bottom: solid 2px #e0bd6c;
}

.tema_carnaval{
  background: $amarelo;
  text-align: center;
  height: 68px;
  background-size: cover;
  background-image: url("/assets/images/banner_carnaval_2025.jpeg");
  z-index: 999999999999;
  border-top: solid 2px #e0bd6c;
  border-bottom: solid 2px #e0bd6c;
  background-position: calc(50%);
  margin-left: -12px;
  margin-right: -12px;
  margin-top: 0px;
}

.tema_dia_maes {
  background: #FB6C66;
  text-align: center;
  background-image: url("/assets/images/banner_mobile_dia_maes.png?v=2");
  background-size: cover;
  height: 98px;
  background-position: calc(50%);
  margin-left: -12px;
  margin-right: -12px;
  margin-top: 0px;
  z-index: 999999999999;
  border-top: solid 2px #e0bd6c;
  border-bottom: solid 2px #e0bd6c;
}

.tema_dia_maes {
  background: #FB6C66;
  text-align: center;
  background-image: url("/assets/images/banner_mobile_dia_maes.png?v=2");
  background-size: cover;
  height: 98px;
  background-position: calc(50%);
  margin-left: -12px;
  margin-right: -12px;
  margin-top: 0px;
  z-index: 999999999999;
}

.tema_pascoa {
  background: $marrom;
  text-align: center;
  background-image: url("/assets/images/banner_pascoa.png");
  background-size: cover;
  height: 98px;
  background-position: calc(50%);
  margin-left: -12px;
  margin-right: -12px;
  margin-top: 0px;
  z-index: 999999999999;
}

.tema_ano_novo {
  background: #1c1c1c;
  text-align: center;
  background-image: url("/assets/images/banner_ano_novo.png?v=4");
  background-size: cover;
  height: 75px;
  background-position: calc(50%);
  margin-left: -12px;
  margin-right: -12px;
  margin-top: 0px;
  z-index: 999999999999;
  border-top: solid 2px #e0bd6c;
  border-bottom: solid 2px #e0bd6c;
}

.tema_blackfriday {
  background: #000015;
  text-align: center;
  background-image: url("/assets/images/banner-blackfriday.jpg");
  background-size: cover;
  height: 98px;
  background-position: calc(50% + 10px);
  margin-left: -12px;
  margin-right: -12px;
  margin-top: 0px;
  z-index: 999999999999;
}

.tema_dia_pais {
  background: #10b3e9;
  text-align: center;
  background-image: url("/assets/images/banner_dia_dos_pais_mobile.png");
  background-size: cover;
  height: 98px;
  background-position: calc(50% + 10px);
  margin-left: -12px;
  margin-right: -12px;
  margin-top: 0px;
  z-index: 999999999999;
}


.tema_dia_namorados {
  background: $rosa;
  text-align: center;
  background-image: url("/assets/images/banner_dia_namorados_mobile.png");
  background-size: cover;
  height: 98px;
  background-position: calc(50% + 10px);
  margin-left: -12px;
  margin-right: -12px;
  margin-top: 0px;
  z-index: 999999999999;
}

.tema_arraia {
  background: $rosa;
  text-align: center;
  background-image: url("/assets/images/banner_arraia_mobile.png");
  background-size: cover;
  height: 98px;
  background-position: calc(50% + 10px);
  margin-left: -12px;
  margin-right: -12px;
  margin-top: 0px;
  z-index: 999999999999;
}

.banner_tema {
  background: #000;
  text-align: center;
  background-image: url("/assets/images/banner_blackfriday2022_mobile.png");
  background-size: cover;
  height: 98px;
  background-position: calc(50% + 10px);
  margin-left: -12px;
  margin-right: -12px;
  margin-top: 0px;
  z-index: 999999999999;
}

.tema_copa_2022 {
  background: $verde;
  text-align: center;
  background-image: url("/assets/images/banner_copa_2022_mobile.png");
  background-size: cover;
  height: 98px;
  background-position: calc(50% + 10px);
  margin-left: -12px;
  margin-right: -12px;
  margin-top: 0px;
  z-index: 999999999999;
}

.fidelidade{
  height: 52px;
  margin: 0px -12px;
  box-shadow: inset 0 0 0 2000px rgb(0 0 0 / 25%);
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  >div{
    font-family: "Avenir LT Std",sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px;
    letter-spacing: -0.9px;
    text-align: left;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #343a40;
    border-radius: 0 0 8px 8px;
    color: #fff;
    overflow: hidden;
    position: relative;
    transition: .3s;
    margin: 0;
    width: 100%;
    height: 100%;
  }
  h5{
    color: $branco;
  }

  &.chinainbox{
    >div{
      background: #e9232b;
    }
  }
  &.carnaval{
    >div{
      background: #D825A9;
    }
    h5{
      color: #fff000;
    }
  }

  &.dia_pais{
    >div{
      background: $azul;
    }
  }
  &.copa_2022{
    >div{
      background: $verde;
    }
  }

  &.dia_namorados{
    >div{
      background: $roxo
    }
  }

  &.arraia{
    >div{
      background: $vermelhoclaro
    }
  }

  &.natal{
    >div{
      background: $vermelho
    }
  }


  &.black_friday_2022 {
    >div{
      background: $ouro;
    }
  }

  &.black_friday,  &.black , &.ano_novo{
    > div {
      background: $pretoclaro;
    }
  }
  &.pascoa{
    > div {
      background: $marromclaro;
    }
  }



}

.tema_cacau_show {
  background: var(--cor-fundo-site);
  text-align: center;
  padding: 0;
  margin: 0;

  img {
    max-width: 100%;
    height: auto;
  }
}

.container_total.cacau_show {
  background-color: var(--cor-fundo-site);

  .cartao.conteudo {
    background-color: var(--cor-fundo-elementos);
    border-bottom: 1px solid var(--cor-borda);
  }

  .dados_empresa {
    .nome_empresa {
      color: var(--cor-texto);
    }

    .endereco {
      color: var(--cor-texto-secundaria);
    }

    .whatsapp {
      color: var(--cor-texto);
      .icone-whatsapp {
        color: var(--cor-texto-secundaria);
      }
      &:hover {
        color: var(--cor-texto-secundaria);
      }
    }

    .horario {
      color: var(--cor-texto);
      .status {
        color: var(--cor-texto-secundaria);
        &.fechado {
          color: var(--cor-texto-secundaria);
        }
      }
    }
  }

  .descricao_empresa {
    color: var(--cor-texto-secundaria);
  }
}

.fidelidade.saldo.cacau_show {
  background-color: var(--cor-fundo-elementos);
  color: var(--cor-texto);

  h5 {
    color: var(--cor-texto);
    b {
      color: var(--cor-texto-secundaria);
    }
  }
}
