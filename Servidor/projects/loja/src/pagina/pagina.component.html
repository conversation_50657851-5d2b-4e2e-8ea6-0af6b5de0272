<app-topo-desktop  *ngIf="isDesktop" #topoDesktop></app-topo-desktop>

<div class="container {{empresa?.tema}}"   [ngClass]="{'finalizar': finalizarPedido, 'remover-padding': !temBordas}">
  <div class="coluna principal"   >
    <app-topo #topo [empresa]="empresa" [exibirTopo]="exibirTopo" [topoReduzido]="topoReduzido"
              *ngIf="!isDesktop" [exibirBannerTema]="exibirBannerTema" [exibirTitulo]="exibirTitulo"></app-topo>

    <div class="conteudo ribbon-box" [ngClass]="{'mt-2': ((!empresa.estaRecebendoPedidos && empresa.mensagemAbrirPedidos) ||
      (empresa.cardapio?.modoVisualizacao) || (empresa.cardapio?.modoVisualizacaoQRcode)) && exibirTopo,
          'overflow-x-hidden':   lojaMenu.exibirMenuCategorias}">
       <span *ngIf="!empresa.estaRecebendoPedidos && empresa.mensagemAbrirPedidos && exibirTopo" class="ribbon ribbon-dark aviso-abertura" >
             {{empresa.mensagemAbrirPedidos}}
       </span>

      <span class="ribbon ribbon-success aviso-abertura" *ngIf="empresa.cardapio?.modoVisualizacao">
        <a class="whatsapp" [href]="'http://wa.me/55' + empresa?.whatsapp">
        Para fazer seus pedidos entre em contato pelo Whatsapp
          <app-exibir-whatsapp [empresa]="empresa" [light]="true"></app-exibir-whatsapp>
        </a>
      </span>

      <span class="ribbon ribbon-success aviso-abertura" *ngIf="(empresa.cardapio?.modoVisualizacaoQRcode || (pedido &&
       pedido.mesa && pedido.mesa.somenteLeitura)) && tipoCardapio === 'MESA'">
        Cardápio Digital para Visualização
      </span>

      <router-outlet (activate)="onActivate($event)"></router-outlet>
    </div>

  </div>
  <div class="coluna carrinho_desktop" *ngIf="!finalizarPedido">
    <div class="sticky" #stickyCarrinho>
      <div class="carrinho">
        <app-loja-carrinho    [desktop]="true"></app-loja-carrinho>
      </div>
    </div>
  </div>
</div>

<div class="barra-cookies p-1" *ngIf="!aceitouCookies && !estaNaTelaTabletPedidos">
  <div class="row d-flex justify-content-center align-items-center" style="max-width: 800px;margin: 0 auto;">
    <div class="col">
      Este website está em conformidade com a lei Geral de Proteção de Dados (LGPD) e utiliza cookies para oferecer uma melhor experiência ao visitante.
    </div>
    <div class="col-auto">
      <button class="btn btn-sm btn-primary" (click)="concordoCookies()">Concordo</button>&nbsp;&nbsp;
    </div>
  </div>
</div>

<footer class="footer {{empresa.tema}}"  >
  <app-carrinho #carrinho [exibindoMenu]="exibirMenu" ></app-carrinho>

  <app-loja-menu #lojaMenu [exibirMenu]="exibirMenu"
         (onBuscarProdutos)="abraTelaDeBusca($event)" (onExibirBrindes)="abraTelaDeBrindes()" > </app-loja-menu>
</footer>

<div kendoDialogContainer></div>
