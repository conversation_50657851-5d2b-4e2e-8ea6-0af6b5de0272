<app-header-tela titulo="Entrega"></app-header-tela>

<form [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"
      novalidate #frm="ngForm" (ngSubmit)="onSubmit()" *ngIf="pedido">
  <div class="produto pt-2 pb-0" *ngIf="pedido">
    <h4 class="titulo">Entrega
      <ng-container *ngIf="apenasAgendamento"> (apenas agendamento)</ng-container>
    </h4>
    <p *ngIf="empresa.id && !formasDeEntrega?.length" class="text-muted">Nenhuma forma de entrega está disponível no
      momento</p>
    <div class="form-group mb-2 mt-3 escolher">
      <ng-container *ngFor="let formaEntrega of formasDeEntrega; let i = index;">
              <span class="mr-1 radio radio-blue mb-1 k-display-block"
                    [ngClass]="{'selecionado': entrega.formaDeEntrega && entrega.formaDeEntrega === formaEntrega.nome}"
                    *ngIf="formaEntrega.ativa">
                  <input id="formaDeEntrega{{i}}" name="formaDeEntrega" type="radio"
                         [(ngModel)]="entrega.formaDeEntrega" [value]="formaEntrega.nome" class="k-radio" kendoRadioButton
                         (change)="alterouFormaDeEntrega(formaEntrega)"
                         [required]="true"/>
                  <label for="formaDeEntrega{{i}}" class="ml-1">{{formaEntrega.nome}}</label>
              </span>
      </ng-container>

      <div class="invalid-feedback" *ngIf="!entrega.formaDeEntrega">
        <p>Escolha a forma de retirada do seu pedido</p>
      </div>
    </div>
  </div>

  <div class="produto pt-2 pb-2" *ngIf="entrega.ehDelivery()">
    <ng-container *ngIf="entrega.endereco">
      <h5 class="text-muted"><i class="fas fa-map-marker-alt"></i> Endereço Escolhido</h5>
      <h5 class="titulo">
        {{entrega.endereco.obtenhaEnderecoCompleto()}}
      </h5>

      <ng-container *ngIf="!escolherTipo">
        <button class="btn btn-blue btn-block" type="button"
                *ngIf="entrega.endereco" (click)="alterarEndereco()">Trocar Endereço
        </button>
      </ng-container>
      <ng-container *ngIf="escolherTipo">
        <p><strong>O que você prefere?</strong></p>
        <ng-container *ngIf="possuiLocalizacao">
          <button class="btn btn-outline-primary btn-block"
                  type="button" (click)="pegueLocalizacao()">
            <i class="fas fa-search-location"></i> Usar Minha Localização
          </button>
          <div class="mt-2 mb-2">
            <span class="ou">&nbsp;ou&nbsp;</span>
          </div>
        </ng-container>
        <button class="btn btn-blue btn-block" type="button"
                *ngIf="entrega.endereco" (click)="informarEndereco()">Informar Endereço
        </button>
      </ng-container>
    </ng-container>

    <ng-container *ngIf="!entrega.endereco">
      <div class="col" *ngIf="entrega.ehDelivery() && formaDelivery.priorizarLocalizacao">
        <ng-container>
          <p><strong>Ative sua localização para informar seu endereço</strong></p>
          <ng-container *ngIf="possuiLocalizacao">
            <button class="btn btn-primary btn-block"
                    type="button" (click)="pegueLocalizacao()">
              <i class="fas fa-search-location"></i> Usar Minha Localização
            </button>
          </ng-container>

          <p class="alert alert-danger mt-2" *ngIf="msgErroLocalizacao">
            <b>
              <i class="fas fa-exclamation-triangle"></i> {{msgErroLocalizacao}}
            </b>
          </p>
          <button class="btn btn-blue btn-block" type="button" (click)="informarEndereco()" *ngIf="erroLocalizacao">
            Informar Endereço
          </button>
        </ng-container>
      </div>

      <div class="col" *ngIf="entrega.ehDelivery() && !formaDelivery.priorizarLocalizacao">
        <ng-container *ngIf="!escolherTipo">
        <button class="btn btn-blue btn-block"
                *ngIf="!entrega.endereco" type="button" (click)="alterarEndereco()">Escolher Endereço
        </button>
        </ng-container>
        <ng-container *ngIf="escolherTipo">
          <p><strong>O que você prefere?</strong></p>
          <ng-container *ngIf="possuiLocalizacao">
            <button class="btn btn-outline-primary btn-block"
                    type="button" (click)="pegueLocalizacao()">
              <i class="fas fa-search-location"></i> Usar Minha Localização
            </button>
            <div class="mt-2 mb-2">
              <span class="ou">&nbsp;ou&nbsp;</span>
            </div>
          </ng-container>
          <button class="btn btn-blue btn-block" type="button"
                  (click)="informarEndereco()">Informar Endereço
          </button>
        </ng-container>
      </div>
    </ng-container>

  </div>


  <div class="col" *ngIf="entrega && entrega.taxaDeEntrega != null">
    <p class="mt-2 mb-0">
      Taxa de Entrega
    </p>
    <h5 class="mt-1" *ngIf="entrega.taxaDeEntrega !== -1">
      {{entrega.taxaDeEntrega | currency: 'BRL'}}
    </h5>
    <h5 class="mt-1" *ngIf="entrega.taxaDeEntrega === -1">
      A Informar
    </h5>
  </div>
  <app-agendar-entrega [empresa]="empresa" [pedido]="pedido" [formasDeEntrega]="formasDeEntrega"
                       [agendarEntrega]="agendouEntrega || apenasAgendamento"
                       [apenasAgendamento]="apenasAgendamento"
                         [tela]="this"></app-agendar-entrega>

  <div class="invalid-feedback" *ngIf="agendarEntrega && (!pedido.dataEntrega || !pedido.horarioEntrega)">
    <p>É necessário escolher o dia e horário da entrega</p>
  </div>

  <p class="alert alert-danger mt-2" *ngIf="msgErro">
    <b>
      <i class="fas fa-exclamation-triangle"></i> {{msgErro}}
    </b>
  </p>
  <div style="height: 40px"></div>

  <footer class="footer" class="{{empresa.tema}}">
    <div>
      <div class="row" style="padding: 15px;">
        <div class="col"  [hidden]=" !entrega.foiPreenchida() && entrega.ehDelivery()">
          <button class="btn btn-primary btn-block btn-tema">Salvar Forma de Entrega</button>
        </div>
      </div>
    </div>
  </footer>
</form>
