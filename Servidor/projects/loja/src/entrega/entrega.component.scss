.mobile {
  .btn-block {
    padding: 1em 0;
  }
}

#form:focus-within .footer {
  position: initial;
}

.footer {
  border-top: solid 1px #eeeeee;
  background: #fff;
  padding: 0px;
  position: fixed;
}

.produto {
  border-bottom: solid 1px #e8e8e8;
}

@media (min-width: 1025px) {
  .footer {
    position: initial !important;
  }
}

.ou {
  display: flex;
  flex-direction: row;
  color: #b6b6b6;
}

.ou:before,
.ou:after {
  content: "";
  flex: 1 1;
  border-bottom: 1px solid #eeecec;
  margin: auto;
}

::ng-deep .black_friday_2022 {
  .footer {
    background: #0e0e0e;
  }

  h4.titulo {
    color: #676767;
  }

  .btn-tema {
    color: #b57e2b;
    border-color: #b57e2b;
  }

  .btn-tema {
    background: #b57e2b;
    color: #0e0e0e;
    font-weight: bold;
    border-color: #986716;
  }

  .btn-tema:active {
    background: #774e09;
    color: #2d1c00;
    border-color: #986716;
  }
}



.chinainbox{
  .btn-blue, .btn-primary {
    background-color: #e52a28de !important;
    border-color: #e52a28 !important;;
  }

  .btn-secondary {
    background-color: #ff9800d6  !important;
    border-color: #e79e406e  !important;
  }
}
