import {Component, EventEmitter, Input, Output, OnInit, ViewChildren, QueryList, ViewChild} from '@angular/core';
import {ItemPedido} from "../objeto/ItemPedido";
import {ConstantsService} from "../services/ConstantsService";
import {CarrinhoService} from "../services/carrinho.service";
import {SiteCampoAdicionalComponent} from "../app/site-campo-adicional/site-campo-adicional.component";
import {AutorizacaoLojaService} from "../services/autorizacao-loja.service";
import {PixelTrackingService} from "../services/pixel-tracking.service";
import { AdicionaisCustomizadosComponent } from '../app/adicionais-customizados/adicionais-customizados.component';
declare var $;
declare var fbq;
declare var gtag;

@Component({
  selector: 'app-site-adicionar-produto',
  templateUrl: './site-adicionar-produto.component.html',
  styleUrls: ['./site-adicionar-produto.component.scss']
})
export class SiteAdicionarProdutoComponent implements OnInit {
  @Input() itemPedido: ItemPedido;
  @Input() pedido: any;
  @Input() indiceItem: any;
  @Input() window: any;
  @Output() onInformarPeso = new EventEmitter();

  ctrlAdicionais: QueryList<SiteCampoAdicionalComponent>;
  adicionaisCustomizados: AdicionaisCustomizadosComponent;

  produto: any = {};
  erro: any;
  estaRecebendoPedidos: boolean;
  mensagemAbrirPedidos: string;
  modoVisualizacao: boolean;
  modoVisualizacaoQRcode: boolean;
  permiteAgendamento: boolean;
  carregando = true;
  empresa: any;
  adicionandoProduto: any;

  constructor( private constantsService: ConstantsService, private carrinhoService: CarrinhoService,
               private autorizacaoLojaService: AutorizacaoLojaService,
               private pixelTrackingService: PixelTrackingService) { }

  ngOnInit(): void {
    this.produto = this.itemPedido.produto;
    this.carregando = false;
    this.constantsService.empresa$.subscribe( (empresa) => {
      if (!empresa || !empresa.id) return;

      this.empresa = empresa;

      this.estaRecebendoPedidos = empresa.estaRecebendoPedidos;
      this.permiteAgendamento = empresa.permiteAgendamento;
      this.mensagemAbrirPedidos = empresa.mensagemAbrirPedidos;
      this.modoVisualizacao = empresa.cardapio && empresa.cardapio.modoVisualizacao;
      this.modoVisualizacaoQRcode = (empresa.cardapio && empresa.cardapio.modoVisualizacaoQRcode) ||
        (this.pedido && this.pedido.mesa &&  this.pedido.mesa.somenteLeitura);


    });
  }



  adicionarProduto() {
    delete this.erro;
    if( this.empresa.fechadoTemporariamente || (!this.estaRecebendoPedidos && !this.permiteAgendamento) ) {
      return  $("#alertaFechado").modal();
    }


    if( this.modoVisualizacaoQRcode && this.pedido.mesa ) {
      this.mensagemAbrirPedidos = 'Cardápio De Mesa apenas para visualização. Chame o garçom para fazer seu pedido!';
      return $("#alertaFechado").modal();
      return;
    }
    if( this.modoVisualizacao ) {
      this.mensagemAbrirPedidos = 'Faça seu pedido pelo nosso Whatsapp!';

      return $("#alertaFechado").modal();
    }

    if(this.adicionaisCustomizados){

      let validos = this.adicionaisCustomizados.valideCampos();

      if(!validos) {
        return;
      }

    }

    this.itemPedido.produto.empresa = {
      id: this.empresa.id,
      nome: this.empresa.nome,
      logo: this.empresa.logo,
      dominio: this.empresa.dominio
    };

    if(this.produto.camposAdicionais ) {
      let totalAdicionais = this.produto.camposAdicionais.length;

      for( let i = 0; i < totalAdicionais; i++ ) {
        const campoAdicional = this.produto.camposAdicionais[i];

        if(!this.itemPedido.valideCampoAdicional(campoAdicional)) {
          this.ctrlAdicionais.toArray()[i].exibaErro('Complemento é obrigatório');
          this.posicioneNoComplemento(campoAdicional);
          this.adicionandoProduto = false;
          return
        }
      }

      let posicaoSabor = this.produto.camposAdicionais.length - 1;

      for( let i = 0; i < this.itemPedido.sabores.length; i++ ) {
        const sabor = this.itemPedido.sabores[i];
        if(sabor.produto !== this.produto.id){
          for( let j = 0; j < sabor.camposAdicionais.length; j++ ) {
            posicaoSabor++
            const campoAdicionalSabor = sabor.camposAdicionais[j];

            if(!this.itemPedido.valideCampoAdicional(campoAdicionalSabor)) {

              this.ctrlAdicionais.toArray()[posicaoSabor].exibaErro('Complemento é obrigatório');
              this.posicioneNoComplemento(campoAdicionalSabor);
              this.adicionandoProduto = false;
              return
            }


          }
        }
      }
    }

    if(this.pedido.codigo)
      this.erro = String(`O pedido #${this.pedido.codigo} está aguardando pagamento`)

    let limiteProdutos =   this.empresa.cardapio ? this.empresa.cardapio.limiteProdutos : null;

    if(limiteProdutos){
      let qtdePedido = this.pedido.obtenhaQtdeItens(this.itemPedido) +  this.itemPedido.qtde;

      if(qtdePedido > limiteProdutos)
        this.erro = `Máximo de itens permitido no carrinho são ${limiteProdutos}`
    }

    if(!this.erro) {
      this.pixelTrackingService.trackAddToCart(this.itemPedido, this.pedido);

      if( typeof gtag !== 'undefined' ) {
        try {
          gtag('event', 'add_to_cart', {
            currency: 'BRL',
            value: this.itemPedido.total,
            items: [
              {
                id: this.itemPedido.produto.id,
                name: this.itemPedido.produto.nome,
                price: this.itemPedido.total / this.itemPedido.qtde,
                quantity: this.itemPedido.qtde
              }
            ]
          });
        } catch (error) {
          console.log(error);
        }
      }

      let itemPedidoAdicionado = null;
      if (!this.indiceItem) {
        itemPedidoAdicionado = this.pedido.adicione(this.itemPedido.produto, this.itemPedido.qtde,
          this.itemPedido.observacao, this.itemPedido.adicionais, this.itemPedido.produtoTamanho, this.itemPedido.sabores);
      }
      else {
        itemPedidoAdicionado = this.pedido.edite(this.indiceItem, this.itemPedido.produto, this.itemPedido.qtde,
          this.itemPedido.observacao, this.itemPedido.adicionais, this.itemPedido.produtoTamanho, this.itemPedido.sabores);
      }
      this.adicionandoProduto = true
      this.carrinhoService.atualizeDescontosETaxas(this.pedido, this.empresa).then(() => {
        this.adicionandoProduto = false;
        this.window.close();
      });

    } else {
      this.adicionandoProduto = false;
    }
  }

  aumentarQtde() {
    this.itemPedido.qtde  = Number((this.itemPedido.qtde + (this.itemPedido.produto.incremento || 1))
      .toFixed(3));

    if( this.produto.pesoMaximo != null && this.itemPedido.qtde > this.produto.pesoMaximo ) {
      this.itemPedido.qtde = this.produto.pesoMaximo;
    }

    this.itemPedido.atualizeTotal()
  }

  diminuirQtde() {
    this.itemPedido.qtde  =  Number((this.itemPedido.qtde  - (this.itemPedido.produto.incremento || 1))
      .toFixed(3));

    if( this.produto.pesoMinimo != null && this.itemPedido.qtde < this.produto.pesoMinimo ) {
      this.itemPedido.qtde = (this.produto.pesoMinimo || this.itemPedido.produto.valorInicial);
    }
    else if( this.itemPedido.qtde < this.produto.qtdeMinima )
      this.itemPedido.qtde = (this.produto.qtdeMinima || this.itemPedido.produto.valorInicial);

    this.itemPedido.atualizeTotal()
  }

  exibirUnidade() {
    return this.itemPedido.produto.tipoDeVenda &&
      this.itemPedido.produto.tipoDeVenda  === 'Peso';
  }

  posicioneNoComplemento(campoAdicional) {
    const $controleAdicional = document.getElementById('adicional_' + campoAdicional.id);

    if($controleAdicional ) {
      const topo = $controleAdicional.offsetTop - 10;

      document.querySelector('.k-dialog-content').scrollTo(0, topo);
    } else {
      setTimeout(() => {
        let $erros: any = document.getElementsByClassName('alert-danger');

        if(!$erros || !$erros.length)
          $erros = document.getElementsByClassName('badge-danger');

        if($erros && $erros.length){
          const topo =   $erros[0].offsetTop - 40;
          document.querySelector('.k-dialog-content').scrollTo(0, topo)
        }

      }, 0)

    }

  }

  informarPeso() {
    this.onInformarPeso.emit({})
  }

  setControlesAdicionais(ctrlAdicionais: QueryList<SiteCampoAdicionalComponent>, adicionaisCustomizados: AdicionaisCustomizadosComponent) {
    this.ctrlAdicionais = ctrlAdicionais;
    this.adicionaisCustomizados = adicionaisCustomizados;
  }
}
