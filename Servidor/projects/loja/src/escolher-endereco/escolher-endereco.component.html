<app-header-tela titulo="Endereço"></app-header-tela>

<div *ngIf="carregou">
  <div *ngIf="!enderecoEscolhido">
    <div class="form-group mt-3" >
      <label for="descricao">Selecione o endereço de entrega</label>
      <kendo-combobox id="descricao" name="descricao" [data]="enderecos" placeholder="selecionar"
                      class="form-control" appAutoFocus [autoFocus]="true"  [allowCustom]="false"
                      [(ngModel)]="endereco" [valueField]="'id'"   [textField]="'descricaoCompleta'">
      </kendo-combobox>
    </div>
    <div *ngIf="msgErro" class="alert alert-danger alert-dismissible fade show mt-2 mb-2" role="alert">
      {{msgErro}}
      <button type="button" class="close" data-dismiss="alert" aria-label="Fechar">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>

    <div  class="mt-3">
      <button   type="button" class="btn btn-blue btn-block"  [disabled]="!endereco || calculandoTaxa" (click)="calculeTaxaEntrega()" >
      <i class="k-icon k-i-loading  " *ngIf="calculandoTaxa"></i>
        Entregar nesse</button>

      <button   type="button" class="btn btn-warning btn-block"  [disabled]="calculandoTaxa" (click)="editarEndereco()"   *ngIf="endereco.id" >
        Alterar endereço</button>

      <button   type="button" class="btn btn-primary btn-block"   [disabled]="calculandoTaxa" (click)="cadastrarNovo()">Cadastrar Novo</button>
    </div>


  </div>

  <div class="mt-2" style="max-width: 800px;" [hidden]="!enderecoEscolhido">
  <div class="mt-3">
       <app-form-endereco (submit)="salveEndereco($event)"
                          [pedido]="pedido" #telaEndereco  [cepObrigatorio]="pedido.entrega.cepObrigatorio" ></app-form-endereco>
  </div>
</div>
</div>
