import {AfterViewInit, Component, OnInit, ViewChild} from '@angular/core';
import {Location} from "@angular/common";
import {ClienteService} from "../services/cliente.service";
import {CarrinhoService} from "../services/carrinho.service";
import {PedidoLoja} from "../objeto/PedidoLoja";

import {ITela} from "../objeto/ITela";
import {DominiosService} from "../services/dominios.service";
import {EnderecoService} from "../services/endereco.service";
import {FormEnderecoComponent} from "../app/form-endereco/form-endereco.component";
import {AutorizacaoLojaService} from "../services/autorizacao-loja.service";
import {Endereco} from "../objeto/Endereco";
import {ConstantsService} from "../services/ConstantsService";
import {ActivatedRoute, Router} from "@angular/router";
import {FormaDeEntrega} from "../objeto/FormaDeEntrega";

@Component({
  selector: 'app-escolher-endereco',
  templateUrl: './escolher-endereco.component.html',
  styleUrls: ['./escolher-endereco.component.scss']
})
export class EscolherEnderecoComponent implements OnInit, ITela, AfterViewInit {
  @ViewChild('telaEndereco', {static: false}) telaEndereco: FormEnderecoComponent;
  pedido: PedidoLoja;
  nomePagina: string;
  msgErro: string;
  enderecos: any = [];
  enderecoEscolhido: boolean;
  carregando: boolean;
  calculandoTaxa: boolean;
  public endereco: any;
  carregou = false;

  zonasDeEntrega: any = [{
    id: 1,
    nome: 'Zona 1'
  }, {
    id: 2,
    nome: 'Zona 2'
  }, {
    id: 3,
    nome: 'Zona 3'
  }];
  zonaDeEntrega: any;
  empresa: any;
  entregaPorZona = false;
  usarEndereco = false;
  formaReceberEmCasa: any;
  constructor(private activatedRoute: ActivatedRoute, private _location: Location, private autorizacao: AutorizacaoLojaService,
              private constantsService: ConstantsService,
              private clienteService: ClienteService, private carrinhoService: CarrinhoService,
              private dominiosService: DominiosService, private enderecoService: EnderecoService,
              private router: Router) {

    this.nomePagina = dominiosService.obtenhaRaizCardapio();
  }

  ngOnInit() {
  }

  ngAfterViewInit() {
    this.activatedRoute.queryParams.subscribe((queryParams: any) => {
      const usarEndereco = queryParams.endereco;
      this.constantsService.empresa$.subscribe((empresa) => {
        if (!empresa) {
          return;
        }

        this.empresa = empresa;
        this.endereco = null;
        this.pedido = this.carrinhoService.obtenhaPedido();
        this.endereco = this.pedido && this.pedido.entrega ? this.pedido.entrega.endereco : null;
        this.formaReceberEmCasa =  this.empresa.formasDeEntrega.find((forma) => forma.nome === FormaDeEntrega.RECEBER_EM_CASA )
        this.enderecoEscolhido = this.endereco != null;
        this.carregando = true;

        this.autorizacao.obtenhaEnderecos().then((enderecos) => {
          this.carregando = false;
          this.carregou = true;

          this.setEnderecos(enderecos);

          setTimeout( () => {
            if(this.empresa.enderecoCompleto && ! this.formaReceberEmCasa.naoUsarCidadePadrao ) //
              this.telaEndereco.setCidadePadrao(this.empresa.enderecoCompleto.cidade)

            if (usarEndereco) {
              this.telaEndereco.buscouUmEndereco = true;
              this.telaEndereco.exibaEndereco(this.endereco);
            }
          }, 0);
        }).catch((erro) => {
          this.carregou = true;
        });
      });
    });
  }

  setEnderecos(enderecos: any = []) {
    this.enderecos = []
    enderecos.forEach((dadosEndereco: any) => {
      let endereco = Endereco.novo();

      Object.assign(endereco, dadosEndereco)

      if (dadosEndereco.cidade) {
        endereco.cidade = dadosEndereco.cidade;
        endereco.estado = dadosEndereco.cidade.estado;
      }

      if (!endereco.descricaoCompleta) {
        endereco.descricaoCompleta = endereco.obtenhaEnderecoCompleto();
      }
      this.enderecos.push(endereco)
    })

    if (this.enderecos.length && !this.enderecoEscolhido)
      this.endereco = this.enderecos[0]
    else if (!this.enderecos.length && !this.enderecoEscolhido) {
      this.enderecoEscolhido = true;
    }
  }

  voltar() {
    this._location.back();
  }

  salveEndereco(endereco: any, dados: any = null) {
    this.pedido.novosEnderecos.push(endereco);
    this.pedido.entrega.setTaxaEntrega(endereco, dados)
    this.carrinhoService.salvePedido(this.pedido);

    this.router.navigate(['/' + this.nomePagina + '/forma-entrega'], {queryParamsHandling: 'merge'}).then( () => {

    });
  }

  deveExibirTopo() {
    return false;
  }

  deveExibirMenu() {
    return false;
  }

  calculeTaxaEntrega() {
    if (this.endereco.faltamInformacoes) {
      this.enderecoEscolhido = true;
      this.telaEndereco.exibaEndereco(this.endereco);
      return;
    }

    this.calculandoTaxa = true;
    this.enderecoService.calculeTaxaDeEntrega('Receber em casa', this.endereco, this.pedido.obtenhaSubTotal()).then((dados) => {
      this.calculandoTaxa = false;
      this.salveEndereco(this.endereco, dados)

    }).catch(erro => {
      this.calculandoTaxa = false;
      this.msgErro = erro;

    })
  }

  deveTerBordas() {
    return true;
  }

  deveExibirBannerTema() {
    return false;
  }

  cadastrarNovo(){
    this.enderecoEscolhido = true
  }

  editarEndereco(){
    this.enderecoEscolhido = true;
    this.telaEndereco.exibaEndereco(this.endereco);
  }
}
