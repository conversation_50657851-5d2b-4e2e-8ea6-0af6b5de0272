import { Injectable } from '@angular/core';
import {ServerService} from "./ServerService";
import {HttpClient} from "@angular/common/http";

@Injectable({
  providedIn: 'root'
})
export class PagseguroService  extends ServerService {
  constructor(http: HttpClient) {
    super(http);
  }

  crieSessao() {
    return new Promise( (resolve, reject) => {
      this.facaPost('/pagseguro/nova-sessao', {}).then( (resposta: any) => {
        resolve(resposta);
      }).catch( (erro: Error) => {
        reject(erro);
      });
    });
  }

  crieSessaoConnect() {
    return new Promise( (resolve, reject) => {
      this.facaPost('/pagseguro/connect/nova-sessao', {}).then( (resposta: any) => {
        resolve(resposta);
      }).catch( (erro: Error) => {
        reject(erro);
      });
    });
  }
}
