import { Injectable } from '@angular/core';
import {ServerService} from "./ServerService";
import {HttpClient} from "@angular/common/http";

@Injectable({
  providedIn: 'root'
})
export class ChinainboxService extends ServerService {
  constructor(protected http: HttpClient) {
    super(http);
  }

  localizeLoja(empresa: any, endereco: any) {
    return this.facaPost(`/api/encontreLoja/` + empresa.id, {
      endereco: endereco
    });
  }
}
