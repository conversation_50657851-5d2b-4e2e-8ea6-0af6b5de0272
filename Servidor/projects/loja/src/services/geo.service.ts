import { Injectable } from '@angular/core';

import {HttpClient} from "@angular/common/http";
import {ServerService} from "./ServerService";

@Injectable({
  providedIn: 'root'
})
export class GeoService extends ServerService {
  constructor(private httpCliente: HttpClient) {
    super(httpCliente);
  }

  obtenhaEndereco(localizacao: { lng: number; lat: number }) {
    return this.obtenha('/geo/geocodeEndereco?lat=' + localizacao.lat + '&lng=' + localizacao.lng, {});
  }
}
