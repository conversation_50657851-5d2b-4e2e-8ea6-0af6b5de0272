import { Injectable } from '@angular/core';
import {ServerService} from "./ServerService";
import {HttpClient} from "@angular/common/http";

@Injectable({
  providedIn: 'root'
})
export class AvaliacaoService extends ServerService {

  constructor(private httpCliente: HttpClient) {
    super(httpCliente);
  }

  envie(avaliacao: any, pedido: any) {
    return this.facaPost(`/avaliacoes/envie`, {
      avaliacao: avaliacao,
      pedido: {
        id: pedido.id
      }
    });
  }

  obtenhaPorIdPedido(pedido: any) {
    return this.obtenha(`/avaliacoes/obtenha/` + pedido.id, {
    });
  }
}
