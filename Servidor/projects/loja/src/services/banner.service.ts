import { Injectable } from '@angular/core';
import {ServerService} from "./ServerService";
import {HttpClient} from "@angular/common/http";

@Injectable({
  providedIn: 'root'
})
export class BannerService extends ServerService {
  constructor(private httpCliente: HttpClient) {
    super(httpCliente);
  }

  obtenhaBanners() {
    let link =  '/api/banners';

    return this.httpCliente.get(link)
      .toPromise().then(this.retorno).catch(this.handleError);
  }
}
