import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {ServerService} from "./ServerService";
import {BehaviorSubject, Observable} from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class AutorizacaoLojaService extends ServerService {
  kEYUSER = 'pkcontato';
  private usuario = new BehaviorSubject<any>(null);
  private sessao = new BehaviorSubject<string>(null);
  usuario$ = this.usuario.asObservable();
  sessao$: Observable<string> = this.sessao.asObservable();

  constructor (protected http: HttpClient) {
    super(http);
    this.estaLogado().then((resposta: any) => {
      if(!resposta.logado )
        this.limpeSessaoLocal();
    })
  }

  estaLogado(): Promise<boolean> {
    return new Promise(resolve => {
      this.http.get('/auth/contato/logado').subscribe((resposta: any) => {
        let usuario = this.getUsuario();

        this.sessao.next(resposta.sessao);

        if(usuario && resposta.informarCpf){
          usuario.informarCpf = true;
          this.salveUsuario(usuario)
        }

        if(usuario && resposta.informarDataNascimento){
          usuario.informarDataNascimento = true;
          this.salveUsuario(usuario)
        }

        if(!usuario && resposta.logado) {
          this.usuario.next(usuario);

         this.atualizeUsuarioLogado().then( () => {
           resolve(resposta);
          });
        } else {
          this.usuario.next(usuario);
          resolve(resposta);
        }
      });
    });
  }

  atualizeUsuarioLogado(){
    localStorage.removeItem(this.kEYUSER );
    return new Promise(resolve => {
      return this.http.get('/auth/contato/me', {})
        .toPromise().then( (resp: any) => {
          if(resp.data && resp.data.telefone) {
            this.salveUsuario(resp.data)
          } else {
            this.usuario.next({});
          }
          resolve(null);
        });
    })

  }

 async atualizeSaldoFidelidade(usuario){
    let resp: any = await this.http.get('/auth/contato/me/saldo') .toPromise();

    let resgate = resp && resp.data ? resp.data : null

   if(resgate) {
     delete usuario.erroFidelidade;
     delete usuario.atualizarCadastro;
     delete usuario.fazerOptinUsarSaldo;
     delete usuario.fazerOptin;

     if(resgate.erroFidelidade ||  resgate.atualizarCadastro || resgate.fazerOptinUsarSaldo || resgate.fazerOptin){
       usuario.saldoFidelidade = 0;
       usuario.erroFidelidade = resgate.erroFidelidade
       usuario.atualizarCadastro = resgate.atualizarCadastro;
       usuario.fazerOptinUsarSaldo = resgate.fazerOptinUsarSaldo;
       usuario.fazerOptin = resgate.fazerOptin;
       this.salveUsuario(usuario)
     } else {
       if(resgate.saldo !== usuario.saldoFidelidade){
         usuario.saldoFidelidade = resgate.saldo;
         usuario.saldoDescricao = resgate.saldoDescricao;
         usuario.descricaoProgramaFidelidade = resgate.descricaoProgramaFidelidade;
         this.salveUsuario(usuario)
       }
     }
   }

  }

  salveUsuario(usuario: any){
    if(usuario && usuario.telefone){
      let usuarioLogado: any = {};

      if(usuario.informarCpf && usuario.cpf)
        delete usuario.informarCpf

      if(usuario.informarDataNascimento && usuario.dataNascimento)
        delete usuario.informarDataNascimento

      Object.assign(usuarioLogado, usuario);

      this.usuario.next(usuarioLogado);
      localStorage.setItem(this.kEYUSER, JSON.stringify(usuarioLogado));
    }

  }

  getUsuario() {
    let _user = localStorage.getItem(this.kEYUSER);

    if(!_user)
      return null;

    return JSON.parse(_user);
  }

  obtenhaConfiguracoesUsuario(): Promise<any> {
    let link =  '/auth/configuracoes';

    return this.http.get(link)
      .toPromise().then(this.retorno).catch(this.handleError);
  }

  atualizeConfiguracoesUsuario(configuracoes: any): Promise<any> {
    let link =  '/auth/configuracoes';

    return this.facaPost(link, configuracoes);
  }

  login(login: string, senha: string): Promise <any> {
      return this.http.post('/auth/contato/login', {login: login, senha: senha})
        .toPromise().then( (resposta: any) => {
          if(resposta.sucesso) {
            const usuario: any = {};

            Object.assign(usuario, resposta.data);

            localStorage.setItem(this.kEYUSER, JSON.stringify(usuario));

            this.usuario.next(usuario);
            return Promise.resolve();
          } else {
            this.usuario.next({});
            return Promise.resolve(resposta.erro)
          }

        }).catch( () => {
          return Promise.resolve('Ops! Não foi possível fazer a autenticação no momento');
        })
  }

  envieEmailRecuperacao(telefone: string): Promise <any> {
    return this.http.post('/auth/contato/recuperar', {telefone: telefone})
      .toPromise().then( (resposta: any) => {
        if(resposta.sucesso) {
          return Promise.resolve(resposta.data);
        } else {
          return Promise.reject(resposta.erro);
        }
      }).catch( (erro) => {
        return Promise.reject(erro);
      })
  }

  verifiqueCodigoConfirmacao(token: string, codigo: string): Promise <any> {
    return this.http.post('/auth/token/verifique/' + token, {codigo: codigo})
      .toPromise().then( (resposta: any) => {
        if(resposta.sucesso) {
          return Promise.resolve(resposta.data);
        } else {
          return Promise.reject(resposta.erro);
        }
      }).catch( (err) => {
        return Promise.reject(err);
      })
  }

  troqueSenha(token: string, senha: string): Promise <any> {
    return this.http.put('/auth/contato/troquesenha/' + token, {senha: senha})
      .toPromise().then( (resposta: any) => {
        if(resposta.sucesso) {
          return Promise.resolve(resposta.data);
        } else {
          return Promise.reject(resposta.erro);
        }
      }).catch( (err) => {
        return Promise.reject(err);
      })
  }

  limpeSessaoLocal() {
    localStorage.removeItem(this.kEYUSER);
  }

  logout(): Promise<any> {
    return    this.http.post('/auth/contato/logout', {}).toPromise().then((resposta: any) => {
      this.limpeSessaoLocal();
      this.usuario.next({});
      return Promise.resolve();
    }).catch( (err) => {
      return Promise.resolve(err);
    })
  }

  clearCookies(){
    return    this.http.post('/auth/clear', {}).toPromise().then((resposta: any) => {
      this.limpeSessaoLocal();
      return Promise.resolve();
    }).catch( (err) => {
      return Promise.resolve(err);
    })
  }

  verifiqueContato(telefone: string): Promise<any> {
    let erroPadrao = 'Ops! Não foi possível verificar seu telefone no momento';

    return this.http.get('/api/contato/loja/' + telefone, {})  .toPromise().then( (resposta: any) => {
        if(resposta.sucesso){
          return Promise.resolve(resposta.data)
        } else {
          return   Promise.reject( resposta.erro);
        }

    }).catch( (err) => {

      return Promise.reject( typeof err === "string" ? err : erroPadrao);
    })
  }

  valideContato(token: string, codigo: string): Promise<any>{
    let erroPadrao = 'Ops! Não foi possível validar seu codigo no momento';

    return this.http.post('/api/contato/valide/' + token, {codigo: codigo})
      .toPromise().then( (resposta: any) => {
        if(resposta.sucesso)
            return Promise.resolve();
        else return Promise.reject(resposta.erro);
      }).catch( (err) => {

        return Promise.reject( typeof err === "string" ? err : erroPadrao);
      })

  }

  crieConta(dados: any): Promise<any> {
    return this.http.post('/api/contato/loja', dados)
      .toPromise().then( (resposta: any) => {
        if(resposta.sucesso) {
          this.limpeSessaoLocal();
          return Promise.resolve();
        } else {
          return Promise.reject(resposta.erro)
        }
      }).catch( (err) => {
        let erroPadrao = 'Ops! Não foi possível criar sua conta no momento';
        return Promise.reject( typeof err === "string" ? err : erroPadrao);
      })
  }

  atualizeConta(dados: any): Promise<any> {
    return this.http.post('/api/contato/loja', dados)
      .toPromise().then( async (resposta: any) => {
        if(resposta.sucesso) {
          await this.atualizeUsuarioLogado()
          localStorage.recarregarSaldo =  true;
          return Promise.resolve();
        } else {
          return Promise.reject(resposta.erro)
        }
      }).catch( (err) => {
        let erroPadrao = 'Ops! Não foi possível atualizar sua conta no momento';
        return Promise.reject( typeof err === "string" ? err : erroPadrao);
      })
  }

  gereCodigoValidacao(contato: any){
    return this.http.put('/api/contato/telefone/gereCodigo' , {id: contato.id, tel: contato.telefone, pais: contato.codigoPais })
      .toPromise().then( (resposta: any) => {
        if(resposta.sucesso) {
          return Promise.resolve(resposta.data);
        } else {
          return Promise.reject(resposta.erro)
        }
      }).catch( (err) => {
        let erroPadrao = 'Ops! Não foi possível gerar codigo no momento';
        return Promise.reject( typeof err === "string" ? err : erroPadrao);
      })
  }

  valideContatoWhatsapp(contato: any, codigo: string){
    return this.http.put('/api/contato/telefone/valideCodigo' , {id: contato.id, codigo: codigo})
      .toPromise().then( (resposta: any) => {
        if(resposta.sucesso) {
          return Promise.resolve(resposta.data);
        } else {
          return Promise.reject(resposta.erro)
        }
      }).catch( (err) => {
        let erroPadrao = 'Ops! Não foi possível validar codigo no momento';
        return Promise.reject( typeof err === "string" ? err : erroPadrao);
      })
  }


  confirmeConta(token: string, contato: any) {
    return this.http.put('/api/contato/loja/' + token, contato)
      .toPromise().then( (resposta: any) => {
        if(resposta.sucesso) {
          this.limpeSessaoLocal();
          return Promise.resolve();
        } else {
          return Promise.reject(resposta.erro)
        }
      }).catch( (err) => {
        let erroPadrao = 'Ops! Não foi possível confirmar sua conta no momento';
        return Promise.reject( typeof err === "string" ? err : erroPadrao);
      })
  }

  obtenhaDadosToken(token: any) {
    let erroPadrao = 'Ops! Não foi possível validar o token';
    return this.http.get('/api/validacao/' + token, {})
      .toPromise().then( (resposta: any) => {
      if(resposta.sucesso){
        return Promise.resolve(resposta.data)
      } else {
        return   Promise.reject( resposta.erro);
      }

    }).catch( (err) => {

      return Promise.reject( typeof err === "string" ? err : erroPadrao);
    })
  }

  obtenhaToken(token: any) {
    let erroPadrao = 'Ops! Não foi possível recuperar o token';
    return this.http.get('/auth/token/' + token, {})
      .toPromise().then( (resposta: any) => {
        if(resposta.sucesso){
          return Promise.resolve(resposta.data)
        } else {
          return   Promise.reject( resposta.erro);
        }

      }).catch( (err) => {

        return Promise.reject( typeof err === "string" ? err : erroPadrao);
      })
  }


  obtenhaEnderecos() {
    return this.http.get('/api/enderecos/me', {})
      .toPromise().then( (resposta: any) => {
        if(resposta.sucesso){
          return Promise.resolve(resposta.data)
        } else {
          return   Promise.reject( resposta.erro);
        }

      }).catch( (err) => {

        return Promise.reject( typeof err === "string" ? err : '');
      })
  }

  removaConta(usuario: any) {
    return this.remova('/auth/usuario/' + usuario.id, {});
  }

  obtenhaIdSessao(): Promise<string> {
    const sessaoAtual = this.sessao.getValue();
    
    if (sessaoAtual) {
      return Promise.resolve(sessaoAtual);
    } else {
      // Se não tiver sessão, tenta obter do servidor
      return new Promise(resolve => {
        this.http.get('/auth/contato/logado').subscribe((resposta: any) => {
          this.sessao.next(resposta.sessao);
          resolve(resposta.sessao);
        }, error => {
          // Em caso de erro, retorna um valor padrão ou null
          resolve(null);
        });
      });
    }
  }
}
