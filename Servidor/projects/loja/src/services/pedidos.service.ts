import {Injectable} from "@angular/core";
import {ServerService} from "./ServerService";
import {HttpClient} from "@angular/common/http";
import {EnumOrigemPedido} from "../objeto/EnumOrigemPedido";

@Injectable({
  providedIn: 'root'
})
export class PedidosService  extends ServerService {

  constructor(http: HttpClient) {
    super(http);
  }

  async salvePedido(dadosPedido: any){
    if(!dadosPedido.origem)
      dadosPedido.origem = EnumOrigemPedido.Loja

    if(dadosPedido.pagamentos && dadosPedido.pagamentos.length){
      for(let i = 0; i < dadosPedido.pagamentos.length; i++){
        let dadospagamento = dadosPedido.pagamentos[i];

        if( dadospagamento.dadosCartao){
          let tokenize: any  = dadospagamento.dadosCartao.tokenize;

          if(tokenize){
            if(tokenize.dataExpiracao && new Date() > tokenize.dataExpiracao){

              let token = await this.tokenizeCartao(tokenize.card, tokenize.publicKey)

              if(token) dadospagamento.dadosCartao.token = token;
            }

            delete dadospagamento.dadosCartao.tokenize
          }
        }
      }
    }

    if(dadosPedido.guid)
      return   this.salve('/pedidos/' + dadosPedido.guid, dadosPedido)

    return this.salve('/pedidos', dadosPedido)
  }

  tokenizeCartao(card: any, publicKey: string){
    let dadosCartao: any = {
      type: "card",
      card: card
    }

    return new Promise( (resolve, reject) => {
      this.http.post('https://api.pagar.me/core/v5/tokens?appId=' + publicKey, dadosCartao)
        .toPromise().then( (res: any) => {
        resolve(res.id);
      }).catch((fail: any) => {
        let erro = 'Cartão inválido';
        if(fail.error && fail.error.errors && fail.error.errors['request.card'])
          erro += ":" +   fail.error.errors['request.card'][0]

        reject(erro)
      } )
    })

  }

  cancelePedido(pedido: any) {
    return this.http.put('/pedidos/' + pedido.guid + '/cancele/loja', pedido)
      .toPromise().then(this.retorno).catch(this.handleError);
  }

  gereLinkPagamento(pedido: any){
    return this.http.post(String(`/pedidos/${pedido.codigo}/pagamento/online`), {})
      .toPromise().then(this.retorno).catch(this.handleError);

  }

  tentePagamentoCartao(dadosPedido: any) {
    return this.salve(`/pedidos/${dadosPedido.codigo}/cartao/novoPagamento`, dadosPedido)
  }

  tentePagamentoPix(dadosPedido){
    return this.salve('/pedidos/cartao/novoPix', dadosPedido)
  }

  listePedidos(inicio: number, total: number){
    let dados: any = { i: inicio, t: total};

    return this.obtenha('/pedidos/me', dados);
  }

  obtenhaAdicionaisPedido(empresa: any) {
    return this.obtenha('/pedidos/adicionais-pedido?eid=' + empresa.id, {});
  }

  monitorePagamentoOnline(codigo: any){
    return this.obtenha('/pedidos/monitoramento/' + codigo, { })
  }


  obtenhaPorGuid(codigo: any, idEmpresa: any = null) {
    return this.obtenha('/pedidos/chave/' + codigo, {  });
  }


  validePedirNovamente(codigo: any) {
    return this.obtenha(String(`/pedidos/${codigo}/valide/pedirnovamente`), {  });
  }

  obtenhaPorCodigo(guid: any , idEmpresa: any = null) {
    return this.obtenha('/pedidos/chave/' + guid, {  });
  }

  configureAdicionaisDoPedido(pedido) {
    for (let item of pedido.itens) {
      item.adicionais = []
      if (item.adicionaisEscolhaSimples && item.adicionaisEscolhaSimples.id) {
        for (let campo in item.adicionaisEscolhaSimples)
          if (campo.startsWith('campo'))
            item.adicionais.push({ opcao: item.adicionaisEscolhaSimples[campo], qtde: 1})

      }
      if(item.adicionaisMultiplaEscolha && item.adicionaisMultiplaEscolha.id) {
        for (let campo in item.adicionaisMultiplaEscolha)
          if (campo.startsWith('lista'))
            for(let valoresOpcao of item.adicionaisMultiplaEscolha[campo].opcoes)
              item.adicionais.push(valoresOpcao)
      }

    }
  }


  async obtenhaComandaDoCartao(mesa: any, codigo: string) {
    return this.facaPost(`/comandas/mesa/${mesa.nome}/cartaocliente`, {mesa: mesa, codigo: codigo});
  }
}
