import { Injectable } from '@angular/core';
import {Router} from "@angular/router";
import {MyDetectorDevice} from "../app/shared/MyDetectorDevice";

@Injectable({
  providedIn: 'root'
})
export class DominiosService {
  codigoMesa: any;
  hashMesa: string;
  constructor(private router: Router, private deviceService: MyDetectorDevice) {   }

  obtenhaRaizCardapio() {
    if( location.host.indexOf('localhost') !== -1 )
      return  'loja'

    if(location.host.indexOf('meucardapio.ai') >= 0) return '';
    if(location.host.indexOf('promokit') < 0) return '';

    return location.pathname.split('/')[1]
  }

  obtenhaUrlCompleta(url){
    let nomePagina = this.obtenhaRaizCardapio();

    if(url.indexOf(nomePagina) >= 0) return url;

    return  "/" + nomePagina + '/' + url;
  }

  vaParaHome(){
    this.router.navigateByUrl(this.obtenhaUrlHome());
  }

  vaParaFinalizarPedido(state: any = {}){
    if( this.deviceService.isMobile() || this.deviceService.isTablet() ) {
      this.vaParaCarrinho(state);
    } else {
      this.router.navigateByUrl( this.obtenhaUrlCompleta('pedido'));
    }
  }

  obtenhaUrlHome(){
    let linkHome = '/' + this.obtenhaRaizCardapio();

    if(this.hashMesa)
      linkHome  = linkHome + '/local/' + this.hashMesa;

    return linkHome;
  }

  vaParaValidarLogin(retorno: string, contato: any = null){
    let link =  this.obtenhaUrlCompleta('login/validar/whatszapp');
    let params =  [];
    if(retorno)
      params.push('t=' + retorno)

    if(contato && contato.telefone)
      params.push('tel=' + contato.telefone)

    if(params.length) link = link  + '?' + params.join('&');


    this.router.navigateByUrl(link, { state: { contato: contato }});
  }
  vaParaLogin(retorno: string = null, telefone: string = null){
    let link =  this.obtenhaUrlCompleta('login');
    let params =  [];

    if(retorno)
      params.push('t=' + retorno)

    if(telefone)
      params.push('tel=' + telefone)

    if(params.length) link = link  + '?' + params.join('&');


    this.router.navigateByUrl(link);
  }

  vaParaCadastro(retorno: string = null, state: any = {}){
    let urlCompleta = this.obtenhaUrlCompleta('cadastro')

    if(retorno)
      urlCompleta = urlCompleta  + '?t=' + retorno


    this.router.navigate([urlCompleta] , { state: state, queryParamsHandling: 'merge' });
  }

  vaParaCarrinho(state: any = null){
    this.router.navigate([this.obtenhaUrlCompleta('carrinho')], { state: state, queryParamsHandling: 'merge' });
  }

  vaParaTelaAcompanharPedido(pedido: any, state: any =  {}){
    state.pedido = pedido;
    this.router.navigateByUrl( this.obtenhaUrlAcompanharPedido(pedido), state);
  }

  navegueParaUrl(url: string, state: any = null) {
    let urlCompleta = this.obtenhaUrlCompleta(url)

    this.router.navigate([urlCompleta] , { state: state, queryParamsHandling: 'merge' });
  }

  obtenhaUrlAtiva() {
    let nomePagina = this.obtenhaRaizCardapio();
    return location.pathname.replace(nomePagina, '').replace(/\//g, '');
  }

  obtenhaUrlPerfil(){
    return this.obtenhaUrlCompleta('perfil');
  }

  obtenhaUrlAcompanharPedido(pedido: any) {
    if(pedido)
      return this.obtenhaUrlCompleta(String(`pedido/acompanhar/${pedido.guid}`));
  }
}
