import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class GoogleMapsService {
  private apiKey  = ''; // Você pode definir um valor padrão ou mantê-lo vazio
  scriptLoaded = false;
  constructor(private httpCliente: HttpClient) { }

  load(libraries: string = null): Promise<void> {
    return new Promise((resolve, reject) => {
      this.httpCliente.get('/api/empresa/googlemaps/key').toPromise().then( (apiKey: any) => {
        this.apiKey = apiKey
        if (this.scriptLoaded) {
          resolve();
        } else {
          let src  = `https://maps.googleapis.com/maps/api/js?key=${this.apiKey}`;
          if(libraries)  src += `&libraries=${libraries}`
          const script = document.createElement('script');
          script.src = src;
          script.async = true;
          script.defer = true;
          script.onload = () => {
            console.log('script googleapis carregado...')
            this.scriptLoaded = true;
            resolve();
          };

          script.onerror = (error: any) => {
            console.error(error)
            reject(error);
          };
          document.head.appendChild(script);
        }
      })
    });
  }



  //novas busca: api

  autocompletePlaces(query: string): Observable<any> {
    const url = `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${query}&key=${this.apiKey}&types=geocode`;
    return this.httpCliente.get<any>(url).pipe(
      map(response => response.predictions.map(prediction => prediction.structured_formatting))
    );
  }

  geocoding(address: string): Observable<any> {
    const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${address}&key=${this.apiKey}`;
    return this.httpCliente.get<any>(url).pipe(
      map(response => response.results[0].geometry)
    );
  }

}
