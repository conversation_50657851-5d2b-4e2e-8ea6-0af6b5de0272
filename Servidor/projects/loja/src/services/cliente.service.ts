import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {ServerService} from "./ServerService";

@Injectable({
  providedIn: 'root'
})
export class ClienteService extends ServerService {

  constructor(protected http: HttpClient) {
    super(http);
  }

  confirmarContato(id: any): Promise<any> {
    return this.obtenha('/api/cliente/confirmar/' + id, {});
  }

  obtenhaMesa(id: any): Promise<any> {
    return this.obtenha('/api/mesas/' + id, {});
  }

  obtenhaMesaPorNome(id: any): Promise<any> {
    return this.obtenha('/api/mesas/nome/' + id, {});
  }

  obtenhaMesaPeloHash(hashMesa: any) {
    return this.obtenha('/api/mesas/hash/' + hashMesa, {});
  }


  obterLink(numero: any): Promise<any> {
    return this.obtenha('/api/cliente/enviarLink/' + numero, {});
  }

  obtenhaCliente(id: any): Promise<any> {
    return this.obtenha('/api/cliente/' + id, {});
  }

  obtenhaClientePorTelefone(telefone: any) {
    return this.obtenha('/api/cliente/tel/' + telefone, {});
  }

  obtenhaCadastroCompletarPeloCpf(cpf: any) {
    //so vai retornar cadastro incompletos do cpf
    if(!cpf) return Promise.resolve({})

    cpf = cpf.replace(/\D/g, "");

    if(cpf.length !== 11) return Promise.resolve({})

    return this.obtenha('/api/cliente/cadastro/' + cpf, {});
  }

  existeClienteComEmail(email: any) {
    return this.obtenha('/api/cliente/email/existe', {email: email});
  }

  obtenhaEmpresa(idOperador: string, ehMesa = false): Promise<any> {
    let url = '/api/empresa/';

    let params = []

    if( idOperador )
      params.push('op=' + idOperador)

    if(ehMesa)
       params.push('pre=1')

    if(params.length)
       url = String(`${url}?${params.join(',')}`)

    return this.obtenha(url, {});
  }

  obtenhaFranquia(grupoDeEmpresas: string): Promise<any> {
    return this.obtenha('/api/franquia/', {
      g: grupoDeEmpresas
    });
  }

  obtenhaLojasFranquia(grupoDeEmpresas: any, texto, inicio): Promise<any> {
    let params: any = {i: inicio, g: grupoDeEmpresas.id}
    if(texto) params.q = texto;
    return this.obtenha('/api/franquia/lojas', params);
  }

  obtenhaImagensInstagram(): Promise<any> {
    return this.obtenha('/api/empresa/instagram', {});
  }

  obtenhaRegras(): Promise<any> {
    return this.obtenha('/api/regras', {});
  }

  envieiMensagem(idMensagem: string, status: string): Promise<any> {
    return this.obtenha('/api/mensagens/enviei', {id: idMensagem, s: status});
  }

  idUltimaMensagem(): Promise<any> {
    return this.obtenha('/api/mensagens/ultima?ngsw-bypass=true&h=' + new Date().getTime(), {});
  }

  obtenhaPlanoEmpresariais(): Promise<any> {
    return this.obtenha('/api/planos-empresariais', {});
  }

  obtenhaIdEmpresa(): string {
    let idEmpresa = window.location.hostname.split(".")[0]

    if(idEmpresa === 'localhost' || idEmpresa === '192') idEmpresa =  'fibo' //default fibo para testes locais

    return  idEmpresa
  }

  valideCodigo(codigo: string, telefone: string) {
    return this.obtenha('/api/cliente/valide', {
      codigo: codigo,
      telefone: telefone
    });
  }

  calculeDescontoCupom(codigoCupom: string, pedido: any) {
    if(!codigoCupom) return Promise.resolve(null);

    return this.http.post('/cupons/desconto/', {pedido: pedido, codigo: codigoCupom})
      .toPromise().then(this.retorno).catch(this.handleError);
  }

  calculeDescontoPromocoes(pedido: any, empresa: any) {
    if(!empresa || !empresa.promocoes || empresa.promocoes.length === 0)
      return Promise.resolve(null) //não gastar a chamada se não existir uma promoção cadastrada

    return this.http.post('/promocoes/desconto', pedido)
      .toPromise().then(this.retorno).catch(this.handleError);
  }


  soliciteGarcom(id: any) {
    return this.http.post('/api/mesas/solicite-garcom/' + id, {})
      .toPromise().then(this.retorno).catch(this.handleError);
  }

  soliciteFecharMesa(id: any) {
    return this.http.post('/api/mesas/solicite-fechar-mesa/' + id, {})
      .toPromise().then(this.retorno).catch(this.handleError);
  }

  valideCupom(codigoCupom: string, pedido: any) {
    return this.http.post('/cupons/valide/' + codigoCupom, pedido)
      .toPromise().then(this.retorno).catch(this.handleError);
  }

  obtenhaSaldoCartao(plano: any) {
    return this.obtenha('/api/cartao/saldo', { pid: plano.id }  );
  }

  obtenhaSaldoResgate() {
    return this.obtenha( String(`/api/resgate/saldo`), {   }  );
  }

  facaOptinFidelidade(contato: any){
    return this.facaPost( String(`/api/fidelidade/gcom/optin`), contato  );
  }

  async obtenhaHorariosIndisponiveis(formaDeEntrega, dataEntrega) {
    return this.obtenha('/api/pedidos/horarios/indisponiveis', {fid: formaDeEntrega.id, dia: dataEntrega }  );
  }

  obtenhaGrupoDaEmpresa() {
    return this.obtenha('/api/obtenhaGrupoDaEmpresa', {});
  }

  obtenhaGrupoDeLojas(hostname: string) {
    return this.obtenha('/api/grupoDeLojas/obtenha', {
      hostname: hostname
    });
  }

  obtenhaGuidPedido() {
    return this.obtenha('/api/pedidos/guid/novo', {  });
  }

  obtenhaCuponsSelecionaveis(){
    return this.obtenha('/cupons/selecionaveis', {  });
  }

}
