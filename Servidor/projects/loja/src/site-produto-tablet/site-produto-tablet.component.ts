import {Component, ElementRef, Input, Output, EventEmitter, ViewEncapsulation, ViewChildren, QueryList, ViewChild} from '@angular/core';
import {ActivatedRoute, Router} from "@angular/router";
import {ProdutoService} from "../services/produto.service";
import {Location} from "@angular/common";
import {ClienteService} from "../services/cliente.service";
import {CarrinhoService} from "../services/carrinho.service";
import {DominiosService} from "../services/dominios.service";
import {ConstantsService} from "../services/ConstantsService";
import {MyDetectorDevice} from "../app/shared/MyDetectorDevice";
import {SiteProdutoBase} from "../site-produto/site-produto-base";
import {SiteCampoAdicionalComponent} from "../app/site-campo-adicional/site-campo-adicional.component";
import {AdicionaisCustomizadosComponent} from "../app/adicionais-customizados/adicionais-customizados.component";

@Component({
  selector: 'app-site-produto-tablet',
  templateUrl: './site-produto-tablet.component.html',
  styleUrls: ['./site-produto-tablet.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class SiteProdutoTabletComponent extends SiteProdutoBase {
  @ViewChildren('adicionalComponent') ctrlAdicionais: QueryList<SiteCampoAdicionalComponent>;
  @ViewChild('adicionaisCustomizados', { static: false }) adicionaisCustomizados: AdicionaisCustomizadosComponent;
  @Input() temaDark: boolean = true;
  @Output() onAdicionarProduto = new EventEmitter<any>();
  @Output() onCancelar = new EventEmitter<any>();

  erro: string = '';
  dialogAberto: boolean = false;
  adicionandoProduto: boolean = false;
  mensagemAbrirPedidos: string = '';
  modoVisualizacao = false;

  empresa: any;

  // Propriedades para gerenciar imagens
  imagens: any[] = [];
  selecionada: number = 0;
  imagensSlider: any[] = [];


  constructor(
    router: Router,
    produtoService: ProdutoService,
    deviceService: MyDetectorDevice,
    el: ElementRef,
    activatedRoute: ActivatedRoute,
    _location: Location,
    clienteService: ClienteService,
    carrinhoService: CarrinhoService,
    dominiosService: DominiosService,
    constantsService: ConstantsService
  ) {
    super(router, produtoService, deviceService, el, activatedRoute, _location,
          clienteService, carrinhoService, dominiosService, constantsService);
  }

  ngOnInit() {
    super.ngOnInit();

    // Aplicar tema escuro se estiver no painel lateral
    if (this.temaDark) {
      document.querySelector('app-site-produto-tablet')?.classList.add('tema-dark');
    }

    if (this.produto) {
      this.carregueImagens();
    }

    this.constantsService.empresa$.subscribe( (empresa) => {
      if (!empresa || !empresa.id) return;

      this.empresa = empresa;
      this.modoVisualizacao = empresa.cardapio && empresa.cardapio.modoVisualizacao;
      /*
      this.modoVisualizacaoQRcode = (empresa.cardapio && empresa.cardapio.modoVisualizacaoQRcode) ||
        (this.pedido && this.pedido.mesa && this.pedido.mesa.somenteLeitura);
       */
    });
  }

  // Método para cancelar
  cancelar(): void {
    this.onCancelar.emit();
  }

  // Método para tratar alteração de tamanho
  onAlterouTamanho(event: any): void {
    if (event && event.tamanho) {
      // Atualiza o tamanho do produto
      this.itemPedido.produtoTamanho = event.tamanho;
      this.itemPedido.atualizeTotal();
    }
  }

  // Método para adicionar ao carrinho
  adicionarAoCarrinho(): void {
    // Verificar se o produto está válido para adicionar
    if (this.valideProduto()) {
      // Emitir evento com o itemPedido
      this.onAdicionarProduto.emit(this.itemPedido);
      // Emitir evento de cancelar para fechar o menu lateral
      this.onCancelar.emit();
    }
  }

  // Método para validar o produto antes de adicionar
  private valideProduto(): boolean {
    // Verificar se existem adicionais customizados e validá-los
    if (this.adicionaisCustomizados) {
      let validos = this.adicionaisCustomizados.valideCampos();
      if (!validos) {
        return false;
      }
    }

    // Validar campos adicionais obrigatórios
    if (this.produto.camposAdicionais) {
      let totalAdicionais = this.produto.camposAdicionais.length;

      for (let i = 0; i < totalAdicionais; i++) {
        const campoAdicional = this.produto.camposAdicionais[i];

        if (!this.itemPedido.valideCampoAdicional(campoAdicional)) {
          if (this.ctrlAdicionais && this.ctrlAdicionais.toArray()[i]) {
            this.ctrlAdicionais.toArray()[i].exibaErro('Complemento é obrigatório');
            this.posicioneNoComplemento(campoAdicional);
          }
          return false;
        }
      }

      // Validar campos adicionais dos sabores
      let posicaoSabor = this.produto.camposAdicionais.length - 1;

      for (let i = 0; i < this.itemPedido.sabores.length; i++) {
        const sabor = this.itemPedido.sabores[i];
        if (sabor.produto !== this.produto.id) {
          for (let j = 0; j < sabor.camposAdicionais.length; j++) {
            posicaoSabor++;
            const campoAdicionalSabor = sabor.camposAdicionais[j];

            if (!this.itemPedido.valideCampoAdicional(campoAdicionalSabor)) {
              if (this.ctrlAdicionais && this.ctrlAdicionais.toArray()[posicaoSabor]) {
                this.ctrlAdicionais.toArray()[posicaoSabor].exibaErro('Complemento é obrigatório');
                this.posicioneNoComplemento(campoAdicionalSabor);
              }
              return false;
            }
          }
        }
      }
    }

    return true;
  }

  // Método para aumentar a quantidade
  aumentarQuantidade(): void {
    this.itemPedido.qtde += this.obtenhaIncremento();
    this.itemPedido.atualizeTotal();
  }

  // Método para diminuir a quantidade
  diminuirQuantidade(): void {
    if (this.itemPedido.qtde > this.obtenhaIncremento()) {
      this.itemPedido.qtde -= this.obtenhaIncremento();
      this.itemPedido.atualizeTotal();
    }
  }

  // Método para escolher opção
  escolheuNovaOpcao(event: any): void {
    const campo = event.campo;
    const opcao = event.opcao;

    if (campo && opcao) {
      if (this.ctrlAdicionais) {
        this.ctrlAdicionais.forEach((ctlAdicional: SiteCampoAdicionalComponent) => {
          ctlAdicional.exibaOuEscondaOpcaoQueDepende(event);
        });
      }

      this.itemPedido.atualizeTotal();

      if (event.elementoHTML) {
        this.scrollToNext(event.elementoHTML);
      }
    }
  }

  // Método para desmarcar opção
  desmarcouNovaOpcao(event: any): void {
    const campo = event.campo;
    const opcao = event.opcao;

    if (campo && opcao) {
      if (this.ctrlAdicionais) {
        this.ctrlAdicionais.forEach((ctlAdicional: SiteCampoAdicionalComponent) => {
          ctlAdicional.desmarcouOpcaoQueDepende(event);
        });
      }

      this.itemPedido.atualizeTotal();
    }
  }

  // Método para carregar imagens do produto
  carregueImagens() {
    if (this.produto && this.produto.imagens) {
      this.imagens = this.produto.imagens;

      // Preparar imagens para o slider
      this.imagensSlider = this.imagens.map(img => {
        return {
          image: 'https://fibo.promokit.com.br/images/' + img.root + '/' + img.linkImagem,
          thumbImage: 'https://fibo.promokit.com.br/images/' + img.root + '/' + img.linkImagem,
          title: this.produto.nome
        };
      });
    }
  }

  // Método para selecionar uma imagem
  selecionou(i: number) {
    this.selecionada = i;
  }

  // Método para exibir imagem em tela cheia
  exibaFullScreen() {
    if (this.nav) {
      this.nav.imageOnClick(this.selecionada);
    }
  }

  // Método para tratar clique na imagem
  abriuImagem($event: number) {
    // Implementação vazia ou personalizada conforme necessário
  }

  private processarUrlsImagens() {
    if (this.imagens) {
      this.imagens = this.imagens.map(imagem => {
        if (!imagem.url && imagem.linkImagem) {
          // Aqui você pode usar uma variável de ambiente ou um serviço de configuração
          // para obter a URL base das imagens
          imagem.url = `/images/empresa/${imagem.linkImagem}`;
        }
        return imagem;
      });
    }
  }

  // Método para posicionar a visualização no complemento que precisa ser preenchido
  posicioneNoComplemento(campoAdicional: any) {
    const $controleAdicional = document.getElementById('adicional_' + campoAdicional.id);

    if ($controleAdicional) {
      const topo = $controleAdicional.offsetTop - 10;
      const container = document.querySelector('.produto-content');
      if (container) {
        container.scrollTo(0, topo);
      }
    } else {
      setTimeout(() => {
        let $erros: any = document.getElementsByClassName('alert-danger');

        if (!$erros || !$erros.length) {
          $erros = document.getElementsByClassName('badge-danger');
        }

        if ($erros && $erros.length) {
          const topo = $erros[0].offsetTop - 40;
          const container = document.querySelector('.produto-content');
          if (container) {
            container.scrollTo(0, topo);
          }
        }
      }, 0);
    }
  }

  // Método para rolar para o próximo elemento
  scrollToNext(adicionalComponent: any) {
    if (!adicionalComponent) return;

    if (adicionalComponent.parentNode && adicionalComponent.parentNode.nextElementSibling) {
      const proximaDiv = adicionalComponent.parentNode.nextElementSibling.childNodes[0];
      if (proximaDiv) {
        proximaDiv.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest' });
      }
    }
  }

  // Método para abrir o dialog
  abrirDialog(mensagem: string): void {
    this.mensagemAbrirPedidos = mensagem;
    this.dialogAberto = true;
  }

  // Método para fechar o dialog
  fecharDialog(): void {
    this.dialogAberto = false;
  }
}
