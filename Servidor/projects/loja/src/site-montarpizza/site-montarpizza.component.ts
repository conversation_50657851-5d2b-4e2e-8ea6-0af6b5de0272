import {Compo<PERSON>, OnInit, Query<PERSON>ist, <PERSON>Child, ViewChildren} from '@angular/core';
import {ActivatedRoute, Router} from "@angular/router";
import {Location} from "@angular/common";
import {DialogCloseResult, DialogService} from "@progress/kendo-angular-dialog";
import {PopupUtils} from "../objeto/PopupUtils";
import {CarrinhoService} from "../services/carrinho.service";
import {ConstantsService} from "../services/ConstantsService";
import {ItemPedido} from "../objeto/ItemPedido";
import {AdicionalUtils} from "../objeto/AdicionalUtils";
import {SiteAdicionarProdutoComponent} from "../site-adicionar-produto/site-adicionar-produto.component";
import {SiteCampoAdicionalComponent} from "../app/site-campo-adicional/site-campo-adicional.component";
import {AdicionaisCustomizadosComponent} from "../app/adicionais-customizados/adicionais-customizados.component";

@Component({
  selector: 'app-site-montarpizza',
  templateUrl: './site-montarpizza.component.html',
  styleUrls: ['./site-montarpizza.component.scss', '../site-produto/site-produto.component.scss']
})
export class SiteMontarpizzaComponent implements OnInit {
  @ViewChild('siteAdicionarProduto' , { static: false }) siteAdicionarProduto: SiteAdicionarProdutoComponent;
  @ViewChildren('adicionalComponent') ctrlAdicionais: QueryList<SiteCampoAdicionalComponent>;
  @ViewChild('adicionaisCustomizados', { static: false }) adicionaisCustomizados: AdicionaisCustomizadosComponent;
  indiceItem: any;
  window: any;
  produtoCategoria: any
  carregando: any;
  pedido: any;
  target: any;
  empresa: any;
  estaRecebendoPedidos: any;
  permiteAgendamento: any;
  mensagemAbrirPedidos: any;
  modoVisualizacao: any;
  itemPedido: ItemPedido;

  erro: any;
  constructor(private carrinhoService: CarrinhoService,  private activatedRoute: ActivatedRoute,
              private constantsService: ConstantsService) {
  }
  ngOnInit( ): void {
    this.pedido = this.carrinhoService.obtenhaPedido();
    this.target = this.activatedRoute.snapshot.queryParams.target

    this.constantsService.empresa$.subscribe( (empresa) => {
      if (!empresa || !empresa.id)  return;

      this.empresa = empresa;
      this.estaRecebendoPedidos = empresa.estaRecebendoPedidos;
      this.permiteAgendamento = empresa.permiteAgendamento;
      this.mensagemAbrirPedidos = empresa.mensagemAbrirPedidos;
      this.modoVisualizacao = empresa.cardapio && empresa.cardapio.modoVisualizacao;


      if(!this.indiceItem)
        this.crieItemPedido();
      else
        this.editarItemPedido(this.indiceItem);

      this.carregou();
    });
  }

  carregou(){
    this.carregando = false;
    setTimeout( () => {
      this.siteAdicionarProduto.setControlesAdicionais(this.ctrlAdicionais,  this.adicionaisCustomizados );
      this.onAlterouTamanho(this.itemPedido.produtoTamanho)
    }, 0)
  }


  private crieItemPedido() {
    this.itemPedido = new ItemPedido(this.produtoCategoria,  1, null);
    this.itemPedido.produtoTamanho = this.produtoCategoria.tamanho;

    AdicionalUtils.prepareItemdoPedido(this.itemPedido)
  }

  private editarItemPedido(indiceItem: any) {
    let itemNoPedido = this.pedido.itens[indiceItem]

    let qtde = itemNoPedido ? itemNoPedido.qtde :  (this.produtoCategoria.valorInicial || 1 );

    this.itemPedido = new ItemPedido(this.produtoCategoria, qtde, itemNoPedido.observacao);

    if(itemNoPedido && itemNoPedido.adicionais)
      this.itemPedido.adicionais = itemNoPedido.adicionais

    if(itemNoPedido && itemNoPedido.produtoTamanho)
      this.itemPedido.produtoTamanho = this.produtoCategoria.tamanhos.find( tamanho => tamanho.id ===  itemNoPedido.produtoTamanho.id);

    this.itemPedido.sabores =  itemNoPedido.sabores;

    AdicionalUtils.prepareItemdoPedido(this.itemPedido);
  }

  fecheTela() {
    this.window.close();
  }

  // tslint:disable-next-line:member-ordering
  static abraComoPopup(router: Router, location: Location, activatedRoute: ActivatedRoute, dialogService: DialogService,
                       isMobile: boolean, produtoCategoria: any, indiceItem: string = null) {

    let dimensao = PopupUtils.calculeAlturaLargura(isMobile);

    const windowRef = dialogService.open({
      title: null,
      content: SiteMontarpizzaComponent,
      minWidth: 250,
      width: dimensao.largura,
      height: dimensao.altura
    });

    let tela:  SiteMontarpizzaComponent = windowRef.content.instance;

    tela.produtoCategoria = produtoCategoria;

    let params: any = {
      t: produtoCategoria.id
    };

    if( indiceItem ) {
      tela.indiceItem = indiceItem;
      params.e = indiceItem;
    }

    tela.window = windowRef;

    PopupUtils.abraJanela(router,  location, activatedRoute, windowRef, params);

    return windowRef;
  }


  onEscoheuSabor(sabores: any) {
    let saborMaisCaro: any = {};

    if(sabores.length){
      sabores.forEach( (saborPizza: any) => {
         if(!saborMaisCaro.preco || saborPizza.preco > saborMaisCaro.preco)
           saborMaisCaro = saborPizza;

      })

      if (saborMaisCaro.produto  !==  this.itemPedido.produto.id){
        let produto = this.constantsService.produtosLoja.find( produtoLoja => produtoLoja.id === saborMaisCaro.produto );

        this.itemPedido.produto = produto;
        this.siteAdicionarProduto.produto = produto;
        this.itemPedido.produtoTamanho = saborMaisCaro.produtoTamanho;

        AdicionalUtils.prepareItemdoPedido(this.itemPedido);
        AdicionalUtils.prepareAdicionaisSabores(  this.itemPedido, produto)

        setTimeout(() => {
          this.onAlterouTamanho(this.itemPedido.produtoTamanho)
        }, 0)
      }

    }
  }

  onAlterouTamanho(produtoTamanho: any) {
    this.ctrlAdicionais.forEach( (ctlAdicioanais) => {
      ctlAdicioanais.alterouTamanho(produtoTamanho);
    })
  }

}
