import {Component, HostListener, Inject, Input, LOCALE_ID, OnDestroy, OnInit, Output, ViewChild} from '@angular/core';
import {PedidoLoja} from "../objeto/PedidoLoja";
import {CarrinhoService} from "../services/carrinho.service";
import {ActivatedRoute, Router} from "@angular/router";
import {NgForm} from "@angular/forms";
import {Subscription} from "rxjs";
import {ITela} from "../objeto/ITela";
import {ClienteService} from "../services/cliente.service";
import {PedidosService} from "../services/pedidos.service";
import {DominiosService} from "../services/dominios.service";
import {SiteProdutoComponent} from "../site-produto/site-produto.component";
import {DialogCloseResult, DialogService} from "@progress/kendo-angular-dialog";
import {formatCurrency, Location} from "@angular/common";
import {AutorizacaoLojaService} from "../services/autorizacao-loja.service";
import {ConstantsService} from "../services/ConstantsService";
import {PopupUtils} from "../objeto/PopupUtils";
import {TelaAdicionalPedidoComponent} from "../app/tela-adicional-pedido/tela-adicional-pedido.component";
import {MyDetectorDevice} from "../app/shared/MyDetectorDevice";
import {AdicionalUtils} from "../objeto/AdicionalUtils";
import {FormaDeEntrega} from "../objeto/FormaDeEntrega";
import {PixelTrackingService} from "../services/pixel-tracking.service";
import {NotificationService} from "@progress/kendo-angular-notification";
import {ModalVincularComandaComponent} from "../loja-modal-vincular-comanda/modal-vincular-comanda.component";

declare var $;
declare var fbq;
declare var gtag;

@Component({
  selector: 'app-loja-carrinho',
  templateUrl: './loja-carrinho.component.html',
  styleUrls: ['./loja-carrinho.component.scss']
})
export class LojaCarrinhoComponent implements OnInit, OnDestroy, ITela {

  @Input() desktop = false;
  @Input() exibirTopo = true;
  empresa: any;
  pedido: PedidoLoja;
  titulo = 'Carrinho';
  exibirSomenteItens = false;
  exibirBotoes = true;
  @ViewChild('frm')  frm: NgForm;
  msgErro: any = '';
  msg: any = '';
  eviandoPedido: any = false;
  estahCompleto: boolean;
  cuponsSelecionar = [];
  urlWhatsapp: string;
  inscricao: Subscription;
  private nomePagina: string;
  FormaDeEntrega = FormaDeEntrega
  window: any;
  isMobile = false;
  calculandoTaxa: boolean;
  codigoMesa: any;
  aplicandoCupom: boolean;
  erroCupom: string;
  aplicarCupom: any = false;
  PassoMeuCarrinho = new Passo('', 'fe-shopping-cart mr-1  ')
  PassoEntrega = new Passo('Entrega')
  PassoIdentificacao = new Passo('Identificação')
  PassoPagamento = new Passo('Pagamento')
  PassoFinalizar = new Passo('Finalizar')
  PassoFeito = new Passo('...')
  passos: any[] = [
    this.PassoMeuCarrinho,
    this.PassoEntrega,
    this.PassoIdentificacao,
    this.PassoPagamento
  ];
  passosExibir: any[] = this.passos;
  camposAdicionais: any;
  cupomSelecionado: any = null//;
  selecionandoCupom: any;
  exibirModalEscolherCupons: any;
  fbqPurchase: any;
  gtagpurchase: any;
  private notificacaoAtual: any = null;
  constructor(private clienteService: ClienteService, private carrinhoService: CarrinhoService,
              private dialogService: DialogService, @Inject(LOCALE_ID) public locale: string,
              private router: Router, private pedidosService: PedidosService, private activatedRoute: ActivatedRoute,
              public dominiosService: DominiosService, private location: Location, private autorizacao: AutorizacaoLojaService,
              private constantsService: ConstantsService, private deviceService: MyDetectorDevice,
              @Inject(PixelTrackingService) private pixelTrackingService: PixelTrackingService,
              private notificationService: NotificationService) {
    this.nomePagina = dominiosService.obtenhaRaizCardapio();

    document.body.classList.add("ativar_fundo_tema");
  }

  static abraComoPopup(loja: LojaCarrinhoComponent, router: Router, location: Location, activatedRoute: ActivatedRoute,
                       dialogService: DialogService, isMobile: boolean, pedido: PedidoLoja) {

    let dimensao = PopupUtils.calculeAlturaLargura(isMobile)

    const windowRef = dialogService.open({
      title: null,
      content: TelaAdicionalPedidoComponent,
      minWidth: 250,
      width: dimensao.largura,
      height: dimensao.altura
    });

    let telaProduto: TelaAdicionalPedidoComponent = windowRef.content.instance;

    let params: any = {};

    telaProduto.inicialize(windowRef, pedido);

    windowRef.result.subscribe((result: any) => {
      if( result === true ) {
        loja.navegueParaPagamento(true);
      }
      else if (result.back) {

      } else if (result instanceof DialogCloseResult) {
      }
    });

    PopupUtils.abraJanela(router,  location, activatedRoute, windowRef, params);

    return windowRef;
  }


  ngOnInit() {

    this.passosExibir[1].proximo = true;
    this.pedido = this.carrinhoService.obtenhaPedido();

    if(this.desktop){
      this.exibirTopo = false;
      this.titulo = 'Seu pedido'
    }

    setTimeout(() => {
      this.configureAdicionais()
    }, 0 )


    this.constantsService.empresa$.subscribe(empresa => {
      if(empresa){
        this.empresa = empresa;
        this.calculeStatusPedido();

        this.clienteService.obtenhaCuponsSelecionaveis().then( (cupons: any) => {
          if(cupons && cupons.length > 0) {
            let cupomNaoSelecionado = {nome: "Sem cupom", codigo: null}
            this.cuponsSelecionar = [cupomNaoSelecionado].concat(cupons);
            console.log(this.cuponsSelecionar)
          }
        })
      }
    });

    this.inscricao = this.carrinhoService.alterouPedido.subscribe( (pedido) => {
      this.msgErro =  null
      this.calculeStatusPedido();
    });


  }

  onItemClick(passo: any): void {
  }

  criarEndereco() {
    if(this.desktop){
      this.vaParaTelaFinalizarDesktop();
    }else {
      this.router.navigate(['/' + this.nomePagina + '/criar-endereco'], {queryParamsHandling: 'merge'});
    }

  }

  abraTelaDadosCliente(){
    if(this.desktop){
      this.vaParaTelaFinalizarDesktop();
    }else {

      this.router.navigate(['/' + this.nomePagina + '/cliente'],
        {queryParamsHandling: 'merge', state: { informarNomeCompleto: this.pedido.contato.informarNomeCompleto} });

    }
  }

  abraTelaFormaDePagamento() {
    if(this.desktop) {
      this.vaParaTelaFinalizarDesktop();
    } else{
      if( !this.camposAdicionais ) {
        this.pedidosService.obtenhaAdicionaisPedido(this.empresa).then((adicionais) => {
          this.camposAdicionais = adicionais;
          this.pedido.adicionais = {};
          this.pedido.camposAdicionais = adicionais;
          AdicionalUtils.prepareAdicionais(this.pedido, adicionais);
          this.navegueParaPagamento();
        });
      } else {
        this.navegueParaPagamento();
      }
    }
  }

  navegueParaPagamento(passouPeloAdiconaisPedidos = false,
                       autenticarCartao = false, mensagem = null){
    if( this.pedido.camposAdicionais && this.possuiOpcoesValidas(this.pedido.camposAdicionais)  && !passouPeloAdiconaisPedidos ) {
      LojaCarrinhoComponent.abraComoPopup(this, this.router, this.location, this.activatedRoute, this.dialogService,
        this.deviceService.isMobile(), this.pedido);

    } else {
      let link: string = '/' + this.nomePagina + '/pagamento';
      let queryParams: any  = {};

      if(autenticarCartao){
        queryParams.authct = 1;
        if(mensagem) queryParams.msg =  mensagem
      }

      this.router.navigate([link], { queryParams: queryParams, queryParamsHandling: 'merge'});
    }
  }

  reabraAutenticacaoCartao(mensagem: string){
    this.navegueParaPagamento(true, true, mensagem)
  }

  abraTelaFormaEntrega() {
    if(this.desktop) {
      this.vaParaTelaFinalizarDesktop();
    } else{
      this.router.navigate(['/' + this.nomePagina + '/forma-entrega'], {queryParamsHandling: 'merge'});
    }

  }

  abraTelaFinalizar(){
    if(this.desktop){
      this.vaParaTelaFinalizarDesktop();
    }else {
      this.abraTelaFormaEntrega();
    }
  }

  vaParaTelaFinalizarDesktop() {
    if( this.empresa.tema === 'quiosque') {
      this.router.navigate(['/' + this.nomePagina + '/pedido-totem'], {queryParamsHandling: 'merge'});
      return;
    }
    this.router.navigate(['/' + this.nomePagina + '/pedido'], {queryParamsHandling: 'merge'});
  }

  gereLinkPagamento(){
    $("#alertaGerandoPagamento").modal();
    this.pedidosService.gereLinkPagamento(this.pedido).then((res: any) => {
      this.pedido.linkPagamento = res.link;
      this.carrinhoService.salvePedido(this.pedido);
      window.location.href = res.link;
    }).catch((erro) => {
      this.msgErro = erro;
      this.mostrarNotificacaoErro(erro);
    });
  }

  facaLoginGuest(resposta: any){
    if(!resposta.fezLoginGuest) return Promise.resolve();

    return this.autorizacao.atualizeUsuarioLogado().then( () => {   Promise.resolve() });
  }

  // Método para fechar todas as notificações ativas
  private fecharNotificacoes(): void {
    if (this.notificacaoAtual) {
      this.notificacaoAtual.hide();
      this.notificacaoAtual = null;
    }
  }

  fazerPedido(preencheu: boolean) {
    // Fechar todas as notificações antes de prosseguir
    this.fecharNotificacoes();

    if(this.eviandoPedido) return;

    if(this.empresa.associarCartaoFechamentoPedido && this.pedido.mesa)
      return this.abraModalVincularComandaPedido()


    this.facaPedidoEfetivo(preencheu);
  }

  possuiOpcoesValidas(camposAdicionais: any[]) {
    for(let campoAdicional of camposAdicionais)
      if(campoAdicional.opcoesDisponiveis && campoAdicional.opcoesDisponiveis.length > 0)
        return true;

    return false;
  }

  facaPedidoEfetivo(preencheu: boolean = false) {

    if(this.pedido.validarValorPedido()){
      let totalPagar = this.pedido.obtenhaValorAhPagar();

      if(totalPagar !== this.pedido.pagamento.dadosCartao.valorPagamento){
        this.msgErro = 'Valor pedido foi alterado, será necessário uma nova autenticação do cartão'
        this.mostrarNotificacaoErro(this.msgErro);
        setTimeout(() => {
          this.reabraAutenticacaoCartao(this.msgErro);
        })
        return;
      }
    }

    this.eviandoPedido = true;
    this.msgErro = null;
    this.fbqPurchase = null;
    this.gtagpurchase = null;
    this.pedidosService.salvePedido(this.pedido.obtenhaDadosEnvio(this.empresa)).then( (resposta: any) => {
      this.eviandoPedido = false;
      this.pedido.codigo = resposta.codigo;
      this.pedido.guid = resposta.guid;

      if(this.pedido.usandoCashbackNoPagamento())
        localStorage.recarregarSaldo =  true;

      this.pixelTrackingService.trackPurchase(this.pedido, resposta.codigo, resposta.id, resposta.guid);

      this.facaLoginGuest(resposta).then( () => {
        if(resposta.gerarLinkPagamento){
          this.pedido.gerarLinkPagamento = true;
          this.carrinhoService.salvePedido(this.pedido);
          this.gereLinkPagamento();
        } else {
          if(resposta.abrirUrl) //este caso só ocorre nos cardápios do módulo 'somente cardapio'
            window.open(  this.urlWhatsapp );
          else if(!this.pedido.mesa || !this.pedido.mesa.id) {
            window.open(resposta.urlAbrir);
          }

          let state: any = { empresa: this.empresa, urlAbrir: resposta.urlAbrir };

          if(resposta.aguardandoTokenizar)
            state.aguardandoTokenizar = resposta.aguardandoTokenizar

          this.carrinhoService.salveUltimoPedido(state);
          console.log(this.pedido)
          //this.router.navigate(resposta.urlAbrir, { });
          this.dominiosService.vaParaTelaAcompanharPedido(this.pedido, state);
        }
      });
    }).catch( (erro) => {
      this.eviandoPedido = false;
      this.msgErro = erro;
      this.mostrarNotificacaoErro(erro);
    });
  }

  abraTelaLoja() {
    let url = '/' + this.nomePagina;

    if(location.pathname.endsWith('/pedido'))
      url = url + '?target=' + location.pathname

    this.router.navigateByUrl(url);
  }

  onSubmit() {

  }

  novoPedido() {
    this.abraTelaLoja();
    this.pedido = this.carrinhoService.obtenhaPedido();
  }

  ngOnDestroy(): void {
    if( this.inscricao ) this.inscricao.unsubscribe();

    document.body.classList.remove("ativar_fundo_tema");
  }

  identificacaoCompleta(){
    delete this.PassoIdentificacao.mensagem;

    let completa = this.pedido.contato.telefone && this.pedido.contato.nome;

    if(completa){
      if(this.empresa.camposExtras  ){
        let campoCpf =  this.empresa.camposExtras.find((item: any) => item.nome === 'cpf');
        if(campoCpf && !campoCpf.opcional)
          if(!this.pedido.contato.cpf   ){
            this.PassoIdentificacao.mensagem = 'Informar o cpf'
            completa = false;
          }

        let campodata =  this.empresa.camposExtras.find((item: any) => item.nome === 'datanascimento');

        if(campodata && !campodata.opcional)
          if(!this.pedido.contato.dataNascimento){
            this.PassoIdentificacao.mensagem = 'Informar data nascimento'
            completa = false;
          }
      }



      if(this.empresa.vendeOnline){
        if(this.pedido.contato.nome.split(' ').length === 1){
          this.pedido.contato.informarNomeCompleto = true;
          this.PassoIdentificacao.mensagem = 'Informar seu nome completo'
          completa = false;
        }
      }
    } else {
      this.PassoIdentificacao.mensagem = 'Informar telefone/nome'
    }

    return completa;
  }

  private calculeStatusPedido() {
    if(!this.empresa) return;
    this.PassoEntrega.preenchido =   this.pedido.entrega.foiPreenchida();
    this.PassoPagamento.preenchido =  this.pedido.informouFormaDePagamento()
    this.PassoIdentificacao.preenchido = this.identificacaoCompleta();

    if(!this.pedido.mesa){
      let temMinimo = this.pedido.temValorMinimoDaFormaEscolhida(this.empresa.formasDeEntrega);
      let ultrapossouMaximo = this.pedido.ultrapassouValorMaximoDaFormaEscolhida(this.empresa.formasDeEntrega)
      this.estahCompleto =  this.PassoEntrega.preenchido  && this.PassoPagamento.preenchido &&
        this.PassoIdentificacao.preenchido  && (temMinimo && !ultrapossouMaximo);

      if(!temMinimo) {
        let valorMinimo = this.pedido.obtenhaValorMinimoDaFormaEscolhida(this.empresa.formasDeEntrega)
        this.msgErro = 'As entregas são feitas somente para valores acima  de R$ ' + valorMinimo.toFixed(2)
        this.mostrarNotificacaoErro(this.msgErro);
      } else if(ultrapossouMaximo){
        let valorMaximo = this.pedido.obtenhaValorMaximoDaFormaEscolhida(this.empresa.formasDeEntrega)

        this.msgErro = 'As entregas são feitas somente para valores abaixo  de R$ ' + valorMaximo.toFixed(2)
        this.mostrarNotificacaoErro(this.msgErro);
      }


    } else{
      this.PassoEntrega.preenchido = true;
      this.PassoPagamento.preenchido = true;
      this.estahCompleto =  this.PassoIdentificacao.preenchido =   this.pedido.contato.nome &&  this.pedido.contato.telefone;
    }

    this.passosExibir = this.passos.filter((passo: any) => !passo.preenchido)

    if(this.passosExibir.length === 1){
      this.passosExibir.push(this.PassoFinalizar);
      this.passosExibir[1].proximo = true;
    } else if(this.passosExibir.length  <  4){
      this.passosExibir[1].proximo = true;
      this.passosExibir.splice(1, 0, this.PassoFeito)
    }

    if(this.empresa.integracaoPedidoFidelidade)
      this.pedido.calculePontosAhReceber(this.empresa, this.empresa.integracaoPedidoFidelidade)

    setTimeout(() => {
      this.configureAdicionais()
    }, 0);
  }



  deveExibirTopo() {
    return false;
  }

  deveExibirMenu(){
    return false;
  }

  removaItem(item: any) {
    this.pedido.removaItem(item);
    this.calculandoTaxa = true;
    this.carrinhoService.atualizeDescontosETaxas(this.pedido, this.empresa).then((erro: any) => {
      this.calculandoTaxa = false;
      if(!erro)
        this.carrinhoService.salvePedido(this.pedido)
      else
        this.msgErro = erro

    })
  }



  setModoPanel(){
    this.exibirTopo = false;
    this.exibirSomenteItens = true;
    this.exibirBotoes = false;
  }

  finalizouPedido(){
    this.exibirSomenteItens = false;
  }

  @HostListener('window:popstate', ['$event'])
  onPopState(event) {
    if( this.window ) {
      this.window.close({
        back: true
      });
    }
  }

  editarItem(i: number, item: any) {
    this.window = SiteProdutoComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
      this.dialogService, this.isMobile, item.produto, i + '');

  }


  private configureAdicionais() {
    this.pedido.configureAdicionais();

    for(let item of this.pedido.itens) {
      if(item.adicionais) {
        item.configureAdicionais();
      }
    }
  }

  apliqueCupom() {//global
    if(!this.pedido.codigoCupom || this.aplicandoCupom) return;

    delete  this.erroCupom;
    this.aplicandoCupom = true;
    this.clienteService.calculeDescontoCupom(this.pedido.codigoCupom, this.pedido.obtenhaDadosEnvio(this.empresa)).then( (cupom: any) => {
      this.aplicandoCupom = false;
      if(cupom.erro){
        this.erroCupom = cupom.erro;
        this.mostrarNotificacaoErro(cupom.erro);
      }  else {
        this.pedido.apliqueCupom(cupom)
        this.carrinhoService.salvePedido(this.pedido);

      }
    }).catch( (erro) => {
      this.aplicandoCupom = false;
      this.erroCupom = 'Ops! nao foi possível verificar o cupom';
      this.mostrarNotificacaoErro(this.erroCupom);
    })

  }

  removaCupom() {
    this.pedido.removaCupom();
    this.carrinhoService.salvePedido(this.pedido);
  }

  toggleAplicarCupom() {
    this.aplicarCupom = ! this.aplicarCupom;

  }

  escolherCupons(event: any){

    this.cupomSelecionado = this.cuponsSelecionar[0];
    this.selecionandoCupom = true;
    this.exibirModalEscolherCupons = true


  }

  proximoPasso() {
    this.dispareEventoInicioCheckout();
    if(!this.PassoEntrega.preenchido)
      return this.abraTelaFormaEntrega();

    if(!this.PassoIdentificacao.preenchido)
      return this.abraTelaDadosCliente();

    return this.abraTelaFormaDePagamento();
  }

  obtenhaPassos() {
    return this.passos.filter(passo => !passo.preenchido)
  }

  valorPencentual(cashback: number) {
    // tslint:disable-next-line:radix
    return  Number.parseInt((cashback * 100).toFixed(2))
  }

  deveTerBordas() {
    return true;
  }

  deveExibirBannerTema() {
    return false;
  }

  abraEmpresa(empresa: any) {
    if( window['telaMultiLoja'] ) {
      if( window['multipedido'] ) {
        this.router.navigateByUrl('/marca/' + empresa.dominio).then( (navegou2) => {
          this.constantsService.recarregueEmpresa();
        });
      } else {
        this.router.navigateByUrl('/loja/' + empresa.dominio);
      }
      return;
    }

    this.abraTelaLoja();
  }

  estaPontuando() {
    if(this.empresa && this.empresa.integracaoPedidoFidelidade){
      let integracao: any = this.empresa.integracaoPedidoFidelidade;

      if(this.pedido && this.pedido.mesa)
         return integracao.pontuarMesas;

      return integracao.pontuando;
    }

    return false;

  }

  escolheuCupom(cupom: any) {
    if(cupom.codigo) {
      this.pedido.codigoCupom = cupom.codigo
      this.apliqueCupom()
    }
    this.cupomSelecionado = null
    this.fecheModalCupom()
    this.selecionandoCupom = false;

  }

  apliqueCupomTelaSelecionar() {
    this.apliqueCupom()
    this.fecheModalCupom()
    this.selecionandoCupom = false;
  }

  verRegras(cupom: any, event: any) {
    cupom.verRegras = !cupom.verRegras;
    event.preventDefault();
  }

  fecheModalCupom() {
    this.exibirModalEscolherCupons = false;
  }

  obtenhaSaldo(saldo) {
    if(!this.empresa) return  saldo;

    let descricao = formatCurrency(saldo, this.locale, "R$")

    if(this.empresa.integracaoFidelidade && this.empresa.integracaoFidelidade.sistema === 'gcom')
      descricao = descricao.replace('R$', this.empresa.idRede === 2 ? "R$G" : "R$C")


    return descricao;
  }

  dispareEventoInicioCheckout() {
    this.autorizacao.obtenhaIdSessao().then( (idSessao: string) => {
      if (typeof fbq !== 'undefined') {
        if (this.pedido && !this.pedido.disparouEventoIniciarCheckout) {
          this.pedido.disparouEventoIniciarCheckout = true;

          const idEvento = idSessao + '-checkout-' + new Date().getTime();
          this.carrinhoService.notifiqueInicioCheckout(this.pedido, idEvento);

          this.pixelTrackingService.trackInitiateCheckout(this.pedido, idEvento);
        }
      }
    });
  }

  finalizarPedidoTotem() {
    this.router.navigate(['/' + this.nomePagina + '/pedido-totem'], {queryParamsHandling: 'merge'});
  }

  // Método para mostrar notificações de erro
  private mostrarNotificacaoErro(mensagem: string): void {
    // Fechar notificação anterior, se existir
    if (this.notificacaoAtual) {
      this.notificacaoAtual.hide();
    }

    this.notificacaoAtual = this.notificationService.show({
      content: mensagem,
      cssClass: 'erro-notificacao',
      animation: { type: 'slide', duration: 400 },
      position: { horizontal: 'center', vertical: 'top' },
      type: { style: 'error', icon: true },
      closable: true,
      hideAfter: 5000
    });
  }

  //loja-carrinho.component.ts

  private abraModalVincularComandaPedido() {
    const dialogRef = this.dialogService.open({
      title: '',
      content: ModalVincularComandaComponent,
      width: 400,
      height: 'auto'
    });

    let tela: any = dialogRef.content.instance;

    tela.mesa = this.pedido.mesa;

    dialogRef.result.subscribe((comanda: any) => {
      if (comanda && comanda.id) {
        console.log('Comanda vinculada com sucesso');
        this.pedido.idComanda = comanda.id;
        this.pedido.mesa.codigoCartaoCliente = comanda.codigoCartaoCliente;
        this.facaPedidoEfetivo();
      } else {
        console.log('Usuário cancelou ou deu erro');
      }
    });
  }

  getCategoriasNomes(cupom) {
    if(cupom.categorias && cupom.categorias.length)
      return  cupom.categorias.map((cat:any) => cat.nome).join(', ')

    return ""
  }

  getTamanhosNomes(cupom: any){
    if(cupom.produtosTemplateTamanho && cupom.produtosTemplateTamanho.length)
      return  cupom.produtosTemplateTamanho.map((cat: any) => cat.descricao).join(', ')

    return ""
  }

  getNomesProdutos(cupom: any){ //.produto.nome
    if(cupom.produtos && cupom.produtos.length)
      return  cupom.produtos.map((prod:any) => prod.nome).join(', ')
    return ""
  }
}

class Passo {
  preenchido = false;
  proximo = false;
  mensagem: string;
  constructor(public descricao: string, public iconClass: string = null){

  }
}
