<div *ngIf="exibirTopo">
  <app-header-tela [titulo]="titulo" [retorno]="'/'"></app-header-tela>

  <div class="row breadcrumb-passos" [hidden]="passosExibir.length <= 1">
    <kendo-breadcrumb [items]="passosExibir" (itemClick)="onItemClick($event)"    >
      <ng-template kendoBreadCrumbItemTemplate let-passo let-index="index"  >
        <div class="item" role="button" [ngClass]="{'incompleto':index > 0,
                                                    'proximo': passo.proximo && passo.descricao != PassoFeito.descricao}" >
          <i class="{{passo.iconClass}}" *ngIf="passo.iconClass"> </i>

          <span>
            {{ passo.descricao }}
          </span>
        </div>
      </ng-template>
    </kendo-breadcrumb>
  </div>

</div>



<div  class="fundo div_carrinho" class="{{empresa?.tema}}">
  <!-- Componente de notificação Kendo UI -->
  <kendo-notification-container>
  </kendo-notification-container>

  <div class="d-flex justify-content-center align-items-center flex-column" style="height: 400px;" *ngIf="pedido && pedido.itens.length == 0">
    <div class="p-2">
      <img src="https://promokit.com.br/assets/loja/sem-pedidos.png" width="96px"/>
    </div>
    <div class="p-0">
      <h3 class="text-muted">Sua sacola está vazia</h3>
    </div>
    <div class="p-0">
      <h5 class="text-muted">Adicione Itens</h5>
    </div>
  </div>

  <form [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"
        novalidate #frm="ngForm" *ngIf="pedido && pedido.itens.length > 0" >
    <div>
      <div class="mt-2 espaco_topo"></div>

      <div class="titulo"  [ngClass]="{'com-mesa': pedido.mesa}">
        <h4 class="d-inline">
          <span *ngIf="pedido.mesa"><b>Mesa {{pedido.mesa.nome}}</b> - </span>
          Seu Pedido <span *ngIf="!pedido.mesa && pedido.codigo"> #{{pedido.codigo}} </span>
        </h4>

        <button *ngIf="exibirTopo || exibirSomenteItens"  [hidden]="pedido.ehDeMultiloja() || true"
                class="btn btn-outline-blue btn-xs  font-13" (click)="abraTelaLoja()">Adicionar mais</button>
      </div>

      <div class="linha nao_mobile"></div>

      <div class="desktop">
        <div *ngFor="let itensDaEmpresa of pedido.itensPorEmpresa">
          <div class="mt-2" style="overflow: auto;">
            <img src="/images/empresa/{{itensDaEmpresa.empresa.logo}}" class="float-left mr-1" style="width: 36px"/>

            <div>
              <div class="float-left">
                <div class="label_empresa">{{itensDaEmpresa.empresa.nome}}</div>
                <div>{{itensDaEmpresa.itens.length}} produto<span *ngIf="itensDaEmpresa.itens.length > 1"></span></div>
              </div>
              <div class="float-right">
                <button class="btn btn-sm btn-secondary" (click)="abraEmpresa(itensDaEmpresa.empresa)">Adicionar Mais</button>
              </div>
            </div>
          </div>
          <div *ngFor="let item of itensDaEmpresa.itens;let i=index;" class="produto pt-2 pb-2 item_produto">
            <section class="cart-item">
              <div class="mb-1 flex flex-row gap-4">
                <img *ngIf="item.produto.imagens" class="img img-fluid mb-1" style="height: 40px;"
                      [src]="'https://promokit.promokit.com.br/images/empresa/' + item.produto.imagens[0].linkImagem" alt="Imagem">
                <div class="full">
                  <div class="mb-0 flex flex-row justify-between">
                    <h4 class="mt-0 mb-1">
                      {{item.qtde}}{{item.obtenhaUnidade()}}
                      {{item.obtenhaDescricao()}}
                    </h4>
                    <div class="flex flex-row gap-1 text-sm">
                      <h4 class="preco mt-0" *ngIf="!item.brinde">
                        {{item.total | currency: 'BRL'}}

                      </h4>

                      <h4 class="resgate-preco mt-0" *ngIf="item.brinde">
                        -{{item.valorResgatado}} {{item.produto.acumulo}}
                      </h4>

                    </div>
                  </div>

                  <div class="mb-0 flex flex-row justify-between">
                    <div  *ngIf="!pedido.codigo">
                      <h6 *ngIf="!item.produto.ehBrinde" class="d-inline cpointer text-blue"  (click)="editarItem(i, item)">
                        <i class="fe-edit mr-1"></i>
                        <b>Editar</b></h6>
                      <h6 class="d-inline cpointer ml-2 text-danger" (click)="removaItem(item)">
                        <i class="fe-trash mr-1"></i>Remover</h6>
                    </div>


                    <div *ngIf="item.cashback > 0">

                      <span class="badge badge-primary">
                          <i class="fe-star-on " ></i> {{item.cashback | currency: 'BRL'}}
                        </span>
                    </div>

                  </div>
                </div>
              </div>



              <div class="flex flex-row items-center gap-4 opcoes">
               <span *ngIf="item.valoresAdicionais && item.valoresAdicionais.length > 0" class="font-11">
                  <ng-container *ngFor="let last=last;let elemento of item.valoresAdicionais ">
                    {{elemento.qtde}}x {{elemento.nome}}{{last ? '' : ', '}}
                  </ng-container>

                </span>

                <span *ngIf="item.observacao" class="font-11 mb-0 ml-1"> ( obs.:<i>"
                  {{item.observacao}}"</i> )</span>
              </div>

            </section>

          </div>
        </div>
      </div>

      <div class="produto item_produto pt-2 pb-0"  >
        <div class="media" >
          <div class="media-body">
            <h5 class="mt-0 mb-1 text-muted"><span>Subtotal</span></h5>
          </div>
          <h5 class="mt-0  " [ngClass]="{'com-desconto':pedido.desconto > 0}">
            {{pedido.obtenhaSubTotal() | currency: 'BRL'}}</h5>
        </div>
        <ng-container *ngFor="let descontoPromocional of pedido.promocoesAplicadas; let i = index;">
          <div class="media mt-0 cupom" *ngIf="descontoPromocional.desconto > 0" [class.mb-0]="i + 1 < pedido.promocoesAplicadas.length">
            <div class="media-body" >
              <i class="fas fa-tags mr-1 fa-lg"></i>

              <h5 class="mt-0 mb-0  float-left ml-2 " style="max-width: 80%">
                <span>Promoção</span>   <br>
                <span class="text-muted"  style="font-size: 12px" ><b>{{descontoPromocional.descricao}}</b></span> <br>

              </h5>
            </div>
            <h5 class="mt-1 text-muted">-{{descontoPromocional.desconto | currency: 'BRL'}} </h5>
          </div>

        </ng-container>

        <div class="media mt-0 cupom" *ngIf="pedido.cupom && !pedido.cupom.aplicarNaTaxaDeEntrega">
          <div class="media-body" >
            <i class="fas fa-ticket-alt mr-1 fa-lg"  ></i>

            <h5 class="mt-0 mb-0  float-left ml-2 ">
              <span>Cupom Desconto</span>   <br>
              <ng-container *ngIf="!pedido.cupom.brindeResgate">
                <span class="text-muted"  ><b>{{pedido.cupom.codigo}}</b></span> <br>
                <span class="td-inline cpointer   text-danger  " (click)="removaCupom()"  >remover</span>
              </ng-container>

              <p *ngIf="pedido.cupom.erro" class="text-danger  font-12 mt-1">
                <i class="fa-times-circle fa "></i>
                 {{pedido.cupom.erro}} </p>
            </h5>
          </div>
          <h5 class="mt-1  text-muted" [hidden]="pedido.cupom.cashback">
            <span *ngIf="pedido.desconto > 0">-</span>
             {{pedido.cupom.desconto | currency: 'BRL'}} </h5>
        </div>

        <div class="media" *ngIf="pedido.descontoFormaDePagamento">
          <div class="media-body">
            <h5 class="mt-0 mb-1 text-muted"><span>Desconto {{pedido.pagamento.formaDePagamento.descricao}}</span></h5>
          </div>
          <h5 class="mt-0  "  >
            {{0 - pedido.descontoFormaDePagamento | currency: 'BRL'}}</h5>
        </div>



        <div class="media" *ngIf="pedido.entrega && pedido.entrega.taxaDeEntrega >= -1">
          <div class="media-body">
            <h5 class="mt-0 mb-1 text-muted"><span>Taxa de entrega</span></h5>
          </div>
          <h5 class="mt-0  " *ngIf="!this.calculandoTaxa && pedido.entrega.taxaDeEntrega !== -1">
            {{pedido.obtenhaValorTaxaEntrega() | currency: 'BRL'}}</h5>
          <h5 class="mt-0 " *ngIf="!this.calculandoTaxa && pedido.entrega.taxaDeEntrega === -1">A Informar</h5>
          <h5 class="mt-0  " *ngIf="this.calculandoTaxa">Recalculando <i class="k-icon k-i-loading mr-1"></i></h5>
        </div>


        <div class="media" *ngIf="pedido.taxaFormaDePagamento">
          <div class="media-body">
            <h5 class="mt-0 mb-1 text-muted"><span>Taxa forma de pagamento</span></h5>
          </div>
          <h5 class="mt-0  "  >
            {{pedido.taxaFormaDePagamento | currency: 'BRL'}}</h5>
        </div>


        <div class="media mt-0 cupom" *ngIf="pedido.cupom && pedido.cupom.aplicarNaTaxaDeEntrega">
          <div class="media-body">
            <i class="fas fa-ticket-alt mr-1 fa-lg"  ></i>

            <h5 class="mt-0 mb-1  float-left ml-3 ">
              <span>Cupom Desconto</span>    <span class="td-inline cpointer ml-2 text-blue ml-1" (click)="removaCupom()">remover</span><br>
              <span class="text-muted"  ><b>{{pedido.cupom.codigo}}</b></span>
              <small *ngIf="!pedido.entrega.endereco" class="font-11"  >
                <br> Cupom de taxa de entrega será aplicado assim que você escolher o endereço de entrega
              </small>
            </h5>
          </div>
          <h5 class="mt-1 text-muted" [hidden]="pedido.cupom.cashback">
            <span *ngIf="pedido.desconto > 0">-</span>
            {{pedido.desconto | currency: 'BRL'}} </h5>

          <p *ngIf="pedido.cupom.erro" class="text-danger  font-12 mt-1">
            <i class="fe-alert-triangle mr-1"></i>
             {{pedido.cupom.erro}} </p>


        </div>

        <div class="media mt-0">
          <div class="media-body">
            <h5 class="mt-0 mb-1"><span>Total</span></h5>
          </div>
          <h4 class="mt-0 preco total"><strong>{{pedido.total | currency: 'BRL'}}</strong></h4>

        </div>

        <div class="media mt-0" *ngIf="pedido.totalResgatado">
          <div class="media-body">
            <h5 class="mt-0 mb-1"><span>Total Resgate</span></h5>
          </div>
          <h4 class="mt-0 resgate-preco"><strong>-{{pedido.totalResgatado}} {{pedido.acumulo}}</strong></h4>

        </div>

      </div>

      <div class="produto item_produto pt-2 pb-0"  *ngIf="pedido.pagamento && pedido.pagamento.trocoPara > 0">

        <div class="media" >
          <div class="media-body">
            <h5 class="mt-0 mb-1 text-muted"><span>Dinheiro</span></h5>
          </div>
          <h5 class="mt-0">{{pedido.pagamento.trocoPara | currency: 'BRL'}}</h5>
        </div>

        <div class="media" *ngIf="pedido.pagamento && pedido.troco">
          <div class="media-body">
            <h5 class="mt-0 mb-1 text-muted"><span>Troco</span></h5>
          </div>
          <h5 class="mt-0 preco ">{{pedido.troco | currency: 'BRL'}}</h5>
        </div>

      </div>

      <ng-container *ngIf="!exibirSomenteItens">

        <div class="linha mt-0 mb-0" *ngIf="pedido.itens.length > 3"></div>

        <div class="produto pt-2 pb-1" (click)="abraTelaFormaEntrega()" *ngIf="pedido.entrega.formaDeEntrega">
        <div class="media mt-1" *ngIf="pedido.entrega && pedido.entrega.formaDeEntrega">
          <div class="media-body">
            <h5 class="mt-0 mb-1"><span>Forma de Entrega</span></h5>
          </div>
          <h5 class="mt-0"><strong>{{pedido.entrega.formaDeEntrega}}</strong></h5>
        </div>
        <div class="media mt-1" *ngIf="pedido.dataEntrega">
          <div class="media-body">
            <h5 class="mt-0 mb-1"><span>Agendado para</span></h5>
          </div>
          <h5 class="mt-0"><strong>{{pedido.dataEntrega | date: 'dd/MM/yyyy'}} às {{pedido.horarioEntrega}}</strong></h5>
        </div>
        <div class="media mt-1" *ngIf="empresa && !pedido.dataEntrega && pedido.permiteAgendamento(pedido.entrega.formaDeEntrega, empresa.formasDeEntrega)">
          <div class="media-body">
            <h5 class="mt-0 mb-1"><span>Agendar pedido</span></h5>
          </div>
          <h5 class="mt-0">Clique aqui</h5>
        </div>

        <div class="pt-0 pb-0" *ngIf="pedido.entrega && pedido.entrega.ehDelivery() && pedido.entrega.endereco">
          <h5 class="text-muted"><i class="fas fa-map-marker-alt"></i> Endereço Escolhido</h5>
          <h5>
            {{pedido.entrega.endereco.obtenhaEnderecoCompleto()}}
          </h5>
        </div>
      </div>

       <div class="produto pt-1 pb-1" *ngIf="pedido.informouFormaDePagamento()">
        <div class="media mt-2" *ngIf="pedido.pagamento" (click)="abraTelaFormaDePagamento()"
                                                          [ngClass]="{'flexcolumn': pedido.maisDeUmaFormaPagamento()}">
          <ng-container  *ngIf="!pedido.maisDeUmaFormaPagamento()">
            <div class="media-body" >
              <h5 class="mt-0 mb-1"><span>Forma De Pagamento</span></h5>
            </div>
            <h5 class="mt-0">
              <strong>{{pedido.obtenhaDescricaoPagamento()}}</strong>
            </h5>
          </ng-container>

          <ng-container  *ngIf="pedido.maisDeUmaFormaPagamento()">
            <div class="media-body" >
              <h5 class="mt-0 mb-1"><span>Formas De Pagamento</span></h5>
            </div>
            <div class="clearfix"></div>
            <div class="  mt-2 d-flex flex-column w-100"  >

              <h5 class="mt-0    flex-row text-right"  *ngIf="pedido.cashback" >
                <strong>Fidelidade Cashback:
                  <span class="preco">  {{obtenhaSaldo(pedido.cashback.valor)}} </span>
                 </strong>
              </h5>

              <h5 class="mt-0    flex-row text-right"  *ngIf="pedido.resgate" >
                <strong>Pontos Consumidos:
                  <span class="resgate-preco">  -{{pedido.resgate.valor}} {{pedido.acumulo}} </span>
                </strong>
              </h5>


              <h5 class="mt-0 text-right  flex-row " *ngIf="pedido.pagamento?.formaDePagamento">
                <strong>{{  pedido.pagamento.formaDePagamento.descricao}}:
                  <span class="preco">  {{ pedido.obtenhaValorAhPagar() | currency: "BRL"}}</span>
               </strong>
              </h5>
            </div>


          </ng-container>

        </div>
       </div>

        <div *ngIf="!pedido.informouFormaDePagamento() && pedido.informouFormaDeEntrega()">
          <h5>Forma de Pagamento</h5>
          <div class="form-group mb-2">
            <button class="btn btn-xs btn-outline-blue" type="button" (click)="abraTelaFormaDePagamento()">Escolha Forma de Pagamento</button>
          </div>
        </div>

      <div class="produto pt-1 pb-0"  >
        <div class="media mt-2" *ngIf="pedido.contato?.nome && pedido.contato?.telefone"  (click)="abraTelaDadosCliente()">
          <div class="media-body">
            <h5 class="mt-0 mb-1"><span class="titulo_escuro">Meus dados</span></h5>
          </div>
          <h5 class="mt-0 text-right">
            <strong>{{pedido.contato.nome}}</strong> <br>
            <label class="text-muted mt-1" *ngIf="pedido.contato.cpf">{{pedido.contato.cpf | cpf}}  / </label>
            <label class="text-muted mt-1" *ngIf="!pedido.contato.naoIdentificado">{{pedido.contato.telefone | telefone}}</label>

            <p class="text-warning" *ngIf="this.PassoIdentificacao.mensagem">
              <i class="fa fa-exclamation-triangle"></i> pendencia:
              <b>{{this.PassoIdentificacao.mensagem}}</b></p>
          </h5>
        </div>

        <div *ngIf="(!pedido.contato.nome || !pedido.contato.telefone) && (empresa && empresa.tema !== 'quiosque')">
          <h5><span class="titulo_escuro">Meus dados</span></h5>

          <div class="form-group mb-2"  >
            <button class="btn btn-xs btn-outline-blue" type="button" (click)="abraTelaDadosCliente()"  *ngIf="!pedido.contato.telefone">
              Entrar ou Cadastrar</button>
            <button class="btn btn-xs btn-outline-blue" type="button" (click)="abraTelaDadosCliente()" *ngIf="pedido.contato.telefone">
              Completar cadastro</button>
          </div>
        </div>
      </div>

      </ng-container>

      <span *ngIf="pedido.valoresAdicionais && pedido.valoresAdicionais.length > 0">
        <div class="produto item_produto pt-2 pb-0"  >
          <div class="media" >
            <div class="media-body">
              <h5 class="mt-0 mb-1 text-muted"><span>Extras do Pedido:</span></h5>
            </div>
            <h5 class="mt-0">
              {{pedido.obtenhaValorAdicionais() | currency: 'BRL'}}</h5>
          </div>
          <ng-container *ngFor="let last=last;let elemento of pedido.valoresAdicionais ">
          {{elemento.qtde}}x {{elemento.nome}}{{last ? '' : ', '}}
          </ng-container>
        </div>
      </span>

      <ng-container *ngIf="estaPontuando()">
        <h6 class="clearfix float-right " style="line-height: 20px">
          <i class="fe-star-on   text-blue"></i>
          <ng-container  *ngIf="empresa.integracaoPedidoFidelidade.plano.tipoDeAcumulo!=='Reais'">

            Compre e ganhe  <span class="" *ngIf="pedido?.pontosReceber"><b>{{pedido.pontosReceber}}</b></span>
                 no programa  <span class=""><b>{{empresa.integracaoPedidoFidelidade.plano.nome}}</b></span>
          </ng-container>
          <ng-container  *ngIf="empresa.integracaoPedidoFidelidade.plano.tipoDeAcumulo==='Reais'">

            <ng-container  *ngIf="(!pedido.contato || pedido.contato.naoIdentificado) && pedido.pontosReceber > 0">
              Ao optar por não informar nome e telefone você esta
               deixando de acumular <span class=""><b>{{pedido.pontosReceber}}</b></span>  nesse pedido
            </ng-container>

            <ng-container
              *ngIf="!pedido.pontosReceber && pedido.total   && empresa.integracaoPedidoFidelidade.plano.valorMinimoPontuar">
              Compre acima de <b>{{empresa.integracaoPedidoFidelidade.plano.valorMinimoPontuar | currency: "BRL"}} </b>
              e ganhe {{valorPencentual(empresa.integracaoPedidoFidelidade.atividade.cashback)}}% de cashback

              <span *ngIf="empresa.integracaoPedidoFidelidade.plano.vencimento">
                para usar até {{ empresa.integracaoPedidoFidelidade.plano.vencimento | date: 'dd/MM'}}
              </span>

              <span *ngIf="empresa.integracaoPedidoFidelidade.plano.validade">
                 para usar nos próximos {{empresa.integracaoPedidoFidelidade.plano.validade}} dias
              </span>
            </ng-container>

            <ng-container *ngIf="pedido.contato && !pedido.contato.naoIdentificado && pedido.pontosReceber">
              Com este pedido, você ganhará <span class=""><b>{{pedido.pontosReceber}}</b></span> de cashback

              <span *ngIf="empresa.integracaoPedidoFidelidade.plano.vencimento">
                para usar até {{ empresa.integracaoPedidoFidelidade.plano.vencimento | date: 'dd/MM'}}
              </span>

              <span *ngIf="empresa.integracaoPedidoFidelidade.plano.validade">
                 para usar nos próximos {{empresa.integracaoPedidoFidelidade.plano.validade}} dias
              </span>

            </ng-container>
          </ng-container>
         </h6>
        <div class="clearfix"></div>
      </ng-container>

      <div class="produto mt-3 item_produto" *ngIf="!pedido.cupom && (!pedido.mesa  || empresa.permitirCupomMesas)">
        <div class="media ">
          <div class="media-body" >

            <ng-container *ngIf="aplicarCupom">
              <div  style="cursor: pointer"  (click)="toggleAplicarCupom()" class="mb-1 div_cupom">
                <i class="fas fa-ticket-alt mr-1 fa-lg"  ></i>
                <label>Adicione um código promocional</label>
                <i class="fa-caret-up fa float-right"></i>

              </div>
              <div class="input-group mb-2">
                <input type="text" class="form-control" name="codigoCupom" placeholder="Código do cupom"
                       [(ngModel)]="pedido.codigoCupom" [disabled]="aplicandoCupom"  >
                <div class="input-group-append">
                  <button class="btn  btn-outline-blue" type="button" (click)="apliqueCupom()">Aplicar</button>
                </div>
              </div>
            </ng-container>


            <div *ngIf="cuponsSelecionar.length">
              <div class="cpointer d-flex justify-content-between" (click)="escolherCupons($event)"   >
                <div class=""  >
                  <i class="fas fa-ticket-alt mr-1 fa-lg"  style="padding-top: 1px" ></i>
                  <label style="padding-top: 2px"> {{cuponsSelecionar.length - 1}} {{cuponsSelecionar.length - 1 ===  1? 'cupom disponível': 'cupons disponíveis'}} </label>
                </div>
                <div class="">
                  <button class="btn btn-sm btn-outline-blue" [disabled]="selecionandoCupom" >Adicionar</button>
                </div>
              </div>
            </div>


            <div (click)="toggleAplicarCupom()" style="cursor: pointer" *ngIf="!aplicarCupom && !cuponsSelecionar.length" class="mb-2 div_cupom">
                <i class="fas fa-ticket-alt mr-1 fa-lg"  ></i>
                <label>Possui cupom?</label>
                <i class="fa-caret-down fa float-right"></i>
            </div>

            <div class="clearfix"></div>
            <p *ngIf="erroCupom" class="text-danger mt-0"><b>{{erroCupom}}</b></p>
          </div>
        </div>
      </div>

      <div style="height: 40px" class="mobile"></div>

      <!-- Adicionando a mensagem de erro fora do footer para que seja sempre visível -->
      <p class="alert alert-danger mt-2" role="alert" *ngIf="msgErro">
        <i class="fa fa-times mr-2"></i> {{msgErro}}
      </p>

      <footer class="footer" [hidden]="exibirSomenteItens || !this.exibirBotoes">
        <p class="alert alert-success" role="alert" *ngIf="msg">
          <i class="mdi mdi-check-all mr-2"></i>  {{msg}}
        </p>

        <ng-container *ngIf="empresa && !pedido.entrega.foiPreenchida() && !pedido.mesa">
          <div class="alert alert-danger  mt-2" [hidden]="msgErro"  *ngIf="pedido.ultrapassouValorMaximo(FormaDeEntrega.RECEBER_EM_CASA, empresa.formasDeEntrega)">
            <i class="fa fa-times mr-2"></i>
            As entregas são feitas somente para valores abaixo de
              {{pedido.obtenhaValorMaximoParaEntrega(FormaDeEntrega.RECEBER_EM_CASA, empresa.formasDeEntrega) | currency: "BRL"}}
          </div>

          <div class="alert alert-danger mt-2" [hidden]="msgErro"  *ngIf="!pedido.temValorMinimo(FormaDeEntrega.RECEBER_EM_CASA, empresa.formasDeEntrega)">
            <i class="fa fa-times mr-2"></i>
            As entregas são feitas somente para valores acima de
            {{pedido.obtenhaValorMinimoParaEntrega(FormaDeEntrega.RECEBER_EM_CASA, empresa.formasDeEntrega) | currency: "BRL"}}
          </div>

        </ng-container>

        <div class="footer">
          <div class="row" style="padding: 15px;">
            <div></div>
            <div class="col" *ngIf="!pedido.codigo">

              <ng-container *ngIf="empresa && empresa.tema === 'quiosque'">
                <button class="btn btn-primary btn-block waves-effect width-lg btn-lg"
                        type="button" (click)="finalizarPedidoTotem()" *ngIf="!estahCompleto">
                  Finalizar Pedido <i class="fe-chevron-right  "></i>
                </button>
              </ng-container>

              <ng-container *ngIf="!desktop && empresa && empresa.tema !== 'quiosque'">
                <button class="btn btn-blue btn-block waves-effect btn-lg-block"
                        type="button" (click)="proximoPasso()" *ngIf="!estahCompleto">
                  Proximo <i class="fe-chevron-right  "></i>
                </button>

                <button class="btn btn-primary btn-block waves-effect width-lg"
                        type="submit"   (click)="fazerPedido(false)" *ngIf="estahCompleto"
                        [disabled]="eviandoPedido || calculandoTaxa" >
                  <i class="k-icon k-i-loading mr-1" *ngIf="eviandoPedido"></i>
                  Fazer Pedido
                </button>
              </ng-container>


              <button class="btn-finalizar-pedido btn btn-blue btn-block waves-effect btn-lg-block"
                      type="button" (click)="vaParaTelaFinalizarDesktop()" *ngIf="desktop">
                 Finalizar Pedido
              </button>



            </div>

            <div class="col" *ngIf="pedido.codigo">
              <a class="btn btn-blue btn-block waves-effect width-lg mb-1" *ngIf="pedido.gerarLinkPagamento"
                 [href]="dominiosService.obtenhaUrlAcompanharPedido(pedido)"  >
                Ver detalhes
              </a>
              <!-- Removido pois estamos abrindo automaticamente
              <a class="btn btn-success btn-block waves-effect width-lg mb-1"  [href]="urlWhatsapp" target="_blank"  *ngIf="!pedido.gerarLinkPagamento" >
                <i class="fab fa-whatsapp fa-lg mr-1" ></i>
                Enviar para Whatsapp {{empresa?.nome}}
              </a>
              -->
            </div>
          </div>
        </div>
      </footer>
    </div>
  </form>

</div>

<div id="alertaGerandoPagamento" class="modal fade" tabindex="-1" role="dialog"   aria-modal="true" data-backdrop="static">
  <div class="modal-dialog ">
    <div class="modal-content">
      <div class="modal-body p-4">
        <div class="text-center">
          <h5 class="mt-2">
            <i class="k-icon k-i-loading mr-1"></i>
            Aguarde que você será redirecionado para tela de  pagamento.</h5>

        </div>
      </div>
    </div>
  </div>
</div>

<kendo-dialog title="Selecionar Cupom" (close)="fecheModalCupom()"
              *ngIf="exibirModalEscolherCupons">
<div id="modalEscolherCupons" *ngIf="this.cuponsSelecionar.length"  tabindex="-1" >
  <div class="text-center">
    <div class="input-group mb-2">
      <input type="text" class="form-control" name="codigoCupom" placeholder="Código do cupom"
             [(ngModel)]="pedido.codigoCupom" [disabled]="aplicandoCupom"  >
      <div class="input-group-append">
        <button class="btn  btn-outline-blue" type="button" (click)="apliqueCupomTelaSelecionar()">Aplicar</button>
      </div>
    </div>
    <form style="padding-bottom: 0px;">
      <div *ngFor="let cupom of cuponsSelecionar" class="card">
        <div class="ml-0 radio radio-blue" >
          <div class="text-left">
            <label  class="w-100 p-2">


              <i class="fas fa-ticket-alt fa-lg ml-2" ></i>
              <span class="nome_opcao " >{{cupom.nome}}</span>
              <input  [(ngModel)]="cupomSelecionado"
                      class="float-right"
                      [value]="cupom"
                      name="cupomSelecionado" type="radio" kendoRadioButton
                      (ngModelChange)="escolheuCupom(cupom)"
              />

            </label>


              <ng-container *ngIf="cupom.fraseDescritiva">
                <br>

                <span class="descricao " class="mr-2" style="margin-left: 33px;display:block">{{cupom.fraseDescritiva}}</span>

              </ng-container>

              <div class="mr-2 mt-2 mb-2" *ngIf="cupom.codigo">
                <a href="#" (click)="verRegras(cupom, $event)" style="margin-left: 33px;" *ngIf="cupom.codigo && !cupom.verRegras">Ver regras</a>
                <a href="#" (click)="verRegras(cupom, $event)" style="margin-left: 33px;" *ngIf="cupom.codigo && cupom.verRegras">Esconder regras</a>
                <ng-container *ngIf="cupom.validade">
                  <span class="float-right text-muted mr">Validade: {{cupom.validade | date: 'dd/MM/yyyy'}}</span>

                </ng-container>
              </div>

              <div *ngIf="cupom.verRegras" style="margin-left: 33px" class="mr-2 mb-2">
                <span class="text-muted" *ngIf="cupom.valorMinimo" >- Válido para pedidos de no mínimo {{cupom.valorMinimo | currency: 'BRL'}}<br></span>
                <span class="text-muted" *ngIf="cupom.delivery" >- Válido apenas para pedido delivery<br></span>
                <span class="text-muted" *ngIf="cupom.retirada" >- Válido apenas para pedido retirado pessoalmente<br></span>
                <span class="text-muted" *ngIf="cupom.comerNoLocal" >- Válido apenas para pedido consumido na loja<br></span>
                <span class="text-muted" *ngIf="cupom.qtdeMaxima && cupom.qtdeMaxima == 1">- No máximo um uso por cliente<br></span>
                <span class="text-muted" *ngIf="cupom.qtdeMaxima && cupom.qtdeMaxima > 1">- No máximo {{cupom.qtdeMaxima}} usos por cliente<br></span>
                <span class="text-muted" *ngIf="cupom.percentualMaximoDescontoProduto">- Não cumulativo com outras promoções<br></span>
                <span class="text-muted" *ngIf="cupom.quantidade && cupom.validade">- Valido até {{cupom.validade | date: 'dd/MM/yyyy'}}
                  ou até atingir a quantidade de cupons disponíveis<br></span>
                <span class="text-muted" *ngIf="!cupom.quantidade && cupom.validade">- Valido até {{cupom.validade | date: 'dd/MM/yyyy'}}<br></span>
                <span class="text-muted" *ngIf="cupom.quantidade && !cupom.validade">- Valido até atingir a quantidade de cupons disponíveis<br></span>
                <span class="text-muted" *ngIf="!cupom.valorMinimo">- Válido para pedido de qualquer valor<br></span>
                <span class="text-muted" *ngIf="cupom.categorias?.length">
                  - É necessário um produto da(s) categoria(s) <strong>{{getCategoriasNomes(cupom)}}</strong> no carrinho<br></span>

                <span class="text-muted" *ngIf="cupom.produtosTemplateTamanho?.length">
                  - É necessário um produto do(s) tamanho(s) <strong>{{getTamanhosNomes(cupom)}}</strong> no carrinho<br></span>

                <span class="text-muted" *ngIf="cupom.produtos && cupom.produtos.length">
                  - É necessário um produto(s) <strong>{{getNomesProdutos(cupom)}}</strong> no carrinho<<br></span>

              </div>

            </div>
        </div>
      </div>
    </form>

    <!--<button type="button" class="btn btn-info my-2" data-dismiss="modal" >Continuar</button>-->
  </div>
</div>
</kendo-dialog>
