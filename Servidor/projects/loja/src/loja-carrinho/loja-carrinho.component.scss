.font-12{
  font-size: 12px;
}

.footer {
  border-top: solid 1px #eeeeee;
  background-color: #f7f8f8;
  padding: 0px;
  position: fixed;
  left: 0px !important;
  z-index: 10;
}

#form:focus-within .footer {
  position: initial;
}

.flexcolumn{
   flex-direction: column;
}


.nome_opcao {
  margin-left: 10px;
  position: relative;
  top: 1px;
  display: inline-block;
}

.container_total {
  margin-left: -12px;
  margin-right: -12px;
}

.label_empresa {
  font-weight: bold;
}

.produto {
  border-bottom: solid 1px #e8e8e8;



}

.capa_empresa {
  height: 200px;
  background-size: cover;
}

.capa_empresa.centralizada {
  z-index: 10;
  background-repeat: no-repeat;
  background-position-x: center;
  background-size: cover;
}

.cartao {
  background: white;
  margin-left: auto;
  margin-right: auto;
  padding: 15px;
}

.cartao.conteudo {
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  box-shadow: 0 4px 10px -2px #E2E3E3;
}

.cartao.conteudo.topo {
  margin-top: 0px;
  width: 100%
}

/*
.cartao.conteudo {
  box-shadow: 0 4px 10px -2px #E2E3E3;
  min-height: 190px;
  border-top: 0;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}
*/


.cartao.semborda {
  margin-top: 20px;
  border: 0 none;
  background-color: transparent;
  padding: 0;
}

.bg-content {
  display: none;
}



.imagem_empresa {
  width: 80px;
  height: 80px;
  float: left ;
}

.detalhes_empresa  {
  float:left;
  margin-left: 10px;
  display: inline-block;
  width: calc(100% - 90px);
}

.nome_empresa {
  font-size: 24px;
  color: black;
  font-weight: 500;
  display: block;
  line-height: 1.2;
  max-height: 1.2em;
  overflow: hidden;
}

.endereco {
  font-size: 11px;
}

.whatsapp {
  display: block;
  margin-bottom: 5px;
}

.whatsapp span{
  font-size:12px;
  font-weight: 600;

  color: #199D0F
}

.dados_empresa {
  min-height: 90px;
  overflow: hidden;
}

.linha {
  border-bottom: #EFEFEF solid 1px;
}

.descricao_empresa {
  margin: 10px;
  font-size: 12px;
  font-weight: 400;

}

.menu {
  color: #525252;
  margin-top: 15px;

}

.brinde {
  margin-top: 10px;
}
.valor {
  position: absolute;
  color: white;
  font-size: 20px;
  top: 10px;
  width: 100%;
  text-align: center;
}

.row {
  padding-left: 5px;
  padding-right: 5px;
}

.container_selo {
  position: relative;
}

.brinde {
  text-align: center;
  position: relative;
}


.nome_brinde {
  display: inline-block;
  margin-top: 5px;
  font-size: 16px;
  background: #4b4b4b;
  color: white;
  margin-left: 2px;
  padding: 5px 10px 5px 10px;
  border-radius: 50px;
  font-weight: 200;
}

.faltam_selos {
  color: #3e48bc;
  margin-bottom: 10px;
}

.container-scroll {
  overflow-x: scroll;
  overflow-y: hidden;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
  margin-right: -20px;
}

.container-scroll .col {
  margin-left: 5px;
  margin-right: 5px;
}


.foto_brinde {
  display: block;
  float:none;
  margin:0 auto;
  border-radius: 30px;
  margin-top: 5px;
  width: 100%;
}


.foto_ambiente {
  display: block;
  float:none;
  margin:0 auto;
  border-radius: 20px;
}


.container-scroll .caixa_brinde {
  border-radius: 5px;
  box-shadow: 0 4px 10px -2px #E2E3E3;
  margin-bottom: 10px;
  height: 250px !important;
  margin-left: -3px;
  margin-right: -3px;
  padding-top: 5px;
  background: white;
}


.container-scroll .foto_brinde {
  width: unset;
  width: 100%;
}

.nome_brinde_pontos {
  font-size: 15px;
  font-weight: 600;

}


.container-scroll .preco_troca {
  font-size: 11px;
  font-weight: 400;
  line-height: 1em;
}

.container-scroll .preco_troca.nao_atingiu {
  color: #F67682;
}

.container-scroll .preco_troca.atingiu {
  color: #6DB31B;
  margin-right: -20px;
}

.resgate-preco{
  color: #F67682;
}

.botoes {
  margin: 20px;
  text-align: center;
  font-size: 16px;
  font-weight: 400;
}

.botao {
  padding: 15px;
}

.botao.verde{
  background: #6DB31B;
  color:  white;
}

.botao.azul {
  border: #1c95d4 solid 1px;
  margin-top: 10px;
  color: #1c95d4;
}

.icone.whatsapp {
  width: 16px;
  display: inline-block;
  margin: 0
}

.icone_voltar {
  display: inline-block;
  fill: #4a81d4;
  width: 32px;
  height: 32px;
  vertical-align: middle;
}



.float{
  position:fixed;
  width:60px;
  height:60px;
  bottom:40px;
  right:40px;
  background-color:#25d366;
  color:#FFF;
  border-radius:50px;
  text-align:center;
  font-size:30px;
  box-shadow: 2px 2px 3px #999;
  z-index:100;
}

.my-float{
  margin-top:16px;
}

.container_imagem {
  overflow: hidden;
  display: flex;
  max-width: 100%;
}

.fidelidade {
  height: 16px;

  width: 24px;
  background: #3B86FF;
  text-align: center;
  float:left;
  line-height: 1em;
  margin: 2px 3px;
}

.azul .coracao{
  display: inline-block;
  fill: white;
  width: 16px;
  height: 16px;
  vertical-align: middle;
  margin: 0;
}

.azul {
  color: #3B86FF;
  font-weight: bold;
}


.bolinha {
  margin: 5px;
  width: 8px;
  height: 8px;
  background: #6DB31B;
  border-radius: 100px;
  float: left;
}

.horario {
  padding-top: 2px;
}

.bolinha.fechado {
  background: red;
}

.horario .descricao {
  font-size: 11px;
  font-weight: bold;
}

.icon-fechar {
  width: 32px;
  height: 32px;
  float: left;
  margin-left: 5px;
}

.slides-fotos h3, .slides-produtos h3{
  text-align: center;
  line-height: 32px;
}

.slides-fotos, .slides-produtos {
  position: fixed;
  overflow: auto;
  z-index: 1000;
  top:0px;
  background: white;
  height: 100%;
  width: 100%;
}

.slides-produtos {
  padding-top: 60px;
  background: rgba(255, 255, 255, 0.9);
  height: 100%;
}


.container-fotos {
  background: black;
}

.cartao.descricao {
  margin-top: 15px;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-right-radius: 10px;
  box-shadow: 0 4px 10px -2px #E2E3E3;

}
.grande {
  font-size: 18px;
  font-weight: bold;
}

.botao_produto {
  border: 1px solid black;
  padding: 15px;
  border-radius: 30px;
  text-align: center;
  font-weight: 500;
  font-size: 14px;
}

.botao_produto.verde {
  border: 1px solid #3fad36;
  color: #3fad36;

}

.titulo-produtos {
  background: white;
}
.slides-produtos .icon-fechar {
  width: 32px;
  height: 32px;
  float: left;
  margin-left: 0;
  margin-top: 9px;
}

.titulo{
  h3{
    position: relative;top: 5px;
  }
  .btn{
    float: right;
  }

  &.com-mesa{
    h3{
      font-size: 1.3rem;
    }
  }
}


@media screen and (min-width: 768px) {
  .cartao.conteudo, .cartao.semborda, .slides-fotos, .slides-produtos   {
    max-width: 90%;
    /* text-align: center; */
    margin-left: auto;
    margin-right: auto;
  }

  .bg-content {
    z-index: 0;
    position: absolute;
    left: 0px;
    top: 0px;
    right: 0px;
    width: 120%;
    height: 120%;
    margin-left: -10%;
    background-position: 0px 0px, 50% 50%;
    background-size: auto, cover;
    background-repeat: repeat, no-repeat;
    opacity: 1;
    -webkit-filter: blur(24px);
    filter: blur(24px);
    display: block;

  }

  .content {
    position: relative;
    z-index: 10;

  }

  .slides-fotos, .slides-produtos {
    left: 20%;
  }

  .sobre_nos {
    border-radius: 5px;
    box-shadow: 0 4px 10px -2px #E2E3E3;
    padding: 10px;
    padding-bottom: 5px;
    background: white;
    margin-top: 10px;
  }
  .brinde {
    margin-top: 0 ;
  }

  .container-scroll {
    overflow-x: unset;
    overflow-y: unset;
    white-space: unset;
    -webkit-overflow-scrolling: touch;
    margin-right: 0px;
  }

  .capa_empresa.centralizada {
    height: 310px;
    max-width: 90%;
    margin: 0 auto;
    box-shadow: 0 4px 10px -2px #a9aaaa;
  }

  .capa_empresa.desfocada {
    height: 305px;
  }

  .cartao.conteudo {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 30px;
    border-top-right-radius: 0px;
    box-shadow: 0 4px 10px -2px #E2E3E3;
  }

  .cartao.conteudo.topo {
    margin-top: 0px;
  }
}

.icone.insta {
  fill: #525252;
}

.cinza a {
  color: #525252 ;
}

.FlexEmbed {
  display: block;
  overflow: hidden;
  position: relative;
}

.FlexEmbed:before {
  content: "";
  display: block;
  width: 100%;
}


.FlexEmbed--2by1:before {
  padding-bottom: 25%;
}

.FlexEmbed.desfocada {
  background-position: 0px 0px, 50% 50%;
  background-size: auto, cover;
  background-repeat: repeat, no-repeat;
  opacity: 1;
  -webkit-filter: blur(12px);
  filter: blur(12px);
  z-index: -1;
  position: absolute;
  width: 100%;
  max-width: 100%;
  display: block;
  top: 0px;
}

.CoverImage {
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  margin: 0 auto;
  max-height: 300px;
  max-width: 90%;
}

@media (max-width: 992px) {

  .nome_empresa {
    font-size: 16px !important;
  }

  .cartao.conteudo.topo {
    width: 100%;
  }
  .cartao{
    padding: 10px;
  }

  .titulo{

    .btn{
      float: none;
      margin-top: 10px;
      position: absolute;
      right: 0;
      top: 4px;
    }

    &.com-mesa{
      h3{
        font-size: 1.2rem;
      }
    }
  }


}

@media (min-width: 992px) {
  .cartao.conteudo, .cartao.semborda, .slides-fotos, .slides-produtos {
    max-width: 100%;
    /* text-align: center; */
    margin-left: auto;
    margin-right: auto;
  }

  .capa_empresa.centralizada {
    height: 310px;
    max-width: 100%;
    margin: 0 auto;
    box-shadow: 0 4px 10px -2px #a9aaaa;
  }

  .CoverImage {
    max-width: 100%;
  }
}

.font-11{
  font-size: 11px;
}

.nao_mobile {
  display: none;
}

.fa-ticket-alt{
  transform: rotate(-45deg);
}

.item_produto {
  h5:not(.total) {
    font-size: 0.95rem !important;
  }

  .com-desconto{
    /* text-decoration: line-through; */

  }
  .text-danger {
    color: #f1556cd1 !important;
  }

  .cupom{
    padding: 15px 0px;
    border: 1px solid #cccccc85;
    border-left: 0;
    border-right: 0;
    margin: 20px 0px;
    .media-body{
      position: relative;    padding-left: 18px;
    }

    .fa-ticket-alt{

      top: 16px;
      /* margin-right: 5px; */
      display: block;
      position: absolute;
      top: 5px;
      left: 0px;
    }

    .fa-tags{
      float: left;
      top: 16px;
      /* margin-right: 5px; */
      display: block;
      position: relative;
    }

    span{
      line-height: 25px;
    }
  }
}

.cart-item{

  .gap-4 {
    gap: 1rem;
  }
  .flex-row {
    flex-direction: row;
  }
  .flex {
    display: flex;
  }
  .justify-between {
    justify-content: space-between;
  }
  .items-center {
    align-items: center;
  }
  img{
    max-width: 100%;
    display: block;
    vertical-align: middle;
  }
  .full{
    width: 100%;
    h4{
      font-size: 15px;
    }
  }
  .badge{
    position: relative;
    top: -5px;
    line-height: 12px;
    padding-top: 3px;
    font-weight: bold;
  }
  .opcoes{
    span{
      color: #343a40c7;
      font-weight: bold;
    }
  }
}


.preco {
  color: #6db31b;
  font-size: 14px;
  &.total{
    font-size: 18px;
  }

  .badge{
    font-size: 10px;position: relative;top: 2px;
    padding-top: 3px;
  }
}

.preco_troca {
  font-weight: 600;
}

@media (min-width: 992px) {
  .nao_mobile {
    display: block;
  }

  .mobile {
    display: none;
  }

  .espaco_topo {
    margin-top: 10px !important;
  }

  .footer {
    position: initial !important;
    margin-top: 0px;
  }

  .quiosque {
    .footer {
      position: fixed !important;
    }
  }

  .desktop {
    overflow: auto;
    max-height: calc(100vh - 400px);
    min-height: 85px;
  }
}

.linha {
  border-top: 1px solid #ececec;
  border-bottom: 1px solid #fff;
  margin-left: 0px;
  margin-right: 15px;
}


.footer .alert{
  border-radius: 0;
  margin-bottom: 0px;
}

.fundo{

}

form{
  padding-bottom: 50px;
}

.descricao_produto{
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  -webkit-line-clamp: 2;
}


::ng-deep .k-breadcrumb .k-breadcrumb-link, ::ng-deep .k-breadcrumb .k-breadcrumb-root-link {
  padding: 15px 1px;
}

.breadcrumb-passos{
  background-color: #f7f8f8 !important;
  border-bottom: 1px solid #eee;
  .fe-shopping-cart{
    font-size: 19px;
  }
}

::ng-deep  .k-breadcrumb .k-breadcrumb-item {
  background-color: #f7f8f8 !important;
}

::ng-deep .k-breadcrumb-container{

  .item{
    padding: 15px 1px;

    padding-bottom: 5px;
    margin-bottom: 10px;
    span{
      font-weight: bold;
      font-size: 13px;
    }
    border-bottom: 2px solid transparent ;
    &.proximo{
      border-color:  #aaa;
    }

    &.incompleto{

      span{
        font-weight: normal;
        color: #656565bf;
      }

    }
  }
}


.ng-image-slider .ng-image-slider-container .main .main-inner {
  -webkit-transition: none !important;
  -moz-transition: none !important;
  -o-transition: none !important;
  transition: none !important;
}

::ng-deep .black_friday_2022 {
  .div_carrinho {
    .titulo {
      h4.d-inline {
        color: #676767;
      }
    }

    .linha {
      border-bottom: 1px solid transparent;
      border-top: 1px solid transparent;
    }

    .produto {
      border-bottom: solid 1px #676767;
    }

    .item_produto {
      border-bottom: solid 1px #676767;

      h5 {
        color: #cfcfcf;
      }

      h6.text-blue {
        color: #b57e2b !important;
      }
    }

    .footer {
      background: var(--cor-fundo-elementos) !important;
      border-top: solid 1px #676767;
    }

    .btn-outline-blue {
      color: #b57e2b;
      border-color: #b57e2b;
    }

    .btn-outline-blue:hover {
      background: #b57e2b;
      color: #3a2400;
      border-color: #986716;
    }

    .btn-blue {
      background: #b57e2b;
      color: #0e0e0e;
      font-weight: bold;
      border-color: #986716;
    }
  }

  .div_cupom {
    color: #cfcfcf;
  }

  .titulo_claro {
    color: #cfcfcf;
  }

  .titulo_escuro {
    color: #676767;
  }

  .breadcrumb-passos {
    background-color: #0e0e0e !important;
    border-bottom: solid 1px #676767;
  }

  .k-breadcrumb  {
    background-color: #0e0e0e !important;
    color: #676767;
    border-color: #676767;

    .k-breadcrumb-item {
      background-color: #0e0e0e !important;

      .item {
        span {
          color: #cfcfcf!important;
        }
      }
    }
  }
}

.chinainbox{
  .btn-blue, .btn-primary {
    background-color: #e52a28de !important;
    border-color: #e52a28 !important;;
  }

  .btn-secondary {
    background-color: #ff9800d6  !important;
    border-color: #e79e406e  !important;
  }
}

.carnaval {
  .btn-blue, .btn-primary {
    background-color: #D825A9 !important;
    border-color: #D825A9 !important;
  }

  .text-blue{
    color: #78b21b !important;
  }

  .btn-secondary {
    background-color: #78b21b  !important;
    border-color: #78b21b  !important;
  }
}


::ng-deep .quiosque {
  .btn {
    padding: 12px;
  }

  .btn-rounded {
    padding: 0.45rem 0.9rem;
  }
}


::ng-deep .tema-personalizado {
  .div_carrinho {
    background: var(--cor-fundo-site, #fff) !important;

    h6.text-blue, i.text-blue, h6 {
      color: var(--cor-texto-secundaria, #333) !important;
    }

    h4 {
      color: var(--cor-texto-primaria, #333) !important;
    }

    span {
      color: var(--cor-texto-secundaria, #333) !important;
    }

    .titulo {
      h4.d-inline {
        color: var(--cor-texto-secundaria, #999);
      }
    }

    .linha {
      border-bottom: 1px solid transparent;
      border-top: 1px solid transparent;
    }

    .produto {
      border-bottom: solid 1px var(--cor-borda, #999);
    }

    .item_produto {
      border-bottom: solid 1px var(--cor-borda, #999);

      h5 {
        color: var(--cor-texto, #333);
      }

      h6.text-blue, i.text-blue {
        color: var(--cor-texto, #333) !important;
      }
    }

    .footer {
      background: var(--cor-fundo-site, #fff);
      border-top: solid 1px var(--cor-borda, #999);


      h6.text-blue, i.text-blue {
        color: var(--cor-texto, #333) !important;
      }
    }

    .btn-outline-blue {
      color: var(--cor-texto, #333);
      border-color: var(--cor-texto, #333);
    }

    .btn-outline-blue:hover {
      background: #b57e2b;
      color: #3a2400;
      border-color: var(--cor-texto, #333);
    }

    .btn-blue {
      background: var(--cor-texto, #333);
      color: var(--cor-fundo-site, #fff);
      font-weight: bold;
      border-color: var(--cor-texto, #333);
    }
  }

  .div_cupom {
    color: #cfcfcf;
  }

  .titulo_claro {
    color: var(--cor-texto, #333);
  }

  .titulo_escuro {
    color: var(--cor-texto-secundaria, #999);
  }

  .breadcrumb-passos {
    background-color: var(--cor-fundo-site, #fff) !important;
    border-bottom: solid 1px var(--cor-texto-secundaria, #999);
  }

  .k-breadcrumb  {
    background-color: var(--cor-fundo-site, #fff) !important;
    color: var(--cor-texto-secundaria, #999);
    border-color: var(--cor-texto-secundaria, #999);

    .k-breadcrumb-item {
      background-color: var(--cor-fundo-site, #fff) !important;

      .item {
        span {
          color: var(--cor-texto, #333) !important;
        }
      }
    }
  }
}

::ng-deep .desktop {
  .tema-personalizado {
    .div_carrinho {
      background: var(--cor-fundo-elementos, #fff) !important;
    }
  }
}

// Estilos para a notificação de erro
::ng-deep {
  .erro-notificacao {
    font-weight: bold;
    z-index: 9999 !important; /* Garantir que fique acima de todos os elementos */
    margin-top: 20px !important; /* Dar espaço do topo da tela */
    cursor: pointer; /* Indicar que pode ser clicado */

    .k-notification-content {
      padding: 12px 25px;
      font-size: 14px;
    }

    .k-i-close {
      /* Destacar o botão de fechar */
      font-size: 14px;
    }
  }

  /* Garantir que o container de notificação também tenha z-index alto */
  .k-notification-container {
    z-index: 9999 !important;
  }
}
